# 优惠券计价接口文档

## 概述

基于优惠券的计价接口，支持用户选择产品和优惠券后计算最终订单金额和优惠详情。

## 接口信息

- **URL**: `/api/v1/wechat-mini-app/coupon/coupon-pricing`
- **方法**: `POST`
- **认证**: 需要在Header中提供token

## 请求参数

### Headers
```
Content-Type: application/json
token: {用户认证token}
```

### Body
```json
{
  "user_id": 1,
  "products": [
    {
      "product_id": 1,
      "quantity": 2
    },
    {
      "product_id": 2,
      "quantity": 1
    }
  ],
  "coupon_usage_record_ids": [1, 2]
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| user_id | int | 是 | 用户ID |
| products | array | 是 | 产品数量组合列表 |
| products[].product_id | int | 是 | 产品ID |
| products[].quantity | int | 是 | 产品数量（必须大于0） |
| coupon_usage_record_ids | array | 否 | 优惠券使用记录ID列表 |

## 响应格式

### 成功响应 (200)
```json
{
  "status": 200,
  "message": "计价成功",
  "data": {
    "pricing_result": {
      "order": {
        "total_amount": 80.0,
        "payable_amount": 60.0,
        "order_items": [
          {
            "product_id": 1,
            "product_name": "产品1",
            "quantity": 2,
            "unit_price": 25.0,
            "final_unit_price": 20.0,
            "subtotal": 50.0,
            "payable_amount": 40.0
          },
          {
            "product_id": 2,
            "product_name": "产品2",
            "quantity": 1,
            "unit_price": 30.0,
            "final_unit_price": 20.0,
            "subtotal": 30.0,
            "payable_amount": 20.0
          }
        ]
      },
      "discount": {
        "total_discount": 20.0,
        "coupons": [
          {
            "coupon_usage_record_id": 1,
            "coupon_id": 1,
            "coupon_name": "现金券",
            "quantity": 1,
            "discount_amount": 20.0
          }
        ]
      }
    },
    "coupon_lists": {
      "used_coupons": [
        {
          "coupon_usage_record_id": 1,
          "coupon_id": 1,
          "coupon_name": "现金券",
          "coupon_type": "cash",
          "status": "valid"
        }
      ],
      "unavailable_coupons": [],
      "available_coupons": []
    }
  }
}
```

### 错误响应

#### 401 未登录
```json
{
  "message": "未登录",
  "status": 401
}
```

#### 400 参数错误
```json
{
  "message": "产品列表不能为空",
  "status": 400
}
```

#### 400 优惠券验证失败
```json
{
  "message": "优惠券已过期，过期时间: 2024-01-01 00:00:00",
  "status": 400
}
```

#### 403 权限错误
```json
{
  "message": "用户ID不匹配",
  "status": 403
}
```

#### 500 服务器错误
```json
{
  "message": "计价失败: 内部错误",
  "status": 500
}
```

## 业务逻辑

### 优惠券验证规则

接口会对选中的优惠券进行以下7项验证：

1. **状态检查**: 验证优惠券使用记录、优惠券、优惠券批次的状态是否为有效
2. **有效期检查**: 验证优惠券批次是否在有效期内
3. **互斥检查**: 验证选中的优惠券之间是否存在互斥关系
4. **订单金额条件**: 验证订单总金额是否满足优惠券的使用条件
5. **产品条件**: 验证产品组合是否满足优惠券的使用条件
6. **使用周期限制**: 验证用户在指定周期内是否已达到使用上限
7. **异常处理**: 任何验证失败都会抛出异常并返回错误信息

### 优惠计算逻辑

支持三种优惠券类型：

1. **现金券**: 直接减免指定金额
2. **折扣券**: 按比例折扣，支持最低消费和最大折扣限制
3. **满减券**: 满足指定金额后减免指定金额

支持两种作用范围：

1. **订单级别**: 对整个订单进行优惠计算
2. **产品级别**: 对指定产品进行优惠计算

### 计算顺序

优惠券按照 `apply_order` 字段排序后依次应用，确保计算结果的一致性。

## 使用示例

### cURL 示例
```bash
curl -X POST "http://localhost:8000/api/v1/wechat-mini-app/coupon/coupon-pricing" \
  -H "Content-Type: application/json" \
  -H "token: your_token_here" \
  -d '{
    "user_id": 1,
    "products": [
      {"product_id": 1, "quantity": 2},
      {"product_id": 2, "quantity": 1}
    ],
    "coupon_usage_record_ids": [1, 2]
  }'
```

### JavaScript 示例
```javascript
const response = await fetch('/api/v1/wechat-mini-app/coupon/coupon-pricing', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'token': 'your_token_here'
  },
  body: JSON.stringify({
    user_id: 1,
    products: [
      { product_id: 1, quantity: 2 },
      { product_id: 2, quantity: 1 }
    ],
    coupon_usage_record_ids: [1, 2]
  })
});

const result = await response.json();
console.log(result);
```

## 注意事项

1. 接口需要有效的用户认证token
2. 用户ID必须与token对应的用户一致
3. 产品ID必须在数据库中存在
4. 优惠券使用记录ID必须属于当前用户
5. 优惠券验证失败会返回具体的错误信息
6. 建议在前端调用前先验证产品和优惠券的有效性

## 测试

可以使用提供的测试脚本 `test_coupon_pricing.py` 进行接口测试：

```bash
python test_coupon_pricing.py
```

测试前请确保：
- 服务器正在运行
- 修改测试脚本中的token和用户ID
- 确保测试数据在数据库中存在
