# yh-vegan-admin
乙禾素食管理系统-后台（测试环境）

乙禾素食管理系统是一个基于FastAPI的后台管理系统，用于管理素食相关的产品、用户、订单等信息。

## 技术栈

- Python 3.12
- FastAPI
- SQLAlchemy
- Pydantic
- Poetry
- Alembic

## 日志系统

系统集成了完整的日志功能：

1. 日志保存在 `logs` 目录下，按天滚动保存，保留最近30天的日志记录
2. 日志同时输出到控制台(stdout)，方便开发调试
3. 数据库日志可通过配置开关控制（在 `.env` 文件中设置 `DB_LOGGING_ENABLED=true/false`）

### 使用日志

在代码中引入并使用日志：

```python
from app.utils import logger

# 记录不同级别的日志
logger.debug("调试信息")
logger.info("一般信息")
logger.warning("警告信息")
logger.error("错误信息")
logger.critical("严重错误信息")
```

## 环境配置

系统支持多个环境，通过设置环境变量 `ENV` 来选择不同的配置文件，默认为stage：

- `stage`: 预发布环境 - 使用 `.env.stage` 配置文件
- `test`: 测试环境 - 使用 `.env.test` 配置文件
- `production`: 生产环境 - 使用 `.env` 配置文件

### Stage 环境配置

Stage 环境是预发布环境，用于在生产环境前进行最终测试。使用 `.env.stage` 配置文件，该文件路径在项目根目录下。如果该文件不存在，请按照以下模板创建：

#### Stage 环境工具

项目提供了 `stage` 目录下的工具脚本，用于管理 Stage 环境：

1. **创建 Stage 数据库**：
   ```bash
   python stage/create_stage_db.py
   ```
   该命令会创建 `yh_vegan_dev_stage` 数据库、运行数据库迁移并添加基础测试数据。

   支持的命令选项：
   - `--skip-db-create`: 跳过数据库创建步骤
   - `--skip-migrations`: 跳过数据库迁移步骤
   - `--skip-test-data`: 跳过测试数据添加步骤

2. **运行 Stage 环境应用**：
   ```bash
   ./stage/run_stage.sh
   ```
   该脚本会设置 `ENV=stage` 环境变量，使应用程序加载 `.env.stage` 配置文件。


#### 环境切换

切换环境的方式为设置 `ENV` 环境变量，例如：

```bash
export ENV=stage
python -m app.main
```

## 本地开发环境部署

### 1. 克隆代码库

```bash
git clone http://git.yihes.com:20080/root/yh-vegan-admin.git
cd yh-vegan-admin
```

### 2. 安装依赖

确保安装了Python 3.12和Poetry：

```bash
# 安装Poetry（如果尚未安装）
curl -sSL https://install.python-poetry.org | python3 -

# 安装项目依赖
poetry install
```

### 3. 配置环境变量

```bash
# 复制环境变量示例文件
cp .env.example .env

# 编辑.env文件，设置适当的环境变量
# 特别是要修改SECRET_KEY、DATABASE_URL等重要配置
```

### 4. 数据库迁移

```bash
# 使用Alembic初始化数据库
poetry run alembic upgrade head
```

### 5. 运行开发服务器

```bash
# 启动开发服务器
poetry run python -m app.main
```

访问 http://localhost:8000/docs 查看API文档。

## 生产环境部署

### 1. 服务器准备

- 推荐使用Ubuntu Server 22.04 LTS
- 安装Python 3.12
- 安装Nginx
- 安装MySQL

```bash
# 安装Python 3.12（Ubuntu示例）
sudo apt update
sudo apt install software-properties-common
sudo add-apt-repository ppa:deadsnakes/ppa
sudo apt install python3.12 python3.12-venv python3.12-dev

# 安装Nginx
sudo apt install nginx

# 安装MySQL（或根据需要替换为PostgreSQL）
sudo apt install mysql-server
```

### 2. 代码部署

```bash
# 克隆代码到服务器
git clone http://git.yihes.com:20080/root/yh-vegan-admin.git /code/yh-vegan-admin
cd /code/yh-vegan-admin

# 创建虚拟环境
python3.12 -m venv venv
source venv/bin/activate

# 安装Poetry
curl -sSL https://install.python-poetry.org | python3 -

# 安装依赖
poetry install --no-dev
```

### 3. 配置环境变量

```bash
# 复制并编辑环境变量
cp .env.example .env
nano .env
```

需要修改的关键配置：
- `SECRET_KEY`: 使用强密钥
- `DATABASE_URL`: 使用生产环境数据库连接串
- `BACKEND_CORS_ORIGINS`: 设置实际的前端域名
- 确保其他敏感信息得到正确配置

### 4. 数据库设置

```bash
# 创建数据库（MySQL示例）
mysql -u root -p
CREATE DATABASE yh_vegan;
CREATE USER 'yh_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON yh_vegan.* TO 'yh_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# 更新.env中的数据库URL
# DATABASE_URL=mysql+pymysql://yh_user:your_secure_password@localhost/yh_vegan

# 运行数据库迁移
source venv/bin/activate
alembic upgrade head
```

### 5. 设置Gunicorn作为WSGI服务器

安装Gunicorn：

```bash
pip install gunicorn
```

创建systemd服务文件 `/etc/systemd/system/yh-vegan.service`：

```
[Unit]
Description=Gunicorn instance to serve yh-vegan-admin
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/code/yh-vegan-admin
Environment="PATH=/code/yh-vegan-admin/venv/bin"
EnvironmentFile=/code/yh-vegan-admin/.env
ExecStart=/code/yh-vegan-admin/venv/bin/gunicorn -w 4 -k uvicorn.workers.UvicornWorker app.main:app -b 127.0.0.1:8000

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
sudo systemctl start yh-vegan
sudo systemctl enable yh-vegan
```

### 6. 配置Nginx反向代理

创建Nginx配置文件 `/etc/nginx/sites-available/yh-vegan`：

```
server {
    listen 80;
    server_name your_domain.com;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias /code/yh-vegan-admin/static;
    }

    location /uploads {
        alias /code/yh-vegan-admin/uploads;
    }
}
```

启用站点并重启Nginx：

```bash
sudo ln -s /etc/nginx/sites-available/yh-vegan /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 7. 设置HTTPS（推荐）

使用Let's Encrypt配置SSL：

```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your_domain.com
```

### 8. 文件上传目录设置

```bash
# 创建上传目录并设置权限
mkdir -p /code/yh-vegan-admin/static/uploads
sudo chown -R www-data:www-data /code/yh-vegan-admin/static/uploads
sudo chmod 755 /code/yh-vegan-admin/static/uploads
```

## 部署更新流程

当代码有更新时，按照以下步骤更新生产环境：

```bash
# 进入项目目录
cd /code/yh-vegan-admin

# 拉取最新代码
git pull

# 激活虚拟环境
source venv/bin/activate

# 更新依赖
poetry install --no-dev

# 更新数据库
alembic upgrade head

# 重启服务
sudo systemctl restart yh-vegan
```

## 备份与恢复

### 数据库备份

```bash
# 创建备份目录
mkdir -p /code/backups

# MySQL备份示例
mysqldump -u yh_user -p yh_vegan > /code/backups/yh_vegan_$(date +%Y%m%d).sql
```

### 数据库恢复

```bash
# 恢复MySQL数据库
mysql -u yh_user -p yh_vegan < /code/backups/yh_vegan_20230101.sql
```

## 监控与日志

### 系统日志

```bash
# 查看应用日志
sudo journalctl -u yh-vegan.service

# 查看Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 设置监控（可选）

可以使用Prometheus + Grafana或其他监控工具对系统进行监控。

## 订阅消息推送（一次性）
curl -k -X POST "https://vegan.*****.com/api/v1/wx/message/send-test-message" -H "Content-Type: application/json" -d '{"openid": "对应用户openid"}'