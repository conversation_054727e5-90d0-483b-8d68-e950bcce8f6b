from logging.config import fileConfig

from sqlalchemy import engine_from_config
from sqlalchemy import pool

from alembic import context
from alembic import op
from alembic.operations import Operations, MigrateOperation
# 导入所有模型
from app.models import *  # noqa
from app.db.base_class import Base
from app.core.config import settings
from app.utils.logger import SQLAlchemyFilter
import logging

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    # 先配置我们自己的SQLAlchemy日志过滤器
    sqlalchemy_filter = SQLAlchemyFilter()
    for logger_name in ["sqlalchemy", "sqlalchemy.engine", "sqlalchemy.engine.base", "sqlalchemy.engine.Engine"]:
        sql_logger = logging.getLogger(logger_name)
        # 移除现有的相同类型过滤器
        for existing_filter in sql_logger.filters:
            if isinstance(existing_filter, SQLAlchemyFilter):
                sql_logger.removeFilter(existing_filter)
        # 添加新的过滤器
        sql_logger.addFilter(sqlalchemy_filter)
        # 如果禁用了数据库日志，则完全禁用这些日志器的传播
        if not settings.DB_LOGGING_ENABLED:
            sql_logger.propagate = False

    # 然后再应用fileConfig
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()



if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
