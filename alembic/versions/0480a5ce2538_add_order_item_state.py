"""add_order_item_state

Revision ID: 0480a5ce2538
Revises: 6dadd17e65cb
Create Date: 2025-05-06 15:31:13.329118

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0480a5ce2538'
down_revision: Union[str, None] = '6dadd17e65cb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('order_items', sa.Column('status', sa.Enum('PENDING', 'PAID', 'SHIPPED', 'DELIVERED', 'VERIFIED', 'COMPLETED', 'CANCELLED', 'REFUNDED', name='orderstatus'), nullable=False, comment='订单状态'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('order_items', 'status')
    # ### end Alembic commands ###
