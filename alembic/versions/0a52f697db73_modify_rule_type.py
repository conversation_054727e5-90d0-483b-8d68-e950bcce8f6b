"""modify rule type

Revision ID: 0a52f697db73
Revises: d32c845f9db4
Create Date: 2025-07-06 11:47:47.232261

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '0a52f697db73'
down_revision: Union[str, None] = 'd32c845f9db4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('rules', 'type',
               existing_type=mysql.ENUM('RESERVATION', 'DINING_RESERVATION', collation='utf8mb4_unicode_ci'),
               nullable=True,
               comment='规则类型',
               existing_server_default=sa.text("'RESERVATION'"))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('rules', 'type',
               existing_type=mysql.ENUM('RULE', 'RESERVATION', 'DINING_RESERVATION', collation='utf8mb4_unicode_ci'),
               nullable=True,
               comment='规则类型',
               existing_server_default=sa.text("'RULE'"))
    # ### end Alembic commands ###
