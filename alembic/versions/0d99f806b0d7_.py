"""empty message

Revision ID: 0d99f806b0d7
Revises: 8958d82e1ed5, f51478a31176
Create Date: 2025-06-22 16:25:07.132886

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0d99f806b0d7'
down_revision: Union[str, None] = ('8958d82e1ed5', 'f51478a31176')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
