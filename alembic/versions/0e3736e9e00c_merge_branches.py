"""merge branches

Revision ID: 0e3736e9e00c
Revises: 2787e4125948, 97383a73d9f1
Create Date: 2025-06-15 20:19:11.765806

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0e3736e9e00c'
down_revision: Union[str, None] = ('2787e4125948', '97383a73d9f1')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
