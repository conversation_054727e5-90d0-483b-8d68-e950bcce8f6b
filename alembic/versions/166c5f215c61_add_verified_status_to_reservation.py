"""add_verified_status_to_reservation

Revision ID: 166c5f215c61
Revises: b984188c8eae
Create Date: 2025-05-07 12:10:24.482125

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '166c5f215c61'
down_revision: Union[str, None] = 'b984188c8eae'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    # MySQL 中修改枚举类型
    op.execute("ALTER TABLE reservation_requests MODIFY COLUMN status ENUM('pending', 'paid_deposit', 'paid_full', 'cancelled', 'verified') NOT NULL DEFAULT 'pending'")

def downgrade():
    # 回滚时移除新添加的状态
    op.execute("ALTER TABLE reservation_requests MODIFY COLUMN status ENUM('pending', 'paid_deposit', 'paid_full', 'cancelled') NOT NULL DEFAULT 'pending'")
