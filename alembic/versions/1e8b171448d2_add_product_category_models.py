"""Add product category models

Revision ID: 1e8b171448d2
Revises: d45eb72c88a2
Create Date: 2025-06-12 11:35:09.775591

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1e8b171448d2'
down_revision: Union[str, None] = 'd45eb72c88a2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('product_categories',
    sa.Column('id', sa.Integer(), nullable=False, comment='分类ID'),
    sa.Column('name', sa.String(length=50), nullable=False, comment='分类名称'),
    sa.Column('description', sa.String(length=200), nullable=True, comment='分类描述'),
    sa.Column('image', sa.String(length=255), nullable=True, comment='分类图片'),
    sa.Column('sort_order', sa.Integer(), nullable=True, comment='排序顺序'),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='status'), nullable=False, comment='分类状态'),
    sa.Column('parent_id', sa.Integer(), nullable=True, comment='父分类ID'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['parent_id'], ['product_categories.id'], name=op.f('fk_product_categories_parent_id_product_categories')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_product_categories'))
    )
    op.create_index(op.f('ix_product_categories_id'), 'product_categories', ['id'], unique=False)
    op.create_table('product_category_relations',
    sa.Column('product_id', sa.Integer(), nullable=True),
    sa.Column('category_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['product_categories.id'], name=op.f('fk_product_category_relations_category_id_product_categories')),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], name=op.f('fk_product_category_relations_product_id_products')),
    sa.UniqueConstraint('product_id', 'category_id', name='uq_product_category')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('product_category_relations')
    op.drop_index(op.f('ix_product_categories_id'), table_name='product_categories')
    op.drop_table('product_categories')
    # ### end Alembic commands ###
