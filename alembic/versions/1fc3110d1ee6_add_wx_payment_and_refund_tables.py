"""add_wx_payment_and_refund_tables

Revision ID: 1fc3110d1ee6
Revises: cb6598f77bbb
Create Date: 2025-04-30 18:19:59.699609

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSON
from app.models.order import WxPaymentStatus, WxRefundStatus
from app.utils.common import get_current_time



# revision identifiers, used by Alembic.
revision: str = '1fc3110d1ee6'
down_revision: Union[str, None] = 'cb6598f77bbb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None



def upgrade():
    # 创建微信支付状态枚举类型
    wx_payment_status = sa.Enum(WxPaymentStatus, name='wxpaymentstatus')
    wx_payment_status.create(op.get_bind())

    # 创建微信退款状态枚举类型
    wx_refund_status = sa.Enum(WxRefundStatus, name='wxrefundstatus')
    wx_refund_status.create(op.get_bind())

    # 创建微信支付记录表
    op.create_table(
        'wx_payment_records',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('order_id', sa.Integer(), nullable=False),
        sa.Column('order_no', sa.String(50), nullable=False),
        sa.Column('transaction_id', sa.String(50), nullable=True),
        sa.Column('prepay_id', sa.String(64), nullable=True),
        sa.Column('openid', sa.String(50), nullable=False),
        sa.Column('total_amount', sa.Float(), nullable=False),
        sa.Column('payer_total', sa.Float(), nullable=True),
        sa.Column('currency', sa.String(10), nullable=False, server_default='CNY'),
        sa.Column('status', wx_payment_status, nullable=False, server_default='unpaid'),
        sa.Column('description', sa.String(128), nullable=False),
        sa.Column('trade_state', sa.String(32), nullable=True),
        sa.Column('trade_state_desc', sa.String(256), nullable=True),
        sa.Column('bank_type', sa.String(32), nullable=True),
        sa.Column('success_time', sa.DateTime(), nullable=True),
        sa.Column('payment_request_params', JSON, nullable=True),
        sa.Column('notify_data', JSON, nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP'), onupdate=sa.text('CURRENT_TIMESTAMP')),
        sa.ForeignKeyConstraint(['order_id'], ['orders.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_wx_payment_records_id', 'wx_payment_records', ['id'])
    op.create_index('ix_wx_payment_records_order_no', 'wx_payment_records', ['order_no'])
    op.create_index('ix_wx_payment_records_transaction_id', 'wx_payment_records', ['transaction_id'])

    # 创建微信退款记录表
    op.create_table(
        'wx_refund_records',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('payment_id', sa.Integer(), nullable=False),
        sa.Column('refund_id', sa.String(50), nullable=True),
        sa.Column('out_refund_no', sa.String(50), nullable=False),
        sa.Column('transaction_id', sa.String(50), nullable=False),
        sa.Column('total_amount', sa.Float(), nullable=False),
        sa.Column('refund_amount', sa.Float(), nullable=False),
        sa.Column('currency', sa.String(10), nullable=False, server_default='CNY'),
        sa.Column('status', wx_refund_status, nullable=False, server_default='processing'),
        sa.Column('reason', sa.String(128), nullable=True),
        sa.Column('refund_time', sa.DateTime(), nullable=True),
        sa.Column('notify_data', JSON, nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP'), onupdate=sa.text('CURRENT_TIMESTAMP')),
        sa.ForeignKeyConstraint(['payment_id'], ['wx_payment_records.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_wx_refund_records_id', 'wx_refund_records', ['id'])
    op.create_index('ix_wx_refund_records_refund_id', 'wx_refund_records', ['refund_id'])
    op.create_index('ix_wx_refund_records_out_refund_no', 'wx_refund_records', ['out_refund_no'])


def downgrade():
    # 删除表
    op.drop_table('wx_refund_records')
    op.drop_table('wx_payment_records')
    
    # 删除枚举类型
    wx_payment_status = sa.Enum(name='wxpaymentstatus')
    wx_payment_status.drop(op.get_bind())
    
    wx_refund_status = sa.Enum(name='wxrefundstatus')
    wx_refund_status.drop(op.get_bind())
