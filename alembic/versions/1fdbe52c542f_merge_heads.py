"""merge heads

Revision ID: 1fdbe52c542f
Revises: 2787e4125948, f1795beab351
Create Date: 2025-06-19 15:56:42.554722

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1fdbe52c542f'
down_revision: Union[str, None] = ('2787e4125948', 'f1795beab351')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
