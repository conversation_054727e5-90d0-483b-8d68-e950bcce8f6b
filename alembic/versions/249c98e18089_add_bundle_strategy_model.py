"""add_bundle_strategy_model

Revision ID: 249c98e18089
Revises: 0e3736e9e00c
Create Date: 2025-06-16 13:12:59.935666

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '249c98e18089'
down_revision: Union[str, None] = '0e3736e9e00c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('bundle_strategies',
    sa.Column('id', sa.Integer(), nullable=False, comment='关联的基础策略ID'),
    sa.Column('deduction', sa.Float(), nullable=False, comment='扣除金额'),
    sa.Column('usage_cycle', sa.Enum('PER_ORDER', 'PER_DAY', 'PER_WEEK', 'PER_MONTH', 'PER_YEAR', name='usagecycle'), nullable=False, comment='使用周期'),
    sa.Column('usage_limit', sa.Integer(), nullable=False, comment='使用限制'),
    sa.Column('need_rematch', sa.Boolean(), nullable=False, comment='是否要重新匹配，即命中一次后下一次需要所有条件都符合才能命中'),
    sa.ForeignKeyConstraint(['id'], ['pricing_strategies.id'], name=op.f('fk_bundle_strategies_id_pricing_strategies')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_bundle_strategies'))
    )
    op.create_table('bundle_strategy_product_rel',
    sa.Column('id', sa.Integer(), nullable=False, comment='ID'),
    sa.Column('bundle_strategy_id', sa.Integer(), nullable=False, comment='捆绑价格策略规则ID'),
    sa.Column('product_id', sa.Integer(), nullable=False, comment='商品ID'),
    sa.Column('quantity', sa.Integer(), nullable=False, comment='数量'),
    sa.ForeignKeyConstraint(['bundle_strategy_id'], ['bundle_strategies.id'], name=op.f('fk_bundle_strategy_product_rel_bundle_strategy_id_bundle_strategies')),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], name=op.f('fk_bundle_strategy_product_rel_product_id_products')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_bundle_strategy_product_rel'))
    )
    op.create_index(op.f('ix_bundle_strategy_product_rel_id'), 'bundle_strategy_product_rel', ['id'], unique=False)
    op.alter_column('pricing_strategies', 'type',
               existing_type=mysql.ENUM('PRICING', 'DISCOUNT', 'FULL_REDUCTION', 'MEMBER_PRICE', 'TIME_LIMITED', collation='utf8mb4_unicode_ci'),
               comment='策略类型',
               existing_nullable=False,
               existing_server_default=sa.text("'PRICING'"))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('pricing_strategies', 'type',
               existing_type=mysql.ENUM('PRICING', 'DISCOUNT', 'FULL_REDUCTION', 'MEMBER_PRICE', 'TIME_LIMITED', collation='utf8mb4_unicode_ci'),
               comment=None,
               existing_comment='策略类型',
               existing_nullable=False,
               existing_server_default=sa.text("'PRICING'"))
    op.drop_index(op.f('ix_bundle_strategy_product_rel_id'), table_name='bundle_strategy_product_rel')
    op.drop_table('bundle_strategy_product_rel')
    op.drop_table('bundle_strategies')
    # ### end Alembic commands ###
