"""add article field

Revision ID: 34137b9c8f7b
Revises: ef8eb016288c
Create Date: 2025-06-20 17:09:08.321877

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '34137b9c8f7b'
down_revision: Union[str, None] = 'ef8eb016288c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('articles', sa.Column('ad_image', sa.String(length=200), nullable=True, comment='广告图片'))
    op.add_column('articles', sa.Column('ad_link', sa.String(length=200), nullable=True, comment='广告链接'))
    op.alter_column('pricing_strategies', 'type',
               existing_type=mysql.ENUM('PRICING', 'DISCOUNT', 'FULL_REDUCTION', 'MEMBER_PRICE', 'TIME_LIMITED', 'BUNDLE', collation='utf8mb4_unicode_ci'),
               comment='策略类型',
               existing_nullable=False,
               existing_server_default=sa.text("'PRICING'"))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('pricing_strategies', 'type',
               existing_type=mysql.ENUM('PRICING', 'DISCOUNT', 'FULL_REDUCTION', 'MEMBER_PRICE', 'TIME_LIMITED', 'BUNDLE', collation='utf8mb4_unicode_ci'),
               comment=None,
               existing_comment='策略类型',
               existing_nullable=False,
               existing_server_default=sa.text("'PRICING'"))
    op.drop_column('articles', 'ad_link')
    op.drop_column('articles', 'ad_image')
    # ### end Alembic commands ###
