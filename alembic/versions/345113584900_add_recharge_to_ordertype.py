"""add_recharge_to_ordertype

Revision ID: 345113584900
Revises: 8f4e2b44d852
Create Date: 2025-04-20 14:59:30.767389

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '345113584900'
down_revision: Union[str, None] = '4597390861f7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # MySQL 中修改枚举类型
    op.execute("ALTER TABLE orders MODIFY COLUMN type ENUM('ORDER', 'DIRECT_SALE', 'RESERVATION', 'RECHARGE') NOT NULL")

def downgrade():
    # 回滚操作
    op.execute("ALTER TABLE orders MODIFY COLUMN type ENUM('ORDER', 'DIRECT_SALE', 'RESERVATION') NOT NULL")
