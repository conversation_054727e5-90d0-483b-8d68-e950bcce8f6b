"""rename coupon fields: scope to apply_scope, scope_objects to apply_objects, calculation_order to apply_order, cycle_usage_quantity to usage_quantity, cycle_distribution_quantity to distribution_quantity, cycle_receive_quantity to receive_quantity

Revision ID: 346ae19bc14f
Revises: 4208f74146e6
Create Date: 2025-08-31 18:17:55.844354

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '346ae19bc14f'
down_revision: Union[str, None] = '4208f74146e6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coupon_batches', sa.Column('distribution_quantity', sa.Integer(), nullable=False, comment='周期发放数量'))
    op.add_column('coupon_batches', sa.Column('receive_quantity', sa.Integer(), nullable=False, comment='周期可领取数量'))
    op.drop_column('coupon_batches', 'cycle_receive_quantity')
    op.drop_column('coupon_batches', 'cycle_distribution_quantity')
    op.add_column('coupons', sa.Column('usage_quantity', sa.Integer(), nullable=False, comment='周期可使用数量'))
    op.add_column('coupons', sa.Column('apply_scope', sa.Enum('ORDER', 'PRODUCT', name='couponscope'), nullable=False, comment='作用范围'))
    op.add_column('coupons', sa.Column('apply_objects', sa.JSON(), nullable=False, comment='作用对象组合'))
    op.add_column('coupons', sa.Column('apply_order', sa.Integer(), nullable=False, comment='计算顺序'))
    op.drop_column('coupons', 'calculation_order')
    op.drop_column('coupons', 'cycle_usage_quantity')
    op.drop_column('coupons', 'scope_objects')
    op.drop_column('coupons', 'scope')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coupons', sa.Column('scope', mysql.ENUM('ORDER', 'PRODUCT'), nullable=False, comment='作用范围'))
    op.add_column('coupons', sa.Column('scope_objects', mysql.JSON(), nullable=False, comment='作用对象组合'))
    op.add_column('coupons', sa.Column('cycle_usage_quantity', mysql.INTEGER(display_width=11), autoincrement=False, nullable=False, comment='周期可使用数量'))
    op.add_column('coupons', sa.Column('calculation_order', mysql.INTEGER(display_width=11), autoincrement=False, nullable=False, comment='计算顺序'))
    op.drop_column('coupons', 'apply_order')
    op.drop_column('coupons', 'apply_objects')
    op.drop_column('coupons', 'apply_scope')
    op.drop_column('coupons', 'usage_quantity')
    op.add_column('coupon_batches', sa.Column('cycle_distribution_quantity', mysql.INTEGER(display_width=11), autoincrement=False, nullable=False, comment='周期发放数量'))
    op.add_column('coupon_batches', sa.Column('cycle_receive_quantity', mysql.INTEGER(display_width=11), autoincrement=False, nullable=False, comment='周期可领取数量'))
    op.drop_column('coupon_batches', 'receive_quantity')
    op.drop_column('coupon_batches', 'distribution_quantity')
    # ### end Alembic commands ###
