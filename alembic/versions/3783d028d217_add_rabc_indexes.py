"""add_rabc_indexes

Revision ID: 3783d028d217
Revises: 0d99f806b0d7
Create Date: 2025-06-24 23:52:32.256836

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3783d028d217'
down_revision: Union[str, None] = '0d99f806b0d7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    """添加RBAC相关索引"""
    # 为permissions表添加索引
    op.create_index('ix_permissions_code', 'permissions', ['code'])
    op.create_index('ix_permissions_permission_type', 'permissions', ['permission_type'])
    op.create_index('ix_permissions_status', 'permissions', ['status'])

    # 为roles表添加索引
    op.create_index('ix_roles_name', 'roles', ['name'])
    op.create_index('ix_roles_status', 'roles', ['status'])

    # 为admins表添加索引（如果不存在）
    try:
        op.create_index('ix_admins_username', 'admins', ['username'])
    except Exception:
        pass  # 索引可能已存在

    try:
        op.create_index('ix_admins_status', 'admins', ['status'])
    except Exception:
        pass  # 索引可能已存在

    # 为关联表添加复合索引
    op.create_index('ix_admin_role_relations_admin_id', 'admin_role_relations', ['admin_id'])
    op.create_index('ix_admin_role_relations_role_id', 'admin_role_relations', ['role_id'])

    op.create_index('ix_role_permission_relations_role_id', 'role_permission_relations', ['role_id'])
    op.create_index('ix_role_permission_relations_permission_id', 'role_permission_relations', ['permission_id'])


def downgrade():
    """移除RBAC相关索引"""
    # 移除关联表索引
    op.drop_index('ix_role_permission_relations_permission_id', 'role_permission_relations')
    op.drop_index('ix_role_permission_relations_role_id', 'role_permission_relations')

    op.drop_index('ix_admin_role_relations_role_id', 'admin_role_relations')
    op.drop_index('ix_admin_role_relations_admin_id', 'admin_role_relations')

    # 移除admins表索引
    try:
        op.drop_index('ix_admins_status', 'admins')
    except Exception:
        pass

    try:
        op.drop_index('ix_admins_username', 'admins')
    except Exception:
        pass

    # 移除roles表索引
    op.drop_index('ix_roles_status', 'roles')
    op.drop_index('ix_roles_name', 'roles')

    # 移除permissions表索引
    op.drop_index('ix_permissions_status', 'permissions')
    op.drop_index('ix_permissions_permission_type', 'permissions')
    op.drop_index('ix_permissions_code', 'permissions')
