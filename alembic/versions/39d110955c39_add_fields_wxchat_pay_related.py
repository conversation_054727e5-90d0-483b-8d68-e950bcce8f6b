"""add_fields_wxchat_pay_related

Revision ID: 39d110955c39
Revises: 6bc9cbe29b81
Create Date: 2025-04-24 11:08:56.508208

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
from sqlalchemy import inspect


# revision identifiers, used by Alembic.
revision: str = '39d110955c39'
down_revision: Union[str, None] = '6bc9cbe29b81'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('recharge_orders',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['id'], ['orders.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    
    # 检查orders表中的列是否已存在
    conn = op.get_bind()
    inspector = inspect(conn)
    order_columns = [col['name'] for col in inspector.get_columns('orders')]
    
    if 'order_no' not in order_columns:
        op.add_column('orders', sa.Column('order_no', sa.String(length=50), nullable=True, comment='订单编号'))
        op.create_index(op.f('ix_orders_order_no'), 'orders', ['order_no'], unique=True)
    
    # 检查personal_users表中的列是否已存在
    columns = [col['name'] for col in inspector.get_columns('personal_users')]
    
    if 'nickname' not in columns:
        op.add_column('personal_users', sa.Column('nickname', sa.String(length=128), nullable=True, comment='用户昵称'))
    if 'avatar_url' not in columns:
        op.add_column('personal_users', sa.Column('avatar_url', sa.String(length=256), nullable=True, comment='头像URL'))
    if 'gender' not in columns:
        op.add_column('personal_users', sa.Column('gender', sa.Integer(), nullable=True, comment='性别'))
    if 'country' not in columns:
        op.add_column('personal_users', sa.Column('country', sa.String(length=64), nullable=True, comment='国家'))
    if 'province' not in columns:
        op.add_column('personal_users', sa.Column('province', sa.String(length=64), nullable=True, comment='省份'))
    if 'city' not in columns:
        op.add_column('personal_users', sa.Column('city', sa.String(length=64), nullable=True, comment='城市'))
    if 'token' not in columns:
        op.add_column('personal_users', sa.Column('token', sa.String(length=128), nullable=True, comment='用户令牌'))
    if 'token_expiry' not in columns:
        op.add_column('personal_users', sa.Column('token_expiry', sa.DateTime(), nullable=True, comment='令牌过期时间'))
    if 'msg_status' not in columns:
        op.add_column('personal_users', sa.Column('msg_status', sa.Integer(), nullable=True, comment='消息状态'))
    
    # 更新payment_method枚举以包含ENTERPRISE_ACCOUNT_BALANCE
    op.execute("ALTER TABLE orders MODIFY COLUMN payment_method ENUM('ACCOUNT_BALANCE', 'ENTERPRISE_ACCOUNT_BALANCE', 'CASH', 'ALIPAY', 'WECHAT_PAY', 'BANK_TRANSFER') NOT NULL COMMENT '支付方式'")
    
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # 恢复payment_method枚举
    op.execute("ALTER TABLE orders MODIFY COLUMN payment_method ENUM('ACCOUNT_BALANCE', 'CASH', 'ALIPAY', 'WECHAT_PAY', 'BANK_TRANSFER') NOT NULL COMMENT '支付方式'")
    
    try:
        op.drop_column('personal_users', 'msg_status')
        op.drop_column('personal_users', 'token_expiry')
        op.drop_column('personal_users', 'token')
        op.drop_column('personal_users', 'city')
        op.drop_column('personal_users', 'province')
        op.drop_column('personal_users', 'country')
        op.drop_column('personal_users', 'gender')
        op.drop_column('personal_users', 'avatar_url')
        op.drop_column('personal_users', 'nickname')
    except Exception:
        pass
        
    try:
        op.drop_index(op.f('ix_orders_order_no'), table_name='orders')
        op.drop_column('orders', 'order_no')
    except Exception:
        pass
        
    op.drop_table('recharge_orders')
    # ### end Alembic commands ###
