"""add meal type to rule item

Revision ID: 408f3bbbeff2
Revises: 0a52f697db73
Create Date: 2025-07-09 11:07:24.991689

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql


# revision identifiers, used by Alembic.
revision: str = '408f3bbbeff2'
down_revision: Union[str, None] = '0a52f697db73'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('rule_items',
                  sa.Column('meal_type', 
                           mysql.ENUM('BREAKFAST', 'LUNCH', 'AFTERNOON_TEA', 'DINNER', 'NIGHT_SNACK', 
                                    collation='utf8mb4_unicode_ci'),
                           nullable=True,
                           server_default='LUNCH',
                           comment='餐食类型'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('rule_items', 'meal_type')
    # ### end Alembic commands ###
