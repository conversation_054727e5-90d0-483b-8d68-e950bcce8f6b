"""modify_coupon_and_coupon_usage_records_fields

Revision ID: 4208f74146e6
Revises: 73ff8441f1fc
Create Date: 2025-08-31 17:51:44.048347

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '4208f74146e6'
down_revision: Union[str, None] = '73ff8441f1fc'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coupon_usage_records', sa.Column('order_item_id', sa.Integer(), nullable=True, comment='订单项ID'))
    op.create_foreign_key(op.f('fk_coupon_usage_records_order_item_id_order_items'), 'coupon_usage_records', 'order_items', ['order_item_id'], ['id'])
    op.drop_column('coupons', 'end_time')
    op.drop_column('coupons', 'start_time')
    op.drop_column('coupons', 'quantity')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coupons', sa.Column('quantity', mysql.INTEGER(display_width=11), autoincrement=False, nullable=False, comment='数量'))
    op.add_column('coupons', sa.Column('start_time', mysql.DATETIME(), nullable=False, comment='生效时间'))
    op.add_column('coupons', sa.Column('end_time', mysql.DATETIME(), nullable=False, comment='结束时间'))
    op.drop_constraint(op.f('fk_coupon_usage_records_order_item_id_order_items'), 'coupon_usage_records', type_='foreignkey')
    op.drop_column('coupon_usage_records', 'order_item_id')
    # ### end Alembic commands ###
