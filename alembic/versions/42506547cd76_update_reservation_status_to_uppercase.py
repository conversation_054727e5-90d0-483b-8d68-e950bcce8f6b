"""update_reservation_status_to_uppercase

Revision ID: 42506547cd76
Revises: 166c5f215c61
Create Date: 2025-05-07 14:05:03.144741

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '42506547cd76'
down_revision: Union[str, None] = '166c5f215c61'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade():
    # 1. 先将所有值更新为大写
    op.execute("UPDATE reservation_requests SET status = UPPER(status)")
    
    # 2. 修改枚举类型为大写
    op.execute("ALTER TABLE reservation_requests MODIFY COLUMN status ENUM('PENDING', 'PAID_DEPOSIT', 'PAID_FULL', 'CANCELLED', 'VERIFIED') NOT NULL DEFAULT 'PENDING'")


def downgrade():
    # 1. 先将所有值更新为小写
    op.execute("UPDATE reservation_requests SET status = LOWER(status)")
    
    # 2. 修改枚举类型为小写
    op.execute("ALTER TABLE reservation_requests MODIFY COLUMN status ENUM('pending', 'paid_deposit', 'paid_full', 'cancelled', 'verified') NOT NULL DEFAULT 'pending'")
