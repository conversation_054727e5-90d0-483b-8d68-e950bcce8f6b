"""add_recharge_to_order_type

Revision ID: 4597390861f7
Revises: 14386f365861
Create Date: 2025-04-20 14:35:59.343652

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4597390861f7'
down_revision: Union[str, None] = '14386f365861'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
