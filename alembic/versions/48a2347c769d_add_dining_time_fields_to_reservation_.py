"""add dining time fields to reservation_requests

Revision ID: 48a2347c769d
Revises: e8ae83e6dbd8
Create Date: 2025-05-10 20:15:52.743787

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '48a2347c769d'
down_revision: Union[str, None] = 'e8ae83e6dbd8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('reservation_requests', sa.Column('dining_start_time', sa.DateTime(), nullable=True, comment='就餐开始时间'))
    op.add_column('reservation_requests', sa.Column('dining_end_time', sa.DateTime(), nullable=True, comment='就餐结束时间'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('reservation_requests', 'dining_end_time')
    op.drop_column('reservation_requests', 'dining_start_time')
    # ### end Alembic commands ###
