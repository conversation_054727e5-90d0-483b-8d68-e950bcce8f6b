"""modify enterprise constrain

Revision ID: 514a073427fb
Revises: 6df7d8cec6e2
Create Date: 2025-04-26 17:25:40.369606

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '514a073427fb'
down_revision: Union[str, None] = '6df7d8cec6e2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('articles_ibfk_1', 'articles', type_='foreignkey')
    op.create_foreign_key(None, 'articles', 'contents', ['id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('dishes_ibfk_1', 'dishes', type_='foreignkey')
    op.create_foreign_key(None, 'dishes', 'contents', ['id'], ['id'], ondelete='CASCADE')
    op.alter_column('enterprises', 'business_license',
               existing_type=mysql.VARCHAR(length=100),
               nullable=True,
               existing_comment='营业执照号')
    op.drop_index('business_license', table_name='enterprises')
    op.create_unique_constraint(None, 'enterprises', ['company_name'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'enterprises', type_='unique')
    op.create_index('business_license', 'enterprises', ['business_license'], unique=True)
    op.alter_column('enterprises', 'business_license',
               existing_type=mysql.VARCHAR(length=100),
               nullable=False,
               existing_comment='营业执照号')
    op.drop_constraint(None, 'dishes', type_='foreignkey')
    op.create_foreign_key('dishes_ibfk_1', 'dishes', 'contents', ['id'], ['id'])
    op.drop_constraint(None, 'articles', type_='foreignkey')
    op.create_foreign_key('articles_ibfk_1', 'articles', 'contents', ['id'], ['id'])
    # ### end Alembic commands ###
