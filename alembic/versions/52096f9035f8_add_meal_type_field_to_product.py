"""add meal_type field to product

Revision ID: 52096f9035f8
Revises: 6d8d69dd2124
Create Date: 2025-05-06 11:31:31.788371

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '52096f9035f8'
down_revision: Union[str, None] = '6d8d69dd2124'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('products', sa.Column('meal_type', sa.Enum('BUSINESS', 'BUFFET', 'DINE_IN', name='mealtype'), nullable=True, comment='餐食类型：商务餐/自助餐/现场点餐'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('products', 'meal_type')
    # ### end Alembic commands ###
