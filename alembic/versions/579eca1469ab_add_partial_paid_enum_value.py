"""add partial paid enum value

Revision ID: 579eca1469ab
Revises: b7f3d9e45a12
Create Date: 2025-08-06 16:44:05.959057

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '579eca1469ab'
down_revision: Union[str, None] = 'b7f3d9e45a12'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###

    # 1. 为OrderStatus枚举添加PARTIAL_PAID状态
    op.execute("ALTER TABLE order_items MODIFY COLUMN status ENUM('PENDING', 'PAID', 'SHIPPED', 'DELIVERED', 'VERIFIED', 'COMPLETED', 'CANCELLED', 'REFUNDED', 'REFUNDED_PARTIAL', 'PARTIAL_PAID') NOT NULL COMMENT '订单状态'")

    # 2. 为order_items表添加unpaid_quantity字段
    op.add_column('order_items', sa.Column('unpaid_quantity', sa.Integer(), nullable=False, server_default='0', comment='未支付数量'))

    # 3. 初始化unpaid_quantity字段
    # 如果订单项状态为PENDING，则unpaid_quantity等于quantity
    op.execute("""
        UPDATE order_items
        SET unpaid_quantity = quantity
        WHERE status = 'PENDING'
    """)

    # 如果订单项状态为PAID，则unpaid_quantity设为0
    op.execute("""
        UPDATE order_items
        SET unpaid_quantity = 0
        WHERE status = 'PAID'
    """)

    # 对于其他状态的订单项，根据具体情况设置unpaid_quantity
    op.execute("""
        UPDATE order_items
        SET unpaid_quantity = quantity
        WHERE status NOT IN ('PENDING', 'PAID') AND unpaid_quantity = 0
    """)

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###

    # 1. 删除unpaid_quantity字段
    op.drop_column('order_items', 'unpaid_quantity')

    # 2. 恢复OrderStatus枚举到原来的状态（移除PARTIAL_PAID）
    op.execute("ALTER TABLE order_items MODIFY COLUMN status ENUM('PENDING', 'PAID', 'SHIPPED', 'DELIVERED', 'VERIFIED', 'COMPLETED', 'CANCELLED', 'REFUNDED', 'REFUNDED_PARTIAL') NOT NULL COMMENT '订单状态'")

    # ### end Alembic commands ###
