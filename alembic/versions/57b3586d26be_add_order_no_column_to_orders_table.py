"""add order_no column to orders table

Revision ID: 57b3586d26be
Revises: a46ce85672bb
Create Date: 2025-04-20 13:15:49.304085

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import inspect


# revision identifiers, used by Alembic.
revision: str = '57b3586d26be'
down_revision: Union[str, None] = 'a46ce85672bb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    inspector = inspect(conn)
    columns = [col['name'] for col in inspector.get_columns('orders')]
    
    if 'order_no' not in columns:
        op.add_column('orders', sa.Column('order_no', sa.String(length=50), nullable=True, comment='订单编号'))
        op.create_index(op.f('ix_orders_order_no'), 'orders', ['order_no'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    try:
        op.drop_index(op.f('ix_orders_order_no'), table_name='orders')
        op.drop_column('orders', 'order_no')
    except Exception:
        pass
    # ### end Alembic commands ###
