"""create_recharge_orders_table

Revision ID: 5842f60150b7
Revises: 9cd0f94c785c
Create Date: 2025-04-20 17:09:25.819780

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import inspect


# revision identifiers, used by Alembic.
revision: str = '5842f60150b7'
down_revision: Union[str, None] = '9cd0f94c785c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    inspector = inspect(conn)
    
    # 检查表是否已存在
    table_names = inspector.get_table_names()
    if 'recharge_orders' not in table_names:
        op.create_table('recharge_orders',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['id'], ['orders.id'], ),
        sa.PrimaryKeyConstraint('id')
        )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    try:
        op.drop_table('recharge_orders')
    except Exception:
        pass
    # ### end Alembic commands ###
