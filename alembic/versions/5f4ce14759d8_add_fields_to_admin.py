"""add_fields_to_admin

Revision ID: 5f4ce14759d8
Revises: d6be0496ca23
Create Date: 2025-04-23 16:03:07.796931

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from app.models.enum import Status
from sqlalchemy import inspect


# revision identifiers, used by Alembic.
revision: str = '5f4ce14759d8'
down_revision: Union[str, None] = 'd6be0496ca23'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    # 获取列信息
    conn = op.get_bind()
    inspector = inspect(conn)
    columns = [col['name'] for col in inspector.get_columns('admins')]
    
    # 添加新字段到admin表
    if 'phone' not in columns:
        op.add_column('admins', sa.Column('phone', sa.String(20), nullable=True, comment="手机号码"))
    if 'email' not in columns:
        op.add_column('admins', sa.Column('email', sa.String(100), nullable=True, comment="电子邮箱"))
    if 'username' not in columns:
        op.add_column('admins', sa.Column('username', sa.String(50), nullable=True, comment="用户名"))
    
    # 检查索引
    indices = [idx['name'] for idx in inspector.get_indexes('admins')]
    if 'ix_admins_phone' not in indices:
        # 添加唯一索引
        op.create_index(op.f('ix_admins_phone'), 'admins', ['phone'], unique=True)

def downgrade():
    # 删除字段
    try:
        op.drop_index(op.f('ix_admins_phone'), table_name='admins')
        op.drop_column('admins', 'username')
        op.drop_column('admins', 'email')
        op.drop_column('admins', 'phone')
    except Exception:
        pass
