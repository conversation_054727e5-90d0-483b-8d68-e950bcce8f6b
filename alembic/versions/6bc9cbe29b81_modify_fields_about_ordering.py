"""modify_fields_about_ordering

Revision ID: 6bc9cbe29b81
Revises: ee75610b0bb2
Create Date: 2025-04-24 01:12:40.772932

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '6bc9cbe29b81'
down_revision: Union[str, None] = 'ee75610b0bb2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('order_items', sa.Column('final_price', sa.Float(), nullable=False, comment='最终单价'))
    op.add_column('order_items', sa.Column('payable_amount', sa.Float(), nullable=False, comment='应付金额'))
    op.add_column('order_items', sa.Column('pricing_remark', sa.String(length=200), nullable=True, comment='计价说明'))
    op.add_column('orders', sa.Column('payable_amount', sa.Float(), nullable=False, comment='应付金额'))
    op.add_column('orders', sa.Column('pricing_remark', sa.String(length=200), nullable=True, comment='计价说明'))
    op.alter_column('pricing_strategies', 'start_time',
               existing_type=mysql.DATETIME(),
               nullable=True,
               existing_comment='策略生效时间')
    op.alter_column('pricing_strategies', 'end_time',
               existing_type=mysql.DATETIME(),
               nullable=True,
               existing_comment='策略结束时间')
    op.add_column('reservation_requests', sa.Column('rule_item_id', sa.Integer(), nullable=False))
    op.create_foreign_key(None, 'reservation_requests', 'rule_items', ['rule_item_id'], ['id'])
    op.add_column('rule_items', sa.Column('start_time_cron_str', sa.String(length=50), nullable=True, comment='cron表达式'))
    op.add_column('rule_items', sa.Column('end_time_cron_str', sa.String(length=50), nullable=True, comment='cron表达式'))
    op.add_column('rule_items', sa.Column('order', sa.Integer(), nullable=False, comment='排序'))
    op.drop_column('rule_items', 'cron')
    op.add_column('rules', sa.Column('type', sa.Enum('RESERVATION', name='ruletype'), nullable=True, comment='规则类型'))
    op.add_column('time_limited_strategies', sa.Column('special_price', sa.Float(), nullable=False, comment='特价金额'))
    op.drop_column('time_limited_strategies', 'price')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('time_limited_strategies', sa.Column('price', mysql.FLOAT(), nullable=False, comment='特价金额'))
    op.drop_column('time_limited_strategies', 'special_price')
    op.drop_column('rules', 'type')
    op.add_column('rule_items', sa.Column('cron', mysql.VARCHAR(length=50), nullable=True, comment='cron表达式'))
    op.drop_column('rule_items', 'order')
    op.drop_column('rule_items', 'end_time_cron_str')
    op.drop_column('rule_items', 'start_time_cron_str')
    op.drop_constraint(None, 'reservation_requests', type_='foreignkey')
    op.drop_column('reservation_requests', 'rule_item_id')
    op.alter_column('pricing_strategies', 'end_time',
               existing_type=mysql.DATETIME(),
               nullable=False,
               existing_comment='策略结束时间')
    op.alter_column('pricing_strategies', 'start_time',
               existing_type=mysql.DATETIME(),
               nullable=False,
               existing_comment='策略生效时间')
    op.drop_column('orders', 'pricing_remark')
    op.drop_column('orders', 'payable_amount')
    op.drop_column('order_items', 'pricing_remark')
    op.drop_column('order_items', 'payable_amount')
    op.drop_column('order_items', 'final_price')
    # ### end Alembic commands ###
