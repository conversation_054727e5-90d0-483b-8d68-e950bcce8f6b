"""add content status

Revision ID: 6df7d8cec6e2
Revises: c10c67a2682e
Create Date: 2025-04-25 18:34:51.029860

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6df7d8cec6e2'
down_revision: Union[str, None] = 'c10c67a2682e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('contents', sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='status'), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('contents', 'status')
    # ### end Alembic commands ###
