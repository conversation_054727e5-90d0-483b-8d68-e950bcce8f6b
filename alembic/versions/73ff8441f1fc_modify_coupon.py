"""modify_coupon

Revision ID: 73ff8441f1fc
Revises: a1b2c3d4e5f6
Create Date: 2025-08-25 15:37:04.553109

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '73ff8441f1fc'
down_revision: Union[str, None] = 'a1b2c3d4e5f6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('coupon_batches',
    sa.Column('id', sa.Integer(), nullable=False, comment='批次ID'),
    sa.Column('name', sa.String(length=100), nullable=False, comment='批次名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='批次描述'),
    sa.Column('batch_number', sa.Integer(), nullable=False, comment='批次'),
    sa.Column('quantity', sa.Integer(), nullable=False, comment='数量'),
    sa.Column('start_time', sa.DateTime(), nullable=False, comment='生效时间'),
    sa.Column('end_time', sa.DateTime(), nullable=False, comment='结束时间'),
    sa.Column('valid_duration', sa.Integer(), nullable=False, comment='有效时长(小时)'),
    sa.Column('coupon_id', sa.Integer(), nullable=False, comment='优惠券ID'),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='status'), nullable=False, comment='状态'),
    sa.Column('distribution_channels', sa.JSON(), nullable=False, comment='发放渠道'),
    sa.Column('cycle_distribution_quantity', sa.Integer(), nullable=False, comment='周期发放数量'),
    sa.Column('distribution_cycle', sa.Enum('PER_ORDER', 'PER_DAY', 'PER_WEEK', 'PER_MONTH', 'PER_YEAR', name='couponusagecycle'), nullable=False, comment='发放周期'),
    sa.Column('cycle_receive_quantity', sa.Integer(), nullable=False, comment='周期可领取数量'),
    sa.Column('receive_cycle', sa.Enum('PER_ORDER', 'PER_DAY', 'PER_WEEK', 'PER_MONTH', 'PER_YEAR', name='couponusagecycle'), nullable=False, comment='领取周期'),
    sa.Column('receive_start_time', sa.DateTime(), nullable=False, comment='领取开始时间'),
    sa.Column('receive_end_time', sa.DateTime(), nullable=False, comment='领取结束时间'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['coupon_id'], ['coupons.id'], name=op.f('fk_coupon_batches_coupon_id_coupons')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_coupon_batches'))
    )
    op.create_index(op.f('ix_coupon_batches_id'), 'coupon_batches', ['id'], unique=False)
    op.add_column('coupon_usage_records', sa.Column('coupon_batch_id', sa.Integer(), nullable=False, comment='优惠券批次ID'))
    op.create_foreign_key(op.f('fk_coupon_usage_records_coupon_batch_id_coupon_batches'), 'coupon_usage_records', 'coupon_batches', ['coupon_batch_id'], ['id'])
    op.add_column('coupons', sa.Column('condition_scope', sa.Enum('ORDER', 'PRODUCT', name='couponscope'), nullable=False, comment='条件范围'))
    op.add_column('coupons', sa.Column('condition_objects', sa.JSON(), nullable=False, comment='条件对象组合'))
    op.add_column('coupons', sa.Column('condition_amount', sa.Float(), nullable=False, comment='条件对象金额'))
    op.add_column('coupons', sa.Column('cycle_usage_quantity', sa.Integer(), nullable=False, comment='周期可使用数量'))
    op.add_column('coupons', sa.Column('mutual_exclusive_rules', sa.JSON(), nullable=False, comment='互斥规则'))
    op.add_column('coupons', sa.Column('payment_channels', sa.JSON(), nullable=False, comment='支付渠道'))
    op.add_column('coupons', sa.Column('scope_objects', sa.JSON(), nullable=False, comment='作用对象组合'))
    op.add_column('coupons', sa.Column('calculation_order', sa.Integer(), nullable=False, comment='计算顺序'))
    op.alter_column('coupons', 'usage_cycle',
               existing_type=mysql.ENUM('PER_ORDER', 'PER_DAY', 'PER_WEEK', 'PER_MONTH', 'PER_YEAR'),
               nullable=False,
               existing_comment='使用周期')
    op.alter_column('coupons', 'usage_limit',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=False,
               existing_comment='使用限制')
    op.alter_column('coupons', 'scope',
               existing_type=mysql.ENUM('ORDER', 'PRODUCT'),
               comment='作用范围',
               existing_comment='优惠券作用范围',
               existing_nullable=False)
    op.alter_column('coupons', 'quantity',
               existing_type=mysql.INTEGER(display_width=11),
               comment='数量',
               existing_comment='优惠券数量',
               existing_nullable=False)
    op.alter_column('coupons', 'start_time',
               existing_type=mysql.DATETIME(),
               nullable=False,
               comment='生效时间',
               existing_comment='优惠券生效时间')
    op.alter_column('coupons', 'end_time',
               existing_type=mysql.DATETIME(),
               nullable=False,
               comment='结束时间',
               existing_comment='优惠券结束时间')
    op.alter_column('coupons', 'status',
               existing_type=mysql.ENUM('ACTIVE', 'INACTIVE'),
               comment='状态',
               existing_comment='优惠券状态',
               existing_nullable=False)
    op.drop_column('coupons', 'is_mutual_exclusive')
    op.alter_column('orders', 'enterprise_paid_amount',
               existing_type=mysql.FLOAT(),
               nullable=True,
               existing_comment='企业已支付金额',
               existing_server_default=sa.text("'0'"))
    op.alter_column('orders', 'personal_paid_amount',
               existing_type=mysql.FLOAT(),
               nullable=True,
               existing_comment='个人已支付金额',
               existing_server_default=sa.text("'0'"))
    op.alter_column('orders', 'requires_personal_payment',
               existing_type=mysql.FLOAT(),
               nullable=True,
               existing_comment='需要个人支付的金额',
               existing_server_default=sa.text("'0'"))
    op.alter_column('orders', 'is_split_payment',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=True,
               existing_comment='是否为分次支付订单',
               existing_server_default=sa.text("'0'"))
    op.drop_index('idx_orders_is_split_payment', table_name='orders')
    op.drop_index('idx_orders_payment_status_split', table_name='orders')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('idx_orders_payment_status_split', 'orders', ['payment_status', 'is_split_payment'], unique=False)
    op.create_index('idx_orders_is_split_payment', 'orders', ['is_split_payment'], unique=False)
    op.alter_column('orders', 'is_split_payment',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=False,
               existing_comment='是否为分次支付订单',
               existing_server_default=sa.text("'0'"))
    op.alter_column('orders', 'requires_personal_payment',
               existing_type=mysql.FLOAT(),
               nullable=False,
               existing_comment='需要个人支付的金额',
               existing_server_default=sa.text("'0'"))
    op.alter_column('orders', 'personal_paid_amount',
               existing_type=mysql.FLOAT(),
               nullable=False,
               existing_comment='个人已支付金额',
               existing_server_default=sa.text("'0'"))
    op.alter_column('orders', 'enterprise_paid_amount',
               existing_type=mysql.FLOAT(),
               nullable=False,
               existing_comment='企业已支付金额',
               existing_server_default=sa.text("'0'"))
    op.add_column('coupons', sa.Column('is_mutual_exclusive', mysql.TINYINT(display_width=1), autoincrement=False, nullable=False, comment='是否互斥'))
    op.alter_column('coupons', 'status',
               existing_type=mysql.ENUM('ACTIVE', 'INACTIVE'),
               comment='优惠券状态',
               existing_comment='状态',
               existing_nullable=False)
    op.alter_column('coupons', 'end_time',
               existing_type=mysql.DATETIME(),
               nullable=True,
               comment='优惠券结束时间',
               existing_comment='结束时间')
    op.alter_column('coupons', 'start_time',
               existing_type=mysql.DATETIME(),
               nullable=True,
               comment='优惠券生效时间',
               existing_comment='生效时间')
    op.alter_column('coupons', 'quantity',
               existing_type=mysql.INTEGER(display_width=11),
               comment='优惠券数量',
               existing_comment='数量',
               existing_nullable=False)
    op.alter_column('coupons', 'scope',
               existing_type=mysql.ENUM('ORDER', 'PRODUCT'),
               comment='优惠券作用范围',
               existing_comment='作用范围',
               existing_nullable=False)
    op.alter_column('coupons', 'usage_limit',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=True,
               existing_comment='使用限制')
    op.alter_column('coupons', 'usage_cycle',
               existing_type=mysql.ENUM('PER_ORDER', 'PER_DAY', 'PER_WEEK', 'PER_MONTH', 'PER_YEAR'),
               nullable=True,
               existing_comment='使用周期')
    op.drop_column('coupons', 'calculation_order')
    op.drop_column('coupons', 'scope_objects')
    op.drop_column('coupons', 'payment_channels')
    op.drop_column('coupons', 'mutual_exclusive_rules')
    op.drop_column('coupons', 'cycle_usage_quantity')
    op.drop_column('coupons', 'condition_amount')
    op.drop_column('coupons', 'condition_objects')
    op.drop_column('coupons', 'condition_scope')
    op.drop_constraint(op.f('fk_coupon_usage_records_coupon_batch_id_coupon_batches'), 'coupon_usage_records', type_='foreignkey')
    op.drop_column('coupon_usage_records', 'coupon_batch_id')
    op.drop_index(op.f('ix_coupon_batches_id'), table_name='coupon_batches')
    op.drop_table('coupon_batches')
    # ### end Alembic commands ###
