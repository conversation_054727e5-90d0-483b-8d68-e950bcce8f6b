"""add_refunded_partial_to_orderstatus

Revision ID: 8794b7eae12d
Revises: 9ed3274f3997
Create Date: 2025-05-07 09:45:52.575009

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8794b7eae12d'
down_revision: Union[str, None] = '9ed3274f3997'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None



def upgrade() -> None:
    # 修改orders表的status列
    op.execute("ALTER TABLE orders MODIFY COLUMN status ENUM('pending', 'paid', 'shipped', 'delivered', 'verified', 'completed', 'cancelled', 'refunded', 'refunded_partial') NOT NULL COMMENT '订单状态'")

    # 修改order_items表的status列
    op.execute("ALTER TABLE order_items MODIFY COLUMN status ENUM('PENDING', 'PAID', 'SHIPPED', 'DELIVERED', 'VERIFIED', 'COMPLETED', 'CANCELLED', 'REFUNDED', 'REFUNDED_PARTIAL') NOT NULL COMMENT '订单状态'")

def downgrade() -> None:
    # 还原orders表的status列
    op.execute("ALTER TABLE orders MODIFY COLUMN status ENUM('pending', 'paid', 'shipped', 'delivered', 'verified', 'completed', 'cancelled', 'refunded') NOT NULL COMMENT '订单状态'")

    # 还原order_items表的status列
    op.execute("ALTER TABLE order_items MODIFY COLUMN status ENUM('PENDING', 'PAID', 'SHIPPED', 'DELIVERED', 'VERIFIED', 'COMPLETED', 'CANCELLED', 'REFUNDED') NOT NULL COMMENT '订单状态'")
