"""merge_heads

Revision ID: 8958d82e1ed5
Revises: 34137b9c8f7b, e67d23317d1b
Create Date: 2025-06-21 11:32:25.638240

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8958d82e1ed5'
down_revision: Union[str, None] = ('34137b9c8f7b', 'e67d23317d1b')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
