"""add_order_type_recharge

Revision ID: 937e7513e28b
Revises: c0d888eeb223
Create Date: 2025-04-27 18:44:25.868725

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '937e7513e28b'
down_revision: Union[str, None] = 'c0d888eeb223'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('orders', 'type',
                    existing_type=sa.Enum('ORDER', 'DIRECT_SALE', 'RESERVATION', name='ordertype'),
                    type_=sa.Enum('ORDER', 'DIRECT_SALE', 'RESERVATION', 'RECHARGE', name='ordertype'),
                    existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('orders', 'type',
                    existing_type=sa.Enum('ORDER', 'DIRECT_SALE', 'RESERVATION', 'RECHARGE', name='ordertype'),
                    type_=sa.Enum('ORDER', 'DIRECT_SALE', 'RESERVATION', name='ordertype'),
                    existing_nullable=True)
    # ### end Alembic commands ###
