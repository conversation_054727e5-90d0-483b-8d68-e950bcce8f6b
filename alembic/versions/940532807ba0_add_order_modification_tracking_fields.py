"""add_order_modification_tracking_fields

Revision ID: 940532807ba0
Revises: 1fdbe52c542f
Create Date: 2025-06-19 15:57:27.594138

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '940532807ba0'
down_revision: Union[str, None] = '1fdbe52c542f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    # 添加订单修改跟踪字段
    op.add_column('orders', sa.Column('is_modified', sa.<PERSON>(), nullable=True, server_default='0', comment="订单是否被修改过"))
    op.add_column('orders', sa.Column('modification_count', sa.Integer(), nullable=True, server_default='0', comment="订单修改次数"))
    op.add_column('orders', sa.Column('last_modified_at', sa.DateTime(), nullable=True, comment="最后修改时间"))
    op.add_column('orders', sa.Column('original_amount', sa.Float(), nullable=True, comment="原始订单金额"))
    op.add_column('orders', sa.Column('current_status', sa.String(200), nullable=True, comment="当前订单状态描述"))


def downgrade():
    # 删除添加的列
    op.drop_column('orders', 'is_modified')
    op.drop_column('orders', 'modification_count')
    op.drop_column('orders', 'last_modified_at')
    op.drop_column('orders', 'original_amount')
    op.drop_column('orders', 'current_status') 
