"""add model coupon

Revision ID: 97383a73d9f1
Revises: f1795beab351
Create Date: 2025-06-15 16:37:36.211856

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '97383a73d9f1'
down_revision: Union[str, None] = 'f1795beab351'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('coupons',
    sa.Column('id', sa.Integer(), nullable=False, comment='优惠券ID'),
    sa.Column('name', sa.String(length=100), nullable=False, comment='优惠券名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='优惠券描述'),
    sa.Column('start_time', sa.DateTime(), nullable=True, comment='优惠券生效时间'),
    sa.Column('end_time', sa.DateTime(), nullable=True, comment='优惠券结束时间'),
    sa.Column('scope', sa.Enum('ORDER', 'PRODUCT', name='couponscope'), nullable=False, comment='优惠券作用范围'),
    sa.Column('is_mutual_exclusive', sa.Boolean(), nullable=False, comment='是否互斥'),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='status'), nullable=False, comment='优惠券状态'),
    sa.Column('type', sa.Enum('COUPON', 'DISCOUNT', 'CASH', 'FULL_REDUCTION', name='coupontype'), nullable=False, comment='优惠券类型'),
    sa.Column('quantity', sa.Integer(), nullable=False, comment='优惠券数量'),
    sa.Column('usage_cycle', sa.Enum('PER_ORDER', 'PER_DAY', 'PER_WEEK', 'PER_MONTH', 'PER_YEAR', name='couponusagecycle'), nullable=True, comment='使用周期'),
    sa.Column('usage_limit', sa.Integer(), nullable=True, comment='使用限制'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_coupons'))
    )
    op.create_index(op.f('ix_coupons_id'), 'coupons', ['id'], unique=False)
    op.create_table('cash_coupons',
    sa.Column('id', sa.Integer(), nullable=False, comment='关联的基础优惠券ID'),
    sa.Column('amount', sa.Float(), nullable=False, comment='抵扣金额'),
    sa.ForeignKeyConstraint(['id'], ['coupons.id'], name=op.f('fk_cash_coupons_id_coupons')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_cash_coupons'))
    )
    op.create_table('discount_coupons',
    sa.Column('id', sa.Integer(), nullable=False, comment='关联的基础优惠券ID'),
    sa.Column('discount_rate', sa.Float(), nullable=False, comment='折扣比例（0-1之间的小数）'),
    sa.Column('min_amount', sa.Float(), nullable=False, comment='最低消费金额'),
    sa.Column('max_discount', sa.Float(), nullable=False, comment='最大折扣金额'),
    sa.ForeignKeyConstraint(['id'], ['coupons.id'], name=op.f('fk_discount_coupons_id_coupons')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_discount_coupons'))
    )
    op.create_table('full_reduction_coupons',
    sa.Column('id', sa.Integer(), nullable=False, comment='关联的基础优惠券ID'),
    sa.Column('full_amount', sa.Float(), nullable=False, comment='满足金额'),
    sa.Column('reduction_amount', sa.Float(), nullable=False, comment='满减金额'),
    sa.ForeignKeyConstraint(['id'], ['coupons.id'], name=op.f('fk_full_reduction_coupons_id_coupons')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_full_reduction_coupons'))
    )
    op.create_table('coupon_usage_records',
    sa.Column('id', sa.Integer(), nullable=False, comment='记录ID'),
    sa.Column('coupon_id', sa.Integer(), nullable=False, comment='优惠券ID'),
    sa.Column('user_id', sa.Integer(), nullable=False, comment='使用者ID'),
    sa.Column('order_id', sa.Integer(), nullable=True, comment='订单ID'),
    sa.Column('used_at', sa.DateTime(), nullable=True, comment='使用时间'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.Column('status', sa.Enum('VALID', 'INVALID', 'NOT_STARTED', 'EXPIRED', 'USED', name='couponusagestatus'), nullable=False, comment='优惠券使用状态'),
    sa.ForeignKeyConstraint(['coupon_id'], ['coupons.id'], name=op.f('fk_coupon_usage_records_coupon_id_coupons')),
    sa.ForeignKeyConstraint(['order_id'], ['orders.id'], name=op.f('fk_coupon_usage_records_order_id_orders')),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_coupon_usage_records_user_id_users')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_coupon_usage_records'))
    )
    op.create_index(op.f('ix_coupon_usage_records_id'), 'coupon_usage_records', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_coupon_usage_records_id'), table_name='coupon_usage_records')
    op.drop_table('coupon_usage_records')
    op.drop_table('full_reduction_coupons')
    op.drop_table('discount_coupons')
    op.drop_table('cash_coupons')
    op.drop_index(op.f('ix_coupons_id'), table_name='coupons')
    op.drop_table('coupons')
    # ### end Alembic commands ###
