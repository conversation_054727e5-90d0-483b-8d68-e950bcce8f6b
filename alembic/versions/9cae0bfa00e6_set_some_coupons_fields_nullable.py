"""set some coupons fields nullable

Revision ID: 9cae0bfa00e6
Revises: f9a8b2c3d4e5
Create Date: 2025-08-31 22:02:52.280870

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '9cae0bfa00e6'
down_revision: Union[str, None] = 'f9a8b2c3d4e5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('coupons', 'condition_scope',
               existing_type=mysql.ENUM('ORDER', 'PRODUCT'),
               nullable=True,
               existing_comment='条件范围')
    op.alter_column('coupons', 'condition_objects',
               existing_type=mysql.JSON(),
               nullable=True,
               existing_comment='条件对象组合')
    op.alter_column('coupons', 'condition_amount',
               existing_type=mysql.FLOAT(),
               nullable=True,
               existing_comment='条件对象金额')
    op.alter_column('coupons', 'usage_quantity',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=True,
               existing_comment='周期可使用数量')
    op.alter_column('coupons', 'usage_cycle',
               existing_type=mysql.ENUM('PER_ORDER', 'PER_DAY', 'PER_WEEK', 'PER_MONTH', 'PER_YEAR'),
               nullable=True,
               existing_comment='使用周期')
    op.alter_column('coupons', 'usage_limit',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=True,
               existing_comment='使用限制')
    op.alter_column('coupons', 'mutual_exclusive_rules',
               existing_type=mysql.JSON(),
               nullable=True,
               existing_comment='互斥规则')
    op.alter_column('coupons', 'payment_channels',
               existing_type=mysql.JSON(),
               nullable=True,
               existing_comment='支付渠道')
    op.alter_column('coupons', 'apply_objects',
               existing_type=mysql.JSON(),
               nullable=True,
               existing_comment='作用对象组合')
    op.alter_column('coupons', 'apply_order',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=True,
               existing_comment='计算顺序')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('coupons', 'apply_order',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=False,
               existing_comment='计算顺序')
    op.alter_column('coupons', 'apply_objects',
               existing_type=mysql.JSON(),
               nullable=False,
               existing_comment='作用对象组合')
    op.alter_column('coupons', 'payment_channels',
               existing_type=mysql.JSON(),
               nullable=False,
               existing_comment='支付渠道')
    op.alter_column('coupons', 'mutual_exclusive_rules',
               existing_type=mysql.JSON(),
               nullable=False,
               existing_comment='互斥规则')
    op.alter_column('coupons', 'usage_limit',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=False,
               existing_comment='使用限制')
    op.alter_column('coupons', 'usage_cycle',
               existing_type=mysql.ENUM('PER_ORDER', 'PER_DAY', 'PER_WEEK', 'PER_MONTH', 'PER_YEAR'),
               nullable=False,
               existing_comment='使用周期')
    op.alter_column('coupons', 'usage_quantity',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=False,
               existing_comment='周期可使用数量')
    op.alter_column('coupons', 'condition_amount',
               existing_type=mysql.FLOAT(),
               nullable=False,
               existing_comment='条件对象金额')
    op.alter_column('coupons', 'condition_objects',
               existing_type=mysql.JSON(),
               nullable=False,
               existing_comment='条件对象组合')
    op.alter_column('coupons', 'condition_scope',
               existing_type=mysql.ENUM('ORDER', 'PRODUCT'),
               nullable=False,
               existing_comment='条件范围')
    # ### end Alembic commands ###
