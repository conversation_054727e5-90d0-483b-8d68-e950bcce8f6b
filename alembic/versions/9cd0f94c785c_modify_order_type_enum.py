"""modify_order_type_enum

Revision ID: 9cd0f94c785c
Revises: 345113584900
Create Date: 2025-04-20 16:15:04.096940

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9cd0f94c785c'
down_revision: Union[str, None] = '345113584900'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # MySQL 中修改枚举类型
    op.execute("ALTER TABLE orders MODIFY COLUMN type ENUM('ORDER', 'DIRECT_SALE', 'RESERVATION', 'RECHARGE') NOT NULL COMMENT '订单类型'")

def downgrade() -> None:
    # 回滚操作
    op.execute("ALTER TABLE orders MODIFY COLUMN type ENUM('ORDER', 'DIRECT_SALE', 'RESERVATION') NOT NULL COMMENT '订单类型'")
