"""add personal user fields

Revision ID: a46ce85672bb
Revises: ee75610b0bb2
Create Date: 2025-04-18 13:55:06.559104

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import inspect


# revision identifiers, used by Alembic.
revision: str = 'a46ce85672bb'
down_revision: Union[str, None] = 'ee75610b0bb2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    inspector = inspect(conn)
    columns = [col['name'] for col in inspector.get_columns('personal_users')]
    
    if 'nickname' not in columns:
        op.add_column('personal_users', sa.Column('nickname', sa.String(length=128), nullable=True, comment='用户昵称'))
    if 'avatar_url' not in columns:    
        op.add_column('personal_users', sa.Column('avatar_url', sa.String(length=256), nullable=True, comment='头像URL'))
    if 'gender' not in columns:
        op.add_column('personal_users', sa.Column('gender', sa.Integer(), nullable=True, comment='性别'))
    if 'country' not in columns:
        op.add_column('personal_users', sa.Column('country', sa.String(length=64), nullable=True, comment='国家'))
    if 'province' not in columns:
        op.add_column('personal_users', sa.Column('province', sa.String(length=64), nullable=True, comment='省份'))
    if 'city' not in columns:
        op.add_column('personal_users', sa.Column('city', sa.String(length=64), nullable=True, comment='城市'))
    if 'token' not in columns:
        op.add_column('personal_users', sa.Column('token', sa.String(length=128), nullable=True, comment='用户令牌'))
    if 'token_expiry' not in columns:
        op.add_column('personal_users', sa.Column('token_expiry', sa.DateTime(), nullable=True, comment='令牌过期时间'))
    if 'msg_status' not in columns:
        op.add_column('personal_users', sa.Column('msg_status', sa.Integer(), nullable=True, comment='消息状态'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('personal_users', 'msg_status')
    op.drop_column('personal_users', 'token_expiry')
    op.drop_column('personal_users', 'token')
    op.drop_column('personal_users', 'city')
    op.drop_column('personal_users', 'province')
    op.drop_column('personal_users', 'country')
    op.drop_column('personal_users', 'gender')
    op.drop_column('personal_users', 'avatar_url')
    op.drop_column('personal_users', 'nickname')
    # ### end Alembic commands ###
