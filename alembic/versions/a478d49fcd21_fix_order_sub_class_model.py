"""fix_order_sub_class_model

Revision ID: a478d49fcd21
Revises: 1fc3110d1ee6
Create Date: 2025-05-01 14:57:39.259806

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'a478d49fcd21'
down_revision: Union[str, None] = '1fc3110d1ee6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('direct_sale_orders',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['id'], ['orders.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('reservation_orders',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('reservation_status', sa.Enum('PENDING', 'APPROVED', 'REJECTED', name='reservationstatus'), nullable=False, comment='预订状态'),
    sa.ForeignKeyConstraint(['id'], ['orders.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.alter_column('orders', 'type',
               existing_type=mysql.ENUM('ORDER', 'DIRECT_SALE', 'RESERVATION', 'RECHARGE'),
               nullable=False,
               comment='订单类型')
    op.alter_column('wx_payment_records', 'id',
               existing_type=mysql.INTEGER(display_width=11),
               comment='支付记录ID',
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('wx_payment_records', 'order_id',
               existing_type=mysql.INTEGER(display_width=11),
               comment='关联订单ID',
               existing_nullable=False)
    op.alter_column('wx_payment_records', 'order_no',
               existing_type=mysql.VARCHAR(length=50),
               comment='商户订单号',
               existing_nullable=False)
    op.alter_column('wx_payment_records', 'transaction_id',
               existing_type=mysql.VARCHAR(length=50),
               comment='微信支付订单号',
               existing_nullable=True)
    op.alter_column('wx_payment_records', 'prepay_id',
               existing_type=mysql.VARCHAR(length=64),
               comment='预支付交易会话标识',
               existing_nullable=True)
    op.alter_column('wx_payment_records', 'openid',
               existing_type=mysql.VARCHAR(length=50),
               comment='用户OpenID',
               existing_nullable=False)
    op.alter_column('wx_payment_records', 'total_amount',
               existing_type=mysql.FLOAT(),
               comment='订单总金额(元)',
               existing_nullable=False)
    op.alter_column('wx_payment_records', 'payer_total',
               existing_type=mysql.FLOAT(),
               comment='用户实际支付金额(元)',
               existing_nullable=True)
    op.alter_column('wx_payment_records', 'currency',
               existing_type=mysql.VARCHAR(length=10),
               comment='货币类型',
               existing_nullable=False,
               existing_server_default=sa.text("'CNY'"))
    op.alter_column('wx_payment_records', 'status',
               existing_type=mysql.ENUM('UNPAID', 'SUCCESS', 'REFUND', 'CLOSED', 'NOTPAY', 'USERPAYING', 'PAYERROR'),
               comment='支付状态',
               existing_nullable=False,
               existing_server_default=sa.text("'UNPAID'"))
    op.alter_column('wx_payment_records', 'description',
               existing_type=mysql.VARCHAR(length=128),
               comment='商品描述',
               existing_nullable=False)
    op.alter_column('wx_payment_records', 'trade_state',
               existing_type=mysql.VARCHAR(length=32),
               comment='交易状态',
               existing_nullable=True)
    op.alter_column('wx_payment_records', 'trade_state_desc',
               existing_type=mysql.VARCHAR(length=256),
               comment='交易状态描述',
               existing_nullable=True)
    op.alter_column('wx_payment_records', 'bank_type',
               existing_type=mysql.VARCHAR(length=32),
               comment='付款银行类型',
               existing_nullable=True)
    op.alter_column('wx_payment_records', 'success_time',
               existing_type=mysql.DATETIME(),
               comment='支付完成时间',
               existing_nullable=True)
    op.alter_column('wx_payment_records', 'payment_request_params',
               existing_type=mysql.JSON(),
               comment='支付请求参数',
               existing_nullable=True)
    op.alter_column('wx_payment_records', 'notify_data',
               existing_type=mysql.JSON(),
               comment='支付回调数据',
               existing_nullable=True)
    op.alter_column('wx_payment_records', 'created_at',
               existing_type=mysql.DATETIME(),
               comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('wx_payment_records', 'updated_at',
               existing_type=mysql.DATETIME(),
               comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('wx_refund_records', 'id',
               existing_type=mysql.INTEGER(display_width=11),
               comment='退款记录ID',
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('wx_refund_records', 'payment_id',
               existing_type=mysql.INTEGER(display_width=11),
               comment='关联支付记录ID',
               existing_nullable=False)
    op.alter_column('wx_refund_records', 'refund_id',
               existing_type=mysql.VARCHAR(length=50),
               comment='微信退款单号',
               existing_nullable=True)
    op.alter_column('wx_refund_records', 'out_refund_no',
               existing_type=mysql.VARCHAR(length=50),
               comment='商户退款单号',
               existing_nullable=False)
    op.alter_column('wx_refund_records', 'transaction_id',
               existing_type=mysql.VARCHAR(length=50),
               comment='微信支付订单号',
               existing_nullable=False)
    op.alter_column('wx_refund_records', 'total_amount',
               existing_type=mysql.FLOAT(),
               comment='原订单金额(元)',
               existing_nullable=False)
    op.alter_column('wx_refund_records', 'refund_amount',
               existing_type=mysql.FLOAT(),
               comment='退款金额(元)',
               existing_nullable=False)
    op.alter_column('wx_refund_records', 'currency',
               existing_type=mysql.VARCHAR(length=10),
               comment='货币类型',
               existing_nullable=False,
               existing_server_default=sa.text("'CNY'"))
    op.alter_column('wx_refund_records', 'status',
               existing_type=mysql.ENUM('PROCESSING', 'SUCCESS', 'ABNORMAL', 'CLOSED'),
               comment='退款状态',
               existing_nullable=False,
               existing_server_default=sa.text("'PROCESSING'"))
    op.alter_column('wx_refund_records', 'reason',
               existing_type=mysql.VARCHAR(length=128),
               comment='退款原因',
               existing_nullable=True)
    op.alter_column('wx_refund_records', 'refund_time',
               existing_type=mysql.DATETIME(),
               comment='退款成功时间',
               existing_nullable=True)
    op.alter_column('wx_refund_records', 'notify_data',
               existing_type=mysql.JSON(),
               comment='退款回调数据',
               existing_nullable=True)
    op.alter_column('wx_refund_records', 'created_at',
               existing_type=mysql.DATETIME(),
               comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('wx_refund_records', 'updated_at',
               existing_type=mysql.DATETIME(),
               comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('wx_refund_records', 'updated_at',
               existing_type=mysql.DATETIME(),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('wx_refund_records', 'created_at',
               existing_type=mysql.DATETIME(),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('wx_refund_records', 'notify_data',
               existing_type=mysql.JSON(),
               comment=None,
               existing_comment='退款回调数据',
               existing_nullable=True)
    op.alter_column('wx_refund_records', 'refund_time',
               existing_type=mysql.DATETIME(),
               comment=None,
               existing_comment='退款成功时间',
               existing_nullable=True)
    op.alter_column('wx_refund_records', 'reason',
               existing_type=mysql.VARCHAR(length=128),
               comment=None,
               existing_comment='退款原因',
               existing_nullable=True)
    op.alter_column('wx_refund_records', 'status',
               existing_type=mysql.ENUM('PROCESSING', 'SUCCESS', 'ABNORMAL', 'CLOSED'),
               comment=None,
               existing_comment='退款状态',
               existing_nullable=False,
               existing_server_default=sa.text("'PROCESSING'"))
    op.alter_column('wx_refund_records', 'currency',
               existing_type=mysql.VARCHAR(length=10),
               comment=None,
               existing_comment='货币类型',
               existing_nullable=False,
               existing_server_default=sa.text("'CNY'"))
    op.alter_column('wx_refund_records', 'refund_amount',
               existing_type=mysql.FLOAT(),
               comment=None,
               existing_comment='退款金额(元)',
               existing_nullable=False)
    op.alter_column('wx_refund_records', 'total_amount',
               existing_type=mysql.FLOAT(),
               comment=None,
               existing_comment='原订单金额(元)',
               existing_nullable=False)
    op.alter_column('wx_refund_records', 'transaction_id',
               existing_type=mysql.VARCHAR(length=50),
               comment=None,
               existing_comment='微信支付订单号',
               existing_nullable=False)
    op.alter_column('wx_refund_records', 'out_refund_no',
               existing_type=mysql.VARCHAR(length=50),
               comment=None,
               existing_comment='商户退款单号',
               existing_nullable=False)
    op.alter_column('wx_refund_records', 'refund_id',
               existing_type=mysql.VARCHAR(length=50),
               comment=None,
               existing_comment='微信退款单号',
               existing_nullable=True)
    op.alter_column('wx_refund_records', 'payment_id',
               existing_type=mysql.INTEGER(display_width=11),
               comment=None,
               existing_comment='关联支付记录ID',
               existing_nullable=False)
    op.alter_column('wx_refund_records', 'id',
               existing_type=mysql.INTEGER(display_width=11),
               comment=None,
               existing_comment='退款记录ID',
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('wx_payment_records', 'updated_at',
               existing_type=mysql.DATETIME(),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('wx_payment_records', 'created_at',
               existing_type=mysql.DATETIME(),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('wx_payment_records', 'notify_data',
               existing_type=mysql.JSON(),
               comment=None,
               existing_comment='支付回调数据',
               existing_nullable=True)
    op.alter_column('wx_payment_records', 'payment_request_params',
               existing_type=mysql.JSON(),
               comment=None,
               existing_comment='支付请求参数',
               existing_nullable=True)
    op.alter_column('wx_payment_records', 'success_time',
               existing_type=mysql.DATETIME(),
               comment=None,
               existing_comment='支付完成时间',
               existing_nullable=True)
    op.alter_column('wx_payment_records', 'bank_type',
               existing_type=mysql.VARCHAR(length=32),
               comment=None,
               existing_comment='付款银行类型',
               existing_nullable=True)
    op.alter_column('wx_payment_records', 'trade_state_desc',
               existing_type=mysql.VARCHAR(length=256),
               comment=None,
               existing_comment='交易状态描述',
               existing_nullable=True)
    op.alter_column('wx_payment_records', 'trade_state',
               existing_type=mysql.VARCHAR(length=32),
               comment=None,
               existing_comment='交易状态',
               existing_nullable=True)
    op.alter_column('wx_payment_records', 'description',
               existing_type=mysql.VARCHAR(length=128),
               comment=None,
               existing_comment='商品描述',
               existing_nullable=False)
    op.alter_column('wx_payment_records', 'status',
               existing_type=mysql.ENUM('UNPAID', 'SUCCESS', 'REFUND', 'CLOSED', 'NOTPAY', 'USERPAYING', 'PAYERROR'),
               comment=None,
               existing_comment='支付状态',
               existing_nullable=False,
               existing_server_default=sa.text("'UNPAID'"))
    op.alter_column('wx_payment_records', 'currency',
               existing_type=mysql.VARCHAR(length=10),
               comment=None,
               existing_comment='货币类型',
               existing_nullable=False,
               existing_server_default=sa.text("'CNY'"))
    op.alter_column('wx_payment_records', 'payer_total',
               existing_type=mysql.FLOAT(),
               comment=None,
               existing_comment='用户实际支付金额(元)',
               existing_nullable=True)
    op.alter_column('wx_payment_records', 'total_amount',
               existing_type=mysql.FLOAT(),
               comment=None,
               existing_comment='订单总金额(元)',
               existing_nullable=False)
    op.alter_column('wx_payment_records', 'openid',
               existing_type=mysql.VARCHAR(length=50),
               comment=None,
               existing_comment='用户OpenID',
               existing_nullable=False)
    op.alter_column('wx_payment_records', 'prepay_id',
               existing_type=mysql.VARCHAR(length=64),
               comment=None,
               existing_comment='预支付交易会话标识',
               existing_nullable=True)
    op.alter_column('wx_payment_records', 'transaction_id',
               existing_type=mysql.VARCHAR(length=50),
               comment=None,
               existing_comment='微信支付订单号',
               existing_nullable=True)
    op.alter_column('wx_payment_records', 'order_no',
               existing_type=mysql.VARCHAR(length=50),
               comment=None,
               existing_comment='商户订单号',
               existing_nullable=False)
    op.alter_column('wx_payment_records', 'order_id',
               existing_type=mysql.INTEGER(display_width=11),
               comment=None,
               existing_comment='关联订单ID',
               existing_nullable=False)
    op.alter_column('wx_payment_records', 'id',
               existing_type=mysql.INTEGER(display_width=11),
               comment=None,
               existing_comment='支付记录ID',
               existing_nullable=False,
               autoincrement=True)
    op.alter_column('orders', 'type',
               existing_type=mysql.ENUM('ORDER', 'DIRECT_SALE', 'RESERVATION', 'RECHARGE'),
               nullable=True,
               comment=None,
               existing_comment='订单类型')
    op.drop_table('reservation_orders')
    op.drop_table('direct_sale_orders')
    # ### end Alembic commands ###
