"""add_order_modification_tracking_fields

Revision ID: ad4d7d3f1bb4
Revises: 940532807ba0
Create Date: 2025-06-19 15:59:09.818759

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ad4d7d3f1bb4'
down_revision: Union[str, None] = '940532807ba0'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
