"""add_verified_time_fields_to_rule_items

Revision ID: a1b2c3d4e5f6
Revises: 579eca1469ab
Create Date: 2025-01-20 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a1b2c3d4e5f6'
down_revision: Union[str, None] = '579eca1469ab'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('rule_items', sa.Column('verified_start_time', sa.Integer(), nullable=True, comment='核销开始时间，单位：分钟'))
    op.add_column('rule_items', sa.Column('verified_end_time', sa.Integer(), nullable=True, comment='核销截止时间，单位：分钟'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('rule_items', 'verified_end_time')
    op.drop_column('rule_items', 'verified_start_time')
    # ### end Alembic commands ###
