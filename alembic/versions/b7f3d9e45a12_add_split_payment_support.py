"""add split payment support

Revision ID: b7f3d9e45a12
Revises: ead044cdef14
Create Date: 2025-01-27 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'b7f3d9e45a12'
down_revision: Union[str, None] = 'ead044cdef14'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """升级操作：添加分次支付功能所需的字段和枚举值"""
    
    # 1. 修改 PaymentStatus 枚举，添加 PARTIAL_PAID 状态
    # 保持数据库中的大写格式，但支持模型中的小写映射
    op.alter_column('orders', 'payment_status',
                   existing_type=mysql.ENUM('UNPAID', 'PAID', 'REFUNDED', name='paymentstatus'),
                   type_=sa.Enum('UNPAID', 'PAID', 'PARTIAL_PAID', 'REFUNDED', name='paymentstatus'),
                   nullable=False,
                   comment='支付状态')
    
    # 2. 添加分次支付相关字段到订单表
    op.add_column('orders', sa.Column(
        'enterprise_paid_amount', 
        sa.Float(), 
        nullable=False, 
        server_default='0.0',
        comment='企业已支付金额'
    ))
    
    op.add_column('orders', sa.Column(
        'personal_paid_amount', 
        sa.Float(), 
        nullable=False, 
        server_default='0.0',
        comment='个人已支付金额'
    ))
    
    op.add_column('orders', sa.Column(
        'requires_personal_payment', 
        sa.Float(), 
        nullable=False, 
        server_default='0.0',
        comment='需要个人支付的金额'
    ))
    
    op.add_column('orders', sa.Column(
        'is_split_payment', 
        sa.Boolean(), 
        nullable=False, 
        server_default='0',
        comment='是否为分次支付订单'
    ))
    
    # 3. 为新字段添加索引以提高查询性能
    op.create_index('idx_orders_is_split_payment', 'orders', ['is_split_payment'])
    op.create_index('idx_orders_payment_status_split', 'orders', ['payment_status', 'is_split_payment'])


def downgrade() -> None:
    """降级操作：删除分次支付功能相关的字段和枚举值"""
    
    # 1. 删除添加的索引
    try:
        op.drop_index('idx_orders_payment_status_split', 'orders')
    except:
        pass  # 索引可能不存在
    
    try:
        op.drop_index('idx_orders_is_split_payment', 'orders')
    except:
        pass  # 索引可能不存在
    
    # 2. 删除添加的列
    op.drop_column('orders', 'is_split_payment')
    op.drop_column('orders', 'requires_personal_payment')
    op.drop_column('orders', 'personal_paid_amount')
    op.drop_column('orders', 'enterprise_paid_amount')
    
    # 3. 恢复原始的 PaymentStatus 枚举（移除 PARTIAL_PAID）
    # 首先将任何 PARTIAL_PAID 状态改为 UNPAID
    op.execute("UPDATE orders SET payment_status = 'UNPAID' WHERE payment_status = 'PARTIAL_PAID'")
    
    # 恢复原始枚举
    op.alter_column('orders', 'payment_status',
                   existing_type=mysql.ENUM('UNPAID', 'PAID', 'PARTIAL_PAID', 'REFUNDED', name='paymentstatus'),
                   type_=sa.Enum('UNPAID', 'PAID', 'REFUNDED', name='paymentstatus'),
                   nullable=False,
                   comment='支付状态')
