"""unify_order_status_enum_values

Revision ID: b984188c8eae
Revises: ce8daaffb66d
Create Date: 2025-05-07 09:59:15.095701

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b984188c8eae'
down_revision: Union[str, None] = 'ce8daaffb66d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 统一为小写枚举值
    
    # 或统一为大写
    op.execute("ALTER TABLE orders MODIFY COLUMN status ENUM('PENDING', 'PAID', 'SHIPPED', 'DELIVERED', 'VERIFIED', 'COMPLETED', 'CANCELLED', 'REFUNDED', 'REFUNDED_PARTIAL') NOT NULL COMMENT '订单状态'")
    op.execute("ALTER TABLE order_items MODIFY COLUMN status ENUM('PENDING', 'PAID', 'SHIPPED', 'DELIVERED', 'VERIFIED', 'COMPLETED', 'CANCELLED', 'REFUNDED', 'REFUNDED_PARTIAL') NOT NULL COMMENT '订单状态'")

def downgrade() -> None:
    """Downgrade schema."""
    pass
