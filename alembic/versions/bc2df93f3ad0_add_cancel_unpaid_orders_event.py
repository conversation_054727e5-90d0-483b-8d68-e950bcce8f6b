"""add cancel_unpaid_orders event

Revision ID: bc2df93f3ad0
Revises: 52096f9035f8
Create Date: 2025-05-06 11:52:24.356009

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'bc2df93f3ad0'
down_revision: Union[str, None] = '52096f9035f8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # 开启事件调度器,没有root权限无法执行
    # op.execute("SET GLOBAL event_scheduler = ON")
    # 检查事件是否存在，不存在则创建
    op.execute("""DROP EVENT IF EXISTS cancel_unpaid_orders""")
    op.execute("""
        CREATE EVENT cancel_unpaid_orders
        ON SCHEDULE EVERY 5 MINUTE
        DO
            UPDATE orders
            SET
                status = 'CANCELLED',
                updated_at = NOW()
            WHERE
                status = 'PENDING'
                AND payment_status = 'UNPAID'
                AND created_at <= DATE_SUB(NOW(), INTERVAL 10 MINUTE)
    """)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("DROP EVENT IF EXISTS cancel_unpaid_orders")
    # ### end Alembic commands ###
