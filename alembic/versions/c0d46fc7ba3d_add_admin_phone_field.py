"""add_admin_phone_field

Revision ID: c0d46fc7ba3d
Revises: 39d110955c39
Create Date: 2025-04-25 13:44:53.352621

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import inspect


# revision identifiers, used by Alembic.
revision: str = 'c0d46fc7ba3d'
down_revision: Union[str, None] = '39d110955c39'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    inspector = inspect(conn)
    columns = [col['name'] for col in inspector.get_columns('admins')]
    
    if 'phone' not in columns:
        op.add_column('admins', sa.Column('phone', sa.String(length=20), nullable=True, comment='手机号码'))
    if 'email' not in columns:
        op.add_column('admins', sa.Column('email', sa.String(length=100), nullable=True, comment='电子邮箱'))
    if 'username' not in columns:
        op.add_column('admins', sa.Column('username', sa.String(length=50), nullable=True, comment='用户名'))
        
    # 检查是否已有唯一索引
    indices = [idx['name'] for idx in inspector.get_indexes('admins')]
    if 'ix_admins_phone' not in indices:
        op.create_unique_constraint(None, 'admins', ['phone'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    try:
        op.drop_constraint(None, 'admins', type_='unique')
        op.drop_column('admins', 'username')
        op.drop_column('admins', 'email')
        op.drop_column('admins', 'phone')
    except Exception:
        pass
    # ### end Alembic commands ###
