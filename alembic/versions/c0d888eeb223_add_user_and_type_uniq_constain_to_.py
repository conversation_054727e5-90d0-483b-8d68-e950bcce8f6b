"""add_user_and_type_uniq_constain_to_account

Revision ID: c0d888eeb223
Revises: c72ca53f0e03
Create Date: 2025-04-27 11:38:53.134988

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c0d888eeb223'
down_revision: Union[str, None] = 'c72ca53f0e03'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint('uq_user_id_type', 'accounts', ['user_id', 'type'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('uq_user_id_type', 'accounts', type_='unique')
    # ### end Alembic commands ###
