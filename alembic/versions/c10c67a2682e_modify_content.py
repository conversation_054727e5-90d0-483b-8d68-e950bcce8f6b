"""modify_content

Revision ID: c10c67a2682e
Revises: c0d46fc7ba3d
Create Date: 2025-04-25 16:59:40.807537

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c10c67a2682e'
down_revision: Union[str, None] = 'c0d46fc7ba3d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('articles',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('summary', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['id'], ['contents.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('content_tag_relations',
    sa.Column('content_id', sa.Integer(), nullable=True),
    sa.Column('tag_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['content_id'], ['contents.id'], ),
    sa.ForeignKeyConstraint(['tag_id'], ['tags.id'], ),
    sa.UniqueConstraint('content_id', 'tag_id', name='uq_content_tag')
    )
    op.create_table('dishes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['id'], ['contents.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.alter_column('contents', 'type',
                    existing_type=sa.Enum('CONTENT', 'ARTICLE', name='contenttype'),
                    type_=sa.Enum('CONTENT', 'ARTICLE', 'DISH', name='contenttype'),
                    existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('dishes')
    op.drop_table('content_tag_relations')
    op.drop_table('articles')
    op.alter_column('contents', 'type',
                    existing_type=sa.Enum('CONTENT', 'ARTICLE', 'DISH', name='contenttype'),
                    type_=sa.Enum('CONTENT', 'ARTICLE', name='contenttype'),
                    existing_nullable=True)
    # ### end Alembic commands ###
