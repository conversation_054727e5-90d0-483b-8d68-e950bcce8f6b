"""add_reservation_rule_sub_type

Revision ID: c255389bef32
Revises: c28cbc50ec9e
Create Date: 2025-05-26 11:20:26.535862

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c255389bef32'
down_revision: Union[str, None] = 'c28cbc50ec9e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        "ALTER TABLE rules MODIFY COLUMN type ENUM('RULE', 'RESERVATION', 'DINING_RESERVATION') NOT NULL DEFAULT 'RULE'")

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        "ALTER TABLE rules MODIFY COLUMN type ENUM('RESERVATION') NOT NULL DEFAULT 'RESERVATION'")
    # ### end Alembic commands ###
