"""add_menu_model

Revision ID: c28cbc50ec9e
Revises: e083699e12b1
Create Date: 2025-05-26 10:56:42.466672

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'c28cbc50ec9e'
down_revision: Union[str, None] = 'e083699e12b1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('dining_reservation_rules',
    sa.Column('id', sa.Integer(), nullable=False, comment='规则ID'),
    sa.Column('alias', sa.String(length=50), nullable=True, comment='别名'),
    sa.Column('dining_start_time_cron_str', sa.String(length=50), nullable=True, comment='核销开始时间cron表达式'),
    sa.Column('dining_end_time_cron_str', sa.String(length=50), nullable=True, comment='核销结束时间cron表达式'),
    sa.Column('verify_start_time_cron_str', sa.String(length=50), nullable=True, comment='核销开始时间cron表达式'),
    sa.Column('verify_end_time_cron_str', sa.String(length=50), nullable=True, comment='核销结束时间cron表达式'),
    sa.Column('order_deadline', sa.Integer(), nullable=True, comment='订购截止提前量，单位：分钟'),
    sa.Column('cancellation_deadline', sa.Integer(), nullable=True, comment='取消截止提前量，单位：分钟'),
    sa.Column('is_auto_verify', sa.Boolean(), nullable=False, comment='是否自动核销'),
    sa.ForeignKeyConstraint(['id'], ['rules.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('menus',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False, comment='菜单名称'),
    sa.Column('description', sa.String(length=200), nullable=True, comment='菜单描述'),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='status'), nullable=False, comment='菜单状态'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.Column('product_id', sa.Integer(), nullable=False, comment='产品ID'),
    sa.Column('rule_id', sa.Integer(), nullable=False, comment='规则ID'),
    sa.Column('available_start_time_cron_str', sa.String(length=50), nullable=True, comment='生效开始时间cron表达式'),
    sa.Column('available_end_time_cron_str', sa.String(length=50), nullable=True, comment='生效结束时间cron表达式'),
    sa.Column('available_date', sa.DateTime(), nullable=True, comment='生效日'),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ),
    sa.ForeignKeyConstraint(['rule_id'], ['rules.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_menus_id'), 'menus', ['id'], unique=False)
    op.create_table('menu_content_relations',
    sa.Column('menu_id', sa.Integer(), nullable=True),
    sa.Column('content_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['content_id'], ['contents.id'], ),
    sa.ForeignKeyConstraint(['menu_id'], ['menus.id'], ),
    sa.UniqueConstraint('menu_id', 'content_id', name='uq_menu_content')
    )
    op.alter_column('accounts', 'type',
               existing_type=mysql.VARCHAR(length=20),
               type_=sa.Enum('ACCOUNT', 'REGULAR', 'GIFT', 'POINTS', 'MEMBER', 'WECHAT', name='accounttype'),
               nullable=False,
               existing_server_default=sa.text("'account'"))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('accounts', 'type',
               existing_type=sa.Enum('ACCOUNT', 'REGULAR', 'GIFT', 'POINTS', 'MEMBER', 'WECHAT', name='accounttype'),
               type_=mysql.VARCHAR(length=20),
               nullable=True,
               existing_server_default=sa.text("'account'"))
    op.drop_table('menu_content_relations')
    op.drop_index(op.f('ix_menus_id'), table_name='menus')
    op.drop_table('menus')
    op.drop_table('dining_reservation_rules')
    # ### end Alembic commands ###
