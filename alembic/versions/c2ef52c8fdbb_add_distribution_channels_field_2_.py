"""add_distribution_channels_field_2_coupon_usage_records

Revision ID: c2ef52c8fdbb
Revises: 9cae0bfa00e6
Create Date: 2025-09-01 12:01:37.720981

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c2ef52c8fdbb'
down_revision: Union[str, None] = '9cae0bfa00e6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coupon_usage_records', sa.Column('distribution_channel', sa.Enum('NEW_USER', 'VIEW_ACTIVITY', 'SHARE_ACTIVITY', 'PURCHASE', 'MANUAL', name='distributionchannel'), nullable=True, comment='分发渠道'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('coupon_usage_records', 'distribution_channel')
    # ### end Alembic commands ###
