"""remove_personal_user_email_uniq_constrain

Revision ID: c72ca53f0e03
Revises: 514a073427fb
Create Date: 2025-04-27 08:54:29.907109

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c72ca53f0e03'
down_revision: Union[str, None] = '514a073427fb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('email', table_name='personal_users')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('email', 'personal_users', ['email'], unique=True)
    # ### end Alembic commands ###
