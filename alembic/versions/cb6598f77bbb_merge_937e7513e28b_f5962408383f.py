"""merge_937e7513e28b_f5962408383f

Revision ID: cb6598f77bbb
Revises: 937e7513e28b, f5962408383f
Create Date: 2025-04-30 12:37:40.733385

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'cb6598f77bbb'
down_revision: Union[str, None] = ('937e7513e28b', 'f5962408383f')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
