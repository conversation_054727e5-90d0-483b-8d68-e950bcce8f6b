"""add_gzh_unionid_field

Revision ID: cc7305f00289
Revises: eb06c7a946a5
Create Date: 2025-06-26 15:00:23.985094

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'cc7305f00289'
down_revision: Union[str, None] = 'eb06c7a946a5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None



def upgrade() -> None:
    """Upgrade schema."""
    # 检查列是否存在
    pass

def downgrade() -> None:
    """Downgrade schema."""
    # 检查列是否存在
    pass
