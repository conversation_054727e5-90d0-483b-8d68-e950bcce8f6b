"""add_refunded_partial_to_orderstatus

Revision ID: ce8daaffb66d
Revises: 8794b7eae12d
Create Date: 2025-05-07 09:50:10.427371

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ce8daaffb66d'
down_revision: Union[str, None] = '8794b7eae12d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """更新数据库架构，添加REFUNDED_PARTIAL状态"""
    # 修改orders表的status列
    op.execute("ALTER TABLE orders MODIFY COLUMN status ENUM('pending', 'paid', 'shipped', 'delivered', 'verified', 'completed', 'cancelled', 'refunded', 'refunded_partial') NOT NULL COMMENT '订单状态'")

    # 修改order_items表的status列
    op.execute("ALTER TABLE order_items MODIFY COLUMN status ENUM('PENDING', 'PAID', 'SHIPPED', 'DELIVERED', 'VERIFIED', 'COMPLETED', 'CANCELLED', 'REFUNDED', 'REFUNDED_PARTIAL') NOT NULL COMMENT '订单状态'")


def downgrade() -> None:
    """回滚数据库架构，移除REFUNDED_PARTIAL状态"""
    # 还原orders表的status列
    op.execute("ALTER TABLE orders MODIFY COLUMN status ENUM('pending', 'paid', 'shipped', 'delivered', 'verified', 'completed', 'cancelled', 'refunded') NOT NULL COMMENT '订单状态'")

    # 还原order_items表的status列
    op.execute("ALTER TABLE order_items MODIFY COLUMN status ENUM('PENDING', 'PAID', 'SHIPPED', 'DELIVERED', 'VERIFIED', 'COMPLETED', 'CANCELLED', 'REFUNDED') NOT NULL COMMENT '订单状态'")
