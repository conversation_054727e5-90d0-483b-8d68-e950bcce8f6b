"""merge heads

Revision ID: d32c845f9db4
Revises: 3783d028d217, dc3c3d25c139
Create Date: 2025-06-30 15:13:30.786321

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd32c845f9db4'
down_revision: Union[str, None] = ('3783d028d217', 'dc3c3d25c139')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
