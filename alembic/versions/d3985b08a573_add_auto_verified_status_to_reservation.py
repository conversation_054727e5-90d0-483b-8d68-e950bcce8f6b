"""add_auto_verified_status_to_reservation

Revision ID: d3985b08a573
Revises: 48a2347c769d
Create Date: 2025-05-15 21:39:50.542203

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd3985b08a573'
down_revision: Union[str, None] = '48a2347c769d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.execute("ALTER TABLE reservation_requests MODIFY COLUMN status ENUM('pending', 'paid_deposit', 'paid_full', 'cancelled', 'verified', 'auto_verified') NOT NULL DEFAULT 'pending'")


def downgrade() -> None:
    """Downgrade schema."""
    op.execute("ALTER TABLE reservation_requests MODIFY COLUMN status ENUM('pending', 'paid_deposit', 'paid_full', 'cancelled', 'verified') NOT NULL DEFAULT 'pending'") 
