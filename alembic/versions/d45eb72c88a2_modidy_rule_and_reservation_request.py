"""modidy_rule_and_reservation_request

Revision ID: d45eb72c88a2
Revises: c255389bef32
Create Date: 2025-06-11 16:26:33.677605

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'd45eb72c88a2'
down_revision: Union[str, None] = 'c255389bef32'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('biz_reservation_requests',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=True, comment='联系人姓名'),
    sa.Column('phone', sa.String(length=20), nullable=True, comment='联系人电话'),
    sa.<PERSON>umn('persons', sa.Integer(), nullable=True, comment='预订人数'),
    sa.Column('remark', sa.String(length=200), nullable=True, comment='备注'),
    sa.ForeignKeyConstraint(['id'], ['reservation_requests.id'], name=op.f('fk_biz_reservation_requests_id_reservation_requests')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_biz_reservation_requests'))
    )
    op.create_table('buffet_reservation_requests',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['id'], ['reservation_requests.id'], name=op.f('fk_buffet_reservation_requests_id_reservation_requests')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_buffet_reservation_requests'))
    )
    op.drop_index('ix_admins_phone', table_name='admins')
    op.drop_index('phone', table_name='admins')
    op.create_unique_constraint(op.f('uq_admins_phone'), 'admins', ['phone'])
    op.add_column('dining_reservation_rules', sa.Column('generated_count', sa.Integer(), nullable=True, comment='生成的数量'))
    op.add_column('dining_reservation_rules', sa.Column('quantity', sa.Integer(), nullable=True, comment='数量'))
    op.alter_column('dining_reservation_rules', 'dining_start_time_cron_str',
               existing_type=mysql.VARCHAR(length=50),
               comment='服务开始时间cron表达式',
               existing_comment='核销开始时间cron表达式',
               existing_nullable=True)
    op.alter_column('dining_reservation_rules', 'dining_end_time_cron_str',
               existing_type=mysql.VARCHAR(length=50),
               comment='服务结束时间cron表达式',
               existing_comment='核销结束时间cron表达式',
               existing_nullable=True)
    op.drop_index('company_name', table_name='enterprises')
    op.create_unique_constraint(op.f('uq_enterprises_company_name'), 'enterprises', ['company_name'])
    op.drop_index('code', table_name='permissions')
    op.drop_index('name', table_name='permissions')
    op.create_unique_constraint(op.f('uq_permissions_code'), 'permissions', ['code'])
    op.create_unique_constraint(op.f('uq_permissions_name'), 'permissions', ['name'])
    op.drop_index('id_card', table_name='personal_users')
    op.drop_index('phone', table_name='personal_users')
    op.drop_index('wechat_id', table_name='personal_users')
    op.create_unique_constraint(op.f('uq_personal_users_id_card'), 'personal_users', ['id_card'])
    op.create_unique_constraint(op.f('uq_personal_users_phone'), 'personal_users', ['phone'])
    op.create_unique_constraint(op.f('uq_personal_users_wechat_id'), 'personal_users', ['wechat_id'])
    op.add_column('reservation_requests', sa.Column('orders_id', sa.Integer(), nullable=True))
    op.add_column('reservation_requests', sa.Column('type', sa.Enum('RESERVATION', 'BUFFET_RESERVATION', 'BIZ_DINING_RESERVATION', name='reservationtype'), nullable=False, comment='预订类型'))
    op.add_column('reservation_requests', sa.Column('scope', sa.Enum('ORDER', 'PRODUCT', name='reservationscope'), nullable=False, comment='预订作用范围'))
    op.alter_column('reservation_requests', 'order_item_id',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=True)
    op.alter_column('reservation_requests', 'product_id',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=True)
    op.alter_column('reservation_requests', 'reservation_period',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               comment='预订时段',
               existing_nullable=True)
    op.create_foreign_key(op.f('fk_reservation_requests_orders_id_orders'), 'reservation_requests', 'orders', ['orders_id'], ['id'])
    op.drop_index('name', table_name='roles')
    op.create_unique_constraint(op.f('uq_roles_name'), 'roles', ['name'])
    op.add_column('rule_items', sa.Column('alias', sa.String(length=50), nullable=True, comment='别名'))
    op.add_column('rules', sa.Column('scope', sa.Enum('ORDER', 'PRODUCT', name='rulescope'), nullable=True, comment='规则作用范围'))
    op.add_column('rules', sa.Column('order_type', sa.Enum('NONE', 'RESERVATION', 'BUFFET', 'BIZ_DINING', 'ACTIVITY', name='ruleordertype'), nullable=True, comment='订单类型'))
    op.alter_column('rules', 'type',
               existing_type=mysql.ENUM('RULE', 'RESERVATION', 'DINING_RESERVATION', collation='utf8mb4_unicode_ci'),
               nullable=True,
               comment='规则类型',
               existing_server_default=sa.text("'RULE'"))
    op.drop_index('name', table_name='tags')
    op.create_unique_constraint(op.f('uq_tags_name'), 'tags', ['name'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f('uq_tags_name'), 'tags', type_='unique')
    op.create_index('name', 'tags', ['name'], unique=True)
    op.alter_column('rules', 'type',
               existing_type=mysql.ENUM('RULE', 'RESERVATION', 'DINING_RESERVATION', collation='utf8mb4_unicode_ci'),
               nullable=False,
               comment=None,
               existing_comment='规则类型',
               existing_server_default=sa.text("'RULE'"))
    op.drop_column('rules', 'order_type')
    op.drop_column('rules', 'scope')
    op.drop_column('rule_items', 'alias')
    op.drop_constraint(op.f('uq_roles_name'), 'roles', type_='unique')
    op.create_index('name', 'roles', ['name'], unique=True)
    op.drop_constraint(op.f('fk_reservation_requests_orders_id_orders'), 'reservation_requests', type_='foreignkey')
    op.alter_column('reservation_requests', 'reservation_period',
               existing_type=mysql.VARCHAR(collation='utf8mb4_unicode_ci', length=50),
               comment=None,
               existing_comment='预订时段',
               existing_nullable=True)
    op.alter_column('reservation_requests', 'product_id',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=False)
    op.alter_column('reservation_requests', 'order_item_id',
               existing_type=mysql.INTEGER(display_width=11),
               nullable=False)
    op.drop_column('reservation_requests', 'scope')
    op.drop_column('reservation_requests', 'type')
    op.drop_column('reservation_requests', 'orders_id')
    op.drop_constraint(op.f('uq_personal_users_wechat_id'), 'personal_users', type_='unique')
    op.drop_constraint(op.f('uq_personal_users_phone'), 'personal_users', type_='unique')
    op.drop_constraint(op.f('uq_personal_users_id_card'), 'personal_users', type_='unique')
    op.create_index('wechat_id', 'personal_users', ['wechat_id'], unique=True)
    op.create_index('phone', 'personal_users', ['phone'], unique=True)
    op.create_index('id_card', 'personal_users', ['id_card'], unique=True)
    op.drop_constraint(op.f('uq_permissions_name'), 'permissions', type_='unique')
    op.drop_constraint(op.f('uq_permissions_code'), 'permissions', type_='unique')
    op.create_index('name', 'permissions', ['name'], unique=True)
    op.create_index('code', 'permissions', ['code'], unique=True)
    op.drop_constraint(op.f('uq_enterprises_company_name'), 'enterprises', type_='unique')
    op.create_index('company_name', 'enterprises', ['company_name'], unique=True)
    op.alter_column('dining_reservation_rules', 'dining_end_time_cron_str',
               existing_type=mysql.VARCHAR(length=50),
               comment='核销结束时间cron表达式',
               existing_comment='服务结束时间cron表达式',
               existing_nullable=True)
    op.alter_column('dining_reservation_rules', 'dining_start_time_cron_str',
               existing_type=mysql.VARCHAR(length=50),
               comment='核销开始时间cron表达式',
               existing_comment='服务开始时间cron表达式',
               existing_nullable=True)
    op.drop_column('dining_reservation_rules', 'quantity')
    op.drop_column('dining_reservation_rules', 'generated_count')
    op.drop_constraint(op.f('uq_admins_phone'), 'admins', type_='unique')
    op.create_index('phone', 'admins', ['phone'], unique=True)
    op.create_index('ix_admins_phone', 'admins', ['phone'], unique=True)
    op.drop_table('buffet_reservation_requests')
    op.drop_table('biz_reservation_requests')
    # ### end Alembic commands ###
