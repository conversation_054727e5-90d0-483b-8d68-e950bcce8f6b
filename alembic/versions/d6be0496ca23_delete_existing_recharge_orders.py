"""delete_existing_recharge_orders

Revision ID: d6be0496ca23
Revises: 5842f60150b7
Create Date: 2025-04-20 17:15:48.191794

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd6be0496ca23'
down_revision: Union[str, None] = '5842f60150b7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.execute("DELETE FROM orders WHERE type = 'recharge'")


def downgrade() -> None:
    """Downgrade schema."""
    pass
