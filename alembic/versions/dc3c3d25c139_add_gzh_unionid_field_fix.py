"""add_gzh_unionid_field_fix

Revision ID: dc3c3d25c139
Revises: cc7305f00289
Create Date: 2025-06-26 15:07:55.233369

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'dc3c3d25c139'
down_revision: Union[str, None] = 'cc7305f00289'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None



def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('personal_users', sa.Column('gzh_unionid', sa.String(length=64), nullable=True, comment='公众号（服务号）用户UnionID'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('personal_users', 'gzh_unionid')
    # ### end Alembic commands ###
