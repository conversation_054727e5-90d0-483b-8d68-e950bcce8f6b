"""update_reservation_status_enum_to_lowercase

Revision ID: e083699e12b1
Revises: d3985b08a573
Create Date: 2025-05-15 22:28:09.223009

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e083699e12b1'
down_revision: Union[str, None] = 'd3985b08a573'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.execute("ALTER TABLE reservation_requests MODIFY COLUMN status ENUM('PENDING', 'PAID_DEPOSIT', 'PAID_FULL', 'CANCELLED', 'VERIFIED', 'AUTO_VERIFIED') NOT NULL DEFAULT 'PENDING'")

def downgrade() -> None:
    """Downgrade schema."""
    op.execute("ALTER TABLE reservation_requests MODIFY COLUMN status ENUM('PENDING', 'PAID_DEPOSIT', 'PAID_FULL', 'CANCELLED', 'VERIFIED') NOT NULL DEFAULT 'PENDING'")
