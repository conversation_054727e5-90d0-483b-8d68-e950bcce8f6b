"""merge branches

Revision ID: e67d23317d1b
Revises: ad4d7d3f1bb4, ef8eb016288c
Create Date: 2025-06-21 11:04:20.733353

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e67d23317d1b'
down_revision: Union[str, None] = ('ad4d7d3f1bb4', 'ef8eb016288c')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
