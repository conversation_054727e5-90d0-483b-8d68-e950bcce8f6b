"""update account models

Revision ID: e8ae83e6dbd8
Revises: 42506547cd76
Create Date: 2025-05-08 14:25:18.188833

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e8ae83e6dbd8'
down_revision: Union[str, None] = '42506547cd76'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    
    # 创建微信账户表
    op.create_table('wechat_accounts',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(['id'], ['accounts.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 更新 accounts 表中的 type 枚举类型
    op.execute("ALTER TABLE accounts MODIFY COLUMN type VARCHAR(20) DEFAULT 'account'")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    
    # 删除微信账户表
    op.drop_table('wechat_accounts')
    
    # 恢复 accounts 表中的 type 字段
    op.execute("ALTER TABLE accounts MODIFY COLUMN type VARCHAR(20) DEFAULT 'account'")
    # ### end Alembic commands ###
