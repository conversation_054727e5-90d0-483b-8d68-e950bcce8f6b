"""add model messager

Revision ID: ead044cdef14
Revises: 408f3bbbeff2
Create Date: 2025-07-16 10:14:02.917761

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'ead044cdef14'
down_revision: Union[str, None] = '408f3bbbeff2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('message_logs',
    sa.Column('id', sa.Integer(), nullable=False, comment='日志ID'),
    sa.Column('message_id', sa.String(length=100), nullable=True, comment='消息ID'),
    sa.Column('batch_id', sa.String(length=100), nullable=True, comment='批次ID'),
    sa.Column('sender_type', sa.String(length=50), nullable=False, comment='发送器类型'),
    sa.Column('recipient_id', sa.String(length=100), nullable=True, comment='接收者ID'),
    sa.Column('recipient_contact', sa.String(length=200), nullable=True, comment='接收者联系方式'),
    sa.Column('send_status', sa.String(length=20), nullable=False, comment='发送状态'),
    sa.Column('error_code', sa.String(length=50), nullable=True, comment='错误代码'),
    sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('sent_at', sa.DateTime(), nullable=True, comment='发送时间'),
    sa.Column('extra_data', mysql.JSON(), nullable=True, comment='额外数据JSON'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_message_logs')),
    comment='消息发送日志表'
    )
    op.create_index('idx_batch_id', 'message_logs', ['batch_id'], unique=False)
    op.create_index('idx_created_at', 'message_logs', ['created_at'], unique=False)
    op.create_index('idx_message_id', 'message_logs', ['message_id'], unique=False)
    op.create_index('idx_recipient_status', 'message_logs', ['recipient_id', 'send_status'], unique=False)
    op.create_index('idx_sender_type', 'message_logs', ['sender_type'], unique=False)
    op.create_index(op.f('ix_message_logs_batch_id'), 'message_logs', ['batch_id'], unique=False)
    op.create_index(op.f('ix_message_logs_id'), 'message_logs', ['id'], unique=False)
    op.create_index(op.f('ix_message_logs_message_id'), 'message_logs', ['message_id'], unique=False)
    op.create_index(op.f('ix_message_logs_recipient_id'), 'message_logs', ['recipient_id'], unique=False)
    op.create_table('message_subscriptions',
    sa.Column('id', sa.Integer(), nullable=False, comment='订阅ID'),
    sa.Column('user_id', sa.String(length=100), nullable=False, comment='用户ID'),
    sa.Column('message_type', sa.Enum('SYSTEM', 'NOTIFICATION', 'ALERT', 'REMINDER', 'ANNOUNCEMENT', name='messagetype'), nullable=False, comment='消息类型'),
    sa.Column('channel', sa.String(length=50), nullable=False, comment='接收渠道'),
    sa.Column('is_enabled', sa.Boolean(), nullable=True, comment='是否启用'),
    sa.Column('priority_filter', sa.String(length=100), nullable=True, comment='优先级过滤'),
    sa.Column('category_filter', sa.String(length=200), nullable=True, comment='分类过滤'),
    sa.Column('tag_filter', sa.String(length=500), nullable=True, comment='标签过滤'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_message_subscriptions')),
    comment='消息订阅表'
    )
    op.create_index('idx_is_enabled', 'message_subscriptions', ['is_enabled'], unique=False)
    op.create_index('idx_user_channel', 'message_subscriptions', ['user_id', 'channel'], unique=False)
    op.create_index('idx_user_type', 'message_subscriptions', ['user_id', 'message_type'], unique=False)
    op.create_index(op.f('ix_message_subscriptions_id'), 'message_subscriptions', ['id'], unique=False)
    op.create_index(op.f('ix_message_subscriptions_user_id'), 'message_subscriptions', ['user_id'], unique=False)
    op.create_table('message_templates',
    sa.Column('id', sa.Integer(), nullable=False, comment='模板ID'),
    sa.Column('template_code', sa.String(length=100), nullable=False, comment='模板代码'),
    sa.Column('template_name', sa.String(length=200), nullable=False, comment='模板名称'),
    sa.Column('title_template', sa.String(length=500), nullable=True, comment='标题模板'),
    sa.Column('content_template', sa.Text(), nullable=False, comment='内容模板'),
    sa.Column('message_type', sa.Enum('SYSTEM', 'NOTIFICATION', 'ALERT', 'REMINDER', 'ANNOUNCEMENT', name='messagetype'), nullable=True, comment='消息类型'),
    sa.Column('priority', sa.Enum('LOW', 'NORMAL', 'HIGH', 'URGENT', name='messagepriority'), nullable=True, comment='默认优先级'),
    sa.Column('variables', mysql.JSON(), nullable=True, comment='模板变量配置JSON'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否启用'),
    sa.Column('description', sa.Text(), nullable=True, comment='模板描述'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.Column('created_by', sa.String(length=100), nullable=True, comment='创建者'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_message_templates')),
    comment='消息模板表'
    )
    op.create_index('idx_is_active', 'message_templates', ['is_active'], unique=False)
    op.create_index('idx_message_type', 'message_templates', ['message_type'], unique=False)
    op.create_index('idx_template_code', 'message_templates', ['template_code'], unique=False)
    op.create_index(op.f('ix_message_templates_id'), 'message_templates', ['id'], unique=False)
    op.create_index(op.f('ix_message_templates_template_code'), 'message_templates', ['template_code'], unique=True)
    op.create_table('system_messages',
    sa.Column('id', sa.Integer(), nullable=False, comment='消息ID'),
    sa.Column('message_id', sa.String(length=100), nullable=True, comment='消息唯一标识'),
    sa.Column('recipient_id', sa.String(length=100), nullable=False, comment='接收者ID'),
    sa.Column('recipient_name', sa.String(length=100), nullable=True, comment='接收者姓名'),
    sa.Column('recipient_type', sa.String(length=50), nullable=True, comment='接收者类型'),
    sa.Column('title', sa.String(length=200), nullable=True, comment='消息标题'),
    sa.Column('content', sa.Text(), nullable=False, comment='消息内容'),
    sa.Column('message_type', sa.Enum('SYSTEM', 'NOTIFICATION', 'ALERT', 'REMINDER', 'ANNOUNCEMENT', name='messagetype'), nullable=True, comment='消息类型'),
    sa.Column('priority', sa.Enum('LOW', 'NORMAL', 'HIGH', 'URGENT', name='messagepriority'), nullable=True, comment='消息优先级'),
    sa.Column('status', sa.Enum('UNREAD', 'READ', 'ARCHIVED', 'DELETED', name='messagestatus'), nullable=True, comment='消息状态'),
    sa.Column('is_read', sa.Boolean(), nullable=True, comment='是否已读'),
    sa.Column('read_at', sa.DateTime(), nullable=True, comment='阅读时间'),
    sa.Column('sender_id', sa.String(length=100), nullable=True, comment='发送者ID'),
    sa.Column('sender_name', sa.String(length=100), nullable=True, comment='发送者姓名'),
    sa.Column('sender_type', sa.String(length=50), nullable=True, comment='发送者类型'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.Column('sent_at', sa.DateTime(), nullable=True, comment='发送时间'),
    sa.Column('expires_at', sa.DateTime(), nullable=True, comment='过期时间'),
    sa.Column('extra_data', mysql.JSON(), nullable=True, comment='额外数据JSON'),
    sa.Column('tags', sa.String(length=500), nullable=True, comment='消息标签，逗号分隔'),
    sa.Column('category', sa.String(length=100), nullable=True, comment='消息分类'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_system_messages')),
    comment='系统消息表'
    )
    op.create_index('idx_created_at', 'system_messages', ['created_at'], unique=False)
    op.create_index('idx_message_type_priority', 'system_messages', ['message_type', 'priority'], unique=False)
    op.create_index('idx_recipient_created', 'system_messages', ['recipient_id', 'created_at'], unique=False)
    op.create_index('idx_recipient_status', 'system_messages', ['recipient_id', 'status'], unique=False)
    op.create_index('idx_sender_id', 'system_messages', ['sender_id'], unique=False)
    op.create_index(op.f('ix_system_messages_id'), 'system_messages', ['id'], unique=False)
    op.create_index(op.f('ix_system_messages_message_id'), 'system_messages', ['message_id'], unique=False)
    op.create_index(op.f('ix_system_messages_recipient_id'), 'system_messages', ['recipient_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_system_messages_recipient_id'), table_name='system_messages')
    op.drop_index(op.f('ix_system_messages_message_id'), table_name='system_messages')
    op.drop_index(op.f('ix_system_messages_id'), table_name='system_messages')
    op.drop_index('idx_sender_id', table_name='system_messages')
    op.drop_index('idx_recipient_status', table_name='system_messages')
    op.drop_index('idx_recipient_created', table_name='system_messages')
    op.drop_index('idx_message_type_priority', table_name='system_messages')
    op.drop_index('idx_created_at', table_name='system_messages')
    op.drop_table('system_messages')
    op.drop_index(op.f('ix_message_templates_template_code'), table_name='message_templates')
    op.drop_index(op.f('ix_message_templates_id'), table_name='message_templates')
    op.drop_index('idx_template_code', table_name='message_templates')
    op.drop_index('idx_message_type', table_name='message_templates')
    op.drop_index('idx_is_active', table_name='message_templates')
    op.drop_table('message_templates')
    op.drop_index(op.f('ix_message_subscriptions_user_id'), table_name='message_subscriptions')
    op.drop_index(op.f('ix_message_subscriptions_id'), table_name='message_subscriptions')
    op.drop_index('idx_user_type', table_name='message_subscriptions')
    op.drop_index('idx_user_channel', table_name='message_subscriptions')
    op.drop_index('idx_is_enabled', table_name='message_subscriptions')
    op.drop_table('message_subscriptions')
    op.drop_index(op.f('ix_message_logs_recipient_id'), table_name='message_logs')
    op.drop_index(op.f('ix_message_logs_message_id'), table_name='message_logs')
    op.drop_index(op.f('ix_message_logs_id'), table_name='message_logs')
    op.drop_index(op.f('ix_message_logs_batch_id'), table_name='message_logs')
    op.drop_index('idx_sender_type', table_name='message_logs')
    op.drop_index('idx_recipient_status', table_name='message_logs')
    op.drop_index('idx_message_id', table_name='message_logs')
    op.drop_index('idx_created_at', table_name='message_logs')
    op.drop_index('idx_batch_id', table_name='message_logs')
    op.drop_table('message_logs')
    # ### end Alembic commands ###
