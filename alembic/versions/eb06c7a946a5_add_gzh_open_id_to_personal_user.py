"""add_gzh_open_id_to_personal_user

Revision ID: eb06c7a946a5
Revises: 0d99f806b0d7
Create Date: 2025-06-25 16:51:20.312079

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'eb06c7a946a5'
down_revision: Union[str, None] = '0d99f806b0d7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('personal_users', sa.Column('gzh_openid', sa.String(length=64), nullable=True, comment='公众号（服务号）用户ID'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('personal_users', 'gzh_openid')
    # ### end Alembic commands ###
