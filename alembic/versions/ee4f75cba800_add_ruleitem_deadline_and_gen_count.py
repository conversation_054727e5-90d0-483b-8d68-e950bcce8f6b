"""add_ruleitem_deadline_and_gen_count

Revision ID: ee4f75cba800
Revises: a478d49fcd21
Create Date: 2025-05-03 11:41:59.107702

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ee4f75cba800'
down_revision: Union[str, None] = 'a478d49fcd21'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('rule_items', sa.Column('order_deadline', sa.Integer(), nullable=True, comment='订购截止时间，单位：分钟'))
    op.add_column('rule_items', sa.Column('cancellation_deadline', sa.Integer(), nullable=True, comment='取消截止时间，单位：分钟'))
    op.add_column('rule_items', sa.Column('generated_count', sa.Integer(), nullable=True, comment='生成的数量'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('rule_items', 'generated_count')
    op.drop_column('rule_items', 'cancellation_deadline')
    op.drop_column('rule_items', 'order_deadline')
    # ### end Alembic commands ###
