"""init

Revision ID: ee75610b0bb2
Revises: 
Create Date: 2025-04-16 21:49:27.383947

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ee75610b0bb2'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('admins',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False, comment='管理员名称'),
    sa.Column('password', sa.String(length=100), nullable=False, comment='管理员密码'),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='status'), nullable=True, comment='管理员状态'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.Column('last_login_time', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_admins_id'), 'admins', ['id'], unique=False)
    op.create_table('contents',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=200), nullable=True),
    sa.Column('content', sa.Text(), nullable=True),
    sa.Column('image', sa.String(length=200), nullable=True),
    sa.Column('thumbnail', sa.String(length=200), nullable=True),
    sa.Column('sort_order', sa.Integer(), nullable=True),
    sa.Column('type', sa.Enum('CONTENT', 'ARTICLE', name='contenttype'), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_contents_id'), 'contents', ['id'], unique=False)
    op.create_table('files',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('filename', sa.String(length=100), nullable=True),
    sa.Column('file_type', sa.Enum('IMAGE', 'VIDEO', 'AUDIO', 'TEXT', 'PDF', 'DOC', 'DOCX', 'XLS', 'XLSX', 'PPT', 'PPTX', 'ZIP', 'RAR', 'JPEG', 'JPG', 'PNG', 'BMP', 'GIF', 'SVG', 'MP4', 'AVI', 'MOV', 'WAV', 'MP3', 'WMA', 'M4A', name='filetype'), nullable=True),
    sa.Column('file_path', sa.String(length=200), nullable=True),
    sa.Column('file_size', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_files_id'), 'files', ['id'], unique=False)
    op.create_table('permissions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False, comment='权限名称'),
    sa.Column('code', sa.String(length=50), nullable=False, comment='权限代码'),
    sa.Column('permission_type', sa.String(length=20), nullable=False, comment='权限类型'),
    sa.Column('description', sa.String(length=200), nullable=True, comment='权限描述'),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='status'), nullable=False, comment='权限状态'),
    sa.Column('created_at', sa.Date(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_permissions_id'), 'permissions', ['id'], unique=False)
    op.create_table('pricing_strategies',
    sa.Column('id', sa.Integer(), nullable=False, comment='策略ID'),
    sa.Column('name', sa.String(length=100), nullable=False, comment='策略名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='策略描述'),
    sa.Column('start_time', sa.DateTime(), nullable=False, comment='策略生效时间'),
    sa.Column('end_time', sa.DateTime(), nullable=False, comment='策略结束时间'),
    sa.Column('scope', sa.Enum('ORDER', 'PRODUCT', name='pricingstrategyscope'), nullable=False, comment='策略作用范围'),
    sa.Column('is_mutual_exclusive', sa.Boolean(), nullable=False, comment='是否互斥'),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='status'), nullable=False, comment='策略状态'),
    sa.Column('type', sa.Enum('PRICING', 'DISCOUNT', 'FULL_REDUCTION', 'MEMBER_PRICE', 'TIME_LIMITED', name='pricingstrategytype'), nullable=False, comment='策略类型'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_pricing_strategies_id'), 'pricing_strategies', ['id'], unique=False)
    op.create_table('products',
    sa.Column('id', sa.Integer(), nullable=False, comment='商品ID'),
    sa.Column('name', sa.String(length=100), nullable=True, comment='商品名称'),
    sa.Column('price', sa.Float(), nullable=True, comment='基本价格'),
    sa.Column('description', sa.Text(), nullable=True, comment='商品描述'),
    sa.Column('stock', sa.Integer(), nullable=True, comment='库存数量'),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='status'), nullable=True, comment='商品状态'),
    sa.Column('type', sa.Enum('PRODUCT', 'DIRECT_SALE', 'RESERVATION', name='producttype'), nullable=True, comment='产品类型'),
    sa.Column('listed_at', sa.DateTime(), nullable=True, comment='上架时间'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_products_id'), 'products', ['id'], unique=False)
    op.create_table('roles',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False, comment='角色名称'),
    sa.Column('description', sa.String(length=200), nullable=True, comment='角色描述'),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='status'), nullable=False, comment='角色状态'),
    sa.Column('created_at', sa.Date(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_roles_id'), 'roles', ['id'], unique=False)
    op.create_table('rules',
    sa.Column('id', sa.Integer(), nullable=False, comment='规则ID'),
    sa.Column('name', sa.String(length=100), nullable=True, comment='规则名称'),
    sa.Column('status', sa.String(length=20), nullable=True, comment='状态'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_rules_id'), 'rules', ['id'], unique=False)
    op.create_index(op.f('ix_rules_name'), 'rules', ['name'], unique=True)
    op.create_table('tags',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_index(op.f('ix_tags_id'), 'tags', ['id'], unique=False)
    op.create_table('users',
    sa.Column('id', sa.Integer(), nullable=False, comment='用户唯一标识'),
    sa.Column('username', sa.String(length=50), nullable=True, comment='用户名'),
    sa.Column('register_time', sa.DateTime(), nullable=True, comment='注册日期'),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='status'), nullable=True, comment='状态,用户管理员对用户对控制'),
    sa.Column('type', sa.Enum('USER', 'PERSONAL', 'ENTERPRISE', name='usertype'), nullable=False, comment='用户类型'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_table('accounts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('balance', sa.Float(), nullable=True),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='status'), nullable=False, comment='账户状态'),
    sa.Column('type', sa.Enum('ACCOUNT', 'REGULAR', 'GIFT', 'POINTS', 'MEMBER', name='accounttype'), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_accounts_id'), 'accounts', ['id'], unique=False)
    op.create_table('admin_role_relations',
    sa.Column('admin_id', sa.Integer(), nullable=True),
    sa.Column('role_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['admin_id'], ['admins.id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.UniqueConstraint('admin_id', 'role_id', name='uq_admin_role')
    )
    op.create_table('content_file_relations',
    sa.Column('content_id', sa.Integer(), nullable=True),
    sa.Column('file_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['content_id'], ['contents.id'], ),
    sa.ForeignKeyConstraint(['file_id'], ['files.id'], ),
    sa.UniqueConstraint('content_id', 'file_id', name='uq_content_file')
    )
    op.create_table('direct_sale_products',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('shipping_fee', sa.Float(), nullable=True, comment='运费'),
    sa.ForeignKeyConstraint(['id'], ['products.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('discount_strategies',
    sa.Column('id', sa.Integer(), nullable=False, comment='关联的基础策略ID'),
    sa.Column('discount_rate', sa.Float(), nullable=False, comment='折扣比例（0-1之间的小数）'),
    sa.Column('min_amount', sa.Float(), nullable=False, comment='最低消费金额'),
    sa.Column('max_discount', sa.Float(), nullable=False, comment='最大优惠金额'),
    sa.ForeignKeyConstraint(['id'], ['pricing_strategies.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('enterprises',
    sa.Column('id', sa.Integer(), nullable=False, comment='关联的基础用户ID'),
    sa.Column('company_name', sa.String(length=100), nullable=False, comment='企业名称'),
    sa.Column('business_license', sa.String(length=100), nullable=False, comment='营业执照号'),
    sa.Column('phone', sa.String(length=20), nullable=True, comment='联系电话'),
    sa.Column('email', sa.String(length=100), nullable=True, comment='企业邮箱'),
    sa.Column('address', sa.String(length=200), nullable=True, comment='企业地址'),
    sa.ForeignKeyConstraint(['id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('business_license')
    )
    op.create_table('full_reduction_strategies',
    sa.Column('id', sa.Integer(), nullable=False, comment='关联的基础策略ID'),
    sa.Column('full_amount', sa.Float(), nullable=False, comment='满足金额'),
    sa.Column('reduction_amount', sa.Float(), nullable=False, comment='减免金额'),
    sa.ForeignKeyConstraint(['id'], ['pricing_strategies.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('member_price_strategies',
    sa.Column('id', sa.Integer(), nullable=False, comment='关联的基础策略ID'),
    sa.Column('member_level', sa.Enum('NONE', 'BASIC', 'PREMIUM', 'VIP', name='memberlevel'), nullable=False, comment='会员等级'),
    sa.Column('price', sa.Float(), nullable=False, comment='会员价格'),
    sa.ForeignKeyConstraint(['id'], ['pricing_strategies.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('orders',
    sa.Column('id', sa.Integer(), nullable=False, comment='订单唯一标识'),
    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
    sa.Column('status', sa.Enum('PENDING', 'PAID', 'SHIPPED', 'DELIVERED', 'VERIFIED', 'COMPLETED', 'CANCELLED', 'REFUNDED', name='orderstatus'), nullable=False, comment='订单状态'),
    sa.Column('payment_status', sa.Enum('UNPAID', 'PAID', 'REFUNDED', name='paymentstatus'), nullable=False, comment='支付状态'),
    sa.Column('payment_time', sa.DateTime(), nullable=True, comment='支付时间'),
    sa.Column('total_amount', sa.Float(), nullable=False, comment='订单总金额'),
    sa.Column('actual_amount_paid', sa.Float(), nullable=False, comment='实际支付金额'),
    sa.Column('payment_method', sa.Enum('ACCOUNT_BALANCE', 'CASH', 'ALIPAY', 'WECHAT_PAY', 'BANK_TRANSFER', name='paymentmethod'), nullable=False, comment='支付方式'),
    sa.Column('type', sa.Enum('ORDER', 'DIRECT_SALE', 'RESERVATION', name='ordertype'), nullable=False, comment='订单类型'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_orders_id'), 'orders', ['id'], unique=False)
    op.create_table('personal_users',
    sa.Column('id', sa.Integer(), nullable=False, comment='关联的基础用户ID'),
    sa.Column('phone', sa.String(length=20), nullable=False, comment='手机号'),
    sa.Column('wechat_id', sa.String(length=50), nullable=True, comment='微信ID'),
    sa.Column('password', sa.String(length=100), nullable=True, comment='密码'),
    sa.Column('email', sa.String(length=100), nullable=True, comment='电子邮箱'),
    sa.Column('address', sa.String(length=200), nullable=True, comment='地址'),
    sa.Column('real_name', sa.String(length=50), nullable=True, comment='真实姓名'),
    sa.Column('id_card', sa.String(length=18), nullable=True, comment='身份证号'),
    sa.ForeignKeyConstraint(['id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('id_card'),
    sa.UniqueConstraint('phone'),
    sa.UniqueConstraint('wechat_id')
    )
    op.create_table('product_content_relations',
    sa.Column('product_id', sa.Integer(), nullable=True),
    sa.Column('content_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['content_id'], ['contents.id'], ),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ),
    sa.UniqueConstraint('product_id', 'content_id', name='uq_product_content')
    )
    op.create_table('product_pricing_strategy_relations',
    sa.Column('product_id', sa.Integer(), nullable=True),
    sa.Column('pricing_strategy_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['pricing_strategy_id'], ['pricing_strategies.id'], ),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ),
    sa.UniqueConstraint('product_id', 'pricing_strategy_id', name='uq_product_pricing_strategy')
    )
    op.create_table('product_rule_relations',
    sa.Column('product_id', sa.Integer(), nullable=True),
    sa.Column('rule_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ),
    sa.ForeignKeyConstraint(['rule_id'], ['rules.id'], ),
    sa.UniqueConstraint('product_id', 'rule_id', name='uq_product_rule')
    )
    op.create_table('product_tag_relations',
    sa.Column('product_id', sa.Integer(), nullable=True),
    sa.Column('tag_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ),
    sa.ForeignKeyConstraint(['tag_id'], ['tags.id'], ),
    sa.UniqueConstraint('product_id', 'tag_id', name='uq_product_tag')
    )
    op.create_table('reservation_products',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('reservation_fee', sa.Float(), nullable=True),
    sa.Column('max_reservations', sa.Integer(), nullable=True),
    sa.Column('reservation_deadline', sa.DateTime(), nullable=True),
    sa.Column('cancellation_deadline', sa.DateTime(), nullable=True),
    sa.Column('is_approval_required', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['id'], ['products.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('role_permission_relations',
    sa.Column('role_id', sa.Integer(), nullable=True),
    sa.Column('permission_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.UniqueConstraint('role_id', 'permission_id', name='uq_role_permission')
    )
    op.create_table('rule_items',
    sa.Column('id', sa.Integer(), nullable=False, comment='规则项ID'),
    sa.Column('rule_id', sa.Integer(), nullable=True, comment='规则ID'),
    sa.Column('name', sa.String(length=100), nullable=True, comment='规则项名称'),
    sa.Column('start_time', sa.DateTime(), nullable=True, comment='开始时间'),
    sa.Column('end_time', sa.DateTime(), nullable=True, comment='结束时间'),
    sa.Column('quantity', sa.Integer(), nullable=True, comment='数量'),
    sa.Column('cron', sa.String(length=50), nullable=True, comment='cron表达式'),
    sa.Column('allowed_operations', sa.String(length=100), nullable=True, comment='允许的操作'),
    sa.Column('forbidden_operations', sa.String(length=100), nullable=True, comment='禁止的操作'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['rule_id'], ['rules.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_rule_items_id'), 'rule_items', ['id'], unique=False)
    op.create_table('time_limited_strategies',
    sa.Column('id', sa.Integer(), nullable=False, comment='关联的基础策略ID'),
    sa.Column('price', sa.Float(), nullable=False, comment='特价金额'),
    sa.Column('stock_limit', sa.Integer(), nullable=False, comment='库存限制'),
    sa.ForeignKeyConstraint(['id'], ['pricing_strategies.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('account_transactions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('order_id', sa.Integer(), nullable=True),
    sa.Column('transaction_type', sa.Enum('NONE', 'PAYMENT', 'DEPOSIT', 'WITHDRAW', 'TRANSFER', 'CONSUME', 'REFUND', name='transactiontype'), nullable=False, comment='交易类型'),
    sa.Column('amount', sa.Float(), nullable=False),
    sa.Column('transaction_time', sa.DateTime(), nullable=False),
    sa.Column('description', sa.String(length=200), nullable=True),
    sa.ForeignKeyConstraint(['account_id'], ['accounts.id'], ),
    sa.ForeignKeyConstraint(['order_id'], ['orders.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_account_transactions_id'), 'account_transactions', ['id'], unique=False)
    op.create_table('enterprise_user_relations',
    sa.Column('id', sa.Integer(), nullable=False, comment='关联关系唯一标识'),
    sa.Column('enterprise_id', sa.Integer(), nullable=False, comment='企业用户ID'),
    sa.Column('personal_user_id', sa.Integer(), nullable=False, comment='个人用户ID'),
    sa.Column('password', sa.String(length=100), nullable=True, comment='密码'),
    sa.Column('is_admin', sa.Boolean(), nullable=True, comment='是否为企业管理员'),
    sa.Column('remark', sa.String(length=200), nullable=True, comment='备注信息'),
    sa.Column('relation_status', sa.Enum('ACTIVE', 'INACTIVE', name='status'), nullable=True, comment='状态,用企业对用户对控制'),
    sa.Column('created_at', sa.Date(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['enterprise_id'], ['enterprises.id'], ),
    sa.ForeignKeyConstraint(['personal_user_id'], ['personal_users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('enterprise_id', 'personal_user_id', name='uq_enterprise_personal')
    )
    op.create_index(op.f('ix_enterprise_user_relations_id'), 'enterprise_user_relations', ['id'], unique=False)
    op.create_table('gift_accounts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('gift_amount', sa.Float(), nullable=True),
    sa.ForeignKeyConstraint(['id'], ['accounts.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('member_accounts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('member_level', sa.String(length=20), nullable=True),
    sa.Column('member_points', sa.Integer(), nullable=True),
    sa.Column('valid_until', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['id'], ['accounts.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('order_items',
    sa.Column('id', sa.Integer(), nullable=False, comment='订单项唯一标识'),
    sa.Column('order_id', sa.Integer(), nullable=False, comment='订单ID'),
    sa.Column('product_id', sa.Integer(), nullable=False, comment='商品ID'),
    sa.Column('quantity', sa.Integer(), nullable=False, comment='购买数量'),
    sa.Column('price', sa.Float(), nullable=False, comment='商品单价'),
    sa.Column('subtotal', sa.Float(), nullable=False, comment='小计金额'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['order_id'], ['orders.id'], ),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_order_items_id'), 'order_items', ['id'], unique=False)
    op.create_table('points_accounts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('points_total', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['id'], ['accounts.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('regular_accounts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['id'], ['accounts.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('reservation_requests',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('order_item_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=False),
    sa.Column('rule_id', sa.Integer(), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'PAID_DEPOSIT', 'PAID_FULL', 'CANCELLED', name='reservationstatus'), nullable=False),
    sa.Column('reservation_period', sa.String(length=50), nullable=True),
    sa.Column('reservation_time', sa.DateTime(), nullable=True),
    sa.Column('verification_code', sa.String(length=128), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['order_item_id'], ['order_items.id'], ),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ),
    sa.ForeignKeyConstraint(['rule_id'], ['rules.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_reservation_requests_id'), 'reservation_requests', ['id'], unique=False)
    op.create_table('approvals',
    sa.Column('id', sa.Integer(), nullable=False, comment='审批请求唯一标识'),
    sa.Column('reservation_request_id', sa.Integer(), nullable=False, comment='预订请求ID'),
    sa.Column('applicant_id', sa.Integer(), nullable=False, comment='申请人ID'),
    sa.Column('enterprise_id', sa.Integer(), nullable=False, comment='企业ID'),
    sa.Column('status', sa.Enum('PENDING', 'APPROVED', 'REJECTED', name='approvalstatus'), nullable=False, comment='审批状态'),
    sa.Column('comment', sa.Text(), nullable=True, comment='审批意见'),
    sa.Column('approver_id', sa.Integer(), nullable=True, comment='审批人ID'),
    sa.Column('approval_time', sa.DateTime(), nullable=True, comment='审批时间'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['applicant_id'], ['enterprise_user_relations.id'], ),
    sa.ForeignKeyConstraint(['approver_id'], ['enterprise_user_relations.id'], ),
    sa.ForeignKeyConstraint(['enterprise_id'], ['enterprises.id'], ),
    sa.ForeignKeyConstraint(['reservation_request_id'], ['reservation_requests.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_approvals_id'), 'approvals', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_approvals_id'), table_name='approvals')
    op.drop_table('approvals')
    op.drop_index(op.f('ix_reservation_requests_id'), table_name='reservation_requests')
    op.drop_table('reservation_requests')
    op.drop_table('regular_accounts')
    op.drop_table('points_accounts')
    op.drop_index(op.f('ix_order_items_id'), table_name='order_items')
    op.drop_table('order_items')
    op.drop_table('member_accounts')
    op.drop_table('gift_accounts')
    op.drop_index(op.f('ix_enterprise_user_relations_id'), table_name='enterprise_user_relations')
    op.drop_table('enterprise_user_relations')
    op.drop_index(op.f('ix_account_transactions_id'), table_name='account_transactions')
    op.drop_table('account_transactions')
    op.drop_table('time_limited_strategies')
    op.drop_index(op.f('ix_rule_items_id'), table_name='rule_items')
    op.drop_table('rule_items')
    op.drop_table('role_permission_relations')
    op.drop_table('reservation_products')
    op.drop_table('product_tag_relations')
    op.drop_table('product_rule_relations')
    op.drop_table('product_pricing_strategy_relations')
    op.drop_table('product_content_relations')
    op.drop_table('personal_users')
    op.drop_index(op.f('ix_orders_id'), table_name='orders')
    op.drop_table('orders')
    op.drop_table('member_price_strategies')
    op.drop_table('full_reduction_strategies')
    op.drop_table('enterprises')
    op.drop_table('discount_strategies')
    op.drop_table('direct_sale_products')
    op.drop_table('content_file_relations')
    op.drop_table('admin_role_relations')
    op.drop_index(op.f('ix_accounts_id'), table_name='accounts')
    op.drop_table('accounts')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_tags_id'), table_name='tags')
    op.drop_table('tags')
    op.drop_index(op.f('ix_rules_name'), table_name='rules')
    op.drop_index(op.f('ix_rules_id'), table_name='rules')
    op.drop_table('rules')
    op.drop_index(op.f('ix_roles_id'), table_name='roles')
    op.drop_table('roles')
    op.drop_index(op.f('ix_products_id'), table_name='products')
    op.drop_table('products')
    op.drop_index(op.f('ix_pricing_strategies_id'), table_name='pricing_strategies')
    op.drop_table('pricing_strategies')
    op.drop_index(op.f('ix_permissions_id'), table_name='permissions')
    op.drop_table('permissions')
    op.drop_index(op.f('ix_files_id'), table_name='files')
    op.drop_table('files')
    op.drop_index(op.f('ix_contents_id'), table_name='contents')
    op.drop_table('contents')
    op.drop_index(op.f('ix_admins_id'), table_name='admins')
    op.drop_table('admins')
    # ### end Alembic commands ###
