"""update_pricing_strategy_type

Revision ID: ef8eb016288c
Revises: 249c98e18089
Create Date: 2025-06-16 13:13:49.129106

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ef8eb016288c'
down_revision: Union[str, None] = '249c98e18089'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.execute("ALTER TABLE pricing_strategies MODIFY COLUMN type ENUM('PRICING', 'DISCOUNT', 'FULL_REDUCTION', 'MEMBER_PRICE', 'TIME_LIMITED', 'BUNDLE') NOT NULL DEFAULT 'PRICING'")

def downgrade() -> None:
    """Downgrade schema."""
    op.execute("ALTER TABLE pricing_strategies MODIFY COLUMN type ENUM('PRICING', 'DISCOUNT', 'FULL_REDUCTION', 'MEMBER_PRICE', 'TIME_LIMITED') NOT NULL DEFAULT 'PRICING'")
