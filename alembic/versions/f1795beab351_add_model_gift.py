"""add_model_gift

Revision ID: f1795beab351
Revises: d45eb72c88a2
Create Date: 2025-06-13 18:01:30.861322

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f1795beab351'
down_revision: Union[str, None] = 'd45eb72c88a2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('gift_rules',
    sa.Column('id', sa.Integer(), nullable=False, comment='赠送ID'),
    sa.Column('name', sa.String(length=100), nullable=False, comment='赠送名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='赠送描述'),
    sa.Column('start_time', sa.DateTime(), nullable=True, comment='赠送生效时间'),
    sa.Column('end_time', sa.DateTime(), nullable=True, comment='赠送结束时间'),
    sa.Column('is_mutual_exclusive', sa.Boolean(), nullable=False, comment='是否互斥'),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='status'), nullable=False, comment='赠送状态'),
    sa.Column('type', sa.Enum('GIFT', 'ORDER_GIFT', name='giftruletype'), nullable=False, comment='赠送规则类型'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_gift_rules'))
    )
    op.create_index(op.f('ix_gift_rules_id'), 'gift_rules', ['id'], unique=False)
    op.create_table('order_gift_rules',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('order_type', sa.Enum('ORDER', 'DIRECT_SALE', 'RESERVATION', 'RECHARGE', name='ordertype'), nullable=False, comment='订单类型'),
    sa.Column('order_start_time', sa.DateTime(), nullable=True, comment='订单生效时间'),
    sa.Column('order_end_time', sa.DateTime(), nullable=True, comment='订单结束时间'),
    sa.Column('order_amount', sa.Float(), nullable=False, comment='订单金额'),
    sa.Column('gift_amount', sa.Float(), nullable=False, comment='赠送金额'),
    sa.ForeignKeyConstraint(['id'], ['gift_rules.id'], name=op.f('fk_order_gift_rules_id_gift_rules')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_order_gift_rules'))
    )
    op.create_table('order_gift_rule_gift_product_rel',
    sa.Column('id', sa.Integer(), nullable=False, comment='条件ID'),
    sa.Column('gift_rule_result_id', sa.Integer(), nullable=False, comment='赠送规则ID'),
    sa.Column('gift_product_id', sa.Integer(), nullable=False, comment='商品ID'),
    sa.Column('quantity', sa.Integer(), nullable=False, comment='数量'),
    sa.ForeignKeyConstraint(['gift_product_id'], ['products.id'], name=op.f('fk_order_gift_rule_gift_product_rel_gift_product_id_products')),
    sa.ForeignKeyConstraint(['gift_rule_result_id'], ['order_gift_rules.id'], name=op.f('fk_order_gift_rule_gift_product_rel_gift_rule_result_id_order_gift_rules')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_order_gift_rule_gift_product_rel'))
    )
    op.create_index(op.f('ix_order_gift_rule_gift_product_rel_id'), 'order_gift_rule_gift_product_rel', ['id'], unique=False)
    op.create_table('order_gift_rule_order_product_rel',
    sa.Column('id', sa.Integer(), nullable=False, comment='条件ID'),
    sa.Column('gift_rule_condition_id', sa.Integer(), nullable=False, comment='赠送规则ID'),
    sa.Column('order_product_id', sa.Integer(), nullable=False, comment='商品ID'),
    sa.Column('quantity', sa.Integer(), nullable=False, comment='数量'),
    sa.ForeignKeyConstraint(['gift_rule_condition_id'], ['order_gift_rules.id'], name=op.f('fk_order_gift_rule_order_product_rel_gift_rule_condition_id_order_gift_rules')),
    sa.ForeignKeyConstraint(['order_product_id'], ['products.id'], name=op.f('fk_order_gift_rule_order_product_rel_order_product_id_products')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_order_gift_rule_order_product_rel'))
    )
    op.create_index(op.f('ix_order_gift_rule_order_product_rel_id'), 'order_gift_rule_order_product_rel', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_order_gift_rule_order_product_rel_id'), table_name='order_gift_rule_order_product_rel')
    op.drop_table('order_gift_rule_order_product_rel')
    op.drop_index(op.f('ix_order_gift_rule_gift_product_rel_id'), table_name='order_gift_rule_gift_product_rel')
    op.drop_table('order_gift_rule_gift_product_rel')
    op.drop_table('order_gift_rules')
    op.drop_index(op.f('ix_gift_rules_id'), table_name='gift_rules')
    op.drop_table('gift_rules')
    # ### end Alembic commands ###
