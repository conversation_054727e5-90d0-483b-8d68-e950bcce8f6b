"""merge_branch

Revision ID: f51478a31176
Revises: 34137b9c8f7b, ad4d7d3f1bb4
Create Date: 2025-06-21 21:46:35.153802

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f51478a31176'
down_revision: Union[str, None] = ('34137b9c8f7b', 'ad4d7d3f1bb4')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
