"""merge_branches_and_fix_migrations

Revision ID: f5962408383f
Revises: 5f4ce14759d8, c0d46fc7ba3d
Create Date: 2025-04-25 15:42:58.742606

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f5962408383f'
down_revision: Union[str, None] = ('5f4ce14759d8', 'c0d46fc7ba3d')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
