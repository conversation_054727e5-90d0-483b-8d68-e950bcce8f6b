"""add discount_amount field to coupon_usage_records table

Revision ID: f9a8b2c3d4e5
Revises: 346ae19bc14f
Create Date: 2025-08-31 18:30:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'f9a8b2c3d4e5'
down_revision: Union[str, None] = '346ae19bc14f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('coupon_usage_records', sa.Column('discount_amount', sa.Float(), nullable=False, server_default='0.0', comment='优惠金额'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('coupon_usage_records', 'discount_amount')
    # ### end Alembic commands ###
