import { loginRequest } from "./service/index"
import { getCode } from "./service/login"
import { getCurrentEnvConfig } from "./config/env"
import { showCurrentEnvInfo } from "./utils/env-helper"

// app.js
App({
  globalData: {
    userInfo: null,
    ...getCurrentEnvConfig(), // 根据环境自动设置 baseUrlHost 和 baseUrl
    isPhoneAuthorized: false,  // 添加手机号授权状态标志
    isLoggedIn: false,         // 添加登录状态标志
    lastAuthTime: 0,           // 添加最后验证时间
    authInProgress: false      // 添加正在验证标志，避免重复验证
  },
  
  async onLaunch() {
    try {
      // 显示当前环境信息
      showCurrentEnvInfo()

      //获取本地token
      const token = wx.getStorageSync('token') || ''

      if (!token) {
        // 没有 token，尝试重新登录
        await this.monitor_token()
        return
      }

      //检查token是否过期
      const res = await loginRequest.post({
        url: '/auth',
        header: {
          token
        }
      })
      
      // 登录
      if (res.message === "已登录") {
        console.log('token 有效，获取用户信息')
        if (res.userInfo) {
          this.globalData.userInfo = res.userInfo
          this.globalData.isPhoneAuthorized = true
          this.globalData.isLoggedIn = true
          this.globalData.lastAuthTime = Date.now()
          console.log('已保存用户信息到全局', this.globalData.userInfo)
        }
      } else {
        console.log('token 无效，重新登录')
        await this.monitor_token()
      }
    } catch (error) {
      console.error('应用启动时验证 token 失败', error)
      // 出错也尝试重新登录
      this.monitor_token().catch(err => {
        console.error('重新登录失败', err)
      })
    }
  },

  async monitor_token() {
    return new Promise(async (resolve, reject) => {
      try {
        //获取code
        const code = await getCode()
        const phone_code = await getCode()
        const type = 0

        //将code和手机号code一起发给后端
        const res = await loginRequest.post({
          url: "/monitor-token",
          data: { 
            code,
            phone_code,
            type
          }
        })

        if (res.status == 200) {
          console.log('已注册，进行登录')
          await this.handlerLogin(phone_code, 0)
          resolve()
        } else {
          console.log('未注册用户')
          // 设置为未登录状态，但不强制跳转
          this.globalData.isPhoneAuthorized = false
          this.globalData.isLoggedIn = false
          resolve()
        }
      } catch (error) {
        console.error('monitor_token 失败', error)
        this.globalData.isPhoneAuthorized = false
        this.globalData.isLoggedIn = false
        resolve()
      }
    })
  },
  
  // 修改登录函数，增加手机号参数
  async handlerLogin(phone_code, type) {
    //获取code
    const code = await getCode()

    //将code和手机号code一起发给后端
    const res = await loginRequest.post({
      url: "/login",
      data: { 
        code,
        phone_code,
        type
      }
    })

    //保存token
    wx.setStorageSync('token', res.token)
    
    // 如果返回了用户信息并且有头像和昵称，则保存到全局
    if (res.userInfo && (res.userInfo.avatarUrl || res.userInfo.nickname)) {
      this.globalData.userInfo = res.userInfo;
      this.globalData.isPhoneAuthorized = true;
      this.globalData.isLoggedIn = true;
      this.globalData.lastAuthTime = Date.now();
      console.log('已保存用户信息到全局', this.globalData.userInfo);
    }
  }
})