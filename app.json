{"pages": ["pages/index/index", "pages/reserve/reserve", "pages/index/article/article", "pages/topic/topic", "pages/user/user", "pages/user/details", "pages/user/about", "pages/user/feedback", "pages/user/record", "pages/booking/booking", "pages/booking_employee/booking_employee", "pages/menu/menu", "pages/booking_business/booking_business", "pages/phoneAuth/phoneAuth", "pages/booking_business_edit/booking_business_edit", "pages/booking_business_pay/booking_business_pay", "pages/scan_redirect/scan_redirect", "pages/interactive_ordering/interactive_ordering", "pages/enterprise/user/user", "pages/enterprise/account/account", "pages/enterprise/statistic/statistic", "pages/admin/statistic/statistic", "pages/admin/scan/scan"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#fff", "navigationBarTitleText": "WeChat", "navigationBarTextStyle": "black"}, "tabBar": {"color": "#9BABBA", "selectedColor": "#495056", "borderStyle": "white", "backgroundColor": "#ffffff", "list": [{"pagePath": "pages/index/index", "iconPath": "images/tar-home.png", "selectedIconPath": "images/tar-home-on.png", "text": "首页"}, {"pagePath": "pages/topic/topic", "iconPath": "images/tar-topic.png", "selectedIconPath": "images/tar-topic-on.png", "text": "我要预约"}, {"pagePath": "pages/reserve/reserve", "iconPath": "images/tar-li.png", "selectedIconPath": "images/tar-li-on.png", "text": "订单记录"}, {"pagePath": "pages/user/user", "iconPath": "images/tar-person.png", "selectedIconPath": "images/tar-person-on.png", "text": "我的信息"}]}, "networkTimeout": {"request": 10000, "connectSocket": 10000, "uploadFile": 10000, "downloadFile": 10000}, "navigateToMiniProgramAppIdList": ["wxc58a034f610866c5"], "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents"}