@import "vendor/ZanUI/index.wxss";

.container {
  padding: 0 24rpx;
  background-color: #fff;
  font-family: Microsoft YaHei, Helvetica, Arial, sans-serif;
}

.header {
  padding: 50rpx 0;
  text-align: center;
  font-weight: normal;
  font-size: 30rpx;
  line-height: 40rpx;
  color: #757575;
  border-bottom: 2rpx solid #ededed;
  margin-bottom: 8rpx;
}


 .wxParse-p
{
  margin-top: 30rpx;
} 


/*  more   */

.loadingmore {
  margin-top: 24rpx;
  text-align: center;
  margin-bottom: 24rpx;
}

.more-button {
  font-size: 0.785714286rem;
  font-weight: normal;
  color: #959595;
  background-color: #eee;
  background-repeat: repeat-x;
  margin-top: 30rpx;
  width: 240rpx;
  border-radius: 300rpx;
}

.more-button::after {
  border:none;
}

.no-more {
  color: #757575;
  font-size: 30rpx;
  line-height: 1.8rem;
  margin-bottom: 15rpx;
  text-align: center;
  margin-top: 15rpx;
}

/*  more  end  */

/*common list  for  index  list page */

.common-list {
  margin-bottom: 24rpx;
}

.common-list .list-item {
  box-shadow:2rpx 4rpx 40rpx #c2c2c2;
  border-radius: 8px;
  position: relative;
  padding: 20rpx;
  min-height: 150rpx;
  overflow: hidden;
  margin-bottom: 15px;
}

.common-list .list-item.has-img image.cover {
  display: block;
  width: 100%;
  height: 300rpx;
  border-radius: 8rpx;
}

.common-list .list-item.has-img .content-title {
  text-shadow: 0 0 15px #3a4040;
  z-index: 2;
  position: relative;
  top: -30px;
}

.common-list .list-item.has-img .content-title text {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  font-size: 30rpx;
  line-height: 1.4;
  font-weight: 400;
  color: #fff;
}

.common-list .list-item .content-date .cate{
  font-weight: bold;
  color: #676767;
  font-size: 30rpx;
}
.common-list .list-item.has-img .content-date image {
  width: 24rpx;
  height: 24rpx;
  margin-right: 10rpx;
  vertical-align: middle;
}

.common-list .list-item .content-date text {
  color: #959595;
  margin-right: 32rpx;
  font-size: 20rpx;
  line-height: 1.2;
  font-weight: normal;
}
.common-list .content-desc{
  position: relative;
  top: -10px;
}

.common-list .content-desc text{
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  font-size: 30rpx;
  line-height: 1.4;
  font-weight: 400;
  color: #969696;
}

/*common list end */

.showerror {
  text-align: center;
  font-weight: normal;
  font-size: 30rpx;
  line-height: 40rpx;
  color: #757575;
  margin-top: 100rpx;
}

.errortext {
  margin-top: 50rpx;
  table-layout: center;
}

.copyright {
  font-size: 26rpx;
  line-height: 1.6;
  font-weight: 400;
  text-align: center;
  color: #c4c4c4;
  margin-top: 48rpx;
  padding-bottom: 80rpx;
}

.common-gap {
  width: 100%;
  height: 24rpx;
  background-color: #f5f7f7;
}


/* 进度条 */
.sk-three-bounce {
  margin: 20rpx auto;
  width: 200rpx;
  text-align: center;
}

.sk-three-bounce .sk-child {
  width: 30rpx;
  height: 30rpx;
  background-color: #118fff;
  border-radius: 100%;
  display: inline-block;
  -webkit-animation: sk-three-bounce 1.4s ease-in-out 0s infinite both;
  animation: sk-three-bounce 1.4s ease-in-out 0s infinite both;
}

.sk-three-bounce .sk-bounce1 {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.sk-three-bounce .sk-bounce2 {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

@-webkit-keyframes sk-three-bounce {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}



@keyframes sk-three-bounce {
  0%, 80%, 100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }

  40% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

/* 进度条 */


/* 登录框 */

.login-popup .zan-popup__container {
    background-color: transparent;
}

.login-popup-wrapper {
    margin: 0 25px;
    
}

.login-popup button {
    background-color: transparent;
    padding: 0;
}

.login-popup .login-inner {
    background-color: #fff;
    padding: 10px 15px;
    border-radius:5px;
}

.login-inner {
    display: flex;
}

.login-inner .avatar {
    width: 50px;
    height: 50px;
    margin-right: 10px;
    flex-shrink: 0;
}

.login-inner .close-btn {
    width: 20px;
    height: 20px;
}

.login-inner .username {
    display: flex;
    align-items: center;
}

.login-inner .username text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size:32rpx;

}

/* 弹窗按钮 */

.popup-btn {
    display: flex;
    width: 100%;
    height: 35px;
    line-height: 35px;
    font-size: 14px;
    text-align: left;
    border: 0;
    border-radius: 0;
    background-color: #fff;
}

.popup-btn .button-main {
    flex-grow: 1;
    overflow: hidden;
}

.popup-btn .go-popup {
    display: flex;
    align-items: center;
}

.popup-btn .go-popup image {
    width: 15px;
    height: 15px;
}

/* 弹窗 */

.login-popup .zan-popup__mask {
    z-index: 11;
}

.login-popup .zan-popup__container {
    z-index: 12;
}

.zan-popup .zan-popup__container {
    width: 100%;
    overflow: visible;
  

}

.zan-popup .popup-wrapper {
    width: 100%;
    padding: 0 15px;
    padding-bottom: 10px;
    overflow: visible;
    
}

.zan-popup .popup-wrapper .popup-title {
    font-size: 16px;
    padding: 15px 0;
}

.zan-popup .btn-primary.confirm {
    height: 50px;
    line-height: 50px;
    border-radius: 0;
}