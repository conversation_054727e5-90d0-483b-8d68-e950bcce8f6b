from fastapi import APIRouter, Depends

from app.api.v1 import (
    admin,
    user,
    product,
    pricing,
    order,
    account,
    tag,
    content,
    file,
    approval,
    auth,
    rule,
    wechat_mini_app,
    payment,
    report,
    menu,
    gift,
    category
)
from app.api.v1.coupon import router as coupon_router
from app.api.v1.reservation import (
    reservation_router,
    buffet_reservation_router,
    biz_reservation_router
)
from app.core.deps import get_current_admin, get_api_auth_deps, get_api_auth_deps_with_rbac

api_router = APIRouter()

# 认证路由 - 不需要认证
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])

# 所有其他API路由都需要RBAC权限检查
admin_auth = get_api_auth_deps_with_rbac()

# 管理路由
api_router.include_router(
    admin.router, 
    prefix="/admin", 
    tags=["管理模块"],
    dependencies=admin_auth
)

# 用户路由
api_router.include_router(
    user.router, 
    prefix="/user", 
    tags=["用户模块"],
    dependencies=admin_auth
)

api_router.include_router(
    category.router,
    prefix="/category",
    tags=["商品分类"],
    dependencies=admin_auth
)

# 产品路由
api_router.include_router(
    product.router, 
    prefix="/product", 
    tags=["商品模块"],
    dependencies=admin_auth
)

# 内容路由
api_router.include_router(
    content.router, 
    prefix="/content", 
    tags=["内容模块"],
    dependencies=admin_auth
)

# 文件路由
api_router.include_router(
    file.router, 
    prefix="/file", 
    tags=["文件模块"],
    dependencies=admin_auth
)

# 价格路由
api_router.include_router(
    pricing.router, 
    prefix="/pricing", 
    tags=["价格模块"],
    dependencies=admin_auth
)

# 规则路由
api_router.include_router(
    rule.router, 
    prefix="/rule", 
    tags=["规则模块"],
    dependencies=admin_auth
)

# 菜单路由
api_router.include_router(
    menu.router,
    prefix="/menu",
    tags=["菜单模块"],
    dependencies=admin_auth
)

# 标签路由
api_router.include_router(
    tag.router, 
    prefix="/tag", 
    tags=["标签模块"],
    dependencies=admin_auth
)

# 订单路由
api_router.include_router(
    order.router, 
    prefix="/order", 
    tags=["订单模块"],
    dependencies=admin_auth
)

# 账户路由
api_router.include_router(
    account.router, 
    prefix="/account", 
    tags=["账户模块"],
    dependencies=admin_auth
)

# 预订相关路由
api_router.include_router(
    reservation_router,
    prefix="/reservation", 
    tags=["基础预订模块"],
    dependencies=admin_auth
)

api_router.include_router(
    buffet_reservation_router,
    prefix="/buffet-reservation",
    tags=["自助餐预订模块"],
    dependencies=admin_auth
)

api_router.include_router(
    biz_reservation_router,
    prefix="/biz-reservation",
    tags=["商务餐预订模块"],
    dependencies=admin_auth
)

# 审批路由
api_router.include_router(
    approval.router, 
    prefix="/approval", 
    tags=["审批模块"],
    dependencies=admin_auth
)

# 支付路由
api_router.include_router(
    payment.router, 
    prefix="/payment", 
    tags=["支付模块"],
    dependencies=admin_auth
)

# 微信小程序
# 不走后端认证，直接走微信小程序的认证
api_router.include_router(
    wechat_mini_app.router, 
    prefix="/wx", 
    tags=["微信小程序"]
)

# 报表模块
api_router.include_router(
    report.router, 
    prefix="/report", 
    tags=["报表模块"],
    dependencies=admin_auth
)

# 赠送模块
api_router.include_router(
    gift.router,
    prefix="/gift",
    tags=["赠送模块"],
    dependencies=admin_auth
)

# 优惠券模块
api_router.include_router(
    coupon_router,
    prefix="/coupon",
    tags=["优惠券模块"],
    dependencies=admin_auth
)