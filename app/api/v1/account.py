from typing import List

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from starlette import status

from app.core.deps import get_db
from app.dao.account import (
    account_dao, regular_account_dao, gift_account_dao,
    points_account_dao, member_account_dao, account_transaction_dao
)
from app.schemas.account import *
from app.schemas.user_account import (
    AccountSearchQuery, AccountSearchResponse, PersonalUserAccountsResponse,
    AccountRechargeReq, UserTransactionListResponse, EnterpriseAccountsResponse,
    EnterpriseRechargeReq, EnterpriseSearchResponse
)
from app.service.account import account_service
from app.service.payment import payment_service

router = APIRouter()


# Account endpoints
@router.post("/", response_model=AccountResponse, status_code=status.HTTP_201_CREATED)
def create_account(account: AccountCreate, db: Session = Depends(get_db)):
    """创建账户"""
    return account_dao.create(session=db, account=account)


@router.get("/{account_id}", response_model=AccountResponse)
def read_account(account_id: int, db: Session = Depends(get_db)):
    """获取账户详情"""
    db_account = account_dao.get(session=db, account_id=account_id)
    if db_account is None:
        raise HTTPException(status_code=404, detail="账户不存在")
    return db_account


@router.get("/", response_model=List[AccountResponse])
def read_accounts(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取账户列表"""
    return account_dao.get_list(session=db, skip=skip, limit=limit)


@router.get("/user/{user_id}", response_model=PersonalUserAccountsResponse)
def read_user_accounts(user_id: int, db: Session = Depends(get_db)):
    """获取用户的所有账户"""
    try:
        user_account_data = account_service.get_user_accounts(session=db, user_id=user_id)
        return PersonalUserAccountsResponse(
            code=200,
            message="获取用户账户信息成功",
            data=user_account_data
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.put("/{account_id}", response_model=AccountResponse)
def update_account(account_id: int, account: AccountUpdate, db: Session = Depends(get_db)):
    """更新账户"""
    db_account = account_dao.update(session=db, account_id=account_id, account=account)
    if db_account is None:
        raise HTTPException(status_code=404, detail="账户不存在")
    return db_account


@router.delete("/{account_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_account(account_id: int, db: Session = Depends(get_db)):
    """删除账户"""
    success = account_dao.delete(session=db, account_id=account_id)
    if not success:
        raise HTTPException(status_code=404, detail="账户不存在")
    return {"message": "账户删除成功"}


# Regular Account endpoints
# 不允许的操作
# @router.post("/regular/", response_model=RegularAccountResponse, status_code=status.HTTP_201_CREATED)
# def create_regular_account(account: RegularAccountCreate, db: Session = Depends(get_db)):
#     """创建普通账户"""
#     return regular_account_dao.create(session=db, account=account)


@router.get("/regular/{account_id}", response_model=RegularAccountResponse)
def read_regular_account(account_id: int, db: Session = Depends(get_db)):
    """获取普通账户详情"""
    db_account = regular_account_dao.get(session=db, account_id=account_id)
    if db_account is None:
        raise HTTPException(status_code=404, detail="普通账户不存在")
    return db_account


@router.get("/regular/", response_model=List[RegularAccountResponse])
def read_regular_accounts(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取普通账户列表"""
    return regular_account_dao.get_list(session=db, skip=skip, limit=limit)


@router.get("/regular/user/{user_id}", response_model=List[RegularAccountResponse])
def read_user_regular_accounts(user_id: int, db: Session = Depends(get_db)):
    """获取用户的所有普通账户"""
    return regular_account_dao.get_by_user_id(session=db, user_id=user_id)


@router.put("/regular/{account_id}", response_model=RegularAccountResponse)
def update_regular_account(account_id: int, account: RegularAccountUpdate, db: Session = Depends(get_db)):
    """更新普通账户"""
    db_account = regular_account_dao.update(session=db, account_id=account_id, account=account)
    if db_account is None:
        raise HTTPException(status_code=404, detail="普通账户不存在")
    return db_account


@router.delete("/regular/{account_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_regular_account(account_id: int, db: Session = Depends(get_db)):
    """删除普通账户"""
    success = regular_account_dao.delete(session=db, account_id=account_id)
    if not success:
        raise HTTPException(status_code=404, detail="普通账户不存在")
    return {"message": "普通账户删除成功"}


# Gift Account endpoints
# 不允许的操作
# @router.post("/gift/", response_model=GiftAccountResponse, status_code=status.HTTP_201_CREATED)
# def create_gift_account(account: GiftAccountCreate, db: Session = Depends(get_db)):
#     """创建赠送账户"""
#     return gift_account_dao.create(session=db, account=account)


@router.get("/gift/{account_id}", response_model=GiftAccountResponse)
def read_gift_account(account_id: int, db: Session = Depends(get_db)):
    """获取赠送账户详情"""
    db_account = gift_account_dao.get(session=db, account_id=account_id)
    if db_account is None:
        raise HTTPException(status_code=404, detail="赠送账户不存在")
    return db_account


@router.get("/gift/", response_model=List[GiftAccountResponse])
def read_gift_accounts(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取赠送账户列表"""
    return gift_account_dao.get_list(session=db, skip=skip, limit=limit)


@router.get("/gift/user/{user_id}", response_model=List[GiftAccountResponse])
def read_user_gift_accounts(user_id: int, db: Session = Depends(get_db)):
    """获取用户的所有赠送账户"""
    return gift_account_dao.get_by_user_id(session=db, user_id=user_id)


@router.put("/gift/{account_id}", response_model=GiftAccountResponse)
def update_gift_account(account_id: int, account: GiftAccountUpdate, db: Session = Depends(get_db)):
    """更新赠送账户"""
    db_account = gift_account_dao.update(session=db, account_id=account_id, account=account)
    if db_account is None:
        raise HTTPException(status_code=404, detail="赠送账户不存在")
    return db_account


@router.delete("/gift/{account_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_gift_account(account_id: int, db: Session = Depends(get_db)):
    """删除赠送账户"""
    success = gift_account_dao.delete(session=db, account_id=account_id)
    if not success:
        raise HTTPException(status_code=404, detail="赠送账户不存在")
    return {"message": "赠送账户删除成功"}


# Points Account endpoints
@router.post("/points/", response_model=PointsAccountResponse, status_code=status.HTTP_201_CREATED)
def create_points_account(account: PointsAccountCreate, db: Session = Depends(get_db)):
    """创建积分账户"""
    return points_account_dao.create(session=db, account=account)


@router.get("/points/{account_id}", response_model=PointsAccountResponse)
def read_points_account(account_id: int, db: Session = Depends(get_db)):
    """获取积分账户详情"""
    db_account = points_account_dao.get(session=db, account_id=account_id)
    if db_account is None:
        raise HTTPException(status_code=404, detail="积分账户不存在")
    return db_account


@router.get("/points/", response_model=List[PointsAccountResponse])
def read_points_accounts(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取积分账户列表"""
    return points_account_dao.get_list(session=db, skip=skip, limit=limit)


@router.get("/points/user/{user_id}", response_model=List[PointsAccountResponse])
def read_user_points_accounts(user_id: int, db: Session = Depends(get_db)):
    """获取用户的所有积分账户"""
    return points_account_dao.get_by_user_id(session=db, user_id=user_id)


@router.put("/points/{account_id}", response_model=PointsAccountResponse)
def update_points_account(account_id: int, account: PointsAccountUpdate, db: Session = Depends(get_db)):
    """更新积分账户"""
    db_account = points_account_dao.update(session=db, account_id=account_id, account=account)
    if db_account is None:
        raise HTTPException(status_code=404, detail="积分账户不存在")
    return db_account


@router.delete("/points/{account_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_points_account(account_id: int, db: Session = Depends(get_db)):
    """删除积分账户"""
    success = points_account_dao.delete(session=db, account_id=account_id)
    if not success:
        raise HTTPException(status_code=404, detail="积分账户不存在")
    return {"message": "积分账户删除成功"}


# Member Account endpoints
@router.post("/member/", response_model=MemberAccountResponse, status_code=status.HTTP_201_CREATED)
def create_member_account(account: MemberAccountCreate, db: Session = Depends(get_db)):
    """创建会员账户"""
    return member_account_dao.create(session=db, account=account)


@router.get("/member/{account_id}", response_model=MemberAccountResponse)
def read_member_account(account_id: int, db: Session = Depends(get_db)):
    """获取会员账户详情"""
    db_account = member_account_dao.get(session=db, account_id=account_id)
    if db_account is None:
        raise HTTPException(status_code=404, detail="会员账户不存在")
    return db_account


@router.get("/member/", response_model=List[MemberAccountResponse])
def read_member_accounts(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取会员账户列表"""
    return member_account_dao.get_list(session=db, skip=skip, limit=limit)


@router.get("/member/user/{user_id}", response_model=List[MemberAccountResponse])
def read_user_member_accounts(user_id: int, db: Session = Depends(get_db)):
    """获取用户的所有会员账户"""
    return member_account_dao.get_by_user_id(session=db, user_id=user_id)


@router.put("/member/{account_id}", response_model=MemberAccountResponse)
def update_member_account(account_id: int, account: MemberAccountUpdate, db: Session = Depends(get_db)):
    """更新会员账户"""
    db_account = member_account_dao.update(session=db, account_id=account_id, account=account)
    if db_account is None:
        raise HTTPException(status_code=404, detail="会员账户不存在")
    return db_account


@router.delete("/member/{account_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_member_account(account_id: int, db: Session = Depends(get_db)):
    """删除会员账户"""
    success = member_account_dao.delete(session=db, account_id=account_id)
    if not success:
        raise HTTPException(status_code=404, detail="会员账户不存在")
    return {"message": "会员账户删除成功"}


# Account Transaction endpoints
@router.post("/transactions/", response_model=AccountTransactionResponse, status_code=status.HTTP_201_CREATED)
def create_transaction(transaction: AccountTransactionCreate, db: Session = Depends(get_db)):
    """创建账户流水"""
    return account_transaction_dao.create(session=db, transaction=transaction)


@router.get("/transactions/{transaction_id}", response_model=AccountTransactionResponse)
def read_transaction(transaction_id: int, db: Session = Depends(get_db)):
    """获取账户流水详情"""
    db_transaction = account_transaction_dao.get(session=db, transaction_id=transaction_id)
    if db_transaction is None:
        raise HTTPException(status_code=404, detail="账户流水不存在")
    return db_transaction


@router.get("/transactions/", response_model=List[AccountTransactionResponse])
def read_transactions(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取账户流水列表"""
    return account_transaction_dao.get_list(session=db, skip=skip, limit=limit)


@router.get("/transactions/account/{account_id}", response_model=List[AccountTransactionResponse])
def read_account_transactions(account_id: int, db: Session = Depends(get_db)):
    """获取账户的所有流水"""
    return account_transaction_dao.get_by_account_id(session=db, account_id=account_id)


@router.get("/transactions/order/{order_id}", response_model=List[AccountTransactionResponse])
def read_order_transactions(order_id: int, db: Session = Depends(get_db)):
    """获取订单相关的所有流水"""
    return account_transaction_dao.get_by_order_id(session=db, order_id=order_id)


@router.put("/transactions/{transaction_id}", response_model=AccountTransactionResponse)
def update_transaction(transaction_id: int, transaction: AccountTransactionUpdate, db: Session = Depends(get_db)):
    """更新账户流水"""
    db_transaction = account_transaction_dao.update(session=db, transaction_id=transaction_id, transaction=transaction)
    if db_transaction is None:
        raise HTTPException(status_code=404, detail="账户流水不存在")
    return db_transaction


@router.delete("/transactions/{transaction_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_transaction(transaction_id: int, db: Session = Depends(get_db)):
    """删除账户流水"""
    success = account_transaction_dao.delete(session=db, transaction_id=transaction_id)
    if not success:
        raise HTTPException(status_code=404, detail="账户流水不存在")
    return {"message": "账户流水删除成功"}


# 先定义特定路由，再定义包含路径参数的通用路由
@router.get("/personal/search", response_model=AccountSearchResponse)
def search_accounts_with_balance(
        keyword: Optional[str] = None,
        name: Optional[str] = None,
        phone: Optional[str] = None,
        status: Optional[Status] = None,
        page: int = Query(1, ge=1),  # 从1开始的页码
        pageSize: int = Query(100, ge=1, le=1000),
        db: Session = Depends(get_db)
):
    """
    搜索用户账户信息，并返回普通账户和赠送账户的余额

    - **keyword**: 搜索关键字，可搜索用户名、手机号、邮箱、真实姓名等
    - **name**: 用户名（精确匹配或模糊匹配）
    - **phone**: 手机号（精确匹配或模糊匹配）
    - **status**: 用户状态
    - **page**: 页码，从1开始
    - **pageSize**: 每页记录数
    """
    # 计算分页参数
    skip = (page - 1) * pageSize
    limit = pageSize

    query = AccountSearchQuery(
        keyword=keyword,
        name=name,
        phone=phone,
        status=status,
        skip=skip,
        limit=limit
    )
    result = account_service.search(db, **query.model_dump())

    # 添加响应的code和message字段
    response = AccountSearchResponse(
        code=200,
        message="获取用户账户信息成功",
        data=result["data"]
    )

    return response


@router.post("/personal/recharge", response_model=PersonalUserAccountsResponse)
def user_account_recharge(account_recharge_req: AccountRechargeReq, db: Session = Depends(get_db)):
    """
    为用户账户充值

    Args:
        account_recharge_req: 账户充值请求
        db: 数据库会话

    Returns:
        用户账户信息，包含用户基本信息和账户余额
    """
    # 调用充值服务
    try:
        recharge_info = account_recharge_req.model_dump()
        user_id = recharge_info.get("user_id")
        amount = recharge_info.get("amount")
        recharge_method = recharge_info.get("payment_method")
        recharge_order = account_service.recharge(session=db, user_id=user_id, amount=amount,
                                                  recharge_method=recharge_method)

        paid_recharge_order = payment_service.recharge(session=db, order_id=recharge_order.id,
                                                       recharge_info=recharge_info)
        # 获取并返回最新的用户账户信息
        user_id = recharge_info.get("user_id")
        user_account_data = account_service.get_user_accounts(session=db, user_id=user_id)
        return PersonalUserAccountsResponse(
            code=200,
            message="用户账户充值成功",
            data=user_account_data
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.get("/personal/{user_id}", response_model=PersonalUserAccountsResponse)
def get_user_accounts(user_id: int, db: Session = Depends(get_db)):
    """
    获取用户的账户信息

    Args:
        user_id: 用户ID
        db: 数据库会话

    Returns:
        用户账户信息，包含用户基本信息和账户余额
    """
    try:
        user_account_data = account_service.get_user_accounts(session=db, user_id=user_id)
        return PersonalUserAccountsResponse(
            code=200,
            message="获取用户账户信息成功",
            data=user_account_data
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.get("/personal/{user_id}/transactions/", response_model=UserTransactionListResponse)
def get_user_transactions(
        user_id: int,
        page: int = Query(1, ge=1),  # 从1开始的页码
        pageSize: int = Query(100, ge=1, le=1000),
        db: Session = Depends(get_db)
):
    """
    获取用户的所有账户的交易记录

    Args:
        user_id: 用户ID
        skip: 分页起始位置
        limit: 分页大小
        db: 数据库会话

    Returns:
        用户的所有账户的交易记录，按交易时间倒序排序
    """
    try:
        skip = (page - 1) * pageSize
        limit = pageSize
        result = account_service.get_user_transactions(session=db, user_id=user_id, skip=skip, limit=limit)
        return UserTransactionListResponse(
            code=200,
            message="获取用户交易记录成功",
            data=result["data"]
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


# 企业账户API
@router.get("/enterprise/search", response_model=EnterpriseSearchResponse)
def search_enterprise_accounts_with_balance(
        keyword: Optional[str] = None,
        name: Optional[str] = None,
        company_name: Optional[str] = None,
        phone: Optional[str] = None,
        status: Optional[Status] = None,
        page: int = Query(1, ge=1),  # 从1开始的页码
        pageSize: int = Query(100, ge=1, le=1000),
        db: Session = Depends(get_db)
):
    """
    搜索企业账户信息，并返回普通账户和赠送账户的余额

    - **keyword**: 搜索关键字，可搜索企业名称、手机号、邮箱等
    - **company_name**: 企业名称（精确匹配或模糊匹配）
    - **phone**: 联系电话（精确匹配或模糊匹配）
    - **status**: 企业状态
    - **page**: 页码，从1开始
    - **pageSize**: 每页记录数
    """
    # 计算分页参数
    skip = (page - 1) * pageSize
    limit = pageSize

    result = account_service.search_enterprise(
        session=db,
        keyword=keyword,
        name=name,
        company_name=company_name,
        phone=phone,
        status=status,
        skip=skip,
        limit=limit
    )

    # 添加响应的code和message字段
    response = EnterpriseSearchResponse(
        code=200,
        message="获取企业账户信息成功",
        data=result["data"]
    )

    return response


@router.post("/enterprise/recharge", response_model=EnterpriseAccountsResponse)
def enterprise_account_recharge(account_recharge_req: EnterpriseRechargeReq, db: Session = Depends(get_db)):
    """
    为企业账户充值

    Args:
        account_recharge_req: 账户充值请求
        db: 数据库会话

    Returns:
        企业账户信息，包含企业基本信息和账户余额
    """
    # 调用充值服务
    try:
        recharge_info = account_recharge_req.model_dump()
        user_id = recharge_info.get("user_id")
        amount = recharge_info.get("amount")
        recharge_method = recharge_info.get("payment_method")

        print(f"企业充值 - 用户ID: {user_id}, 金额: {amount}, 支付方式: {recharge_method}")

        recharge_order = account_service.recharge(session=db, user_id=user_id, amount=amount,
                                                  recharge_method=recharge_method)

        print(
            f"创建充值订单 - 订单ID: {recharge_order.id}, 状态: {recharge_order.status}, 支付状态: {recharge_order.payment_status}")

        paid_recharge_order = payment_service.recharge(session=db, order_id=recharge_order.id,
                                                       recharge_info=recharge_info)

        print(
            f"支付充值订单 - 订单ID: {paid_recharge_order.id}, 状态: {paid_recharge_order.status}, 支付状态: {paid_recharge_order.payment_status}")

        # 获取并返回最新的企业账户信息
        enterprise_id = recharge_info.get("user_id")
        enterprise_account_data = account_service.get_enterprise_accounts(session=db, enterprise_id=enterprise_id)
        return EnterpriseAccountsResponse(
            code=200,
            message="企业账户充值成功",
            data=enterprise_account_data["data"]
        )
    except ValueError as e:
        print(f"企业充值错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.get("/enterprise/{enterprise_id}/transactions/", response_model=UserTransactionListResponse)
def get_enterprise_transactions(
        enterprise_id: int,
        page: int = Query(1, ge=1),  # 从1开始的页码
        pageSize: int = Query(100, ge=1, le=1000),
        db: Session = Depends(get_db)
):
    """
    获取企业的所有账户的交易记录

    Args:
        enterprise_id: 企业ID
        page: 页码，从1开始
        pageSize: 每页记录数
        db: 数据库会话

    Returns:
        企业的所有账户的交易记录，按交易时间倒序排序
    """
    try:
        skip = (page - 1) * pageSize
        limit = pageSize
        result = account_service.get_enterprise_transactions(
            session=db,
            enterprise_id=enterprise_id,
            skip=skip,
            limit=limit
        )
        return UserTransactionListResponse(
            code=200,
            message="获取企业交易记录成功",
            data=result["data"]
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.get("/enterprise/{enterprise_id}", response_model=EnterpriseAccountsResponse)
def get_enterprise_accounts(enterprise_id: int, db: Session = Depends(get_db)):
    """
    获取企业的账户信息

    Args:
        enterprise_id: 企业ID
        db: 数据库会话

    Returns:
        企业账户信息，包含企业基本信息和账户余额
    """
    try:
        enterprise_account_data = account_service.get_enterprise_accounts(session=db, enterprise_id=enterprise_id)
        return EnterpriseAccountsResponse(
            code=200,
            message="获取企业账户信息成功",
            data=enterprise_account_data["data"]
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
