from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from starlette import status

from app.core.deps import get_db
from app.dao.admin import admin_dao
from app.schemas.admin import *

router = APIRouter()


# Admin endpoints
@router.post("/", response_model=AdminCreateResponse, status_code=status.HTTP_201_CREATED)
def create_admin(admin: AdminCreate, db: Session = Depends(get_db)):
    db_admin = admin_dao.create(session=db, admin=admin)
    return AdminCreateResponse(
        code=200,
        message="管理员创建成功",
        data=db_admin
    )


@router.get("/{admin_id}", response_model=AdminDetailAPIResponse)
def read_admin(admin_id: int, db: Session = Depends(get_db)):
    db_admin = admin_dao.get(session=db, admin_id=admin_id)
    if db_admin is None:
        raise HTTPException(status_code=404, detail="管理员不存在")
    return AdminDetailAPIResponse(
        code=200,
        message="获取管理员信息成功",
        data=db_admin
    )


@router.get("/search/", response_model=AdminSearchResponse)
def search_admins(request: AdminSearchRequest = Depends(), db: Session = Depends(get_db)):
    """根据关键字搜索管理员列表"""
    skip = max(0, request.page - 1) * request.pageSize
    limit = request.pageSize
    admins = admin_dao.search(session=db, keyword=request.keyword, skip=skip, limit=limit)
    return AdminSearchResponse(
        code=200,
        message="搜索管理员列表成功",
        data=admins
    )


@router.get("/", response_model=AdminListResponse)
def read_admins(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    admins = admin_dao.get_list(session=db, skip=skip, limit=limit)
    return AdminListResponse(
        code=200,
        message="获取管理员列表成功",
        data=admins
    )


@router.put("/{admin_id}", response_model=AdminUpdateResponse)
def update_admin(admin_id: int, admin: AdminUpdate, db: Session = Depends(get_db)):
    db_admin = admin_dao.update(session=db, admin_id=admin_id, admin=admin)
    if db_admin is None:
        raise HTTPException(status_code=404, detail="管理员不存在")
    return AdminUpdateResponse(
        code=200,
        message="管理员更新成功",
        data=db_admin
    )


@router.put("/", response_model=AdminDetailAPIResponse)
def update_admin_by_id_in_body(admin_update: AdminUpdateWithId, db: Session = Depends(get_db)):
    """根据请求体中的id更新管理员"""
    if not admin_update.id:
        raise HTTPException(status_code=400, detail="管理员ID不能为空")
    
    # 提取id，剩余字段用于更新
    admin_id = admin_update.id
    admin_data = admin_update.model_dump(
        exclude={'id', 'created_at', 'updated_at', 'last_login_time', 'roles'}, 
        exclude_unset=True
    )
    
    # 处理status字段的转换
    if 'status' in admin_data and admin_data['status'] is not None:
        from app.models.enum import Status
        admin_data['status'] = Status.ACTIVE if admin_data['status'] == 1 else Status.INACTIVE
    
    admin = AdminUpdate(**admin_data)
    
    db_admin = admin_dao.update(session=db, admin_id=admin_id, admin=admin)
    if db_admin is None:
        raise HTTPException(status_code=404, detail="管理员不存在")
    return AdminDetailAPIResponse(
        code=200,
        message="管理员更新成功",
        data=db_admin
    )


@router.delete("/{admin_id}", response_model=AdminDeleteResponse)
def delete_admin(admin_id: int, db: Session = Depends(get_db)):
    success = admin_dao.delete(session=db, admin_id=admin_id)
    if not success:
        raise HTTPException(status_code=404, detail="管理员不存在")
    return AdminDeleteResponse(
        code=200,
        message="管理员删除成功",
        data={"success": True}
    )


# Admin-Role relationship endpoints
@router.post("/{admin_id}/roles/{role_id}", response_model=AdminDetailAPIResponse)
def add_role_to_admin(admin_id: int, role_id: int, db: Session = Depends(get_db)):
    db_admin = admin_dao.add_role(session=db, admin_id=admin_id, role_id=role_id)
    if db_admin is None:
        raise HTTPException(status_code=404, detail="管理员或角色不存在")
    return AdminDetailAPIResponse(
        code=200,
        message="角色添加成功",
        data=db_admin
    )


@router.delete("/{admin_id}/roles/{role_id}", response_model=AdminDetailAPIResponse)
def remove_role_from_admin(admin_id: int, role_id: int, db: Session = Depends(get_db)):
    db_admin = admin_dao.remove_role(session=db, admin_id=admin_id, role_id=role_id)
    if db_admin is None:
        raise HTTPException(status_code=404, detail="管理员或角色不存在，或该管理员没有此角色")
    return AdminDetailAPIResponse(
        code=200,
        message="角色移除成功",
        data=db_admin
    )


@router.get("/{admin_id}/roles", response_model=RoleListResponse)
def get_admin_roles(admin_id: int, db: Session = Depends(get_db)):
    roles = admin_dao.get_roles(session=db, admin_id=admin_id)
    return RoleListResponse(
        code=200,
        message="获取管理员角色列表成功",
        data=roles
    )


# 批量操作端点
@router.post("/status", response_model=AdminBatchOperationResponse)
def batch_update_admin_status(request: AdminBatchStatusRequest, db: Session = Depends(get_db)):
    """批量更新管理员状态"""
    success = admin_dao.batch_update_status(
        session=db, 
        admin_ids=request.admin_ids, 
        status=request.status
    )
    
    if not success:
        raise HTTPException(status_code=400, detail="批量更新管理员状态失败")
    
    status_text = "启用" if request.status == 1 else "禁用"
    return AdminBatchOperationResponse(
        code=200,
        message=f"批量{status_text}管理员成功",
        data={"success": True, "updated_count": len(request.admin_ids)}
    )


@router.delete("/", response_model=AdminBatchOperationResponse)
def batch_delete_admins(request: AdminBatchDeleteRequest, db: Session = Depends(get_db)):
    """批量删除管理员"""
    success = admin_dao.batch_delete(session=db, admin_ids=request.ids)
    
    if not success:
        raise HTTPException(status_code=400, detail="批量删除管理员失败")
    
    return AdminBatchOperationResponse(
        code=200,
        message="批量删除管理员成功",
        data={"success": True, "deleted_count": len(request.ids)}
    )
