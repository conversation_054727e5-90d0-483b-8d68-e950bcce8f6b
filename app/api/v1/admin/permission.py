from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from starlette import status

from app.core.deps import get_db
from app.dao.admin import permission_dao
from app.schemas.admin import *

router = APIRouter()


# Permission endpoints
@router.post("/permissions/", response_model=PermissionCreateResponse, status_code=status.HTTP_201_CREATED)
def create_permission(permission: PermissionCreate, db: Session = Depends(get_db)):
    db_permission = permission_dao.create(session=db, permission=permission)
    return PermissionCreateResponse(
        code=200,
        message="权限创建成功",
        data=db_permission
    )


@router.get("/permissions/{permission_id}", response_model=PermissionDetailAPIResponse)
def read_permission(permission_id: int, db: Session = Depends(get_db)):
    db_permission = permission_dao.get(session=db, permission_id=permission_id)
    if db_permission is None:
        raise HTTPException(status_code=404, detail="权限不存在")
    return PermissionDetailAPIResponse(
        code=200,
        message="获取权限信息成功",
        data=db_permission
    )


@router.get("/permissions/search/", response_model=PermissionSearchResponse)
def search_permissions(request: PermissionSearchRequest = Depends(), db: Session = Depends(get_db)):
    """根据关键字搜索权限列表"""
    skip = max(0, request.page - 1) * request.pageSize
    limit = request.pageSize
    permissions = permission_dao.search(session=db, keyword=request.keyword, skip=skip, limit=limit)
    return PermissionSearchResponse(
        code=200,
        message="搜索权限列表成功",
        data=permissions
    )


@router.get("/permissions/", response_model=PermissionListResponse)
def read_permissions(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    permissions = permission_dao.get_list(session=db, skip=skip, limit=limit)
    return PermissionListResponse(
        code=200,
        message="获取权限列表成功",
        data=permissions
    )


@router.put("/permissions/{permission_id}", response_model=PermissionUpdateResponse)
def update_permission(permission_id: int, permission: PermissionUpdate, db: Session = Depends(get_db)):
    db_permission = permission_dao.update(session=db, permission_id=permission_id, permission=permission)
    if db_permission is None:
        raise HTTPException(status_code=404, detail="权限不存在")
    return PermissionUpdateResponse(
        code=200,
        message="权限更新成功",
        data=db_permission
    )


@router.put("/permissions/", response_model=PermissionDetailAPIResponse)
def update_permission_by_id_in_body(permission_update: PermissionUpdateWithId, db: Session = Depends(get_db)):
    """根据请求体中的id更新权限"""
    if not permission_update.id:
        raise HTTPException(status_code=400, detail="权限ID不能为空")
    
    # 提取id，剩余字段用于更新
    permission_id = permission_update.id
    permission_data = permission_update.model_dump(
        exclude={'id', 'created_at', 'updated_at', 'roles'}, 
        exclude_unset=True
    )
    
    # 处理status字段的转换
    if 'status' in permission_data and permission_data['status'] is not None:
        from app.models.enum import Status
        permission_data['status'] = Status.ACTIVE if permission_data['status'] == 1 else Status.INACTIVE
    
    permission = PermissionUpdate(**permission_data)
    
    db_permission = permission_dao.update(session=db, permission_id=permission_id, permission=permission)
    if db_permission is None:
        raise HTTPException(status_code=404, detail="权限不存在")
    return PermissionDetailAPIResponse(
        code=200,
        message="权限更新成功",
        data=db_permission
    )


# 批量操作端点
@router.post("/permissions/status", response_model=PermissionBatchOperationResponse)
def batch_update_permission_status(request: PermissionBatchStatusRequest, db: Session = Depends(get_db)):
    """批量更新权限状态"""
    success = permission_dao.batch_update_status(
        session=db, 
        permission_ids=request.permission_ids, 
        status=request.status
    )
    
    if not success:
        raise HTTPException(status_code=400, detail="批量更新权限状态失败")
    
    status_text = "启用" if request.status == 1 else "禁用"
    return PermissionBatchOperationResponse(
        code=200,
        message=f"批量{status_text}权限成功",
        data={"success": True, "updated_count": len(request.permission_ids)}
    )


@router.delete("/permissions/", response_model=PermissionBatchOperationResponse)
def batch_delete_permissions(request: PermissionBatchDeleteRequest, db: Session = Depends(get_db)):
    """批量删除权限"""
    success = permission_dao.batch_delete(session=db, permission_ids=request.ids)
    
    if not success:
        raise HTTPException(status_code=400, detail="批量删除权限失败")
    
    return PermissionBatchOperationResponse(
        code=200,
        message="批量删除权限成功",
        data={"success": True, "deleted_count": len(request.ids)}
    )


@router.delete("/permissions/{permission_id}", response_model=PermissionDeleteResponse)
def delete_permission(permission_id: int, db: Session = Depends(get_db)):
    success = permission_dao.delete(session=db, permission_id=permission_id)
    if not success:
        raise HTTPException(status_code=404, detail="权限不存在")
    return PermissionDeleteResponse(
        code=200,
        message="权限删除成功",
        data={"success": True}
    )
