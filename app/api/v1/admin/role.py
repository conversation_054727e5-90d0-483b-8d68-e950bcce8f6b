from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from starlette import status

from app.core.deps import get_db
from app.dao.admin import role_dao
from app.schemas.admin import *

router = APIRouter()


# Role endpoints
@router.post("/roles/", response_model=RoleCreateResponse, status_code=status.HTTP_201_CREATED)
def create_role(role: RoleCreate, db: Session = Depends(get_db)):
    db_role = role_dao.create(session=db, role=role)
    return RoleCreateResponse(
        code=200,
        message="角色创建成功",
        data=db_role
    )


@router.get("/roles/{role_id}", response_model=RoleDetailAPIResponse)
def read_role(role_id: int, db: Session = Depends(get_db)):
    db_role = role_dao.get(session=db, role_id=role_id)
    if db_role is None:
        raise HTTPException(status_code=404, detail="角色不存在")
    return RoleDetailAPIResponse(
        code=200,
        message="获取角色信息成功",
        data=db_role
    )


@router.get("/roles/search/", response_model=RoleSearchResponse)
def search_roles(request: RoleSearchRequest = Depends(), db: Session = Depends(get_db)):
    """根据关键字搜索角色列表"""
    skip = max(0, request.page - 1) * request.pageSize
    limit = request.pageSize
    roles = role_dao.search(session=db, keyword=request.keyword, skip=skip, limit=limit)
    return RoleSearchResponse(
        code=200,
        message="搜索角色列表成功",
        data=roles
    )


@router.get("/roles/", response_model=RoleListResponse)
def read_roles(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    roles = role_dao.get_list(session=db, skip=skip, limit=limit)
    return RoleListResponse(
        code=200,
        message="获取角色列表成功",
        data=roles
    )


@router.put("/roles/{role_id}", response_model=RoleUpdateResponse)
def update_role(role_id: int, role: RoleUpdate, db: Session = Depends(get_db)):
    db_role = role_dao.update(session=db, role_id=role_id, role=role)
    if db_role is None:
        raise HTTPException(status_code=404, detail="角色不存在")
    return RoleUpdateResponse(
        code=200,
        message="角色更新成功",
        data=db_role
    )


@router.put("/roles/", response_model=RoleDetailAPIResponse)
def update_role_by_id_in_body(role_update: RoleUpdateWithId, db: Session = Depends(get_db)):
    """根据请求体中的id更新角色"""
    if not role_update.id:
        raise HTTPException(status_code=400, detail="角色ID不能为空")
    
    # 提取id，剩余字段用于更新
    role_id = role_update.id
    role_data = role_update.model_dump(
        exclude={'id', 'created_at', 'updated_at', 'permissions'}, 
        exclude_unset=True
    )
    
    # 处理status字段的转换
    if 'status' in role_data and role_data['status'] is not None:
        from app.models.enum import Status
        role_data['status'] = Status.ACTIVE if role_data['status'] == 1 else Status.INACTIVE
    
    role = RoleUpdate(**role_data)
    
    db_role = role_dao.update(session=db, role_id=role_id, role=role)
    if db_role is None:
        raise HTTPException(status_code=404, detail="角色不存在")
    return RoleDetailAPIResponse(
        code=200,
        message="角色更新成功",
        data=db_role
    )


@router.delete("/roles/{role_id}", response_model=RoleDeleteResponse)
def delete_role(role_id: int, db: Session = Depends(get_db)):
    success = role_dao.delete(session=db, role_id=role_id)
    if not success:
        raise HTTPException(status_code=404, detail="角色不存在")
    return RoleDeleteResponse(
        code=200,
        message="角色删除成功",
        data={"success": True}
    )


# Role-Permission relationship endpoints
@router.post("/roles/{role_id}/permissions/{permission_id}", response_model=RoleDetailAPIResponse)
def add_permission_to_role(role_id: int, permission_id: int, db: Session = Depends(get_db)):
    db_role = role_dao.add_permission(session=db, role_id=role_id, permission_id=permission_id)
    if db_role is None:
        raise HTTPException(status_code=404, detail="角色或权限不存在")
    return RoleDetailAPIResponse(
        code=200,
        message="权限添加成功",
        data=db_role
    )


@router.delete("/roles/{role_id}/permissions/{permission_id}", response_model=RoleDetailAPIResponse)
def remove_permission_from_role(role_id: int, permission_id: int, db: Session = Depends(get_db)):
    db_role = role_dao.remove_permission(session=db, role_id=role_id, permission_id=permission_id)
    if db_role is None:
        raise HTTPException(status_code=404, detail="角色或权限不存在，或该角色没有此权限")
    return RoleDetailAPIResponse(
        code=200,
        message="权限移除成功",
        data=db_role
    )


@router.get("/roles/{role_id}/permissions", response_model=RolePermissionsResponse)
def get_role_permissions(role_id: int, db: Session = Depends(get_db)):
    permissions = role_dao.get_permissions(session=db, role_id=role_id)
    return RolePermissionsResponse(
        code=200,
        message="获取角色权限列表成功",
        data=permissions
    )


# 批量操作端点
@router.post("/roles/status", response_model=RoleBatchOperationResponse)
def batch_update_role_status(request: RoleBatchStatusRequest, db: Session = Depends(get_db)):
    """批量更新角色状态"""
    success = role_dao.batch_update_status(
        session=db, 
        role_ids=request.role_ids, 
        status=request.status
    )
    
    if not success:
        raise HTTPException(status_code=400, detail="批量更新角色状态失败")
    
    status_text = "启用" if request.status == 1 else "禁用"
    return RoleBatchOperationResponse(
        code=200,
        message=f"批量{status_text}角色成功",
        data={"success": True, "updated_count": len(request.role_ids)}
    )


@router.delete("/roles/", response_model=RoleBatchOperationResponse)
def batch_delete_roles(request: RoleBatchDeleteRequest, db: Session = Depends(get_db)):
    """批量删除角色"""
    success = role_dao.batch_delete(session=db, role_ids=request.ids)
    
    if not success:
        raise HTTPException(status_code=400, detail="批量删除角色失败")
    
    return RoleBatchOperationResponse(
        code=200,
        message="批量删除角色成功",
        data={"success": True, "deleted_count": len(request.ids)}
    )
