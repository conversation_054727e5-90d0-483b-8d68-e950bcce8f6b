from typing import List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from starlette import status

from app.core.deps import get_db
from app.dao.approval import approval_dao
from app.models.approval import ApprovalStatus
from app.schemas.approval import (
    Approval, ApprovalCreate, ApprovalUpdate,
    ApprovalDetail, ApprovalActionResponse
)

router = APIRouter()


# 审批请求基本CRUD接口
@router.post("/", response_model=Approval, status_code=status.HTTP_201_CREATED)
def create_approval(approval: ApprovalCreate, db: Session = Depends(get_db)):
    """创建审批请求"""
    return approval_dao.create(session=db, approval=approval)


@router.get("/{approval_id}", response_model=ApprovalDetail)
def read_approval(approval_id: int, db: Session = Depends(get_db)):
    """获取审批请求详情"""
    db_approval = approval_dao.get(session=db, approval_id=approval_id)
    if db_approval is None:
        raise HTTPException(status_code=404, detail="审批请求不存在")
    return db_approval


@router.get("/", response_model=List[Approval])
def read_approvals(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取审批请求列表"""
    return approval_dao.get_list(session=db, skip=skip, limit=limit)


@router.put("/{approval_id}", response_model=Approval)
def update_approval(approval_id: int, approval: ApprovalUpdate, db: Session = Depends(get_db)):
    """更新审批请求"""
    db_approval = approval_dao.update(session=db, approval_id=approval_id, approval=approval)
    if db_approval is None:
        raise HTTPException(status_code=404, detail="审批请求不存在")
    return db_approval


@router.delete("/{approval_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_approval(approval_id: int, db: Session = Depends(get_db)):
    """删除审批请求"""
    success = approval_dao.delete(session=db, approval_id=approval_id)
    if not success:
        raise HTTPException(status_code=404, detail="审批请求不存在")
    return {"message": "审批请求删除成功"}


# 按企业获取审批请求
@router.get("/enterprise/{enterprise_id}", response_model=List[Approval])
def get_enterprise_approvals(enterprise_id: int, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取指定企业的所有审批请求"""
    return approval_dao.get_by_enterprise(session=db, enterprise_id=enterprise_id, skip=skip, limit=limit)


# 按申请人获取审批请求
@router.get("/applicant/{applicant_id}", response_model=List[Approval])
def get_applicant_approvals(applicant_id: int, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取指定申请人的所有审批请求"""
    return approval_dao.get_by_applicant(session=db, applicant_id=applicant_id, skip=skip, limit=limit)


# 按审批人获取审批请求
@router.get("/approver/{approver_id}", response_model=List[Approval])
def get_approver_approvals(approver_id: int, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取指定审批人的所有审批请求"""
    return approval_dao.get_by_approver(session=db, approver_id=approver_id, skip=skip, limit=limit)


# 按预订请求获取审批请求
@router.get("/reservation-request/{reservation_request_id}", response_model=Approval)
def get_reservation_request_approval(reservation_request_id: int, db: Session = Depends(get_db)):
    """获取指定预订请求的审批"""
    db_approval = approval_dao.get_by_reservation_request(session=db, reservation_request_id=reservation_request_id)
    if db_approval is None:
        raise HTTPException(status_code=404, detail="该预订请求没有关联的审批")
    return db_approval


# 按状态获取审批请求
@router.get("/status/{status}", response_model=List[Approval])
def get_approvals_by_status(status: ApprovalStatus, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取指定状态的所有审批请求"""
    return approval_dao.get_by_status(session=db, status=status, skip=skip, limit=limit)


# 审批操作接口
@router.post("/{approval_id}/approve", response_model=ApprovalActionResponse)
def approve_request(approval_id: int, approver_id: int, comment: str = None, db: Session = Depends(get_db)):
    """审批通过"""
    db_approval = approval_dao.approve(session=db, approval_id=approval_id, approver_id=approver_id, comment=comment)
    if db_approval is None:
        raise HTTPException(status_code=404, detail="审批请求不存在或当前状态无法操作")
    return ApprovalActionResponse(
        success=True,
        message="审批已通过",
        approval=db_approval
    )


@router.post("/{approval_id}/reject", response_model=ApprovalActionResponse)
def reject_request(approval_id: int, approver_id: int, comment: str = None, db: Session = Depends(get_db)):
    """审批拒绝"""
    db_approval = approval_dao.reject(session=db, approval_id=approval_id, approver_id=approver_id, comment=comment)
    if db_approval is None:
        raise HTTPException(status_code=404, detail="审批请求不存在或当前状态无法操作")
    return ApprovalActionResponse(
        success=True,
        message="审批已拒绝",
        approval=db_approval
    ) 