from datetime import timed<PERSON><PERSON>
from typing import Any, Dict
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app.core import security
from app.core.config import settings
from app.core.deps import get_db, get_current_admin
from app.dao import admin as admin_crud
from app.schemas.admin import AdminResponse, AdminCreate, AdminUpdate
from app.schemas.token import Token
from app.schemas.auth import EnhancedOAuth2PasswordRequestForm

router = APIRouter()


@router.post("/login", response_model=Token)
def login(
        db: Session = Depends(get_db),
        form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    管理员登录
    """
    admin = admin_crud.get_admin_by_username(db=db, username=form_data.username)
    if not admin or not security.verify_password(form_data.password, admin.password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码不正确",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 获取管理员的权限
    admin_permissions = admin_crud.get_admin_permissions(db=db, admin_id=admin.id)
    
    # 创建token，包含admin范围
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = security.create_access_token(
        data={"sub": str(admin.id), "scopes": ["admin"] + admin_permissions},
        expires_delta=access_token_expires
    )

    return {
        "access_token": access_token,
        "token_type": "bearer"
    }


@router.post("/passwd/login", response_model=Dict)
def passwd_login(
        db: Session = Depends(get_db),
        form_data: EnhancedOAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    管理员登录（密码方式）- 返回更多用户信息
    """
    admin = admin_crud.get_admin_by_username(db=db, username=form_data.username)
    if not admin or not security.verify_password(form_data.password, admin.password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码不正确",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 获取管理员的权限
    admin_permissions = admin_crud.get_admin_permissions(db=db, admin_id=admin.id)

    # 创建token，包含admin范围
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = security.create_access_token(
        data={
            "sub": str(admin.id),
            "scopes": ["admin"] + admin_permissions,
            "company_type": form_data.company_type,
            "company_id": form_data.company_id,
            "user_id": admin.id,
            "admin_id": admin.id,
        },
        expires_delta=access_token_expires
    )

    user_info = {
        "company_id": form_data.company_id,  # 使用表单中提供的公司ID
        "company_type": form_data.company_type, # 使用表单中提供的公司类型
        "email": admin.email if admin.email else "",
        "identity": "",
        "phone": admin.phone,
        "state": 0,
        "types": 3,  # 乙禾公司用户类型为3
        "user_id": admin.id,
        "user_name": admin.name,
        "access_token": access_token
    }

    return {
        "code": 200,
        "msg": "登录成功",
        "data": user_info
    }


@router.get("/user/info", response_model=Dict)
def get_user_info(
        db: Session = Depends(get_db),
        current_admin = Depends(get_current_admin),
) -> Any:
    """
    获取当前用户信息
    """
    # 获取管理员的权限列表
    admin_permissions = admin_crud.get_admin_permissions(db=db, admin_id=current_admin.id)
    
    user_info = {
        "user_name": current_admin.name,
        "types": 3,  # 管理员类型固定为3
        "permissions": admin_permissions,  # 权限码数组
        "user_id": current_admin.id,
        "phone": current_admin.phone or "",
        "email": current_admin.email or ""
    }

    return {
        "code": 200,
        "message": "获取成功",
        "data": user_info
    }


@router.post("/register", response_model=AdminResponse)
def register(
        *,
        db: Session = Depends(get_db),
        admin_in: AdminCreate,
) -> Any:
    """
    创建新管理员
    """
    admin = admin_crud.get_admin_by_username(db=db, username=admin_in.username)
    if admin:
        raise HTTPException(
            status_code=400,
            detail="该用户名已存在",
        )
    admin = admin_crud.create_admin(db=db, admin=admin_in)
    return admin


@router.get("/me", response_model=AdminResponse)
def read_admin_me(
        current_admin = Depends(get_current_admin),
) -> Any:
    """
    获取当前管理员信息
    """
    return current_admin


@router.put("/me", response_model=AdminResponse)
def update_admin_me(
        *,
        db: Session = Depends(get_db),
        admin_in: AdminUpdate,
        current_admin = Depends(get_current_admin),
) -> Any:
    """
    更新当前管理员信息
    """
    admin = admin_crud.update_admin(
        db=db, admin_id=current_admin.id, admin=admin_in
    )
    return admin
