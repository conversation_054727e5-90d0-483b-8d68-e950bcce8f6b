from typing import List, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status, Body
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.dao.product import product_category_dao
from app.models.enum import Status
from app.schemas.common import CommonResponse
from app.schemas.product import (
    ProductCategoryCreate, ProductCategoryUpdate, ProductCategoryResponse,
    ProductCategorySearchRequest, ProductCategoryListResponse, ProductCategoryListData,
    ProductCategoryTreeResponse, ProductCategoryTreeNode, ProductCategoryStatusUpdateRequest
)

router = APIRouter()


@router.post("/create", response_model=CommonResponse, status_code=status.HTTP_201_CREATED)
def create_category(category: ProductCategoryCreate, db: Session = Depends(get_db)):
    """创建产品分类"""
    try:
        # 直接传递 Pydantic 模型对象给 DAO
        created_category = product_category_dao.create(session=db, category=category)
        
        return CommonResponse(
            code=200,
            message="产品分类创建成功",
            data={
                "id": created_category.id,
                "name": created_category.name,
                "description": created_category.description,
                "image": created_category.image,
                "sort_order": created_category.sort_order,
                "parent_id": created_category.parent_id,
                "status": created_category.status,
                "created_at": created_category.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "updated_at": created_category.updated_at.strftime("%Y-%m-%d %H:%M:%S")
            }
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"创建产品分类失败: {str(e)}")


@router.get("/view/{category_id}", response_model=CommonResponse)
def get_category(category_id: int, db: Session = Depends(get_db)):
    """获取产品分类详情"""
    category = product_category_dao.get(session=db, category_id=category_id)
    if not category:
        return CommonResponse(
            code=404,
            message="产品分类不存在",
            data=None
        )
    
    return CommonResponse(
        code=200,
        message="获取产品分类成功",
        data={
            "id": category.id,
            "name": category.name,
            "description": category.description,
            "image": category.image,
            "sort_order": category.sort_order,
            "parent_id": category.parent_id,
            "status": category.status,
            "created_at": category.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": category.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
            "products_count": len(category.products) if category.products else 0,
            "children_count": len([c for c in category.children]) if hasattr(category, 'children') else 0
        }
    )


@router.put("/update/{category_id}", response_model=CommonResponse)
def update_category(category_id: int, category: ProductCategoryUpdate, db: Session = Depends(get_db)):
    """更新产品分类"""
    try:
        # 检查分类是否存在
        existing_category = product_category_dao.get(session=db, category_id=category_id)
        if not existing_category:
            return CommonResponse(
                code=404,
                message="产品分类不存在",
                data=None
            )
        
        # 更新分类
        updated_category = product_category_dao.update(session=db, category_id=category_id, category=category)
        
        return CommonResponse(
            code=200,
            message="产品分类更新成功",
            data={
                "id": updated_category.id,
                "name": updated_category.name,
                "description": updated_category.description,
                "image": updated_category.image,
                "sort_order": updated_category.sort_order,
                "parent_id": updated_category.parent_id,
                "status": updated_category.status,
                "created_at": updated_category.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "updated_at": updated_category.updated_at.strftime("%Y-%m-%d %H:%M:%S")
            }
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"更新产品分类失败: {str(e)}")


@router.delete("/delete/{category_id}", response_model=CommonResponse)
def delete_category(category_id: int, db: Session = Depends(get_db)):
    """删除产品分类"""
    category = product_category_dao.get(session=db, category_id=category_id)
    if not category:
        return CommonResponse(
            code=404,
            message="产品分类不存在",
            data=None
        )
    
    # 检查是否有子分类
    children = product_category_dao.get_children_categories(session=db, parent_id=category_id)
    if children:
        return CommonResponse(
            code=400,
            message="无法删除分类，该分类下存在子分类",
            data=None
        )
    
    # 检查是否有关联的产品
    if category.products:
        return CommonResponse(
            code=400,
            message="无法删除分类，该分类下存在关联的产品",
            data=None
        )
    
    success = product_category_dao.delete(session=db, category_id=category_id)
    if success:
        return CommonResponse(
            code=200,
            message="产品分类删除成功",
            data=None
        )
    else:
        return CommonResponse(
            code=400,
            message="删除产品分类失败",
            data=None
        )


@router.post("/status/{category_id}", response_model=CommonResponse)
def update_category_status(category_id: int, request: ProductCategoryStatusUpdateRequest, db: Session = Depends(get_db)):
    """更新产品分类状态"""
    category = product_category_dao.get(session=db, category_id=category_id)
    if not category:
        return CommonResponse(
            code=404,
            message="产品分类不存在",
            data=None
        )
    
    try:
        category_update = ProductCategoryUpdate(status=request.status)
        updated_category = product_category_dao.update(session=db, category_id=category_id, category=category_update)
        
        return CommonResponse(
            code=200,
            message="产品分类状态更新成功",
            data={
                "id": updated_category.id,
                "status": updated_category.status
            }
        )
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"更新产品分类状态失败: {str(e)}")


@router.get("/search", response_model=ProductCategoryListResponse)
def search_categories(request: ProductCategorySearchRequest = Depends(), db: Session = Depends(get_db)):
    """搜索产品分类"""
    # 计算分页参数
    skip = (request.page - 1) * request.pageSize
    limit = request.pageSize
    
    # 搜索分类
    result = product_category_dao.search(
        session=db,
        keyword=request.keyword,
        name=request.name,
        status=request.status,
        parent_id=request.parent_id,
        skip=skip,
        limit=limit
    )
    
    # 转换数据格式
    categories = []
    for category in result["list"]:
        categories.append({
            "id": category.id,
            "name": category.name,
            "description": category.description,
            "image": category.image,
            "sort_order": category.sort_order,
            "parent_id": category.parent_id,
            "status": category.status,
            "created_at": category.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": category.updated_at.strftime("%Y-%m-%d %H:%M:%S")
        })
    
    return ProductCategoryListResponse(
        code=200,
        message="搜索成功",
        data=ProductCategoryListData(
            total=result["total"],
            list=categories
        )
    )


@router.get("/tree", response_model=ProductCategoryTreeResponse)
def get_category_tree(db: Session = Depends(get_db)):
    """获取产品分类树形结构"""
    try:
        tree_data = product_category_dao.get_tree(session=db)
        
        def convert_node(node):
            """递归转换节点格式"""
            converted_node = {
                "id": node["id"],
                "name": node["name"],
                "description": node["description"],
                "image": node["image"],
                "sort_order": node["sort_order"],
                "parent_id": node["parent_id"],
                "status": node["status"],
                "created_at": node["created_at"].strftime("%Y-%m-%d %H:%M:%S"),
                "updated_at": node["updated_at"].strftime("%Y-%m-%d %H:%M:%S"),
                "children": []
            }
            
            # 递归处理子节点
            for child in node.get("children", []):
                converted_node["children"].append(convert_node(child))
            
            return converted_node
        
        # 转换数据格式
        tree_nodes = []
        for node in tree_data:
            tree_nodes.append(convert_node(node))
        
        return ProductCategoryTreeResponse(
            code=200,
            message="获取分类树成功",
            data=tree_nodes
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取分类树失败: {str(e)}")


@router.get("/", response_model=ProductCategoryListResponse)
def get_categories(
    page: int = 1,
    pageSize: int = 10,
    status: Status = None,
    db: Session = Depends(get_db)
):
    """获取产品分类列表"""
    skip = (page - 1) * pageSize
    
    if status:
        result = product_category_dao.search(
            session=db,
            status=status,
            skip=skip,
            limit=pageSize
        )
    else:
        categories = product_category_dao.get_list(session=db, skip=skip, limit=pageSize)
        total = product_category_dao.count(session=db)
        result = {
            "total": total,
            "list": categories
        }
    
    # 转换数据格式
    categories = []
    for category in result["list"]:
        categories.append({
            "id": category.id,
            "name": category.name,
            "description": category.description,
            "image": category.image,
            "sort_order": category.sort_order,
            "parent_id": category.parent_id,
            "status": category.status,
            "created_at": category.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": category.updated_at.strftime("%Y-%m-%d %H:%M:%S")
        })
    
    return ProductCategoryListResponse(
        code=200,
        message="获取分类列表成功",
        data=ProductCategoryListData(
            total=result["total"],
            list=categories
        )
    )


@router.get("/active", response_model=ProductCategoryListResponse)
def get_active_categories(
    page: int = 1,
    pageSize: int = 10,
    db: Session = Depends(get_db)
):
    """获取活跃状态的产品分类列表"""
    skip = (page - 1) * pageSize
    categories = product_category_dao.get_active_categories(session=db, skip=skip, limit=pageSize)
    
    # 计算活跃分类的总数
    result = product_category_dao.search(
        session=db,
        status=Status.ACTIVE,
        skip=0,
        limit=1000  # 用一个大数来获取总数
    )
    total = result["total"]
    
    # 转换数据格式
    formatted_categories = []
    for category in categories:
        formatted_categories.append({
            "id": category.id,
            "name": category.name,
            "description": category.description,
            "image": category.image,
            "sort_order": category.sort_order,
            "parent_id": category.parent_id,
            "status": category.status,
            "created_at": category.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": category.updated_at.strftime("%Y-%m-%d %H:%M:%S")
        })
    
    return ProductCategoryListResponse(
        code=200,
        message="获取活跃分类列表成功",
        data=ProductCategoryListData(
            total=total,
            list=formatted_categories
        )
    )


@router.get("/parents", response_model=ProductCategoryListResponse)
def get_parent_categories(
    page: int = 1,
    pageSize: int = 10,
    db: Session = Depends(get_db)
):
    """获取所有父分类"""
    skip = (page - 1) * pageSize
    categories = product_category_dao.get_parent_categories(session=db, skip=skip, limit=pageSize)
    
    # 计算父分类的总数
    all_parents = product_category_dao.get_parent_categories(session=db, skip=0, limit=1000)
    total = len(all_parents)
    
    # 转换数据格式
    formatted_categories = []
    for category in categories:
        formatted_categories.append({
            "id": category.id,
            "name": category.name,
            "description": category.description,
            "image": category.image,
            "sort_order": category.sort_order,
            "parent_id": category.parent_id,
            "status": category.status,
            "created_at": category.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": category.updated_at.strftime("%Y-%m-%d %H:%M:%S")
        })
    
    return ProductCategoryListResponse(
        code=200,
        message="获取父分类列表成功",
        data=ProductCategoryListData(
            total=total,
            list=formatted_categories
        )
    )


@router.get("/children/{parent_id}", response_model=ProductCategoryListResponse)
def get_children_categories(
    parent_id: int,
    page: int = 1,
    pageSize: int = 10,
    db: Session = Depends(get_db)
):
    """获取指定父分类的子分类"""
    skip = (page - 1) * pageSize
    categories = product_category_dao.get_children_categories(session=db, parent_id=parent_id, skip=skip, limit=pageSize)
    
    # 计算子分类的总数
    all_children = product_category_dao.get_children_categories(session=db, parent_id=parent_id, skip=0, limit=1000)
    total = len(all_children)
    
    # 转换数据格式
    formatted_categories = []
    for category in categories:
        formatted_categories.append({
            "id": category.id,
            "name": category.name,
            "description": category.description,
            "image": category.image,
            "sort_order": category.sort_order,
            "parent_id": category.parent_id,
            "status": category.status,
            "created_at": category.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "updated_at": category.updated_at.strftime("%Y-%m-%d %H:%M:%S")
        })
    
    return ProductCategoryListResponse(
        code=200,
        message="获取子分类列表成功",
        data=ProductCategoryListData(
            total=total,
            list=formatted_categories
        )
    ) 