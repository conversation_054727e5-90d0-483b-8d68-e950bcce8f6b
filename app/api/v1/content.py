import os
import pprint
from typing import List, Dict, Optional

from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.dao.content import content_dao, article_dao, dish_dao
from app.dao.file import file_dao
from app.dao.product import product_dao
from app.schemas.content import (
    ContentCreate, ContentUpdate, ContentResponse, ContentWithFiles,
    ContentAndFiles, ArticleCreate, ArticleUpdate, ArticleResponse,
    ArticleWithFiles, ArticleListResponse, ArticleCreateResponse, ArticleUpdateResponse,
    ArticleWithFilesResponse, ArticleDeleteRequest, ArticleSearchRequest, ArticleSearchResponse,
    DishCreate, DishUpdate, DishResponse, DishListResponse, DishWithFilesReponse,
    DishDeleteRequest, ContentBatchStatusUpdate, DishCreateResponse, DishUpdateResponse,
    DishSearchRequest, DishSearchResponse
)
from app.schemas.file import FileResponse
from app.utils.file import save_uploaded_image

router = APIRouter()


# Content endpoints
@router.post("/", response_model=ContentResponse, status_code=status.HTTP_201_CREATED)
def create_content(content: ContentCreate, db: Session = Depends(get_db)):
    """创建内容"""
    return content_dao.create(session=db, content=content)


@router.get("/{content_id}", response_model=ContentWithFiles)
def read_content(content_id: int, db: Session = Depends(get_db)):
    """获取内容详情，包含关联的文件"""
    db_content = content_dao.get(session=db, content_id=content_id)
    if db_content is None:
        raise HTTPException(status_code=404, detail="内容不存在")
    return db_content


@router.get("/", response_model=List[ContentResponse])
def read_contents(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取内容列表"""
    return content_dao.get_list(session=db, skip=skip, limit=limit)


@router.get("/by/product/{product_id}/", response_model=List[ContentResponse])
def read_content_by_product(product_id: int, db: Session = Depends(get_db)):
    """获取产品相关的内容列表"""
    product = product_dao.get(session=db, product_id=product_id)
    if product is None:
        raise HTTPException(status_code=404, detail="产品不存在")
    return content_dao.get_by_product(session=db, product_id=product_id)


@router.put("/{content_id}", response_model=ContentResponse)
def update_content(content_id: int, content: ContentUpdate, db: Session = Depends(get_db)):
    """更新内容信息"""
    db_content = content_dao.update(session=db, content_id=content_id, content=content)
    if db_content is None:
        raise HTTPException(status_code=404, detail="内容不存在")
    return db_content


@router.delete("/{content_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_content(content_id: int, db: Session = Depends(get_db)):
    """删除内容"""
    success = content_dao.delete(session=db, content_id=content_id)
    if not success:
        raise HTTPException(status_code=404, detail="内容不存在")
    return {"message": "内容删除成功"}


# 向内容添加文件端点
@router.post("/add/files", status_code=status.HTTP_200_OK, response_model=Dict)
def add_files_to_content(binding: ContentAndFiles, db: Session = Depends(get_db)):
    """批量将文件绑定到内容"""
    result = content_dao.add_files(
        session=db,
        file_ids=binding.file_ids,
        content_id=binding.content_id
    )

    if not result["success"] and result["failed"]:
        raise HTTPException(status_code=404, detail="所有文件绑定失败")

    return {
        "message": "文件批量绑定到内容操作完成",
        "success_count": len(result["success"]),
        "failed_count": len(result["failed"]),
        "success_ids": result["success"],
        "failed_ids": result["failed"]
    }


@router.post("/remove/files", status_code=status.HTTP_200_OK, response_model=Dict)
def remove_files_from_content(binding: ContentAndFiles, db: Session = Depends(get_db)):
    """批量将文件从内容解绑"""
    result = content_dao.remove_files(
        session=db,
        file_ids=binding.file_ids,
        content_id=binding.content_id
    )

    if not result["success"] and result["failed"]:
        raise HTTPException(status_code=404, detail="所有文件解绑失败")

    return {
        "message": "文件批量从内容解绑操作完成",
        "success_count": len(result["success"]),
        "failed_count": len(result["failed"]),
        "success_ids": result["success"],
        "failed_ids": result["failed"]
    }


# 内容文件端点
@router.get("/{content_id}/files/", response_model=List[FileResponse])
def read_content_files(content_id: int, db: Session = Depends(get_db)):
    """获取内容关联的文件列表"""
    content = content_dao.get(session=db, content_id=content_id)
    if content is None:
        raise HTTPException(status_code=404, detail="内容不存在")
    return file_dao.get_by_content(session=db, content_id=content_id)


# Article endpoints
@router.post("/article/", response_model=ArticleCreateResponse, status_code=status.HTTP_201_CREATED)
async def create_article(
        name: str = Form(...),
        content: Optional[str] = Form(None),
        summary: Optional[str] = Form(None),
        sort_order: Optional[int] = Form(0),
        status: Optional[str] = Form("ACTIVE"),
        ad_link: Optional[str] = Form(None),
        image_file: Optional[UploadFile] = File(None),
        ad_image_file: Optional[UploadFile] = File(None),
        db: Session = Depends(get_db)
):
    """创建文章，支持图片上传"""
    article_data = {
        "name": name,
        "content": content,
        "summary": summary,
        "sort_order": sort_order,
        "status": status,
        "ad_link": ad_link
    }

    # 如果有上传主图片文件，处理文件
    if image_file:
        # 重置文件指针位置
        await image_file.seek(0)
        # 保存图片并获取URL
        image_url, thumbnail_url = save_uploaded_image(image_file)
        article_data["image"] = image_url
        article_data["thumbnail"] = thumbnail_url

    # 如果有上传广告图片文件，处理文件
    if ad_image_file:
        # 重置文件指针位置
        await ad_image_file.seek(0)
        # 保存广告图片并获取URL
        ad_image_url, _ = save_uploaded_image(ad_image_file)
        article_data["ad_image"] = ad_image_url

    # 创建文章
    article = ArticleCreate(**article_data)
    db_article = article_dao.create(session=db, article=article)

    return {
        "code": 200,
        "message": "创建文章信息成功",
        "data": db_article
    }


@router.get("/article/{article_id}", response_model=ArticleWithFilesResponse)
def read_article(article_id: int, db: Session = Depends(get_db)):
    """获取文章详情，包含关联的文件"""
    db_article = article_dao.get_with_files(session=db, article_id=article_id)
    if db_article is None:
        raise HTTPException(status_code=404, detail="文章不存在")
    return {
        "code": 200,
        "message": "获取文章详情成功",
        "data": db_article,
    }


@router.get("/article/", response_model=ArticleListResponse)
def read_articles(skip: int = 0, limit: int = 10, page: int = 0, pageSize: int = 10, db: Session = Depends(get_db)):
    """获取文章列表"""
    skip = max(0, page - 1) * pageSize
    limit = pageSize
    articles = article_dao.get_list(session=db, skip=skip, limit=limit)
    return {
        "code": 200,
        "message": "获取文章列表成功",
        "data": articles
    }


@router.get("/article/search/", response_model=ArticleSearchResponse)
def search_articles(request: ArticleSearchRequest = Depends(), db: Session = Depends(get_db)):
    """根据关键字搜索文章列表"""
    skip = max(0, request.page - 1) * request.pageSize
    limit = request.pageSize
    articles = article_dao.search(session=db, keyword=request.keyword, skip=skip, limit=limit)
    return {
        "code": 200,
        "message": "搜索文章列表成功",
        "data": articles
    }


@router.get("/article/by/product/{product_id}/", response_model=List[ArticleResponse])
def read_article_by_product(product_id: int, db: Session = Depends(get_db)):
    """获取产品相关的文章列表"""
    product = product_dao.get(session=db, product_id=product_id)
    if product is None:
        raise HTTPException(status_code=404, detail="产品不存在")
    return article_dao.get_by_product(session=db, product_id=product_id)


@router.put("/article/", response_model=ArticleUpdateResponse)
async def update_article(
        id: int = Form(None),
        name: Optional[str] = Form(None),
        content: Optional[str] = Form(None),
        summary: Optional[str] = Form(None),
        sort_order: Optional[int] = Form(None),
        status: Optional[str] = Form(None),
        ad_link: Optional[str] = Form(None),
        image_file: Optional[UploadFile] = File(None),
        ad_image_file: Optional[UploadFile] = File(None),
        db: Session = Depends(get_db)
):
    """更新文章信息，支持图片上传"""
    article_data = {}
    if name is not None:
        article_data["name"] = name
    if content is not None:
        article_data["content"] = content
    if summary is not None:
        article_data["summary"] = summary
    if sort_order is not None:
        article_data["sort_order"] = sort_order
    if status is not None:
        article_data["status"] = status
    if ad_link is not None:
        article_data["ad_link"] = ad_link

    # 如果有上传主图片文件，处理文件
    if image_file:
        # 重置文件指针位置
        await image_file.seek(0)
        # 保存图片并获取URL
        image_url, thumbnail_url = save_uploaded_image(image_file)
        article_data["image"] = image_url
        article_data["thumbnail"] = thumbnail_url

    # 如果有上传广告图片文件，处理文件
    if ad_image_file:
        # 重置文件指针位置
        await ad_image_file.seek(0)
        # 保存广告图片并获取URL
        ad_image_url, _ = save_uploaded_image(ad_image_file)
        article_data["ad_image"] = ad_image_url

    # 更新文章
    article = ArticleUpdate(**article_data)
    db_article = article_dao.update(session=db, article_id=id, article=article)
    if db_article is None:
        raise HTTPException(status_code=404, detail="文章不存在")

    return {
        "code": 200,
        "message": "更新文章信息成功",
        "data": article
    }


@router.delete("/article/{article_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_article(article_id: int, db: Session = Depends(get_db)):
    """删除文章"""
    success = article_dao.delete(session=db, article_id=article_id)
    if not success:
        raise HTTPException(status_code=404, detail="文章不存在")
    return {"message": "文章删除成功"}


@router.delete("/article/", status_code=status.HTTP_204_NO_CONTENT)
def delete_articles(data: ArticleDeleteRequest, db: Session = Depends(get_db)):
    """批量删除文章"""
    success = article_dao.delete_many(session=db, article_ids=data.ids)
    if not success:
        raise HTTPException(status_code=404, detail="文章不存在")
    return {"message": "文章删除成功"}


# Dish endpoints
@router.post("/dish/", response_model=DishCreateResponse, status_code=status.HTTP_201_CREATED)
async def create_dish(
        name: str = Form(...),
        content: Optional[str] = Form(None),
        sort_order: Optional[int] = Form(0),
        status: Optional[str] = Form("ACTIVE"),
        image_file: Optional[UploadFile] = File(None),
        db: Session = Depends(get_db)
):
    """创建菜品，支持图片上传"""
    dish_data = {
        "name": name,
        "content": content,
        "sort_order": sort_order,
        "status": status
    }

    # 如果有上传文件，处理文件
    if image_file:
        # 重置文件指针位置
        await image_file.seek(0)
        # 保存图片并获取URL
        image_url, thumbnail_url = save_uploaded_image(image_file)
        dish_data["image"] = image_url
        dish_data["thumbnail"] = thumbnail_url

    # 创建菜品
    dish = DishCreate(**dish_data)
    db_dish = dish_dao.create(session=db, dish=dish)

    return {
        "code": 200,
        "message": "创建菜品信息成功",
        "data": db_dish
    }


@router.get("/dish/{dish_id}", response_model=DishWithFilesReponse)
def read_dish(dish_id: int, db: Session = Depends(get_db)):
    """获取菜品详情，包含关联的文件"""
    db_dish = dish_dao.get_with_files(session=db, dish_id=dish_id)
    if db_dish is None:
        raise HTTPException(status_code=404, detail="菜品不存在")
    return {
        "code": 200,
        "message": "获取菜品详情成功",
        "data": db_dish,
    }


@router.get("/dish/", response_model=DishListResponse)
def read_dishes(skip: int = 0, limit: int = 10, page: int = 0, pageSize: int = 10, db: Session = Depends(get_db)):
    """获取菜品列表"""
    skip = max(0, page - 1) * pageSize
    limit = pageSize
    dishes = dish_dao.get_list(session=db, skip=skip, limit=limit)
    return {
        "code": 200,
        "message": "获取菜品列表成功",
        "data": dishes
    }


@router.get("/dish/search/", response_model=DishSearchResponse)
def search_dishes(request: DishSearchRequest = Depends(), db: Session = Depends(get_db)):
    """根据关键字搜索菜品列表"""
    skip = max(0, request.page - 1) * request.pageSize
    limit = request.pageSize
    dishes = dish_dao.search(session=db, keyword=request.keyword, skip=skip, limit=limit)
    return {
        "code": 200,
        "message": "搜索菜品列表成功",
        "data": dishes
    }


@router.get("/dish/by/product/{product_id}/", response_model=List[DishResponse])
def read_dish_by_product(product_id: int, db: Session = Depends(get_db)):
    """获取产品相关的菜品列表"""
    product = product_dao.get(session=db, product_id=product_id)
    if product is None:
        raise HTTPException(status_code=404, detail="产品不存在")
    return dish_dao.get_by_product(session=db, product_id=product_id)


@router.put("/dish/", response_model=DishUpdateResponse)
async def update_dish(
        id: int = Form(None),
        name: Optional[str] = Form(None),
        content: Optional[str] = Form(None),
        sort_order: Optional[int] = Form(None),
        status: Optional[str] = Form(None),
        image_file: Optional[UploadFile] = File(None),
        db: Session = Depends(get_db)
):
    """更新菜品信息，支持图片上传"""
    dish_data = {}
    if name is not None:
        dish_data["name"] = name
    if content is not None:
        dish_data["content"] = content
    if sort_order is not None:
        dish_data["sort_order"] = sort_order
    if status is not None:
        dish_data["status"] = status

    # 如果有上传文件，处理文件
    if image_file:
        # 重置文件指针位置
        await image_file.seek(0)
        # 保存图片并获取URL
        image_url, thumbnail_url = save_uploaded_image(image_file)
        dish_data["image"] = image_url
        dish_data["thumbnail"] = thumbnail_url

    # 更新菜品
    dish = DishUpdate(**dish_data)
    db_dish = dish_dao.update(session=db, dish_id=id, dish=dish)
    if db_dish is None:
        raise HTTPException(status_code=404, detail="菜品不存在")

    return {
        "code": 200,
        "message": "更新菜品信息成功",
        "data": dish
    }


@router.delete("/dish/{dish_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_dish(dish_id: int, db: Session = Depends(get_db)):
    """删除菜品"""
    success = dish_dao.delete(session=db, dish_id=dish_id)
    if not success:
        raise HTTPException(status_code=404, detail="菜品不存在")
    return {"message": "菜品删除成功"}


@router.delete("/dish/", status_code=status.HTTP_204_NO_CONTENT)
def delete_dish(data: DishDeleteRequest, db: Session = Depends(get_db)):
    """删除菜品"""
    success = dish_dao.delete_many(session=db, dish_ids=data.ids)
    if not success:
        raise HTTPException(status_code=404, detail="菜品不存在")
    return {"message": "菜品删除成功"}


# 批量设置内容状态
@router.post("/status/", status_code=status.HTTP_200_OK)
def batch_set_content_status(data: ContentBatchStatusUpdate, db: Session = Depends(get_db)):
    """批量设置内容状态"""
    if not data.content_ids:
        raise HTTPException(status_code=400, detail="内容ID列表不能为空")

    affected_count = content_dao.batch_set_status(
        session=db,
        content_ids=data.content_ids,
        status=data.status
    )

    return {
        "code": 200,
        "message": f"成功更新{affected_count}个内容的状态",
        "data": {
            "affected_count": affected_count
        }
    }
