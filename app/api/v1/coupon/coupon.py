from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.dao.coupon import cash_coupon_dao, full_reduction_coupon_dao
from app.dao.coupon import coupon_dao, discount_coupon_dao
from app.models.coupon import CouponType
from app.schemas.common import CommonResponse
from app.schemas.coupon import (
    CouponUpdate, CouponSearch, CouponListResponse,
    DiscountCouponCreate, CashCouponCreate, FullReductionCouponCreate, CouponListData,
    CouponResponse, FullReductionCouponResponse, CouponStatusUpdateRequest,
    CouponNameSearchResponse
)
from app.schemas.coupon import DiscountCouponResponse, CashCouponResponse

router = APIRouter()


@router.post("/create/", response_model=CommonResponse)
def create_coupon(
        *,
        db: Session = Depends(get_db),
        body: dict = Body(...),
) -> Any:
    """创建优惠券"""
    try:
        coupon_type = body.get("type", None)
        if coupon_type is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "code": 400,
                    "message": "缺少优惠券类型",
                    "data": None
                }
            )

        # 根据优惠券类型创建对应的优惠券
        if coupon_type == "discount":
            # 创建折扣券
            coupon_model = DiscountCouponCreate(**body)
            db_result = discount_coupon_dao.create(db, coupon_model)
            result = DiscountCouponResponse.model_validate(db_result)
        elif coupon_type == "cash":
            # 创建现金券
            coupon_model = CashCouponCreate(**body)
            db_result = cash_coupon_dao.create(db, coupon_model)
            result = CashCouponResponse.model_validate(db_result)
        elif coupon_type == "full_reduction":
            # 创建满减券
            coupon_model = FullReductionCouponCreate(**body)
            db_result = full_reduction_coupon_dao.create(db, coupon_model)
            result = FullReductionCouponResponse.model_validate(db_result)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "code": 400,
                    "message": f"不支持的优惠券创建类型: {str(coupon_type)}",
                    "data": None
                }
            )

        return {
            "code": 200,
            "message": f"创建{coupon_type}优惠券成功",
            "data": result.model_dump()
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"创建优惠券失败: {str(e)}",
                "data": None
            }
        )


@router.get("/view/{coupon_id}", response_model=CommonResponse)
def get_coupon(
        *,
        db: Session = Depends(get_db),
        coupon_id: int,
) -> Any:
    """获取优惠券详情"""
    try:
        # 先获取基础优惠券信息以确定类型
        base_coupon = coupon_dao.get(db, coupon_id)
        if not base_coupon:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "优惠券不存在",
                    "data": None
                }
            )

        # 根据优惠券类型使用对应的DAO获取完整信息
        if base_coupon.type == CouponType.DISCOUNT:
            coupon = discount_coupon_dao.get(db, coupon_id)
            result = DiscountCouponResponse.model_validate(coupon)
        elif base_coupon.type == CouponType.CASH:
            coupon = cash_coupon_dao.get(db, coupon_id)
            result = CashCouponResponse.model_validate(coupon)
        elif base_coupon.type == CouponType.FULL_REDUCTION:
            coupon = full_reduction_coupon_dao.get(db, coupon_id)
            result = FullReductionCouponResponse.model_validate(coupon)
        else:
            coupon = base_coupon
            result = CouponResponse.model_validate(coupon)

        return {
            "code": 200,
            "message": f"获取{base_coupon.type.value}优惠券详情成功",
            "data": result.model_dump()
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"获取优惠券详情失败: {str(e)}",
                "data": None
            }
        )


@router.put("/update/{coupon_id}", response_model=CommonResponse)
def update_coupon(
        *,
        db: Session = Depends(get_db),
        coupon_id: int,
        body: dict = Body(...),
) -> Any:
    """更新优惠券"""
    try:
        # 先获取优惠券基础信息
        coupon = coupon_dao.get(db, coupon_id)
        if not coupon:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "优惠券不存在",
                    "data": None
                }
            )

        # 根据优惠券类型更新信息
        if coupon.type == "discount":
            coupon_data = DiscountCouponCreate(**body)
            db_result = discount_coupon_dao.update(db, coupon_id, coupon_data)
            result = DiscountCouponResponse.model_validate(db_result)
        elif coupon.type == "cash":
            coupon_data = CashCouponCreate(**body)
            db_result = cash_coupon_dao.update(db, coupon_id, coupon_data)
            result = CashCouponResponse.model_validate(db_result)
        elif coupon.type == "full_reduction":
            coupon_data = FullReductionCouponCreate(**body)
            db_result = full_reduction_coupon_dao.update(db, coupon_id, coupon_data)
            result = FullReductionCouponResponse.model_validate(db_result)
        else:
            coupon_data = CouponUpdate(**body)
            db_result = coupon_dao.update(db, coupon_id, coupon_data)
            result = CouponResponse.model_validate(db_result)

        return {
            "code": 200,
            "message": f"更新{coupon.type}优惠券成功",
            "data": result.model_dump()
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"更新优惠券失败: {str(e)}",
                "data": None
            }
        )


@router.delete("/delete/{coupon_id}", response_model=CommonResponse)
def delete_coupon(
        *,
        db: Session = Depends(get_db),
        coupon_id: int,
) -> Any:
    """删除优惠券"""
    result = coupon_dao.delete(db, coupon_id)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "code": 404,
                "message": "优惠券不存在",
                "data": None
            }
        )
    return {
        "code": 200,
        "message": "删除成功",
        "data": None
    }


@router.post("/status/{coupon_id}", response_model=CommonResponse)
def update_coupon_status(
        *,
        db: Session = Depends(get_db),
        coupon_id: int,
        request: CouponStatusUpdateRequest,
) -> Any:
    """修改优惠券状态"""
    coupon = coupon_dao.update_status(db, coupon_id, request.status)
    if not coupon:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "code": 404,
                "message": "优惠券不存在",
                "data": None
            }
        )

    coupon_model = CouponResponse.model_validate(coupon)
    return {
        "code": 200,
        "message": "优惠券状态更新成功",
        "data": coupon_model.model_dump()
    }


@router.post("/search/", response_model=CouponListResponse)
def search_coupons(
        *,
        db: Session = Depends(get_db),
        search: CouponSearch,
) -> Any:
    """搜索优惠券"""
    skip = (search.page - 1) * search.pageSize
    result = coupon_dao.search(
        session=db,
        keyword=search.keyword,
        status=search.status,
        coupon_type=search.type,
        skip=skip,
        limit=search.pageSize
    )
    return {
        "code": 200,
        "message": "搜索成功",
        "data": CouponListData(
            total=result["total"],
            list=result["list"]
        )
    }


@router.get("/search/name", response_model=CouponNameSearchResponse)
def search_coupons_by_name(
        name: str,
        db: Session = Depends(get_db)
):
    """
    根据优惠券名称进行模糊搜索
    """
    coupons = coupon_dao.search_by_name(db, name)
    return {
        "code": 200,
        "message": "搜索成功",
        "data": coupons
    }
