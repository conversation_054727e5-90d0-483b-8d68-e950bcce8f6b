from typing import Any
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status, Body
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.dao.coupon import coupon_dao, coupon_batch_dao
from app.schemas.common import CommonResponse

router = APIRouter()


def serialize_coupon_batch(batch):
    """序列化优惠券批次对象为字典"""
    if not batch:
        return None

    try:
        return {
            "id": batch.id,
            "name": batch.name or "",
            "description": batch.description or "",
            "batch_number": batch.batch_number or 0,
            "quantity": batch.quantity or 0,
            "start_time": batch.start_time.isoformat() if batch.start_time else None,
            "end_time": batch.end_time.isoformat() if batch.end_time else None,
            "valid_duration": batch.valid_duration or 24,
            "coupon_id": batch.coupon_id,
            "status": batch.status.value if batch.status else 0,
            "distribution_channels": batch.distribution_channels or [],
            "distribution_quantity": batch.distribution_quantity or 0,
            "distribution_cycle": batch.distribution_cycle.value if batch.distribution_cycle else None,
            "receive_quantity": batch.receive_quantity or 0,
            "receive_cycle": batch.receive_cycle.value if batch.receive_cycle else None,
            "receive_start_time": batch.receive_start_time.isoformat() if batch.receive_start_time else None,
            "receive_end_time": batch.receive_end_time.isoformat() if batch.receive_end_time else None,
            "created_at": batch.created_at.isoformat() if batch.created_at else None,
            "updated_at": batch.updated_at.isoformat() if batch.updated_at else None
        }
    except Exception as e:
        print(f"Error serializing batch: {e}")
        return {
            "id": batch.id,
            "name": batch.name or "",
            "description": batch.description or "",
            "batch_number": batch.batch_number or 0,
            "quantity": batch.quantity or 0,
            "coupon_id": batch.coupon_id,
            "status": 1
        }


def serialize_coupon_batch_detail(batch):
    """序列化优惠券批次对象为字典"""
    if not batch:
        return None

    try:
        return {
            "id": batch.id,
            "name": batch.name or "",
            "description": batch.description or "",
            "batch_number": batch.batch_number or 0,
            "quantity": batch.quantity or 0,
            "start_time": batch.start_time.isoformat() if batch.start_time else None,
            "end_time": batch.end_time.isoformat() if batch.end_time else None,
            "valid_duration": batch.valid_duration or 0,
            "coupon_id": batch.coupon_id,

            "distribution_channels": batch.distribution_channels or [],
            "distribution_cycle": batch.distribution_cycle or None,
            "distribution_quantity": batch.distribution_quantity or 0,

            "receive_cycle": batch.receive_cycle or None,
            "receive_quantity": batch.receive_quantity or 0,
            "receive_start_time": batch.receive_start_time.isoformat() if batch.receive_start_time else None,
            "receive_end_time": batch.receive_end_time.isoformat() if batch.receive_end_time else None,

            "status": batch.status.value if batch.status else 0,
            "created_at": batch.created_at.isoformat() if batch.created_at else None,
            "updated_at": batch.updated_at.isoformat() if batch.updated_at else None
        }
    except Exception as e:
        print(f"Error serializing batch: {e}")
        return {
            "id": batch.id,
            "name": batch.name or "",
            "description": batch.description or "",
            "batch_number": batch.batch_number or 0,
            "quantity": batch.quantity or 0,
            "coupon_id": batch.coupon_id,
            "status": 1
        }


@router.post("/batches/search", response_model=CommonResponse)
def search_coupon_batches(
        *,
        db: Session = Depends(get_db),
        body: dict = Body(...),
) -> Any:
    """搜索优惠券批次"""
    try:
        page = body.get("page", 1)
        page_size = body.get("pageSize", 10)
        keyword = body.get("keyword", None)
        coupon_id = body.get("coupon_id", None)
        status_value = body.get("status", None)

        skip = (page - 1) * page_size

        # 使用简化的数据库查询
        from app.models.coupon import CouponBatch
        query = db.query(CouponBatch)

        # 按关键词搜索（批次名称）
        if keyword:
            query = query.filter(CouponBatch.name.contains(keyword))

        # 按优惠券ID筛选
        if coupon_id:
            query = query.filter(CouponBatch.coupon_id == coupon_id)

        # 按状态筛选
        if status_value is not None:
            from app.models.enum import Status
            if status_value == 1:
                query = query.filter(CouponBatch.status == Status.ACTIVE)
            else:
                query = query.filter(CouponBatch.status == Status.INACTIVE)

        # 获取总数
        total = query.count()

        # 分页查询
        batches = query.offset(skip).limit(page_size).all()

        # 手动序列化
        batch_list = []
        for batch in batches:
            batch_dict = serialize_coupon_batch(batch)
            if batch_dict:
                batch_list.append(batch_dict)

        return {
            "code": 200,
            "message": "搜索成功",
            "data": {
                "total": total,
                "list": batch_list
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"搜索优惠券批次失败: {str(e)}",
                "data": None
            }
        )


@router.get("/batches/view/{batch_id}", response_model=CommonResponse)
def get_coupon_batch(
        *,
        db: Session = Depends(get_db),
        batch_id: int,
) -> Any:
    """获取优惠券批次详情"""
    try:
        batch = coupon_batch_dao.get(db, batch_id)
        if not batch:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "优惠券批次不存在",
                    "data": None
                }
            )

        return {
            "code": 200,
            "message": "获取优惠券批次详情成功",
            "data": {
                "id": batch.id,
                "name": batch.name,
                "description": batch.description,
                "batch_number": batch.batch_number,
                "quantity": batch.quantity,
                "start_time": batch.start_time.isoformat() if batch.start_time else None,
                "end_time": batch.end_time.isoformat() if batch.end_time else None,
                "valid_duration": batch.valid_duration,
                "coupon_id": batch.coupon_id,
                "status": batch.status.value,
                "distribution_channels": batch.distribution_channels,
                "distribution_quantity": batch.distribution_quantity,
                "distribution_cycle": batch.distribution_cycle.value if batch.distribution_cycle else None,
                "receive_quantity": batch.receive_quantity,
                "receive_cycle": batch.receive_cycle.value if batch.receive_cycle else None,
                "receive_start_time": batch.receive_start_time.isoformat() if batch.receive_start_time else None,
                "receive_end_time": batch.receive_end_time.isoformat() if batch.receive_end_time else None,
                "created_at": batch.created_at.isoformat() if batch.created_at else None,
                "updated_at": batch.updated_at.isoformat() if batch.updated_at else None
            }
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"获取优惠券批次详情失败: {str(e)}",
                "data": None
            }
        )


@router.post("/batches/create", response_model=CommonResponse)
def create_coupon_batch(
        *,
        db: Session = Depends(get_db),
        body: dict = Body(...),
) -> Any:
    """创建优惠券批次"""
    try:
        # 验证必需字段
        required_fields = ["name", "coupon_id", "quantity", "start_time", "end_time"]
        for field in required_fields:
            if field not in body:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={
                        "code": 400,
                        "message": f"缺少必需字段: {field}",
                        "data": None
                    }
                )

        # 验证优惠券是否存在
        coupon = coupon_dao.get(db, body["coupon_id"])
        if not coupon:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "关联的优惠券不存在",
                    "data": None
                }
            )

        # 使用真实的数据库操作
        from app.models.coupon import CouponBatch
        from app.models.enum import Status
        from datetime import datetime

        # 创建批次对象
        batch_data = {
            "name": body.get("name"),
            "description": body.get("description", ""),
            "batch_number": body.get("batch_number"),
            "quantity": body.get("quantity"),
            "coupon_id": body.get("coupon_id"),
            "valid_duration": body.get("valid_duration", 0),
            "status": Status.ACTIVE if body.get("status", 1) == 1 else Status.INACTIVE,

            # 发放相关字段
            "distribution_channels": body.get("distribution_channels", []),
            "distribution_quantity": body.get("distribution_quantity", 0),
            "distribution_cycle": body.get("distribution_cycle", "per_day"),

            # 领取相关字段
            "receive_quantity": body.get("receive_quantity", 0),
            "receive_cycle": body.get("receive_cycle", "per_day"),
        }

        # 处理时间字段
        if body.get("start_time"):
            batch_data["start_time"] = datetime.fromisoformat(body.get("start_time").replace('Z', '+00:00'))
        if body.get("end_time"):
            batch_data["end_time"] = datetime.fromisoformat(body.get("end_time").replace('Z', '+00:00'))
        if body.get("receive_start_time"):
            batch_data["receive_start_time"] = datetime.fromisoformat(body.get("receive_start_time").replace('Z', '+00:00'))
        if body.get("receive_end_time"):
            batch_data["receive_end_time"] = datetime.fromisoformat(body.get("receive_end_time").replace('Z', '+00:00'))

        # 处理枚举字段
        from app.models.coupon import CouponUsageCycle
        if body.get("distribution_cycle"):
            batch_data["distribution_cycle"] = CouponUsageCycle(body.get("distribution_cycle"))
        if body.get("receive_cycle"):
            batch_data["receive_cycle"] = CouponUsageCycle(body.get("receive_cycle"))

        batch = CouponBatch(**batch_data)
        db.add(batch)
        db.commit()
        db.refresh(batch)

        # 手动序列化返回结果
        result_data = serialize_coupon_batch_detail(batch)

        return {
            "code": 200,
            "message": "创建优惠券批次成功",
            "data": result_data
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"创建优惠券批次失败: {str(e)}",
                "data": None
            }
        )


@router.put("/batches/update/{batch_id}", response_model=CommonResponse)
def update_coupon_batch(
        *,
        db: Session = Depends(get_db),
        batch_id: int,
        body: dict = Body(...),
) -> Any:
    """更新优惠券批次"""
    try:
        # 检查批次是否存在
        existing_batch = coupon_batch_dao.get(db, batch_id)
        if not existing_batch:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "优惠券批次不存在",
                    "data": None
                }
            )

        # 如果更新了coupon_id，验证优惠券是否存在
        if "coupon_id" in body:
            coupon = coupon_dao.get(db, body["coupon_id"])
            if not coupon:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "关联的优惠券不存在",
                        "data": None
                    }
                )

        # 处理时间字段和枚举字段
        update_data = dict(body)

        # 处理时间字段
        time_fields = ['start_time', 'end_time', 'receive_start_time', 'receive_end_time']
        for field in time_fields:
            if field in update_data and update_data[field]:
                try:
                    update_data[field] = datetime.fromisoformat(update_data[field].replace('Z', '+00:00'))
                except ValueError:
                    # 如果解析失败，保持原值
                    pass

        # 处理枚举字段
        from app.models.coupon import CouponUsageCycle, Status
        if 'distribution_cycle' in update_data:
            update_data['distribution_cycle'] = CouponUsageCycle(update_data['distribution_cycle'])
        if 'receive_cycle' in update_data:
            update_data['receive_cycle'] = CouponUsageCycle(update_data['receive_cycle'])
        if 'status' in update_data:
            update_data['status'] = Status.ACTIVE if update_data['status'] == 1 else Status.INACTIVE

        # 更新批次
        updated_batch = coupon_batch_dao.update(db, batch_id, **update_data)
        if not updated_batch:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "code": 400,
                    "message": "更新优惠券批次失败",
                    "data": None
                }
            )

        return {
            "code": 200,
            "message": "更新优惠券批次成功",
            "data": {
                "id": updated_batch.id,
                "name": updated_batch.name,
                "description": updated_batch.description,
                "batch_number": updated_batch.batch_number,
                "quantity": updated_batch.quantity,
                "coupon_id": updated_batch.coupon_id,
                "status": updated_batch.status.value,
                "start_time": updated_batch.start_time.isoformat() if updated_batch.start_time else None,
                "end_time": updated_batch.end_time.isoformat() if updated_batch.end_time else None,
                "valid_duration": updated_batch.valid_duration
            }
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"更新优惠券批次失败: {str(e)}",
                "data": None
            }
        )


@router.delete("/batches/delete/{batch_id}", response_model=CommonResponse)
def delete_coupon_batch(
        *,
        db: Session = Depends(get_db),
        batch_id: int,
) -> Any:
    """删除优惠券批次"""
    try:
        result = coupon_batch_dao.delete(db, batch_id)
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "优惠券批次不存在",
                    "data": None
                }
            )

        return {
            "code": 200,
            "message": "删除优惠券批次成功",
            "data": None
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"删除优惠券批次失败: {str(e)}",
                "data": None
            }
        )


@router.post("/batches/status/{batch_id}", response_model=CommonResponse)
def update_coupon_batch_status(
        *,
        db: Session = Depends(get_db),
        batch_id: int,
        body: dict = Body(...),
) -> Any:
    """更新优惠券批次状态"""
    try:
        status_value = body.get("status")
        if status_value is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "code": 400,
                    "message": "缺少状态参数",
                    "data": None
                }
            )

        batch = coupon_batch_dao.update_status(db, batch_id, status_value)
        if not batch:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "优惠券批次不存在",
                    "data": None
                }
            )

        return {
            "code": 200,
            "message": "优惠券批次状态更新成功",
            "data": {
                "id": batch.id,
                "name": batch.name,
                "status": batch.status.value
            }
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"更新优惠券批次状态失败: {str(e)}",
                "data": None
            }
        )


@router.get("/batches/coupon/{coupon_id}", response_model=CommonResponse)
def get_coupon_batches_by_coupon_id(
        *,
        db: Session = Depends(get_db),
        coupon_id: int,
) -> Any:
    """根据优惠券ID获取批次列表"""
    try:
        # 验证优惠券是否存在
        coupon = coupon_dao.get(db, coupon_id)
        if not coupon:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "优惠券不存在",
                    "data": None
                }
            )

        batches = coupon_batch_dao.get_by_coupon_id(db, coupon_id)

        # 转换为字典格式
        batch_list = []
        for batch in batches:
            batch_dict = {
                "id": batch.id,
                "name": batch.name,
                "description": batch.description,
                "batch_number": batch.batch_number,
                "quantity": batch.quantity,
                "start_time": batch.start_time.isoformat() if batch.start_time else None,
                "end_time": batch.end_time.isoformat() if batch.end_time else None,
                "valid_duration": batch.valid_duration,
                "coupon_id": batch.coupon_id,
                "status": batch.status.value,
                "created_at": batch.created_at.isoformat() if batch.created_at else None,
                "updated_at": batch.updated_at.isoformat() if batch.updated_at else None
            }
            batch_list.append(batch_dict)

        return {
            "code": 200,
            "message": "获取优惠券批次列表成功",
            "data": {
                "total": len(batch_list),
                "list": batch_list
            }
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"获取优惠券批次列表失败: {str(e)}",
                "data": None
            }
        )
