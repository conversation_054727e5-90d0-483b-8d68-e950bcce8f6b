from typing import Any
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status, Body
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.dao.coupon import coupon_usage_record_dao
from app.service.coupon import CouponService
from app.schemas.common import CommonResponse
from app.schemas.coupon import (
    CouponUsageRecordCreate, CouponUsageRecordResponse, CouponUsageRecordListResponse,
    CouponUsageRecordSearch, CouponUsageRecordBatchCreate, CouponUsageRecordBatchResponse,
    CouponListResponse, CouponListData
)

router = APIRouter()


@router.post("/coupon-usage-records", response_model=CouponUsageRecordResponse)
def create_usage_record(
        *,
        db: Session = Depends(get_db),
        record_in: CouponUsageRecordCreate,
) -> Any:
    """创建优惠券使用记录"""
    record = coupon_usage_record_dao.create(db, record_in)
    return {
        "code": 200,
        "message": "创建成功",
        "data": record
    }


@router.post("/coupon-usage-records/batch", response_model=CouponUsageRecordBatchResponse)
def batch_create_usage_records(
        *,
        db: Session = Depends(get_db),
        batch_request: CouponUsageRecordBatchCreate,
) -> Any:
    """批量创建优惠券使用记录"""
    try:
        if not batch_request.records:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "code": 400,
                    "message": "批量创建记录列表不能为空",
                    "data": None
                }
            )

        # 按照 coupon_id 和 coupon_batch_id 分组记录
        grouped_records = {}
        for record in batch_request.records:
            key = (record.coupon_id, record.coupon_batch_id)
            if key not in grouped_records:
                grouped_records[key] = []
            grouped_records[key].append(record.user_id)

        # 统计总数
        total_records = len(batch_request.records)
        success_count = 0
        failed_count = 0
        errors = []
        created_records = []

        # 对每个分组调用手动发放服务
        for (coupon_id, coupon_batch_id), user_ids in grouped_records.items():
            try:
                # 调用优惠券手动发放服务
                distribute_result = CouponService.distribute_coupons_manually(
                    session=db,
                    user_ids=user_ids,
                    coupon_id=coupon_id,
                    coupon_batch_id=coupon_batch_id
                )

                if distribute_result["success"]:
                    success_count += distribute_result["distributed_count"]
                    failed_count += distribute_result["failed_count"]

                    # 为成功发放的用户创建记录信息（模拟原有格式）
                    for user_id in user_ids:
                        if user_id not in distribute_result["failed_users"]:
                            created_records.append({
                                "id": None,  # 实际ID由数据库生成
                                "coupon_id": coupon_id,
                                "coupon_batch_id": coupon_batch_id,
                                "user_id": user_id,
                                "order_id": None,
                                "used_at": None,
                                "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                "status": "valid"
                            })

                    # 记录失败的用户
                    for failed_user_id in distribute_result["failed_users"]:
                        errors.append({
                            "user_id": failed_user_id,
                            "coupon_id": coupon_id,
                            "coupon_batch_id": coupon_batch_id,
                            "error": "发放失败"
                        })
                else:
                    # 整个分组发放失败
                    failed_count += len(user_ids)
                    for user_id in user_ids:
                        errors.append({
                            "user_id": user_id,
                            "coupon_id": coupon_id,
                            "coupon_batch_id": coupon_batch_id,
                            "error": distribute_result["message"]
                        })

            except Exception as e:
                # 分组发放异常
                failed_count += len(user_ids)
                for user_id in user_ids:
                    errors.append({
                        "user_id": user_id,
                        "coupon_id": coupon_id,
                        "coupon_batch_id": coupon_batch_id,
                        "error": f"发放异常: {str(e)}"
                    })

        # 构造返回结果（保持原有格式）
        result = {
            "success_count": success_count,
            "failed_count": failed_count,
            "total_count": total_records,
            "errors": errors,
            "created_records": created_records
        }

        # 根据结果返回相应的状态码和消息
        if result["failed_count"] == 0:
            message = f"批量创建成功，共创建 {result['success_count']} 条记录"
            code = 200
        elif result["success_count"] == 0:
            message = f"批量创建失败，共 {result['failed_count']} 条记录创建失败"
            code = 400
        else:
            message = f"批量创建部分成功，成功 {result['success_count']} 条，失败 {result['failed_count']} 条"
            code = 200

        return CouponUsageRecordBatchResponse(
            code=code,
            message=message,
            data=result
        )

    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "code": 500,
                "message": f"批量创建优惠券使用记录失败: {str(e)}",
                "data": None
            }
        )


@router.post("/coupon-usage-records/search", response_model=CouponUsageRecordListResponse)
def search_coupon_usage_records(
        *,
        db: Session = Depends(get_db),
        search_params: CouponUsageRecordSearch = Body(...),  # 改为请求体接收
        skip: int = 0,
        limit: int = 100,
) -> Any:
    """搜索优惠券使用记录"""
    result = coupon_usage_record_dao.search_records(
        session=db,
        search_params=search_params,
        skip=skip,
        limit=limit
    )

    # 转换为响应格式
    response_records = []
    for record in result["list"]:
        response_record = CouponUsageRecordResponse(
            id=record.id,
            user_id=record.user_id,
            username=record.user.username,
            coupon_id=record.coupon_id,
            coupon_name=record.coupon.name,
            order_id=record.order_id,
            discount_amount=record.discount_amount,
            distribution_channel=record.distribution_channel.value if record.distribution_channel else None,
            used_at=record.used_at,
            # status=record.status,
            created_at=record.created_at,
            updated_at=record.updated_at
        )
        response_records.append(response_record)

    return CouponUsageRecordListResponse(
        code=200,
        message="查询成功",
        data=response_records
    )


@router.get("/coupon-usage-records/order/{order_id}", response_model=CouponListResponse)
def get_order_usage_records(
        *,
        db: Session = Depends(get_db),
        order_id: int,
) -> Any:
    """获取订单的优惠券使用记录"""
    records = coupon_usage_record_dao.get_by_order(db, order_id)
    return {
        "code": 200,
        "message": "获取成功",
        "data": CouponListData(
            total=len(records),
            list=records
        )
    }


@router.get("/coupon-usage-records/user/{user_id}", response_model=CouponListResponse)
def get_user_usage_records(
        *,
        db: Session = Depends(get_db),
        user_id: int,
) -> Any:
    """获取用户的优惠券使用记录"""
    records = coupon_usage_record_dao.get_by_user(db, user_id)
    return {
        "code": 200,
        "message": "获取成功",
        "data": CouponListData(
            total=len(records),
            list=records
        )
    }
