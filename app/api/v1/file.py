from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.dao.content import content_dao
from app.dao.file import file_dao
from app.schemas.file import (
    FileCreate, FileUpdate, FileResponse
)

router = APIRouter()


# File endpoints
@router.post("/files/", response_model=FileResponse, status_code=status.HTTP_201_CREATED)
def create_file(file: FileCreate, db: Session = Depends(get_db)):
    """创建文件"""
    return file_dao.create(session=db, file=file)


@router.get("/{file_id}", response_model=FileResponse)
def read_file(file_id: int, db: Session = Depends(get_db)):
    """获取文件详情"""
    db_file = file_dao.get(session=db, file_id=file_id)
    if db_file is None:
        raise HTTPException(status_code=404, detail="文件不存在")
    return db_file


@router.get("/files/", response_model=List[FileResponse])
def read_files(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取文件列表"""
    return file_dao.get_list(session=db, skip=skip, limit=limit)


@router.get("/{content_id}/files/", response_model=List[FileResponse])
def read_content_files(content_id: int, db: Session = Depends(get_db)):
    """获取内容相关的文件列表"""
    content = content_dao.get(session=db, content_id=content_id)
    if content is None:
        raise HTTPException(status_code=404, detail="内容不存在")
    return file_dao.get_by_content(session=db, content_id=content_id)


@router.put("/{file_id}", response_model=FileResponse)
def update_file(file_id: int, file: FileUpdate, db: Session = Depends(get_db)):
    """更新文件信息"""
    db_file = file_dao.update(session=db, file_id=file_id, file=file)
    if db_file is None:
        raise HTTPException(status_code=404, detail="文件不存在")
    return db_file


@router.delete("/{file_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_file(file_id: int, db: Session = Depends(get_db)):
    """删除文件"""
    success = file_dao.delete(session=db, file_id=file_id)
    if not success:
        raise HTTPException(status_code=404, detail="文件不存在")
    return {"message": "文件删除成功"}
