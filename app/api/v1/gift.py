from typing import List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.dao.gift import (
    gift_rule_dao, order_gift_rule_dao
)
from app.schemas.gift import (
    GiftRuleCreate, GiftRuleUpdate, GiftRuleResponse,
    OrderGiftRuleCreate, OrderGiftRuleUpdate, OrderGiftRuleResponse,
    GiftRuleSearchRequest, GiftRuleListFormattedResponse,
    GiftRuleStatusUpdateRequest, BatchOperationResponse, OrderGiftRuleWithDetailsResponse,
    OrderGiftRuleSearchRequest
)

router = APIRouter()


@router.post("/gift-rules", response_model=GiftRuleResponse)
def create_gift_rule(
    *,
    db: Session = Depends(get_db),
    gift_rule_in: GiftRuleCreate
) -> dict:
    """
    创建赠送规则
    """
    gift_rule = gift_rule_dao.create(db, gift_rule_in)
    return {
        "code": 200,
        "message": "创建成功",
        "data": gift_rule_dao.to_dict(gift_rule)
    }


@router.get("/gift-rules/{gift_rule_id}", response_model=GiftRuleResponse)
def get_gift_rule(
    gift_rule_id: int,
    db: Session = Depends(get_db),
) -> dict:
    """
    获取赠送规则详情
    """
    gift_rule = gift_rule_dao.get(db, gift_rule_id)
    if not gift_rule:
        raise HTTPException(status_code=404, detail="赠送规则不存在")
    return {
        "code": 200,
        "message": "获取成功",
        "data": gift_rule_dao.to_dict(gift_rule)
    }


@router.put("/gift-rules/{gift_rule_id}", response_model=GiftRuleResponse)
def update_gift_rule(
    *,
    db: Session = Depends(get_db),
    gift_rule_id: int,
    gift_rule_in: GiftRuleUpdate
) -> dict:
    """
    更新赠送规则
    """
    gift_rule = gift_rule_dao.get(db, gift_rule_id)
    if not gift_rule:
        raise HTTPException(status_code=404, detail="赠送规则不存在")
    gift_rule = gift_rule_dao.update(db, gift_rule_id, gift_rule_in)
    return {
        "code": 200,
        "message": "更新成功",
        "data": gift_rule_dao.to_dict(gift_rule)
    }


@router.delete("/gift-rules/{gift_rule_id}")
def delete_gift_rule(
    *,
    db: Session = Depends(get_db),
    gift_rule_id: int
) -> dict:
    """
    删除赠送规则
    """
    gift_rule = gift_rule_dao.get(db, gift_rule_id)
    if not gift_rule:
        raise HTTPException(status_code=404, detail="赠送规则不存在")
    gift_rule_dao.delete(db, gift_rule_id)
    return {
        "code": 200,
        "message": "删除成功",
        "data": None
    }


@router.get("/gift-rules", response_model=GiftRuleListFormattedResponse)
def search_gift_rules(
    *,
    db: Session = Depends(get_db),
    search_params: GiftRuleSearchRequest = Depends()
) -> dict:
    """
    搜索赠送规则
    """
    skip = (search_params.page - 1) * search_params.pageSize
    result = gift_rule_dao.search(
        db,
        keyword=search_params.keyword,
        name=search_params.name,
        status=search_params.status,
        gift_type=search_params.type,
        skip=skip,
        limit=search_params.pageSize
    )
    return {
        "code": 200,
        "message": "查询成功",
        "data": {
            "list": [gift_rule_dao.to_dict(item) for item in result["list"]],
            "total": result["total"],
            "page": search_params.page,
            "pageSize": search_params.pageSize
        }
    }


@router.put("/gift-rules/{gift_rule_id}/status")
def update_gift_rule_status(
    *,
    db: Session = Depends(get_db),
    gift_rule_id: int,
    status_update: GiftRuleStatusUpdateRequest
) -> dict:
    """
    更新赠送规则状态
    """
    gift_rule = gift_rule_dao.get(db, gift_rule_id)
    if not gift_rule:
        raise HTTPException(status_code=404, detail="赠送规则不存在")
    gift_rule = gift_rule_dao.update(db, gift_rule_id, {"status": status_update.status})
    return {
        "code": 200,
        "message": "状态更新成功",
        "data": gift_rule_dao.to_dict(gift_rule)
    }


# 订单赠送规则相关API
@router.post("/order-gift-rules", response_model=GiftRuleListFormattedResponse)
def create_order_gift_rule(
    *,
    db: Session = Depends(get_db),
    order_gift_rule_in: OrderGiftRuleCreate
) -> dict:
    """
    创建订单赠送规则
    """
    order_gift_rule = order_gift_rule_dao.create(db, order_gift_rule_in)
    return {
        "code": 200,
        "message": "创建成功",
        "data": order_gift_rule_dao.to_dict(order_gift_rule)
    }


@router.get("/order-gift-rules/{rule_id}", response_model=OrderGiftRuleWithDetailsResponse)
def get_order_gift_rule(
    rule_id: int,
    db: Session = Depends(get_db),
) -> dict:
    """
    获取订单赠送规则详情
    """
    import logging
    logger = logging.getLogger(__name__)
    
    order_gift_rule = order_gift_rule_dao.get(db, rule_id)
    if not order_gift_rule:
        raise HTTPException(status_code=404, detail="订单赠送规则不存在")
    
    # 记录数据转换前的状态
    logger.info("Before conversion:")
    logger.info(f"Order product rels: {order_gift_rule.order_product_rels}")
    logger.info(f"Gift product rels: {order_gift_rule.gift_product_rels}")
    
    # 转换数据
    data = order_gift_rule_dao.to_dict(order_gift_rule)
    
    # 记录转换后的数据
    logger.info("After conversion:")
    logger.info(f"Converted data: {data}")
    
    return {
        "code": 200,
        "message": "获取成功",
        "data": data
    }


@router.get("/order-gift-rules", response_model=GiftRuleListFormattedResponse)
def search_order_gift_rules(
    *,
    db: Session = Depends(get_db),
    search_params: OrderGiftRuleSearchRequest = Depends()
) -> dict:
    """
    搜索订单赠送规则
    """
    skip = (search_params.page - 1) * search_params.pageSize
    result = order_gift_rule_dao.search(
        db,
        keyword=search_params.keyword,
        name=search_params.name,
        status=search_params.status,
        order_type=search_params.order_type,
        skip=skip,
        limit=search_params.pageSize
    )
    return {
        "code": 200,
        "message": "查询成功",
        "data": {
            "list": [order_gift_rule_dao.to_dict(item) for item in result["list"]],
            "total": result["total"],
            "page": search_params.page,
            "pageSize": search_params.pageSize
        }
    }


@router.put("/order-gift-rules/{rule_id}", response_model=OrderGiftRuleWithDetailsResponse)
def update_order_gift_rule(
    *,
    db: Session = Depends(get_db),
    rule_id: int,
    order_gift_rule_in: OrderGiftRuleUpdate
) -> dict:
    """
    更新订单赠送规则
    """
    order_gift_rule = order_gift_rule_dao.get(db, rule_id)
    if not order_gift_rule:
        raise HTTPException(status_code=404, detail="订单赠送规则不存在")
    
    # 更新基本信息
    order_gift_rule = order_gift_rule_dao.update(db, rule_id, order_gift_rule_in)
    
    # 如果提供了订单商品列表，更新订单商品关联
    if order_gift_rule_in.order_products is not None:
        # 先删除现有关联
        order_gift_rule_dao.remove_order_products(db, rule_id, [rel.order_product_id for rel in order_gift_rule.order_product_rels])
        # 添加新关联
        if order_gift_rule_in.order_products:
            order_gift_rule_dao.add_order_products(db, rule_id, [{"product_id": pid, "quantity": 1} for pid in order_gift_rule_in.order_products])
    
    # 如果提供了赠送商品列表，更新赠送商品关联
    if order_gift_rule_in.gift_products is not None:
        # 先删除现有关联
        order_gift_rule_dao.remove_gift_products(db, rule_id, [rel.gift_product_id for rel in order_gift_rule.gift_product_rels])
        # 添加新关联
        if order_gift_rule_in.gift_products:
            order_gift_rule_dao.add_gift_products(db, rule_id, [{"product_id": pid, "quantity": 1} for pid in order_gift_rule_in.gift_products])
    
    # 重新获取更新后的数据（包含关联信息）
    updated_rule = order_gift_rule_dao.get(db, rule_id)
    return {
        "code": 200,
        "message": "更新成功",
        "data": order_gift_rule_dao.to_dict(updated_rule)
    }


@router.delete("/order-gift-rules/{rule_id}")
def delete_order_gift_rule(
    *,
    db: Session = Depends(get_db),
    rule_id: int
) -> dict:
    """
    删除赠送规则
    """
    order_gift_rule = order_gift_rule_dao.get(db, rule_id)
    if not order_gift_rule:
        raise HTTPException(status_code=404, detail="赠送规则不存在")
    order_gift_rule_dao.delete(db, rule_id)
    return {
        "code": 200,
        "message": "删除成功",
        "data": None
    }


@router.post("/order-gift-rules/{rule_id}/order-products", response_model=BatchOperationResponse)
def add_order_products(
    *,
    db: Session = Depends(get_db),
    rule_id: int,
    product_quantities: List[dict]
) -> dict:
    """
    添加订单商品到赠送规则
    """
    result = order_gift_rule_dao.add_order_products(db, rule_id, product_quantities)
    return {
        "code": 200,
        "message": "添加成功",
        "data": {
            "success_count": len(result["success"]),
            "failed_count": len(result["failed"]),
            "success_ids": [item["product_id"] for item in result["success"]],
            "failed_ids": [item["product_id"] for item in result["failed"]]
        }
    }


@router.post("/order-gift-rules/{rule_id}/gift-products", response_model=BatchOperationResponse)
def add_gift_products(
    *,
    db: Session = Depends(get_db),
    rule_id: int,
    product_quantities: List[dict]
) -> dict:
    """
    添加赠送商品到赠送规则
    """
    result = order_gift_rule_dao.add_gift_products(db, rule_id, product_quantities)
    return {
        "code": 200,
        "message": "添加成功",
        "data": {
            "success_count": len(result["success"]),
            "failed_count": len(result["failed"]),
            "success_ids": [item["product_id"] for item in result["success"]],
            "failed_ids": [item["product_id"] for item in result["failed"]]
        }
    }


@router.delete("/order-gift-rules/{rule_id}/order-products", response_model=BatchOperationResponse)
def remove_order_products(
    *,
    db: Session = Depends(get_db),
    rule_id: int,
    product_ids: List[int]
) -> dict:
    """
    从赠送规则中移除订单商品
    """
    result = order_gift_rule_dao.remove_order_products(db, rule_id, product_ids)
    return {
        "code": 200,
        "message": "移除成功",
        "data": {
            "success_count": len(result["success"]),
            "failed_count": len(result["failed"]),
            "success_ids": result["success"],
            "failed_ids": result["failed"]
        }
    }


@router.delete("/order-gift-rules/{rule_id}/gift-products", response_model=BatchOperationResponse)
def remove_gift_products(
    *,
    db: Session = Depends(get_db),
    rule_id: int,
    product_ids: List[int]
) -> dict:
    """
    从赠送规则中移除赠送商品
    """
    result = order_gift_rule_dao.remove_gift_products(db, rule_id, product_ids)
    return {
        "code": 200,
        "message": "移除成功",
        "data": {
            "success_count": len(result["success"]),
            "failed_count": len(result["failed"]),
            "success_ids": result["success"],
            "failed_ids": result["failed"]
        }
    }


@router.put("/order-gift-rules/{rule_id}/status", response_model=OrderGiftRuleWithDetailsResponse)
def update_order_gift_rule_status(
    *,
    db: Session = Depends(get_db),
    rule_id: int,
    status_update: GiftRuleStatusUpdateRequest
) -> dict:
    """
    更新订单赠送规则状态
    """
    order_gift_rule = order_gift_rule_dao.get(db, rule_id)
    if not order_gift_rule:
        raise HTTPException(status_code=404, detail="订单赠送规则不存在")
    
    # 创建更新对象
    update_data = OrderGiftRuleUpdate(status=status_update.status)
    order_gift_rule = order_gift_rule_dao.update(db, rule_id, update_data)
    
    return {
        "code": 200,
        "message": "状态更新成功",
        "data": order_gift_rule_dao.to_dict(order_gift_rule)
    }