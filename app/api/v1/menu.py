from typing import Dict, Any, List, Optional
from datetime import datetime, date

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.dao.menu import menu_dao
from app.models.enum import Status
from app.schemas.menu import (
    MenuCreate, MenuUpdate, MenuResponse, MenuCreateResponse,
    MenuDetailResponse, MenuUpdateResponse, MenuListResponse,
    MenuListData, MenuDeleteRequest, MenuDeleteResponse,
    MenuSearchRequest, MenuSearchResponse, MenuResponseWithDiningRule,
    MenuSearchListData, MenuListWithDiningRuleResponse, MenuWithContentsDetailResponse,
    MenuStatusUpdate, MenuStatusUpdateResponse
)
from app.utils.logger import logger

router = APIRouter()


@router.post("/", response_model=MenuCreateResponse)
async def create_menu(menu: MenuCreate, db: Session = Depends(get_db)) -> Dict[str, Any]:
    """创建菜单
    
    Args:
        menu: 菜单创建数据
        db: 数据库会话
        
    Returns:
        包含创建的菜单信息的响应
    """
    try:
        logger.info(f"开始创建菜单: {menu.model_dump()}")
        menu_obj = menu_dao.create(db, menu)

        return {
            "code": 200,
            "message": "创建菜单成功",
            "data": menu_obj
        }
    except Exception as e:
        logger.error(f"创建菜单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建菜单失败: {str(e)}")


@router.post("/search", response_model=MenuSearchResponse)
async def search_menus(
        keyword: Optional[str] = None,
        name: Optional[str] = None,
        product_id: Optional[int] = None,
        rule_id: Optional[int] = None,
        status: Optional[Status] = None,
        page: int = Query(1, description="页码，从1开始"),
        pageSize: int = Query(10, description="每页记录数"),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """搜索菜单 (POST方法)
    
    Args:
        request: 搜索菜单请求
        db: 数据库会话
        
    Returns:
        包含搜索结果的响应
    """
    try:
        logger.info(f"开始搜索菜单，关键词: {keyword}")
        skip = max(0, page - 1) * pageSize
        limit = pageSize

        # 使用优化后的搜索方法
        search_result = menu_dao.search(
            db,
            keyword=keyword,
            name=name,
            product_id=product_id,
            rule_id=rule_id,
            status=status,
            skip=skip,
            limit=limit
        )

        return {
            "code": 200,
            "message": "搜索菜单成功",
            "data": search_result
        }
    except Exception as e:
        logger.error(f"搜索菜单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索菜单失败: {str(e)}")


@router.get("/search", response_model=MenuSearchResponse)
async def search_menus_get(
        keyword: Optional[str] = None,
        name: Optional[str] = None,
        product_id: Optional[int] = None,
        rule_id: Optional[int] = None,
        status: Optional[Status] = None,
        page: int = Query(1, description="页码，从1开始"),
        pageSize: int = Query(10, description="每页记录数"),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """搜索菜单 (GET方法)
    
    Args:
        keyword: 搜索关键词，可搜索菜单名称、菜单描述等
        name: 菜单名称（模糊匹配）
        product_id: 按产品ID筛选
        rule_id: 按规则ID筛选
        status: 按状态筛选
        page: 页码，从1开始
        pageSize: 每页记录数
        db: 数据库会话
        
    Returns:
        包含搜索结果的响应
    """
    try:
        # 计算跳过的记录数
        skip = max(0, page - 1) * pageSize
        limit = pageSize

        # 使用优化后的搜索方法
        search_result = menu_dao.search(
            db,
            keyword=keyword,
            name=name,
            product_id=product_id,
            rule_id=rule_id,
            status=status,
            skip=skip,
            limit=limit
        )

        return {
            "code": 200,
            "message": "搜索菜单成功",
            "data": search_result
        }
    except Exception as e:
        logger.error(f"搜索菜单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"搜索菜单失败: {str(e)}")


@router.get("/{menu_id}", response_model=MenuDetailResponse)
async def get_menu(menu_id: int, db: Session = Depends(get_db)) -> Dict[str, Any]:
    """获取菜单详情
    
    Args:
        menu_id: 菜单ID
        db: 数据库会话
        
    Returns:
        包含菜单详情的响应，包括相关的餐厅预订规则信息（如果关联的规则是餐厅预订规则）
    """
    try:
        logger.info(f"开始获取菜单，ID: {menu_id}")
        menu_obj = menu_dao.get(db, menu_id)

        if not menu_obj:
            logger.error(f"菜单不存在，ID: {menu_id}")
            raise HTTPException(status_code=404, detail=f"菜单不存在，ID: {menu_id}")

        # 如果关联了规则，获取规则的详细信息
        if menu_obj.rule_id:
            rule_data = menu_dao.get_rule_data(db, menu_obj.rule_id)
            # 将规则数据合并到菜单对象中
            for key, value in rule_data.items():
                setattr(menu_obj, key, value)

        return {
            "code": 200,
            "message": "获取菜单成功",
            "data": menu_obj
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取菜单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取菜单失败: {str(e)}")


@router.get("/{menu_id}/with/contents", response_model=MenuWithContentsDetailResponse)
async def get_menu_with_contents(menu_id: int, db: Session = Depends(get_db)) -> Dict[str, Any]:
    """获取菜单详情及其关联的内容详情
    
    Args:
        menu_id: 菜单ID
        db: 数据库会话
        
    Returns:
        包含菜单详情及其关联内容的响应，包括相关的餐厅预订规则信息（如果关联的规则是餐厅预订规则）
    """
    try:
        logger.info(f"开始获取菜单及内容详情，ID: {menu_id}")
        menu_obj = menu_dao.get_menu_with_contents(db, menu_id)

        if not menu_obj:
            logger.error(f"菜单不存在，ID: {menu_id}")
            raise HTTPException(status_code=404, detail=f"菜单不存在，ID: {menu_id}")

        return {
            "code": 200,
            "message": "获取菜单及内容详情成功",
            "data": menu_obj
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取菜单及内容详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取菜单及内容详情失败: {str(e)}")


@router.get("/list", response_model=MenuListResponse)
async def list_menus(
        skip: int = 0,
        limit: int = 10,
        product_id: Optional[int] = None,
        rule_id: Optional[int] = None,
        status: Optional[Status] = None,
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取菜单列表
    
    Args:
        skip: 跳过的记录数
        limit: 限制返回的记录数
        product_id: 按产品ID筛选
        rule_id: 按规则ID筛选
        status: 按状态筛选
        db: 数据库会话
        
    Returns:
        包含菜单列表的响应
    """
    try:
        logger.info(f"开始获取菜单列表")

        # 获取总数
        total = menu_dao.count(db, product_id=product_id, rule_id=rule_id, status=status)

        # 获取列表，现在直接返回字典列表，包含产品和规则名称
        menus = menu_dao.get_list(
            db,
            skip=skip,
            limit=limit,
            product_id=product_id,
            rule_id=rule_id,
            status=status
        )

        return {
            "code": 200,
            "message": "获取菜单列表成功",
            "data": {
                "total": total,
                "list": menus
            }
        }
    except Exception as e:
        logger.error(f"获取菜单列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取菜单列表失败: {str(e)}")


@router.put("/{menu_id}", response_model=MenuUpdateResponse)
async def update_menu(
        menu_id: int,
        menu: MenuUpdate,
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """更新菜单
    
    Args:
        menu_id: 菜单ID
        menu: 菜单更新数据
        db: 数据库会话
        
    Returns:
        包含更新后的菜单信息的响应
    """
    try:
        logger.info(f"开始更新菜单，ID: {menu_id}")

        menu_obj = menu_dao.update(db, menu_id, menu)

        if not menu_obj:
            logger.error(f"菜单不存在，ID: {menu_id}")
            raise HTTPException(status_code=404, detail=f"菜单不存在，ID: {menu_id}")

        return {
            "code": 200,
            "message": "更新菜单成功",
            "data": menu_obj
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新菜单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新菜单失败: {str(e)}")


@router.put("/status/{menu_id}", response_model=MenuStatusUpdateResponse)
async def update_menu_status(
        menu_id: int,
        status_update: MenuStatusUpdate,
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """更新菜单状态
    
    Args:
        menu_id: 菜单ID
        status_update: 状态更新数据
        db: 数据库会话
        
    Returns:
        包含更新后的菜单信息的响应
    """
    try:
        logger.info(f"开始更新菜单状态，ID: {menu_id}, 新状态: {status_update.status}")

        menu_obj = menu_dao.update_status(db, menu_id, status_update.status)

        if not menu_obj:
            logger.error(f"菜单不存在，ID: {menu_id}")
            raise HTTPException(status_code=404, detail=f"菜单不存在，ID: {menu_id}")

        return {
            "code": 200,
            "message": "更新菜单状态成功",
            "data": menu_obj
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新菜单状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新菜单状态失败: {str(e)}")


@router.delete("/{menu_id}", response_model=MenuDeleteResponse)
async def delete_menu(menu_id: int, db: Session = Depends(get_db)) -> Dict[str, Any]:
    """删除菜单
    
    Args:
        menu_id: 菜单ID
        db: 数据库会话
        
    Returns:
        删除操作结果的响应
    """
    try:
        logger.info(f"开始删除菜单，ID: {menu_id}")

        result = menu_dao.delete(db, menu_id)

        if not result:
            logger.error(f"菜单不存在或删除失败，ID: {menu_id}")
            raise HTTPException(status_code=404, detail=f"菜单不存在或删除失败，ID: {menu_id}")

        return {
            "code": 200,
            "message": "删除菜单成功",
            "data": {"success": True}
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除菜单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除菜单失败: {str(e)}")


@router.post("/batch-delete", response_model=MenuDeleteResponse)
async def batch_delete_menus(
        request: MenuDeleteRequest,
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """批量删除菜单
    
    Args:
        request: 包含要删除的菜单ID列表的请求
        db: 数据库会话
        
    Returns:
        删除操作结果的响应
    """
    try:
        logger.info(f"开始批量删除菜单，IDs: {request.ids}")

        if not request.ids:
            return {
                "code": 200,
                "message": "没有需要删除的菜单",
                "data": {"success": True}
            }

        success_count = 0
        for menu_id in request.ids:
            if menu_dao.delete(db, menu_id):
                success_count += 1

        return {
            "code": 200,
            "message": f"成功删除 {success_count}/{len(request.ids)} 个菜单",
            "data": {
                "success": True,
                "success_count": success_count,
                "total_count": len(request.ids)
            }
        }
    except Exception as e:
        logger.error(f"批量删除菜单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量删除菜单失败: {str(e)}")


@router.get("/by-date", response_model=MenuListWithDiningRuleResponse)
async def get_menus_by_date(
        product_id: int,
        target_date: date = Query(None, description="目标日期，格式：YYYY-MM-DD"),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取指定日期的有效菜单
    
    Args:
        product_id: 产品ID
        target_date: 目标日期
        db: 数据库会话
        
    Returns:
        包含菜单列表的响应，包含餐厅预订规则详细信息
    """
    try:
        logger.info(f"开始获取指定日期的菜单，产品ID: {product_id}, 日期: {target_date}")

        if not target_date:
            target_date = datetime.now().date()

        # 转换为datetime对象
        target_datetime = datetime.combine(target_date, datetime.min.time())

        # 获取菜单列表，现在直接返回字典列表，包含产品和规则名称以及餐厅预订规则属性
        menus = menu_dao.get_active_menu_for_date(db, product_id, target_datetime)

        return {
            "code": 200,
            "message": "获取指定日期菜单成功",
            "data": {
                "total": len(menus),
                "list": menus
            }
        }
    except Exception as e:
        logger.error(f"获取指定日期菜单失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取指定日期菜单失败: {str(e)}")
