from typing import List

from fastapi import APIRouter, Depends, HTTPException, Path
from sqlalchemy.orm import Session
from starlette import status

from app.core.deps import get_db
from app.dao.order import order_dao, order_item_dao
from app.models.order import PaymentMethod
from app.schemas.order import (
    OrderCreate, OrderUpdate, OrderResponse, OrderDetailResponse,
    OrderItemCreate, OrderItemUpdate, OrderItemResponse,
    OrderStatusUpdate, OrderPaymentStatusUpdate, OrderRequest, CreateOrderResponse
)
from app.service.order import order_service

router = APIRouter()


# 添加新的订单创建端点
@router.post("/create", response_model=CreateOrderResponse, status_code=status.HTTP_201_CREATED)
def create_new_order(order_request: OrderRequest, db: Session = Depends(get_db)):
    """创建订单

    按照流程图处理订单创建:
    1. 获取订购产品列表
    2. 生成订单对象
    3. 遍历订购产品列表生成订单项
    4. 计算订单价格
    5. 应用价格策略
    6. 提交订单

    Args:
        order_request: 订单请求对象
        db: 数据库会话

    Returns:
        订单创建响应
    """
    try:
        # 调用服务层处理订单创建
        products = [item.model_dump() for item in order_request.products]
        order = order_service.create_order(
            session=db,
            user_id=order_request.user_id,
            products=products
        )
        return CreateOrderResponse(
            success=True,
            message="订单创建成功",
            order=order
        )
    except ValueError as e:
        return CreateOrderResponse(
            success=False,
            message=str(e)
        )
    except Exception as e:
        # 捕获其他异常
        return CreateOrderResponse(
            success=False,
            message=f"订单创建失败: {str(e)}"
        )


@router.patch("/{order_id}/pay", response_model=OrderDetailResponse)
def pay_order(
    order_id: int = Path(..., title="要支付的订单ID"),
    payment_method: PaymentMethod = PaymentMethod.ACCOUNT_BALANCE,
    db: Session = Depends(get_db)
):
    """支付订单

    Args:
        order_id: 订单ID
        payment_method: 支付方式
        db: 数据库会话

    Returns:
        支付后的订单信息
    """
    try:
        updated_order = order_service.pay_order(
            session=db,
            order_id=order_id,
            payment_method=payment_method
        )
        if not updated_order:
            raise HTTPException(status_code=404, detail="订单不存在")
        return updated_order
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.patch("/{order_id}/cancel", response_model=OrderDetailResponse)
def cancel_order(
    order_id: int = Path(..., title="要取消的订单ID"),
    db: Session = Depends(get_db)
):
    """取消订单

    Args:
        order_id: 订单ID
        db: 数据库会话

    Returns:
        取消后的订单信息
    """
    try:
        cancelled_order = order_service.cancel_order(
            session=db,
            order_id=order_id
        )
        if not cancelled_order:
            raise HTTPException(status_code=404, detail="订单不存在")
        return cancelled_order
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


# Order endpoints
@router.post("/", response_model=OrderDetailResponse, status_code=status.HTTP_201_CREATED)
def create_order(order: OrderCreate, db: Session = Depends(get_db)):
    """创建订单"""
    return order_dao.create(session=db, order=order)


@router.get("/{order_id}", response_model=OrderDetailResponse)
def read_order(order_id: int = Path(..., title="要获取的订单ID"), db: Session = Depends(get_db)):
    """获取订单详情"""
    db_order = order_dao.get(session=db, order_id=order_id)
    if db_order is None:
        raise HTTPException(status_code=404, detail="订单不存在")
    return db_order


@router.get("/", response_model=List[OrderResponse])
def read_orders(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取订单列表"""
    return order_dao.get_list(session=db, skip=skip, limit=limit)


@router.put("/{order_id}", response_model=OrderResponse)
def update_order(
    order: OrderUpdate,
    order_id: int = Path(..., title="要更新的订单ID"),
    db: Session = Depends(get_db)
):
    """更新订单"""
    db_order = order_dao.update(session=db, order_id=order_id, order=order)
    if db_order is None:
        raise HTTPException(status_code=404, detail="订单不存在")
    return db_order


@router.delete("/{order_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_order(order_id: int = Path(..., title="要删除的订单ID"), db: Session = Depends(get_db)):
    """删除订单"""
    success = order_dao.delete(session=db, order_id=order_id)
    if not success:
        raise HTTPException(status_code=404, detail="订单不存在")
    return {"message": "订单删除成功"}


@router.get("/user/{user_id}", response_model=List[OrderResponse])
def get_user_orders(
    user_id: int = Path(..., title="用户ID"),
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取用户的订单列表"""
    return order_dao.get_by_user(session=db, user_id=user_id, skip=skip, limit=limit)


@router.patch("/{order_id}/status", response_model=OrderResponse)
def update_order_status(
    status_update: OrderStatusUpdate,
    order_id: int = Path(..., title="要更新状态的订单ID"),
    db: Session = Depends(get_db)
):
    """更新订单状态"""
    db_order = order_dao.update_status(session=db, order_id=order_id, status=status_update.status)
    if db_order is None:
        raise HTTPException(status_code=404, detail="订单不存在")
    return db_order


@router.patch("/{order_id}/payment-status", response_model=OrderResponse)
def update_order_payment_status(
    payment_status_update: OrderPaymentStatusUpdate,
    order_id: int = Path(..., title="要更新支付状态的订单ID"),
    db: Session = Depends(get_db)
):
    """更新订单支付状态"""
    # 先更新支付状态
    db_order = order_dao.update_payment_status(
        session=db, 
        order_id=order_id, 
        payment_status=payment_status_update.payment_status
    )
    if db_order is None:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    # 如果有支付时间，需要单独更新
    if payment_status_update.payment_time:
        order_update = OrderUpdate(payment_time=payment_status_update.payment_time)
        db_order = order_dao.update(session=db, order_id=order_id, order=order_update)
    
    return db_order


# OrderItem endpoints
@router.post("/{order_id}/items", response_model=OrderItemResponse, status_code=status.HTTP_201_CREATED)
def create_order_item(
    order_id: int = Path(..., title="订单ID"),
    db: Session = Depends(get_db),
    item_data: dict = None
):
    """为订单添加订单项"""
    # 确认订单存在
    db_order = order_dao.get(session=db, order_id=order_id)
    if db_order is None:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    # 创建OrderItemCreate对象
    item_data["order_id"] = order_id
    order_item = OrderItemCreate(**item_data)
    
    # 创建订单项
    return order_item_dao.create(session=db, order_item=order_item)


@router.get("/{order_id}/items", response_model=List[OrderItemResponse])
def read_order_items(
    order_id: int = Path(..., title="订单ID"),
    db: Session = Depends(get_db)
):
    """获取订单的所有订单项"""
    # 确认订单存在
    db_order = order_dao.get(session=db, order_id=order_id)
    if db_order is None:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    return order_item_dao.get_by_order(session=db, order_id=order_id)


@router.get("/items/{item_id}", response_model=OrderItemResponse)
def read_order_item(
    item_id: int = Path(..., title="订单项ID"),
    db: Session = Depends(get_db)
):
    """获取订单项详情"""
    db_item = order_item_dao.get(session=db, order_item_id=item_id)
    if db_item is None:
        raise HTTPException(status_code=404, detail="订单项不存在")
    return db_item


@router.put("/items/{item_id}", response_model=OrderItemResponse)
def update_order_item(
    order_item: OrderItemUpdate,
    item_id: int = Path(..., title="要更新的订单项ID"),
    db: Session = Depends(get_db)
):
    """更新订单项"""
    db_item = order_item_dao.update(session=db, order_item_id=item_id, order_item=order_item)
    if db_item is None:
        raise HTTPException(status_code=404, detail="订单项不存在")
    return db_item


@router.delete("/items/{item_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_order_item(
    item_id: int = Path(..., title="要删除的订单项ID"),
    db: Session = Depends(get_db)
):
    """删除订单项"""
    success = order_item_dao.delete(session=db, order_item_id=item_id)
    if not success:
        raise HTTPException(status_code=404, detail="订单项不存在")
    return {"message": "订单项删除成功"} 