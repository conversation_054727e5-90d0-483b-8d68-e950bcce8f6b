from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.models.order import OrderStatus, PaymentStatus, PaymentMethod
from app.schemas.order import OrderResponse
from app.service.payment import payment_service
from app.dao.order import order_dao

router = APIRouter()


@router.post("/order/{order_id}/pay", response_model=OrderResponse)
def pay_order(
    order_id: int,
    payment_info: Dict[str, Any] = {},
    db: Session = Depends(get_db)
):
    """
    支付订单
    - **order_id**: 订单ID
    - **payment_info**: 支付信息，如企业账户支付时需要提供enterprise_id
    """
    # 检查订单是否存在
    order = order_dao.get(db, order_id)
    if not order:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="订单不存在"
        )
    
    try:
        # 调用支付服务进行支付
        updated_order = payment_service.pay_order(db, order_id, payment_info)
        return updated_order
    except ValueError as e:
        # 处理支付过程中的错误
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        ) 