from typing import Dict, List

from fastapi import APIRouter, Depends, HTTPException, status, Body
from starlette.responses import JSONResponse

from app.core.deps import get_db
from app.dao.pricing import *
from app.schemas.pricing import *
from app.schemas.product import ProductResponse
from app.schemas.pricing import PricingSearchRequest, PricingStatusUpdateRequest, PricingStrategyListFormattedResponse, BundleStrategyFormattedResponse

router = APIRouter()


# 定价策略接口
@router.post("/pricing-strategies/", response_model=PricingStrategyResponse, status_code=status.HTTP_201_CREATED)
def create_pricing_strategy(strategy: PricingStrategyCreate, db: Session = Depends(get_db)):
    """创建定价策略"""
    return pricing_strategy_dao.create(db, strategy)


@router.get("/pricing-strategies/{strategy_id}", response_model=PricingStrategyResponse)
def read_pricing_strategy(strategy_id: int, db: Session = Depends(get_db)):
    """获取定价策略详情"""
    strategy = pricing_strategy_dao.get(db, strategy_id)
    if not strategy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="定价策略不存在")
    return strategy


@router.get("/pricing-strategies/", response_model=List[PricingStrategyResponse])
def read_pricing_strategies(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取定价策略列表"""
    return pricing_strategy_dao.get_list(db, skip=skip, limit=limit)


@router.put("/pricing-strategies/{strategy_id}", response_model=PricingStrategyResponse)
def update_pricing_strategy(strategy_id: int, strategy: PricingStrategyUpdate, db: Session = Depends(get_db)):
    """更新定价策略"""
    updated_strategy = pricing_strategy_dao.update(db, strategy_id, strategy)
    if not updated_strategy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="定价策略不存在")
    return updated_strategy


@router.delete("/pricing-strategies/{strategy_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_pricing_strategy(strategy_id: int, db: Session = Depends(get_db)):
    """删除定价策略"""
    success = pricing_strategy_dao.delete(db, strategy_id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="定价策略不存在")
    return {"message": "定价策略已删除"}


# 添加新端点：获取定价策略绑定的产品
@router.get("/pricing-strategies/{strategy_id}/products", response_model=List[ProductResponse])
def get_products_by_pricing_strategy(
        strategy_id: int,
        skip: int = 0,
        limit: int = 100,
        db: Session = Depends(get_db)
):
    """获取指定定价策略绑定的所有产品"""
    strategy = pricing_strategy_dao.get(db, strategy_id)
    if not strategy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="定价策略不存在")

    products = pricing_strategy_dao.get_products_by_strategy(
        session=db,
        strategy_id=strategy_id,
        skip=skip,
        limit=limit
    )
    return products


# 定价策略与产品绑定解绑端点
@router.post("/pricing-strategies/add/products/", status_code=status.HTTP_200_OK, response_model=Dict)
def add_products_to_pricing_strategy(binding: PricingStrategyAndProduct, db: Session = Depends(get_db)):
    """批量将产品绑定到定价策略"""
    result = pricing_strategy_dao.add_products(
        session=db,
        product_ids=binding.product_ids,
        strategy_id=binding.pricing_strategy_id
    )

    if not result["success"] and result["failed"]:
        message = "所有产品绑定失败"
    else:
        message = "产品批量绑定到定价策略操作完成"

    return {
        "message": message,
        "success_count": len(result["success"]),
        "failed_count": len(result["failed"]),
        "success_ids": result["success"],
        "failed_ids": result["failed"]
    }


@router.post("/pricing-strategies/remove/products/", status_code=status.HTTP_200_OK, response_model=Dict)
def remove_products_from_pricing_strategy(binding: PricingStrategyAndProduct, db: Session = Depends(get_db)):
    """批量将产品从定价策略解绑"""
    result = pricing_strategy_dao.remove_products(
        session=db,
        product_ids=binding.product_ids,
        strategy_id=binding.pricing_strategy_id
    )

    if not result["success"] and result["failed"]:
        raise HTTPException(status_code=404, detail="所有产品解绑失败")

    return {
        "message": "产品批量从定价策略解绑操作完成",
        "success_count": len(result["success"]),
        "failed_count": len(result["failed"]),
        "success_ids": result["success"],
        "failed_ids": result["failed"]
    }


# 折扣策略接口
@router.post("/discount-strategies/", response_model=DiscountStrategyResponse, status_code=status.HTTP_201_CREATED)
def create_discount_strategy(strategy: DiscountStrategyCreate, db: Session = Depends(get_db)):
    """创建折扣策略"""
    return discount_strategy_dao.create(db, strategy)


@router.get("/discount-strategies/{strategy_id}", response_model=DiscountStrategyResponse)
def read_discount_strategy(strategy_id: int, db: Session = Depends(get_db)):
    """获取折扣策略详情"""
    strategy = discount_strategy_dao.get(db, strategy_id)
    if not strategy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="折扣策略不存在")
    return strategy


@router.get("/discount-strategies/", response_model=List[DiscountStrategyResponse])
def read_discount_strategies(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取折扣策略列表"""
    return discount_strategy_dao.get_list(db, skip=skip, limit=limit)


@router.put("/discount-strategies/{strategy_id}", response_model=DiscountStrategyResponse)
def update_discount_strategy(strategy_id: int, strategy: DiscountStrategyUpdate, db: Session = Depends(get_db)):
    """更新折扣策略"""
    updated_strategy = discount_strategy_dao.update(db, strategy_id, strategy)
    if not updated_strategy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="折扣策略不存在")
    return updated_strategy


@router.delete("/discount-strategies/{strategy_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_discount_strategy(strategy_id: int, db: Session = Depends(get_db)):
    """删除折扣策略"""
    success = discount_strategy_dao.delete(db, strategy_id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="折扣策略不存在")
    return {"message": "折扣策略已删除"}


# 满减策略接口
@router.post("/full-reduction-strategies/", response_model=FullReductionStrategyResponse,
             status_code=status.HTTP_201_CREATED)
def create_full_reduction_strategy(strategy: FullReductionStrategyCreate, db: Session = Depends(get_db)):
    """创建满减策略"""
    return full_reduction_strategy_dao.create(db, strategy)


@router.get("/full-reduction-strategies/{strategy_id}", response_model=FullReductionStrategyResponse)
def read_full_reduction_strategy(strategy_id: int, db: Session = Depends(get_db)):
    """获取满减策略详情"""
    strategy = full_reduction_strategy_dao.get(db, strategy_id)
    if not strategy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="满减策略不存在")
    return strategy


@router.get("/full-reduction-strategies/", response_model=List[FullReductionStrategyResponse])
def read_full_reduction_strategies(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取满减策略列表"""
    return full_reduction_strategy_dao.get_list(db, skip=skip, limit=limit)


@router.put("/full-reduction-strategies/{strategy_id}", response_model=FullReductionStrategyResponse)
def update_full_reduction_strategy(strategy_id: int, strategy: FullReductionStrategyUpdate,
                                   db: Session = Depends(get_db)):
    """更新满减策略"""
    updated_strategy = full_reduction_strategy_dao.update(db, strategy_id, strategy)
    if not updated_strategy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="满减策略不存在")
    return updated_strategy


@router.delete("/full-reduction-strategies/{strategy_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_full_reduction_strategy(strategy_id: int, db: Session = Depends(get_db)):
    """删除满减策略"""
    success = full_reduction_strategy_dao.delete(db, strategy_id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="满减策略不存在")
    return {"message": "满减策略已删除"}


# 限时策略接口
@router.post("/time-limited-strategies/", response_model=TimeLimitedStrategyResponse,
             status_code=status.HTTP_201_CREATED)
def created_at_limited_strategy(strategy: TimeLimitedStrategyCreate, db: Session = Depends(get_db)):
    """创建限时策略"""
    return time_limited_strategy_dao.create(db, strategy)


@router.get("/time-limited-strategies/{strategy_id}", response_model=TimeLimitedStrategyResponse)
def read_time_limited_strategy(strategy_id: int, db: Session = Depends(get_db)):
    """获取限时策略详情"""
    strategy = time_limited_strategy_dao.get(db, strategy_id)
    if not strategy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="限时策略不存在")
    return strategy


@router.get("/time-limited-strategies/", response_model=List[TimeLimitedStrategyResponse])
def read_time_limited_strategies(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取限时策略列表"""
    return time_limited_strategy_dao.get_list(db, skip=skip, limit=limit)


@router.put("/time-limited-strategies/{strategy_id}", response_model=TimeLimitedStrategyResponse)
def updated_at_limited_strategy(strategy_id: int, strategy: TimeLimitedStrategyUpdate, db: Session = Depends(get_db)):
    """更新限时策略"""
    updated_strategy = time_limited_strategy_dao.update(db, strategy_id, strategy)
    if not updated_strategy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="限时策略不存在")
    return updated_strategy


@router.delete("/time-limited-strategies/{strategy_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_time_limited_strategy(strategy_id: int, db: Session = Depends(get_db)):
    """删除限时策略"""
    success = time_limited_strategy_dao.delete(db, strategy_id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="限时策略不存在")
    return {"message": "限时策略已删除"}


# 会员价格策略接口
@router.post("/member-price-strategies/", response_model=MemberPriceStrategyResponse,
             status_code=status.HTTP_201_CREATED)
def create_member_price_strategy(strategy: MemberPriceStrategyCreate, db: Session = Depends(get_db)):
    """创建会员价格策略"""
    return member_price_strategy_dao.create(db, strategy)


@router.get("/member-price-strategies/{strategy_id}", response_model=MemberPriceStrategyResponse)
def read_member_price_strategy(strategy_id: int, db: Session = Depends(get_db)):
    """获取会员价格策略详情"""
    strategy = member_price_strategy_dao.get(db, strategy_id)
    if not strategy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="会员价格策略不存在")
    return strategy


@router.get("/member-price-strategies/", response_model=List[MemberPriceStrategyResponse])
def read_member_price_strategies(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取会员价格策略列表"""
    return member_price_strategy_dao.get_list(db, skip=skip, limit=limit)


@router.put("/member-price-strategies/{strategy_id}", response_model=MemberPriceStrategyResponse)
def update_member_price_strategy(strategy_id: int, strategy: MemberPriceStrategyUpdate, db: Session = Depends(get_db)):
    """更新会员价格策略"""
    updated_strategy = member_price_strategy_dao.update(db, strategy_id, strategy)
    if not updated_strategy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="会员价格策略不存在")
    return updated_strategy


@router.delete("/member-price-strategies/{strategy_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_member_price_strategy(strategy_id: int, db: Session = Depends(get_db)):
    """删除会员价格策略"""
    success = member_price_strategy_dao.delete(db, strategy_id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="会员价格策略不存在")
    return {"message": "会员价格策略已删除"}


# 捆绑销售策略接口
@router.post("/bundle-strategies/", response_model=BundleStrategyResponse, status_code=status.HTTP_201_CREATED)
def create_bundle_strategy(strategy: BundleStrategyCreate, db: Session = Depends(get_db)):
    """创建捆绑销售策略"""
    return bundle_strategy_dao.create(db, strategy)


@router.get("/bundle-strategies/{strategy_id}", response_model=BundleStrategyResponse)
def read_bundle_strategy(strategy_id: int, db: Session = Depends(get_db)):
    """获取捆绑销售策略详情"""
    strategy = bundle_strategy_dao.get(db, strategy_id)
    if not strategy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="捆绑销售策略不存在")
    return strategy


@router.get("/bundle-strategies/", response_model=List[BundleStrategyResponse])
def read_bundle_strategies(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取捆绑销售策略列表"""
    return bundle_strategy_dao.get_list(db, skip=skip, limit=limit)


@router.put("/bundle-strategies/{strategy_id}", response_model=BundleStrategyResponse)
def update_bundle_strategy(strategy_id: int, strategy: BundleStrategyUpdate, db: Session = Depends(get_db)):
    """更新捆绑销售策略"""
    updated_strategy = bundle_strategy_dao.update(db, strategy_id, strategy)
    if not updated_strategy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="捆绑销售策略不存在")
    return updated_strategy


@router.delete("/bundle-strategies/{strategy_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_bundle_strategy(strategy_id: int, db: Session = Depends(get_db)):
    """删除捆绑销售策略"""
    success = bundle_strategy_dao.delete(db, strategy_id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="捆绑销售策略不存在")
    return {"message": "捆绑销售策略已删除"}


# 捆绑销售策略产品管理接口
@router.get("/bundle-strategies/{strategy_id}/products", response_model=List[BundleStrategyProductRelResponse])
def get_bundle_strategy_products(strategy_id: int, db: Session = Depends(get_db)):
    """获取捆绑销售策略关联的产品列表"""
    strategy = bundle_strategy_dao.get(db, strategy_id)
    if not strategy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="捆绑销售策略不存在")

    products = bundle_strategy_dao.get_bundle_products(db, strategy_id)
    return products


@router.post("/bundle-strategies/{strategy_id}/products", status_code=status.HTTP_200_OK, response_model=Dict)
def add_bundle_strategy_products(
    strategy_id: int,
    products: List[BundleStrategyProductRel],
    db: Session = Depends(get_db)
):
    """批量添加捆绑策略产品"""
    strategy = bundle_strategy_dao.get(db, strategy_id)
    if not strategy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="捆绑销售策略不存在")

    product_data = [product.model_dump() for product in products]
    result = bundle_strategy_dao.add_bundle_products(db, strategy_id, product_data)

    return {
        "message": "批量添加捆绑策略产品操作完成",
        "success_count": len(result["success"]),
        "failed_count": len(result["failed"]),
        "success_products": result["success"],
        "failed_products": result["failed"]
    }


@router.delete("/bundle-strategies/{strategy_id}/products", status_code=status.HTTP_200_OK, response_model=Dict)
def remove_bundle_strategy_products(
    strategy_id: int,
    product_ids: List[int] = Body(...),
    db: Session = Depends(get_db)
):
    """批量移除捆绑策略产品"""
    strategy = bundle_strategy_dao.get(db, strategy_id)
    if not strategy:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="捆绑销售策略不存在")

    result = bundle_strategy_dao.remove_bundle_products(db, strategy_id, product_ids)

    return {
        "message": "批量移除捆绑策略产品操作完成",
        "success_count": len(result["success"]),
        "failed_count": len(result["failed"]),
        "success_ids": result["success"],
        "failed_ids": result["failed"]
    }


@router.get("/bundle-strategies/{strategy_id}/with-products", response_model=CommonResponse)
def read_bundle_strategy_with_products(strategy_id: int, db: Session = Depends(get_db)):
    """获取捆绑销售策略详情（包含关联的产品信息）"""
    try:
        strategy = bundle_strategy_dao.get_with_bundle_products(db, strategy_id)
        if not strategy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "捆绑销售策略不存在",
                    "data": None
                }
            )

        # 将SQLAlchemy模型转换为Pydantic模型
        bundle_response = BundleStrategyResponse.model_validate(strategy)
        # 转换为格式化响应
        result = BundleStrategyFormattedResponse.from_bundle_strategy(bundle_response)

        return {
            "code": 200,
            "message": "获取捆绑销售策略详情成功",
            "data": result.model_dump()
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"获取捆绑销售策略详情失败: {str(e)}",
                "data": None
            }
        )


@router.get("/bundle-strategies/with-products", response_model=CommonResponse)
def read_bundle_strategies_with_products(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取捆绑销售策略列表（包含关联的产品信息）"""
    try:
        strategies = bundle_strategy_dao.get_list_with_bundle_products(db, skip=skip, limit=limit)

        # 将SQLAlchemy模型列表转换为Pydantic模型列表
        bundle_responses = [BundleStrategyResponse.model_validate(strategy) for strategy in strategies]
        # 转换为格式化响应列表
        result = [BundleStrategyFormattedResponse.from_bundle_strategy(bundle_response) for bundle_response in bundle_responses]

        return {
            "code": 200,
            "message": "获取捆绑销售策略列表成功",
            "data": [item.model_dump() for item in result]
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"获取捆绑销售策略列表失败: {str(e)}",
                "data": None
            }
        )


@router.post("/create", response_model=CommonResponse, status_code=status.HTTP_201_CREATED)
def create_pricing(body: dict = Body(...), db: Session = Depends(get_db)):
    """
    创建定价策略

    通过JSON请求体传入PricingCreateRequest创建不同类型的定价策略：
    - 折扣策略 (discount)
    - 满减策略 (full_reduction)
    - 限时策略 (time_limited)
    - 会员价格策略 (member_price)
    - 基础定价策略 (pricing)

    示例:
    {
        "type": "discount",
        "data": {
            "name": "折扣策略",
            "description": "这是一个折扣策略",
            "start_time": "2023-01-01 00:00:00",
            "end_time": "2025-01-01 00:00:00",
            "discount_rate": 0.8,
            "min_amount": 100,
            "max_discount": 50
        }
    }
    """
    try:
        pricing_type = body.get("type", None)
        # data = body.get("data", {})
        data = body
        if pricing_type is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "code": 400,
                    "message": "缺少定价策略类型",
                    "data": None
                }
            )

        # 根据定价策略类型创建对应的策略
        if pricing_type == PricingStrategyType.DISCOUNT.value:
            # 创建折扣策略
            pricing_model = DiscountStrategyCreate(**data)
            db_result = discount_strategy_dao.create(session=db, strategy=pricing_model)
            result = DiscountStrategyResponse.model_validate(db_result)
        elif pricing_type == PricingStrategyType.FULL_REDUCTION.value:
            # 创建满减策略
            pricing_model = FullReductionStrategyCreate(**data)
            db_result = full_reduction_strategy_dao.create(session=db, strategy=pricing_model)
            result = FullReductionStrategyResponse.model_validate(db_result)
        elif pricing_type == PricingStrategyType.TIME_LIMITED.value:
            # 创建限时策略
            pricing_model = TimeLimitedStrategyCreate(**data)
            db_result = time_limited_strategy_dao.create(session=db, strategy=pricing_model)
            result = TimeLimitedStrategyResponse.model_validate(db_result)
        elif pricing_type == PricingStrategyType.MEMBER_PRICE.value:
            # 创建会员价格策略
            pricing_model = MemberPriceStrategyCreate(**data)
            db_result = member_price_strategy_dao.create(session=db, strategy=pricing_model)
            result = MemberPriceStrategyResponse.model_validate(db_result)
        elif pricing_type == PricingStrategyType.BUNDLE.value:
            # 创建捆绑销售策略
            pricing_model = BundleStrategyCreate(**data)
            db_result = bundle_strategy_dao.create(session=db, strategy=pricing_model)
            # 重新获取包含产品关联的完整数据
            db_result_with_products = bundle_strategy_dao.get_with_bundle_products(session=db, strategy_id=db_result.id)
            bundle_response = BundleStrategyResponse.model_validate(db_result_with_products)
            # 转换为格式化响应
            result = BundleStrategyFormattedResponse.from_bundle_strategy(bundle_response)
        elif pricing_type == PricingStrategyType.PRICING.value:
            # 创建基础定价策略
            pricing_model = PricingStrategyCreate(**data)
            db_result = pricing_strategy_dao.create(session=db, strategy=pricing_model)
            result = PricingStrategyResponse.model_validate(db_result)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "code": 400,
                    "message": f"不支持的定价策略创建类型: {str(pricing_type)}",
                    "data": None
                }
            )

        return {
            "code": 200,
            "message": f"创建{pricing_type}定价策略成功",
            "data": result.model_dump()
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"创建定价策略失败: {str(e)}",
                "data": None
            }
        )


@router.delete("/delete/{strategy_id}")
def delete_pricing(strategy_id: int, db: Session = Depends(get_db)):
    """删除产品"""
    # 先检查是否有关联的订单项
    db_pricing = pricing_strategy_dao.get(session=db, strategy_id=strategy_id)
    if not db_pricing:
        return JSONResponse({
            "code": 400,
            "message": "价格策略不存在",
            "data": None
        }, status_code=200)

    success = pricing_strategy_dao.delete(session=db, strategy_id=strategy_id)
    return JSONResponse({"message": "价格策略删除成功"}, status_code=204)


@router.get("/view/{strategy_id}", response_model=CommonResponse)
def view_pricing(strategy_id: int, db: Session = Depends(get_db)):
    """
    查看定价策略详情

    根据传入的id，获取定价策略，再根据对应的类型，获取对应类型的定价策略详情
    """
    try:
        # 先获取基础定价策略信息，确定策略类型
        base_strategy = pricing_strategy_dao.get(session=db, strategy_id=strategy_id)
        if base_strategy is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "定价策略不存在",
                    "data": None
                }
            )

        # 根据策略类型获取详细信息
        if base_strategy.type == PricingStrategyType.DISCOUNT:
            db_result = discount_strategy_dao.get(session=db, strategy_id=strategy_id)
            if db_result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "折扣策略不存在",
                        "data": None
                    }
                )
            # 将SQLAlchemy模型转换为Pydantic模型
            result = DiscountStrategyResponse.model_validate(db_result)
        elif base_strategy.type == PricingStrategyType.FULL_REDUCTION:
            db_result = full_reduction_strategy_dao.get(session=db, strategy_id=strategy_id)
            if db_result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "满减策略不存在",
                        "data": None
                    }
                )
            # 将SQLAlchemy模型转换为Pydantic模型
            result = FullReductionStrategyResponse.model_validate(db_result)
        elif base_strategy.type == PricingStrategyType.TIME_LIMITED:
            db_result = time_limited_strategy_dao.get(session=db, strategy_id=strategy_id)
            if db_result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "限时策略不存在",
                        "data": None
                    }
                )
            # 将SQLAlchemy模型转换为Pydantic模型
            result = TimeLimitedStrategyResponse.model_validate(db_result)
        elif base_strategy.type == PricingStrategyType.MEMBER_PRICE:
            db_result = member_price_strategy_dao.get(session=db, strategy_id=strategy_id)
            if db_result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "会员价格策略不存在",
                        "data": None
                    }
                )
            # 将SQLAlchemy模型转换为Pydantic模型
            result = MemberPriceStrategyResponse.model_validate(db_result)
        elif base_strategy.type == PricingStrategyType.BUNDLE:
            db_result = bundle_strategy_dao.get_with_bundle_products(session=db, strategy_id=strategy_id)
            if db_result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "捆绑销售策略不存在",
                        "data": None
                    }
                )
            # 将SQLAlchemy模型转换为Pydantic模型
            bundle_response = BundleStrategyResponse.model_validate(db_result)
            # 转换为格式化响应
            result = BundleStrategyFormattedResponse.from_bundle_strategy(bundle_response)
        else:
            # 将SQLAlchemy模型转换为Pydantic模型
            result = PricingStrategyResponse.model_validate(base_strategy)

        return {
            "code": 200,
            "message": f"获取{base_strategy.type.value}定价策略详情成功",
            "data": result.model_dump()
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"获取定价策略详情失败: {str(e)}",
                "data": None
            }
        )


@router.put("/update/{strategy_id}", response_model=CommonResponse)
def update_pricing(strategy_id: int, body: dict = Body(...), db: Session = Depends(get_db)):
    """
    更新定价策略信息

    根据传入的id，判断对应的定价策略类型，找到对应的定价策略，进行更新
    """
    try:
        # 先获取基础定价策略信息，确定策略类型
        base_strategy = pricing_strategy_dao.get(session=db, strategy_id=strategy_id)
        if base_strategy is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "定价策略不存在",
                    "data": None
                }
            )

        # 根据策略类型更新信息
        if base_strategy.type == PricingStrategyType.DISCOUNT:
            strategy_data = DiscountStrategyUpdate(**body)
            db_result = discount_strategy_dao.update(session=db, strategy_id=strategy_id, strategy=strategy_data)
            if db_result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "折扣策略不存在",
                        "data": None
                    }
                )
            # 将SQLAlchemy模型转换为Pydantic模型
            result = DiscountStrategyResponse.model_validate(db_result)
        elif base_strategy.type == PricingStrategyType.FULL_REDUCTION:
            strategy_data = FullReductionStrategyUpdate(**body)
            db_result = full_reduction_strategy_dao.update(session=db, strategy_id=strategy_id, strategy=strategy_data)
            if db_result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "满减策略不存在",
                        "data": None
                    }
                )
            # 将SQLAlchemy模型转换为Pydantic模型
            result = FullReductionStrategyResponse.model_validate(db_result)
        elif base_strategy.type == PricingStrategyType.TIME_LIMITED:
            strategy_data = TimeLimitedStrategyUpdate(**body)
            db_result = time_limited_strategy_dao.update(session=db, strategy_id=strategy_id, strategy=strategy_data)
            if db_result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "限时策略不存在",
                        "data": None
                    }
                )
            # 将SQLAlchemy模型转换为Pydantic模型
            result = TimeLimitedStrategyResponse.model_validate(db_result)
        elif base_strategy.type == PricingStrategyType.MEMBER_PRICE:
            strategy_data = MemberPriceStrategyUpdate(**body)
            db_result = member_price_strategy_dao.update(session=db, strategy_id=strategy_id, strategy=strategy_data)
            if db_result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "会员价格策略不存在",
                        "data": None
                    }
                )
            # 将SQLAlchemy模型转换为Pydantic模型
            result = MemberPriceStrategyResponse.model_validate(db_result)
        elif base_strategy.type == PricingStrategyType.BUNDLE:
            strategy_data = BundleStrategyUpdate(**body)
            db_result = bundle_strategy_dao.update(session=db, strategy_id=strategy_id, strategy=strategy_data)
            if db_result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "捆绑销售策略不存在",
                        "data": None
                    }
                )
            # 重新获取包含产品关联的完整数据
            db_result_with_products = bundle_strategy_dao.get_with_bundle_products(session=db, strategy_id=strategy_id)
            # 将SQLAlchemy模型转换为Pydantic模型
            bundle_response = BundleStrategyResponse.model_validate(db_result_with_products)
            # 转换为格式化响应
            result = BundleStrategyFormattedResponse.from_bundle_strategy(bundle_response)
        else:
            strategy_data = PricingStrategyUpdate(**body)
            db_result = pricing_strategy_dao.update(session=db, strategy_id=strategy_id, strategy=strategy_data)
            if db_result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "定价策略不存在",
                        "data": None
                    }
                )
            # 将SQLAlchemy模型转换为Pydantic模型
            result = PricingStrategyResponse.model_validate(db_result)

        return {
            "code": 200,
            "message": f"更新{base_strategy.type.value}定价策略成功",
            "data": result.model_dump()
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"更新定价策略失败: {str(e)}",
                "data": None
            }
        )


@router.get("/search/", response_model=PricingStrategyListFormattedResponse)
def search_pricing(request: PricingSearchRequest = Depends(), db: Session = Depends(get_db)):
    """
    根据条件搜索定价策略列表

    可以根据以下条件进行搜索:
    - keyword: 关键词，用于全文搜索
    - name: 策略名称
    - type: 策略类型
    - status: 策略状态
    """
    skip = max(0, request.page - 1) * request.pageSize
    limit = request.pageSize
    result = pricing_strategy_dao.search(
        session=db,
        keyword=request.keyword,
        name=request.name,
        strategy_type=request.type,
        status=request.status,
        skip=skip,
        limit=limit
    )

    return {
        "code": 200,
        "message": "搜索定价策略列表成功",
        "data": result
    }


@router.post("/status/{strategy_id}", response_model=Dict)
def update_pricing_status(strategy_id: int, request: PricingStatusUpdateRequest, db: Session = Depends(get_db)):
    """修改定价策略状态"""
    try:
        result = pricing_strategy_dao.update(
            session=db,
            strategy_id=strategy_id,
            strategy=PricingStrategyUpdate(status=request.status)
        )
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "定价策略不存在",
                    "data": None
                }
            )

        response_model = PricingStrategyResponse.model_validate(result)
        return {
            "code": 200,
            "message": "定价策略状态更新成功",
            "data": response_model.model_dump()
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"定价策略状态更新失败: {str(e)}",
                "data": None
            }
        )
