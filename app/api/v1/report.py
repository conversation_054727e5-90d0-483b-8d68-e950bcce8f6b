from typing import Optional, List

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from fastapi.responses import StreamingResponse

from app.core.deps import get_db
from app.schemas.reservation import (
    ReservationReportRequest, ReservationReportResponse, ReservationReportItem,
    ReservationOrderReportRequest, ReservationOrderReportResponse, ReservationOrderReportItem
)
from app.service.report import report_service
from datetime import datetime

router = APIRouter()


# 预订报表接口
@router.post("/reservation", response_model=ReservationReportResponse)
def get_reservation_report(
        request: ReservationReportRequest = None,
        phone: Optional[str] = Query(None, description="手机号"),
        real_name: Optional[str] = Query(None, description="姓名"),
        nick_name: Optional[str] = Query(None, description="微信昵称"),
        status: Optional[List[str]] = Query(None, description="状态，可以多选"),
        dining_start_time: Optional[str] = Query(None, description="就餐开始时间 (YYYY-MM-DD HH:MM:SS)"),
        dining_end_time: Optional[str] = Query(None, description="就餐结束时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_start_time: Optional[str] = Query(None, description="预订开始时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_end_time: Optional[str] = Query(None, description="预订结束时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_period: Optional[str] = Query(None, description="预订时段"),
        payment_enterprise:Optional[str] = Query(None, description="支付企业"),
        page: Optional[int] = Query(None, description="页码"),
        page_size: Optional[int] = Query(None, description="每页记录数"),
        db: Session = Depends(get_db)
):
    """
    使用Raw SQL获取预订报表 (POST方法)

    查询条件：
    - 预订时间范围
    - 预订时段
    - 手机号
    - 分页参数

    返回结果包含：
    "用户真名、手机号、产品、份数、费用、预订状态、预订时段、预订时间"
    """
    # 合并请求参数
    params = {}
    if request:
        params = request.model_dump(exclude_none=True)

    # 处理查询字符串参数
    if dining_start_time and 'dining_start_time' not in params:
        try:
            params['dining_start_time'] = datetime.strptime(dining_start_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的开始时间格式，请使用 YYYY-MM-DD HH:MM:SS")

    if dining_end_time and 'dining_end_time' not in params:
        try:
            params['dining_end_time'] = datetime.strptime(dining_end_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的结束时间格式，请使用 YYYY-MM-DD HH:MM:SS")

    if reservation_start_time and 'start_time' not in params:
        try:
            params['start_time'] = datetime.strptime(reservation_start_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的开始时间格式，请使用 YYYY-MM-DD HH:MM:SS")

    if reservation_end_time and 'reservation_end_time' not in params:
        try:
            params['end_time'] = datetime.strptime(reservation_end_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的结束时间格式，请使用 YYYY-MM-DD HH:MM:SS")

    if reservation_period and 'reservation_period' not in params:
        params['reservation_period'] = reservation_period

    if phone and 'phone' not in params:
        params['phone'] = phone

    if real_name and 'real_name' not in params:
        params['real_name'] = real_name

    if nick_name and 'nick_name' not in params:
        params['nick_name'] = nick_name

    if payment_enterprise:
        params['payment_enterprise'] = payment_enterprise

    if status and 'status' not in params:
        params['status'] = status

    if page and 'page' not in params:
        params['page'] = page

    if page_size and 'page_size' not in params:
        params['page_size'] = page_size

    # 使用Raw SQL获取报表数据
    reservation_list, total_count = report_service.get_reservation_report_raw(
        db=db,
        **params
    )

    data = {
        "list": [ReservationReportItem(**item) for item in reservation_list],
        "total": total_count,
    }

    response = {
        "code": 200,
        "message": "success",
        "data": data
    }

    return response


# 下载预订报表Excel文件
@router.post("/reservation/download_excel")
def download_reservation_report_excel(
        request: ReservationReportRequest = None,
        phone: Optional[str] = Query(None, description="手机号"),
        real_name: Optional[str] = Query(None, description="姓名"),
        nick_name: Optional[str] = Query(None, description="微信昵称"),
        status: Optional[List[str]] = Query(None, description="状态，可以多选"),
        dining_start_time: Optional[str] = Query(None, description="就餐开始时间 (YYYY-MM-DD HH:MM:SS)"),
        dining_end_time: Optional[str] = Query(None, description="就餐结束时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_start_time: Optional[str] = Query(None, description="预订开始时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_end_time: Optional[str] = Query(None, description="预订结束时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_period: Optional[str] = Query(None, description="预订时段"),
        payment_enterprise: Optional[str] = Query(None, description="支付企业"),
        db: Session = Depends(get_db)
):
    """
    下载预订报表Excel文件
    
    查询条件：
    - 预订时间范围
    - 预订时段
    - 手机号
    - 姓名
    - 微信昵称
    - 状态
    
    返回Excel文件
    """
    # 处理参数
    params = {}
    if request:
        params = request.model_dump(exclude_none=True)

    if dining_start_time and 'dining_start_time' not in params:
        try:
            params['dining_start_time'] = datetime.strptime(dining_start_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的开始时间格式，请使用 YYYY-MM-DD HH:MM:SS")

    if dining_end_time and 'dining_end_time' not in params:
        try:
            params['dining_end_time'] = datetime.strptime(dining_end_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的结束时间格式，请使用 YYYY-MM-DD HH:MM:SS")

    if reservation_start_time:
        try:
            params['reservation_start_time'] = datetime.strptime(reservation_start_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的开始时间格式，请使用 YYYY-MM-DD HH:MM:SS")

    if reservation_end_time:
        try:
            params['reservation_end_time'] = datetime.strptime(reservation_end_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的结束时间格式，请使用 YYYY-MM-DD HH:MM:SS")

    if reservation_period:
        params['reservation_period'] = reservation_period

    if phone:
        params['phone'] = phone

    if real_name:
        params['real_name'] = real_name

    if nick_name:
        params['nick_name'] = nick_name

    if status:
        params['status'] = status

    if payment_enterprise:
        params['payment_enterprise'] = payment_enterprise
    # 生成Excel文件
    excel_file = report_service.get_reservation_report_excel(
        db=db,
        **params
    )

    # 设置文件名，包含当前时间
    current_time = datetime.now().strftime("%Y%m%d%H%M%S")
    filename = f"reservation_report_{current_time}.xlsx"
    
    # 返回文件流
    return StreamingResponse(
        excel_file,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )


# 预订订单报表接口
@router.post("/reservation_order", response_model=ReservationOrderReportResponse)
def get_reservation_order_report(
        request: ReservationOrderReportRequest = None,
        phone: Optional[str] = Query(None, description="手机号"),
        real_name: Optional[str] = Query(None, description="姓名"),
        nick_name: Optional[str] = Query(None, description="微信昵称"),
        status: Optional[List[str]] = Query(None, description="状态，可以多选"),
        dining_start_time: Optional[str] = Query(None, description="就餐开始时间 (YYYY-MM-DD HH:MM:SS)"),
        dining_end_time: Optional[str] = Query(None, description="就餐结束时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_start_time: Optional[str] = Query(None, description="预订开始时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_end_time: Optional[str] = Query(None, description="预订结束时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_period: Optional[str] = Query(None, description="预订时段"),
        payment_enterprise: Optional[str] = Query(None, description="支付企业"),
        page: Optional[int] = Query(None, description="页码"),
        page_size: Optional[int] = Query(None, description="每页记录数"),
        db: Session = Depends(get_db)
):
    """
    获取预订订单报表数据，关联biz_reservation_requests和orders表

    查询条件：
    - 就餐时间范围
    - 预订时间范围
    - 预订时段
    - 手机号
    - 姓名
    - 微信昵称
    - 状态
    - 支付企业
    - 分页参数

    返回结果包含：
    - 预订信息（reservation_requests + biz_reservation_requests）
    - 订单信息（orders）
    - 订单项信息（order_items）
    - 用户信息
    """
    # 合并请求参数
    params = {}
    if request:
        params = request.model_dump(exclude_none=True)

    # 处理查询字符串参数
    if dining_start_time and 'dining_start_time' not in params:
        try:
            params['dining_start_time'] = datetime.strptime(dining_start_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的就餐开始时间格式，请使用 YYYY-MM-DD HH:MM:SS")

    if dining_end_time and 'dining_end_time' not in params:
        try:
            params['dining_end_time'] = datetime.strptime(dining_end_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的就餐结束时间格式，请使用 YYYY-MM-DD HH:MM:SS")

    if reservation_start_time and 'reservation_start_time' not in params:
        try:
            params['reservation_start_time'] = datetime.strptime(reservation_start_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的预订开始时间格式，请使用 YYYY-MM-DD HH:MM:SS")

    if reservation_end_time and 'reservation_end_time' not in params:
        try:
            params['reservation_end_time'] = datetime.strptime(reservation_end_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的预订结束时间格式，请使用 YYYY-MM-DD HH:MM:SS")

    if reservation_period and 'reservation_period' not in params:
        params['reservation_period'] = reservation_period

    if phone and 'phone' not in params:
        params['phone'] = phone

    if real_name and 'real_name' not in params:
        params['real_name'] = real_name

    if nick_name and 'nick_name' not in params:
        params['nick_name'] = nick_name

    if payment_enterprise:
        params['payment_enterprise'] = payment_enterprise

    if status and 'status' not in params:
        params['status'] = status

    if page and 'page' not in params:
        params['page'] = page

    if page_size and 'page_size' not in params:
        params['page_size'] = page_size

    # 使用新的函数获取预订订单报表数据
    reservation_list, total_count = report_service.get_reservation_order_report_raw(
        db=db,
        **params
    )

    data = {
        "list": [ReservationOrderReportItem(**item) for item in reservation_list],
        "total": total_count,
    }

    response = {
        "code": 200,
        "message": "success",
        "data": data
    }

    return response


# 下载预订订单报表Excel文件
@router.post("/reservation_order/download_excel")
def download_reservation_order_report_excel(
        request: ReservationOrderReportRequest = None,
        phone: Optional[str] = Query(None, description="手机号"),
        real_name: Optional[str] = Query(None, description="姓名"),
        nick_name: Optional[str] = Query(None, description="微信昵称"),
        status: Optional[List[str]] = Query(None, description="状态，可以多选"),
        dining_start_time: Optional[str] = Query(None, description="就餐开始时间 (YYYY-MM-DD HH:MM:SS)"),
        dining_end_time: Optional[str] = Query(None, description="就餐结束时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_start_time: Optional[str] = Query(None, description="预订开始时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_end_time: Optional[str] = Query(None, description="预订结束时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_period: Optional[str] = Query(None, description="预订时段"),
        payment_enterprise: Optional[str] = Query(None, description="支付企业"),
        db: Session = Depends(get_db)
):
    """
    下载预订订单报表Excel文件，使用合并单元格显示预订信息和订单项

    查询条件：
    - 就餐时间范围
    - 预订时间范围
    - 预订时段
    - 手机号
    - 姓名
    - 微信昵称
    - 状态
    - 支付企业

    返回Excel文件，包含：
    - 预订信息（合并单元格显示）
    - 订单信息（合并单元格显示）
    - 订单项详情（每行一个订单项）
    """
    # 处理参数
    params = {}
    if request:
        params = request.model_dump(exclude_none=True)

    if dining_start_time and 'dining_start_time' not in params:
        try:
            params['dining_start_time'] = datetime.strptime(dining_start_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的就餐开始时间格式，请使用 YYYY-MM-DD HH:MM:SS")

    if dining_end_time and 'dining_end_time' not in params:
        try:
            params['dining_end_time'] = datetime.strptime(dining_end_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的就餐结束时间格式，请使用 YYYY-MM-DD HH:MM:SS")

    if reservation_start_time:
        try:
            params['reservation_start_time'] = datetime.strptime(reservation_start_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的预订开始时间格式，请使用 YYYY-MM-DD HH:MM:SS")

    if reservation_end_time:
        try:
            params['reservation_end_time'] = datetime.strptime(reservation_end_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的预订结束时间格式，请使用 YYYY-MM-DD HH:MM:SS")

    if reservation_period:
        params['reservation_period'] = reservation_period

    if phone:
        params['phone'] = phone

    if real_name:
        params['real_name'] = real_name

    if nick_name:
        params['nick_name'] = nick_name

    if status:
        params['status'] = status

    if payment_enterprise:
        params['payment_enterprise'] = payment_enterprise

    # 生成Excel文件
    excel_file = report_service.get_reservation_order_report_excel(
        db=db,
        **params
    )

    # 设置文件名，包含当前时间
    current_time = datetime.now().strftime("%Y%m%d%H%M%S")
    filename = f"reservation_order_report_{current_time}.xlsx"

    # 返回文件流
    return StreamingResponse(
        excel_file,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )
