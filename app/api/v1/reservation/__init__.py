"""
预订相关API模块

包含三种预订类型的API：
- 基础预订请求 (reservation)
- 自助餐预订请求 (buffet_reservation)  
- 商务餐预订请求 (biz_reservation)
"""

from .reservation import router as reservation_router
from .buffet_reservation import router as buffet_reservation_router
from .biz_reservation import router as biz_reservation_router

__all__ = [
    "reservation_router",
    "buffet_reservation_router", 
    "biz_reservation_router"
] 