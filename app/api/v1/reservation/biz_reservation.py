from typing import List

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from starlette import status

from app.core.deps import get_db
from app.dao.reservation import biz_reservation_request_dao
from app.schemas.reservation import (
    BizReservationRequest, BizReservationRequestCreate, BizReservationRequestUpdate,
    ReservationActionResponse
)

router = APIRouter()


# 商务餐预订请求基本CRUD接口
@router.post("/", response_model=BizReservationRequest, status_code=status.HTTP_201_CREATED)
def create_biz_reservation_request(reservation: BizReservationRequestCreate, db: Session = Depends(get_db)):
    """创建商务餐预订请求"""
    return biz_reservation_request_dao.create(session=db, reservation=reservation)


@router.get("/{reservation_id}", response_model=BizReservationRequest)
def read_biz_reservation_request(reservation_id: int, db: Session = Depends(get_db)):
    """获取商务餐预订请求详情"""
    db_reservation = biz_reservation_request_dao.get(session=db, reservation_id=reservation_id)
    if db_reservation is None:
        raise HTTPException(status_code=404, detail="商务餐预订请求不存在")
    return db_reservation


@router.get("/", response_model=List[BizReservationRequest])
def read_biz_reservation_requests(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取商务餐预订请求列表"""
    return biz_reservation_request_dao.get_list(session=db, skip=skip, limit=limit)


@router.put("/{reservation_id}", response_model=BizReservationRequest)
def update_biz_reservation_request(reservation_id: int, reservation: BizReservationRequestUpdate, db: Session = Depends(get_db)):
    """更新商务餐预订请求"""
    db_reservation = biz_reservation_request_dao.update(session=db, reservation_id=reservation_id, reservation=reservation)
    if db_reservation is None:
        raise HTTPException(status_code=404, detail="商务餐预订请求不存在")
    return db_reservation


@router.delete("/{reservation_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_biz_reservation_request(reservation_id: int, db: Session = Depends(get_db)):
    """删除商务餐预订请求"""
    success = biz_reservation_request_dao.delete(session=db, reservation_id=reservation_id)
    if not success:
        raise HTTPException(status_code=404, detail="商务餐预订请求不存在")
    return {"message": "商务餐预订请求删除成功"}


# 按用户获取商务餐预订请求
@router.get("/user/{user_id}", response_model=List[BizReservationRequest])
def get_user_biz_reservations(user_id: int, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取指定用户的所有商务餐预订请求"""
    return biz_reservation_request_dao.get_by_user(session=db, user_id=user_id, skip=skip, limit=limit)


# 根据联系信息搜索商务餐预订请求
@router.get("/search/contact", response_model=List[BizReservationRequest])
def search_biz_reservations_by_contact(
    name: str = Query(None, description="联系人姓名（模糊搜索）"),
    phone: str = Query(None, description="联系人电话（模糊搜索）"),
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """根据联系信息搜索商务餐预订请求"""
    return biz_reservation_request_dao.get_by_contact_info(
        session=db, name=name, phone=phone, skip=skip, limit=limit
    )
