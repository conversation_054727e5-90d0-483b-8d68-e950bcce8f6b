from typing import List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from starlette import status

from app.core.deps import get_db
from app.dao.reservation import buffet_reservation_request_dao
from app.schemas.reservation import (
    BuffetReservationRequest, BuffetReservationRequestCreate, BuffetReservationRequestUpdate,
    ReservationActionResponse
)

router = APIRouter()


# 自助餐预订请求基本CRUD接口
@router.post("/", response_model=BuffetReservationRequest, status_code=status.HTTP_201_CREATED)
def create_buffet_reservation_request(reservation: BuffetReservationRequestCreate, db: Session = Depends(get_db)):
    """创建自助餐预订请求"""
    return buffet_reservation_request_dao.create(session=db, reservation=reservation)


@router.get("/{reservation_id}", response_model=BuffetReservationRequest)
def read_buffet_reservation_request(reservation_id: int, db: Session = Depends(get_db)):
    """获取自助餐预订请求详情"""
    db_reservation = buffet_reservation_request_dao.get(session=db, reservation_id=reservation_id)
    if db_reservation is None:
        raise HTTPException(status_code=404, detail="自助餐预订请求不存在")
    return db_reservation


@router.get("/", response_model=List[BuffetReservationRequest])
def read_buffet_reservation_requests(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取自助餐预订请求列表"""
    return buffet_reservation_request_dao.get_list(session=db, skip=skip, limit=limit)


@router.put("/{reservation_id}", response_model=BuffetReservationRequest)
def update_buffet_reservation_request(reservation_id: int, reservation: BuffetReservationRequestUpdate, db: Session = Depends(get_db)):
    """更新自助餐预订请求"""
    db_reservation = buffet_reservation_request_dao.update(session=db, reservation_id=reservation_id, reservation=reservation)
    if db_reservation is None:
        raise HTTPException(status_code=404, detail="自助餐预订请求不存在")
    return db_reservation


@router.delete("/{reservation_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_buffet_reservation_request(reservation_id: int, db: Session = Depends(get_db)):
    """删除自助餐预订请求"""
    success = buffet_reservation_request_dao.delete(session=db, reservation_id=reservation_id)
    if not success:
        raise HTTPException(status_code=404, detail="自助餐预订请求不存在")
    return {"message": "自助餐预订请求删除成功"}


# 按用户获取自助餐预订请求
@router.get("/user/{user_id}", response_model=List[BuffetReservationRequest])
def get_user_buffet_reservations(user_id: int, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取指定用户的所有自助餐预订请求"""
    return buffet_reservation_request_dao.get_by_user(session=db, user_id=user_id, skip=skip, limit=limit) 