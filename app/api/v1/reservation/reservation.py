from typing import List
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from starlette import status

from app.core.deps import get_db
from app.dao.reservation import reservation_request_dao
from app.models.reservation import ReservationStatus, ReservationType, ReservationScope
from app.schemas.reservation import (
    ReservationRequest, ReservationRequestCreate, ReservationRequestUpdate,
    ReservationRequestDetail, ReservationActionResponse
)

router = APIRouter()


# 预订请求基本CRUD接口
@router.post("/", response_model=ReservationRequest, status_code=status.HTTP_201_CREATED)
def create_reservation_request(reservation: ReservationRequestCreate, db: Session = Depends(get_db)):
    """创建预订请求"""
    return reservation_request_dao.create(session=db, reservation=reservation)


@router.get("/{reservation_id}", response_model=ReservationRequestDetail)
def read_reservation_request(reservation_id: int, db: Session = Depends(get_db)):
    """获取预订请求详情"""
    db_reservation = reservation_request_dao.get(session=db, reservation_id=reservation_id)
    if db_reservation is None:
        raise HTTPException(status_code=404, detail="预订请求不存在")
    return db_reservation


@router.get("/", response_model=List[ReservationRequest])
def read_reservation_requests(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取预订请求列表"""
    return reservation_request_dao.get_list(session=db, skip=skip, limit=limit)


@router.put("/{reservation_id}", response_model=ReservationRequest)
def update_reservation_request(reservation_id: int, reservation: ReservationRequestUpdate, db: Session = Depends(get_db)):
    """更新预订请求"""
    db_reservation = reservation_request_dao.update(session=db, reservation_id=reservation_id, reservation=reservation)
    if db_reservation is None:
        raise HTTPException(status_code=404, detail="预订请求不存在")
    return db_reservation


@router.delete("/{reservation_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_reservation_request(reservation_id: int, db: Session = Depends(get_db)):
    """删除预订请求"""
    success = reservation_request_dao.delete(session=db, reservation_id=reservation_id)
    if not success:
        raise HTTPException(status_code=404, detail="预订请求不存在")
    return {"message": "预订请求删除成功"}


# 按用户获取预订请求
@router.get("/user/{user_id}", response_model=List[ReservationRequest])
def get_user_reservations(user_id: int, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取指定用户的所有预订请求"""
    return reservation_request_dao.get_by_user(session=db, user_id=user_id, skip=skip, limit=limit)


# 按产品获取预订请求
@router.get("/product/{product_id}", response_model=List[ReservationRequest])
def get_product_reservations(product_id: int, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取指定产品的所有预订请求"""
    return reservation_request_dao.get_by_product(session=db, product_id=product_id, skip=skip, limit=limit)


# 按订单获取预订请求
@router.get("/order/{orders_id}", response_model=List[ReservationRequest])
def get_order_reservations(orders_id: int, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取指定订单的所有预订请求"""
    return reservation_request_dao.get_by_order(session=db, orders_id=orders_id, skip=skip, limit=limit)


# 按状态获取预订请求
@router.get("/status/{status}", response_model=List[ReservationRequest])
def get_reservations_by_status(status: ReservationStatus, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取指定状态的所有预订请求"""
    return reservation_request_dao.get_by_status(session=db, status=status, skip=skip, limit=limit)


# 按类型获取预订请求
@router.get("/type/{reservation_type}", response_model=List[ReservationRequest])
def get_reservations_by_type(reservation_type: ReservationType, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取指定类型的所有预订请求"""
    return reservation_request_dao.get_by_type(session=db, reservation_type=reservation_type, skip=skip, limit=limit)


# 按范围获取预订请求
@router.get("/scope/{scope}", response_model=List[ReservationRequest])
def get_reservations_by_scope(scope: ReservationScope, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取指定范围的所有预订请求"""
    return reservation_request_dao.get_by_scope(session=db, scope=scope, skip=skip, limit=limit)


# 按就餐时间范围获取预订请求
@router.get("/dining-time", response_model=List[ReservationRequest])
def get_reservations_by_dining_time(
    start_time: datetime = Query(None, description="就餐开始时间"),
    end_time: datetime = Query(None, description="就餐结束时间"),
    skip: int = 0, 
    limit: int = 100, 
    db: Session = Depends(get_db)
):
    """获取指定就餐时间范围内的预订请求"""
    return reservation_request_dao.get_by_dining_time_range(session=db, start_time=start_time, end_time=end_time, skip=skip, limit=limit)


# 根据验证码获取预订请求
@router.get("/verification/{verification_code}", response_model=ReservationRequestDetail)
def get_reservation_by_verification_code(verification_code: str, db: Session = Depends(get_db)):
    """根据验证码获取预订请求"""
    db_reservation = reservation_request_dao.get_by_verification_code(session=db, verification_code=verification_code)
    if db_reservation is None:
        raise HTTPException(status_code=404, detail="预订请求不存在")
    return db_reservation


# 特殊操作接口
@router.post("/{reservation_id}/pay-deposit", response_model=ReservationActionResponse)
def pay_deposit(reservation_id: int, db: Session = Depends(get_db)):
    """支付定金"""
    db_reservation = reservation_request_dao.pay_deposit(session=db, reservation_id=reservation_id)
    if db_reservation is None:
        raise HTTPException(status_code=404, detail="预订请求不存在")
    return ReservationActionResponse(
        success=True,
        message="定金支付成功",
        reservation=db_reservation
    )


@router.post("/{reservation_id}/pay-balance", response_model=ReservationActionResponse)
def pay_balance(reservation_id: int, db: Session = Depends(get_db)):
    """支付尾款"""
    # 先获取预订请求
    db_reservation = reservation_request_dao.get(session=db, reservation_id=reservation_id)
    if db_reservation is None:
        raise HTTPException(status_code=404, detail="预订请求不存在")
    
    # 检查是否可以支付尾款（必须已支付定金）
    if db_reservation.status != ReservationStatus.PAID_DEPOSIT:
        raise HTTPException(status_code=400, detail="当前状态无法支付尾款，请先支付定金")
    
    # 支付尾款
    db_reservation = reservation_request_dao.pay_balance(session=db, reservation_id=reservation_id)
    
    return ReservationActionResponse(
        success=True,
        message="尾款支付成功",
        reservation=db_reservation
    )


@router.post("/{reservation_id}/cancel", response_model=ReservationActionResponse)
def cancel_reservation(reservation_id: int, db: Session = Depends(get_db)):
    """取消预订"""
    db_reservation = reservation_request_dao.cancel_reservation(session=db, reservation_id=reservation_id)
    if db_reservation is None:
        raise HTTPException(status_code=404, detail="预订请求不存在")
    return ReservationActionResponse(
        success=True,
        message="预订已取消",
        reservation=db_reservation
    ) 