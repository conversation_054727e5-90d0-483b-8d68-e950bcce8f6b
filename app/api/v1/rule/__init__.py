from fastapi import APIRouter
from .archive_rule import router as archive_rule_router
from .archive_rule_item import router as archive_rule_item_router
from .rule import router as rule_router
from .rule_dining_reservation import router as rule_dining_reservation_router
from .rule_item import router as rule_item_router
from .rule_rel import router as rule_rel_router

# 创建主路由
router = APIRouter()

# 包含所有子路由
router.include_router(archive_rule_router)
router.include_router(archive_rule_item_router)
router.include_router(rule_router)
router.include_router(rule_dining_reservation_router)
router.include_router(rule_item_router)
router.include_router(rule_rel_router)

__all__ = ["router"]
