from typing import List

from fastapi import Depends, HTTPException, APIRouter
from sqlalchemy.orm import Session
from starlette import status

from app.core.deps import get_db
from app.dao.rule import rule_dao
from app.schemas.rule import RuleResponse, RuleCreate, RuleWithItems, RuleUpdate

router = APIRouter()


@router.post("/", response_model=RuleResponse, status_code=status.HTTP_201_CREATED)
def create_rule(rule: RuleCreate, db: Session = Depends(get_db)):
    """创建规则"""
    return rule_dao.create(session=db, rule=rule)


@router.delete("/{rule_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_rule(rule_id: int, db: Session = Depends(get_db)):
    """删除规则"""
    success = rule_dao.delete(session=db, rule_id=rule_id)
    if not success:
        raise HTTPException(status_code=404, detail="规则不存在")
    return {"message": "规则删除成功"}


@router.put("/{rule_id}", response_model=RuleResponse)
def update_rule(rule_id: int, rule: RuleUpdate, db: Session = Depends(get_db)):
    """更新规则信息"""
    db_rule = rule_dao.update(session=db, rule_id=rule_id, rule=rule)
    if db_rule is None:
        raise HTTPException(status_code=404, detail="规则不存在")
    return db_rule


@router.get("/", response_model=List[RuleResponse])
def read_rules(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取规则列表"""
    return rule_dao.get_list(session=db, skip=skip, limit=limit)


@router.get("/active/", response_model=List[RuleResponse])
def read_active_rules(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取活跃状态的规则列表"""
    return rule_dao.get_active_rules(session=db, skip=skip, limit=limit)


@router.get("/by/product/{product_id}", response_model=List[RuleResponse])
def get_rules_by_product(product_id: int, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取产品关联的规则列表"""
    return rule_dao.get_rules_by_product(session=db, product_id=product_id, skip=skip, limit=limit)


@router.get("/{rule_id}", response_model=RuleWithItems)
def read_rule(rule_id: int, db: Session = Depends(get_db)):
    """获取规则详情，包含规则项"""
    db_rule = rule_dao.get(session=db, rule_id=rule_id)
    if db_rule is None:
        raise HTTPException(status_code=404, detail="规则不存在")
    return db_rule
