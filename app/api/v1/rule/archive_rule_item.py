from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.dao.rule import rule_dao, rule_item_dao
from app.schemas.rule import (
    RuleItemCreate, RuleItemUpdate, RuleItemResponse
)

router = APIRouter()


@router.post("/item/", response_model=RuleItemResponse, status_code=status.HTTP_201_CREATED)
def create_rule_item(rule_item: RuleItemCreate, db: Session = Depends(get_db)):
    """创建规则项"""
    # 检查规则是否存在
    rule = rule_dao.get(session=db, rule_id=rule_item.rule_id)
    if not rule:
        raise HTTPException(status_code=404, detail="关联的规则不存在")
    return rule_item_dao.create(session=db, rule_item=rule_item)


@router.delete("/item/{rule_item_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_rule_item(rule_item_id: int, db: Session = Depends(get_db)):
    """删除规则项"""
    success = rule_item_dao.delete(session=db, rule_item_id=rule_item_id)
    if not success:
        raise HTTPException(status_code=404, detail="规则项不存在")
    return {"message": "规则项删除成功"}


@router.put("/item/{rule_item_id}", response_model=RuleItemResponse)
def update_rule_item(rule_item_id: int, rule_item: RuleItemUpdate, db: Session = Depends(get_db)):
    """更新规则项"""
    db_rule_item = rule_item_dao.update(session=db, rule_item_id=rule_item_id, rule_item=rule_item)
    if db_rule_item is None:
        raise HTTPException(status_code=404, detail="规则项不存在")
    return db_rule_item


@router.get("/item/{rule_item_id}", response_model=RuleItemResponse)
def read_rule_item(rule_item_id: int, db: Session = Depends(get_db)):
    """获取规则项详情"""
    db_rule_item = rule_item_dao.get(session=db, rule_item_id=rule_item_id)
    if db_rule_item is None:
        raise HTTPException(status_code=404, detail="规则项不存在")
    return db_rule_item


@router.get("/{rule_id}/items/", response_model=List[RuleItemResponse])
def read_rule_items_by_rule(rule_id: int, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取特定规则的所有规则项"""
    # 检查规则是否存在
    rule = rule_dao.get(session=db, rule_id=rule_id)
    if not rule:
        raise HTTPException(status_code=404, detail="规则不存在")
    return rule_item_dao.get_by_rule(session=db, rule_id=rule_id, skip=skip, limit=limit)
