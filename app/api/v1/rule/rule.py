from typing import Dict

from fastapi import APIRouter, Depends, HTTPException, status, Body
from sqlalchemy.orm import Session
from starlette.responses import JSONResponse

from app.core.deps import get_db
from app.dao.rule import rule_dao
from app.schemas.common import CommonResponse
from app.schemas.rule import (
    RuleCreate, RuleUpdate, RuleResponse, RuleWithItems,
    RuleSearchRequest, RuleListFormattedResponse,
    RuleStatusUpdateRequest
)

router = APIRouter()


# 新增功能-搜索规则
@router.get("/search/", response_model=RuleListFormattedResponse)
def search_rules(request: RuleSearchRequest = Depends(), db: Session = Depends(get_db)):
    """
    根据条件搜索规则列表

    可以根据以下条件进行搜索:
    - keyword: 关键词，用于全文搜索
    - name: 规则名称
    - status: 规则状态
    """
    skip = max(0, request.page - 1) * request.pageSize
    limit = request.pageSize

    # 调用DAO层进行搜索
    result = rule_dao.search(
        session=db,
        keyword=request.keyword,
        name=request.name,
        status=request.status,
        skip=skip,
        limit=limit
    )

    return {
        "code": 200,
        "message": "搜索规则列表成功",
        "data": result
    }


# 新增功能-创建规则
@router.post("/create", response_model=CommonResponse, status_code=status.HTTP_201_CREATED)
def create_rule(body: dict = Body(...), db: Session = Depends(get_db)):
    """
    创建规则，支持创建不同类型的规则
    
    支持创建不同类型的规则：
    - 一般规则 (reservation)
    - 餐厅预订规则 (dining_reservation)
    
    示例一般规则:
    {
        "name": "新规则",
        "status": "active",
        "type": "reservation",
        "scope": "product",
        "order_type": "none"
    }
    
    示例餐厅预订规则:
    {
        "name": "餐厅预订规则",
        "status": "active",
        "type": "dining_reservation",
        "scope": "product", 
        "order_type": "reservation",
        "alias": "晚餐",
        "dining_start_time_cron_str": "0 18 * * *",
        "dining_end_time_cron_str": "0 21 * * *",
        "verify_start_time_cron_str": "0 17 * * *",
        "verify_end_time_cron_str": "0 22 * * *",
        "order_deadline": 120,
        "cancellation_deadline": 60,
        "is_auto_verify": false,
        "generated_count": 1,
        "quantity": 50
    }
    """
    try:
        # 根据规则类型创建对应的规则
        rule_type = body.get("type", "reservation")
        
        if rule_type == "dining_reservation":
            # 创建餐厅预订规则
            from app.schemas.rule import DiningReservationRuleCreate
            rule_model = DiningReservationRuleCreate(**body)
            db_result = rule_dao.create_dining_reservation_rule(session=db, rule=rule_model)
            from app.schemas.rule import DiningReservationRuleResponse
            result = DiningReservationRuleResponse.model_validate(db_result)
        else:
            # 创建一般规则
            rule_model = RuleCreate(**body)
            db_result = rule_dao.create(session=db, rule=rule_model)
            result = RuleResponse.model_validate(db_result)

        return {
            "code": 200,
            "message": f"创建{rule_type}规则成功",
            "data": result.model_dump()
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"创建规则失败: {str(e)}",
                "data": None
            }
        )


# 新增功能-删除规则
@router.delete("/delete/{rule_id}")
def delete_rule_new(rule_id: int, db: Session = Depends(get_db)):
    """删除规则"""
    # 先检查规则是否存在
    db_rule = rule_dao.get(session=db, rule_id=rule_id)
    if not db_rule:
        return JSONResponse({
            "code": 400,
            "message": "规则不存在",
            "data": None
        }, status_code=200)

    # 检查是否存在关联的预约请求
    if db_rule.reservation_requests and len(db_rule.reservation_requests) > 0:
        return JSONResponse({
            "code": 400,
            "message": "无法删除规则，存在关联的预约请求，请先删除或修改相关预约",
            "data": None
        }, status_code=200)

    success = rule_dao.delete(session=db, rule_id=rule_id)
    return JSONResponse({"message": "规则删除成功"}, status_code=204)


# 新增功能-查看规则详情
@router.get("/view/{rule_id}", response_model=CommonResponse)
def view_rule(rule_id: int, db: Session = Depends(get_db)):
    """
    查看规则详情
    
    根据传入的id，获取规则，再根据对应的类型，获取对应类型的规则详情
    """
    try:
        # 先获取基础规则信息，确定规则类型
        base_rule = rule_dao.get(session=db, rule_id=rule_id)
        if base_rule is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "规则不存在",
                    "data": None
                }
            )

        # 根据规则类型获取详细信息
        if base_rule.type.value == "dining_reservation":
            db_result = rule_dao.get_dining_reservation_rule(session=db, rule_id=rule_id)
            if db_result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "餐厅预订规则不存在",
                        "data": None
                    }
                )
            # 将SQLAlchemy模型转换为Pydantic模型
            from app.schemas.rule import DiningReservationRuleWithItems
            result = DiningReservationRuleWithItems.model_validate(db_result)
        else:
            # 将SQLAlchemy模型转换为Pydantic模型
            result = RuleWithItems.model_validate(base_rule)

        return {
            "code": 200,
            "message": f"获取{base_rule.type.value}规则详情成功",
            "data": result.model_dump()
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"获取规则详情失败: {str(e)}",
                "data": None
            }
        )


# 新增功能-更新规则
@router.put("/update/{rule_id}", response_model=CommonResponse)
def update_rule(rule_id: int, body: dict = Body(...), db: Session = Depends(get_db)):
    """
    更新规则信息
    
    根据传入的id，判断对应的规则类型，找到对应的规则，进行更新
    """
    try:
        # 先获取基础规则信息，确定规则类型
        base_rule = rule_dao.get(session=db, rule_id=rule_id)
        if base_rule is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "规则不存在",
                    "data": None
                }
            )

        # 根据规则类型更新信息
        if base_rule.type.value == "dining_reservation":
            from app.schemas.rule import DiningReservationRuleUpdate
            rule_data = DiningReservationRuleUpdate(**body)
            db_result = rule_dao.update_dining_reservation_rule(session=db, rule_id=rule_id, rule=rule_data)
            if db_result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "餐厅预订规则不存在",
                        "data": None
                    }
                )
            # 将SQLAlchemy模型转换为Pydantic模型
            from app.schemas.rule import DiningReservationRuleResponse
            result = DiningReservationRuleResponse.model_validate(db_result)
        else:
            # 更新一般规则
            rule_data = RuleUpdate(**body)
            db_result = rule_dao.update(session=db, rule_id=rule_id, rule=rule_data)
            if db_result is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "规则不存在",
                        "data": None
                    }
                )
            # 将SQLAlchemy模型转换为Pydantic模型
            result = RuleResponse.model_validate(db_result)

        return {
            "code": 200,
            "message": f"更新{base_rule.type.value}规则成功",
            "data": result.model_dump()
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"更新规则失败: {str(e)}",
                "data": None
            }
        )


# 新增功能-更新规则状态
@router.post("/status/{rule_id}", response_model=Dict)
def update_rule_status(rule_id: int, request: RuleStatusUpdateRequest = Body(...), db: Session = Depends(get_db)):
    """修改规则状态"""
    try:
        # 更新规则状态
        result = rule_dao.update(
            session=db,
            rule_id=rule_id,
            rule=RuleUpdate(status=request.status)
        )
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "规则不存在",
                    "data": None
                }
            )

        # 将SQLAlchemy模型转换为Pydantic模型
        response_model = RuleResponse.model_validate(result)
        return {
            "code": 200,
            "message": "规则状态更新成功",
            "data": response_model.model_dump()
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"规则状态更新失败: {str(e)}",
                "data": None
            }
        )
