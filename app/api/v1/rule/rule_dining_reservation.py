from typing import Dict, Any

from fastapi import Depends, HTTPException, Body, APIRouter
from sqlalchemy.orm import Session
from starlette import status

from app.core.deps import get_db
from app.dao.rule import rule_dao
from app.models.rule import RuleType
from app.schemas.common import CommonResponse
from app.schemas.rule import DiningReservationRuleCreate, DiningReservationRuleResponse, DiningReservationRuleUpdate, \
    DiningReservationRuleWithItems

router = APIRouter()


@router.get("/dining-reservation/{rule_id}", response_model=Dict[str, Any])
def get_dining_reservation_rule(rule_id: int, db: Session = Depends(get_db)):
    """获取餐厅预订规则详情

    根据规则ID获取餐厅预订规则(DiningReservationRule)的所有字段

    Args:
        rule_id: 规则ID
        db: 数据库会话

    Returns:
        Dict: 包含餐厅预订规则所有字段的字典
    """
    # 先检查基本规则是否存在
    basic_rule = rule_dao.get(session=db, rule_id=rule_id)
    if not basic_rule:
        raise HTTPException(status_code=404, detail=f"规则ID {rule_id} 不存在")

    # 检查规则类型是否为餐厅预订规则
    if basic_rule.type != RuleType.DINING_RESERVATION:
        raise HTTPException(status_code=400, detail=f"规则ID {rule_id} 不是餐厅预订规则类型")

    # 获取餐厅预订规则详情
    dining_rule = rule_dao.get_dining_reservation_rule(session=db, rule_id=rule_id)
    if not dining_rule:
        raise HTTPException(status_code=404, detail=f"餐厅预订规则ID {rule_id} 不存在")

    # 转换为包含所有字段的字典
    result = rule_dao.dining_reservation_rule_to_dict(dining_rule)

    return {
        "code": 200,
        "message": "获取餐厅预订规则成功",
        "data": result
    }


@router.post("/dining-reservation/create", response_model=CommonResponse, status_code=status.HTTP_201_CREATED)
def create_dining_reservation_rule(body: dict = Body(...), db: Session = Depends(get_db)):
    """创建餐厅预订规则

    创建一个新的餐厅预订规则，包含基本规则字段和餐厅预订特有字段
    """
    try:
        # 创建餐厅预订规则对象
        rule_data = DiningReservationRuleCreate(**body)
        db_result = rule_dao.create_dining_reservation_rule(session=db, rule=rule_data)

        # 转换为响应对象
        result = DiningReservationRuleResponse.model_validate(db_result)

        return {
            "code": 200,
            "message": "创建餐厅预订规则成功",
            "data": result.model_dump()
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"创建餐厅预订规则失败: {str(e)}",
                "data": None
            }
        )


@router.put("/dining-reservation/update/{rule_id}", response_model=CommonResponse)
def update_dining_reservation_rule(rule_id: int, body: dict = Body(...), db: Session = Depends(get_db)):
    """更新餐厅预订规则

    根据传入的id更新餐厅预订规则信息，包括基本规则字段和餐厅预订特有字段
    """
    try:
        # 先检查规则是否存在且为餐厅预订规则类型
        basic_rule = rule_dao.get(session=db, rule_id=rule_id)
        if not basic_rule:
            return {
                "code": 404,
                "message": "规则不存在",
                "data": None
            }

        if basic_rule.type != RuleType.DINING_RESERVATION:
            return {
                "code": 400,
                "message": "该规则不是餐厅预订规则类型",
                "data": None
            }

        # 创建更新餐厅预订规则对象
        rule_data = DiningReservationRuleUpdate(**body)

        # 更新规则
        db_result = rule_dao.update_dining_reservation_rule(session=db, rule_id=rule_id, rule=rule_data)
        if db_result is None:
            return {
                "code": 404,
                "message": "规则不存在",
                "data": None
            }

        # 将SQLAlchemy模型转换为Pydantic模型
        result = DiningReservationRuleResponse.model_validate(db_result)

        return {
            "code": 200,
            "message": "更新餐厅预订规则成功",
            "data": result.model_dump()
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"更新餐厅预订规则失败: {str(e)}",
                "data": None
            }
        )


@router.get("/dining-reservation/view/{rule_id}", response_model=CommonResponse)
def view_dining_reservation_rule_with_items(rule_id: int, db: Session = Depends(get_db)):
    """查看餐厅预订规则详情，包含规则项

    根据传入的id获取餐厅预订规则详情，包括基本规则字段、餐厅预订特有字段和关联的规则项
    """
    try:
        # 先检查基本规则是否存在
        basic_rule = rule_dao.get(session=db, rule_id=rule_id)
        if not basic_rule:
            return {
                "code": 404,
                "message": "规则不存在",
                "data": None
            }

        # 检查规则类型是否为餐厅预订规则
        if basic_rule.type != RuleType.DINING_RESERVATION:
            return {
                "code": 400,
                "message": "该规则不是餐厅预订规则类型",
                "data": None
            }

        # 获取餐厅预订规则详情
        dining_rule = rule_dao.get_dining_reservation_rule(session=db, rule_id=rule_id)
        if not dining_rule:
            return {
                "code": 404,
                "message": "餐厅预订规则不存在",
                "data": None
            }

        # 将SQLAlchemy模型转换为Pydantic模型
        result = DiningReservationRuleWithItems.model_validate(dining_rule)

        return {
            "code": 200,
            "message": "获取餐厅预订规则详情成功",
            "data": result.model_dump()
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"获取餐厅预订规则详情失败: {str(e)}",
                "data": None
            }
        )
