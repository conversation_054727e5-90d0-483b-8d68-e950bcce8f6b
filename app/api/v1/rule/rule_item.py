from fastapi import Body, Depends, HTTPException, APIRouter
from sqlalchemy.orm import Session
from starlette import status
from starlette.responses import JSONResponse

from app.core.deps import get_db
from app.dao.rule import rule_dao, rule_item_dao
from app.schemas.common import CommonResponse
from app.schemas.rule import RuleItemCreate, RuleItemResponse, RuleItemUpdate

router = APIRouter()


@router.post("/item/create", response_model=CommonResponse, status_code=status.HTTP_201_CREATED)
def create_rule_item_new(body: dict = Body(...), db: Session = Depends(get_db)):
    """
    创建规则项

    通过JSON请求体传入RuleItemCreate数据创建规则项
    """
    try:
        # 创建规则项对象
        rule_item_data = RuleItemCreate(**body)

        # 检查规则是否存在
        rule = rule_dao.get(session=db, rule_id=rule_item_data.rule_id)
        if not rule:
            return JSONResponse({
                "code": 400,
                "message": "关联的规则不存在",
                "data": None
            }, status_code=200)

        db_result = rule_item_dao.create(session=db, rule_item=rule_item_data)

        # 转换为响应对象
        result = RuleItemResponse.model_validate(db_result)

        return {
            "code": 200,
            "message": "创建规则项成功",
            "data": result.model_dump()
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"创建规则项失败: {str(e)}",
                "data": None
            }
        )


@router.delete("/item/delete/{rule_item_id}", response_model=CommonResponse)
def delete_rule_item_new(rule_item_id: int, db: Session = Depends(get_db)):
    """
    删除规则项

    根据传入的id删除规则项
    """
    try:
        # 先检查规则项是否存在
        db_rule_item = rule_item_dao.get(session=db, rule_item_id=rule_item_id)
        if not db_rule_item:
            return {
                "code": 400,
                "message": "规则项不存在",
                "data": None
            }

        success = rule_item_dao.delete(session=db, rule_item_id=rule_item_id)
        return {
            "code": 200,
            "message": "规则项删除成功",
            "data": None
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"删除规则项失败: {str(e)}",
                "data": None
            }
        )


@router.put("/item/update/{rule_item_id}", response_model=CommonResponse)
def update_rule_item_new(rule_item_id: int, body: dict = Body(...), db: Session = Depends(get_db)):
    """
    更新规则项信息

    根据传入的id更新规则项信息
    """
    try:
        # 创建更新规则项对象
        rule_item_data = RuleItemUpdate(**body)

        # 更新规则项
        db_result = rule_item_dao.update(session=db, rule_item_id=rule_item_id, rule_item=rule_item_data)
        if db_result is None:
            return {
                "code": 400,
                "message": "规则项不存在",
                "data": None
            }

        # 将SQLAlchemy模型转换为Pydantic模型
        result = RuleItemResponse.model_validate(db_result)

        return {
            "code": 200,
            "message": "更新规则项成功",
            "data": result.model_dump()
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"更新规则项失败: {str(e)}",
                "data": None
            }
        )


@router.get("/item/view/{rule_item_id}", response_model=CommonResponse)
def view_rule_item(rule_item_id: int, db: Session = Depends(get_db)):
    """
    查看规则项详情

    根据传入的id获取规则项详情
    """
    try:
        # 获取规则项
        db_rule_item = rule_item_dao.get(session=db, rule_item_id=rule_item_id)
        if db_rule_item is None:
            return {
                "code": 400,
                "message": "规则项不存在",
                "data": None
            }

        # 将SQLAlchemy模型转换为Pydantic模型
        result = RuleItemResponse.model_validate(db_rule_item)

        return {
            "code": 200,
            "message": "获取规则项详情成功",
            "data": result.model_dump()
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"获取规则项详情失败: {str(e)}",
                "data": None
            }
        )
