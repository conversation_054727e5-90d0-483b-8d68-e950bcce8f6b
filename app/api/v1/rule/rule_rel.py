from typing import Dict, List

from fastapi import Depends, HTTPException, APIRouter
from sqlalchemy.orm import Session
from starlette import status

from app.core.deps import get_db
from app.dao.rule import rule_dao
from app.schemas.product import ProductResponse
from app.schemas.rule import ProductAndRule

router = APIRouter()


@router.post("/add/products/", status_code=status.HTTP_200_OK, response_model=Dict)
def add_products_to_rule(binding: ProductAndRule, db: Session = Depends(get_db)):
    """批量将规则绑定到产品"""
    result = rule_dao.add_products(
        session=db,
        rule_id=binding.rule_id,
        product_ids=binding.product_ids
    )

    if not result["success"] and result["failed"]:
        message = "所有产品绑定失败"
    else:
        message = "规则批量绑定到产品操作完成"

    return {
        "message": message,
        "success_count": len(result["success"]),
        "failed_count": len(result["failed"]),
        "success_ids": result["success"],
        "failed_ids": result["failed"]
    }


@router.post("/remove/products/", status_code=status.HTTP_200_OK, response_model=Dict)
def remove_products_from_rule(binding: ProductAndRule, db: Session = Depends(get_db)):
    """批量将规则从产品解绑"""
    result = rule_dao.remove_products(
        session=db,
        rule_id=binding.rule_id,
        product_ids=binding.product_ids
    )

    if not result["success"] and result["failed"]:
        message = "所有产品解绑失败"
    else:
        message = "规则批量从产品解绑操作完成"

    return {
        "message": message,
        "success_count": len(result["success"]),
        "failed_count": len(result["failed"]),
        "success_ids": result["success"],
        "failed_ids": result["failed"]
    }


@router.get("/{rule_id}/products", response_model=List[ProductResponse])
def get_products_by_rule(rule_id: int, skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取与特定规则关联的所有产品"""
    rule = rule_dao.get(session=db, rule_id=rule_id)
    if not rule:
        raise HTTPException(status_code=404, detail="规则不存在")

    products = rule_dao.get_products_by_rule(
        session=db,
        rule_id=rule_id,
        skip=skip,
        limit=limit
    )
    return products
