from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.dao.product import product_dao
from app.dao.tag import tag_dao
from app.schemas.tag import TagCreate, TagUpdate, TagResponse, MultiProductBinding

router = APIRouter()


@router.post("/", response_model=TagResponse, status_code=status.HTTP_201_CREATED)
def create_tag(tag: TagCreate, db: Session = Depends(get_db)):
    """创建标签"""
    return tag_dao.create(session=db, tag=tag)


@router.get("/{tag_id}", response_model=TagResponse)
def read_tag(tag_id: int, db: Session = Depends(get_db)):
    """获取标签详情"""
    db_tag = tag_dao.get(session=db, tag_id=tag_id)
    if db_tag is None:
        raise HTTPException(status_code=404, detail="标签不存在")
    return db_tag


@router.get("/by-name/{name}", response_model=TagResponse)
def read_tag_by_name(name: str, db: Session = Depends(get_db)):
    """根据名称获取标签"""
    db_tag = tag_dao.get_by_name(session=db, name=name)
    if db_tag is None:
        raise HTTPException(status_code=404, detail="标签不存在")
    return db_tag


@router.get("/", response_model=List[TagResponse])
def read_tags(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取标签列表"""
    return tag_dao.get_list(session=db, skip=skip, limit=limit)


@router.get("/products/{product_id}/", response_model=List[TagResponse])
def read_product_tags(product_id: int, db: Session = Depends(get_db)):
    """获取产品相关的标签列表"""
    product = product_dao.get(session=db, product_id=product_id)
    if product is None:
        raise HTTPException(status_code=404, detail="产品不存在")
    return tag_dao.get_by_product(session=db, product_id=product_id)


@router.put("/{tag_id}", response_model=TagResponse)
def update_tag(tag_id: int, tag: TagUpdate, db: Session = Depends(get_db)):
    """更新标签信息"""
    db_tag = tag_dao.update(session=db, tag_id=tag_id, tag=tag)
    if db_tag is None:
        raise HTTPException(status_code=404, detail="标签不存在")
    return db_tag


@router.delete("/{tag_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_tag(tag_id: int, db: Session = Depends(get_db)):
    """删除标签"""
    success = tag_dao.delete(session=db, tag_id=tag_id)
    if not success:
        raise HTTPException(status_code=404, detail="标签不存在")
    return {"message": "标签删除成功"}


@router.post("/{tag_id}/bind/products", response_model=TagResponse)
def bind_products_to_tag(tag_id: int, binding: MultiProductBinding, db: Session = Depends(get_db)):
    """将标签同时绑定到多个产品"""
    # 验证标签存在
    tag = tag_dao.get(session=db, tag_id=tag_id)
    if not tag:
        raise HTTPException(status_code=404, detail="标签不存在")

    # 验证至少有一个产品
    if not binding.product_ids:
        raise HTTPException(status_code=400, detail="必须提供至少一个产品ID")

    # 批量绑定
    tag = tag_dao.bind_products(session=db, tag_id=tag_id, product_ids=binding.product_ids)
    if not tag:
        raise HTTPException(status_code=404, detail="没有找到有效的产品")

    return tag


@router.post("/{tag_id}/unbind/products", response_model=TagResponse)
def unbind_products_from_tag(tag_id: int, binding: MultiProductBinding, db: Session = Depends(get_db)):
    """将标签同时从多个产品解绑"""
    # 验证标签存在
    tag = tag_dao.get(session=db, tag_id=tag_id)
    if not tag:
        raise HTTPException(status_code=404, detail="标签不存在")

    # 验证至少有一个产品
    if not binding.product_ids:
        raise HTTPException(status_code=400, detail="必须提供至少一个产品ID")

    # 批量解绑
    tag = tag_dao.unbind_products(session=db, tag_id=tag_id, product_ids=binding.product_ids)

    return tag
