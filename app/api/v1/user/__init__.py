from fastapi import APIRouter
from .user import router as user_router
from .personal import router as personal_router
from .enterprise import router as enterprise_router
from .relations import router as relations_router

# 创建主路由
router = APIRouter()

# 包含所有子路由
router.include_router(user_router)
router.include_router(personal_router)
router.include_router(enterprise_router)
router.include_router(relations_router)

__all__ = ["router"]
