from typing import List, Dict, Any, Optional
import logging  # 添加日志模块导入
from datetime import datetime
import random
from typing import Dict

from fastapi import APIRouter, Depends, HTTPException, <PERSON><PERSON>, <PERSON>er, Query, Request, Form
from sqlalchemy.orm import Session
from starlette import status
from redis import Redis

from app.core.config import settings
from app.core.deps import get_db
from app.core.security import verify_password, oauth2_scheme, create_access_token, verify_token, get_password_hash
from app.dao.admin import admin_dao
from app.dao.user import user_dao, personal_user_dao, enterprise_dao, enterprise_user_relation_dao
from app.schemas.user import *
from app.schemas.user import (
    EnterpriseUserCreateRelationResponse,
    EnterpriseUserGetRelationResponse,
    EnterpriseUserUpdateRelationResponse)
from app.schemas.user import PersonalUserCreate, PersonalUserUpdate, PersonalUserResponse, PersonalUserDetailResponse, \
    PersonalUserSearchRequest, PersonalUserListResponse, PersonalUserSearchResponse, PersonalUserCreateResponse, \
    PersonalUserUpdateResponse, PersonalUserDeleteRequest, PersonalUserBatchStatusUpdate, EnterpriseCreate, \
    EnterpriseUpdate, EnterpriseResponse, EnterpriseDetailResponse, EnterpriseSearchRequest, EnterpriseListResponse, \
    EnterpriseSearchResponse, EnterpriseCreateResponse, \
    EnterpriseUpdateResponse, EnterpriseBatchStatusUpdate
from app.service.user import user_service
from app.core.security import verify_password, oauth2_scheme, create_access_token, verify_token, get_password_hash
from app.core.config import settings
from app.models.enum import Status
from app.utils.common import send_sms
from app.dao.admin import admin_dao
from app.utils.common import get_current_time

# 创建日志器
logger = logging.getLogger(__name__)
# 创建Redis连接
redis_client = Redis.from_url(settings.REDIS_URL)

router = APIRouter()


# 获取企业列表

@router.get("/enterprise/search/", response_model=EnterpriseSearchResponse)
def search_enterprises(request: EnterpriseSearchRequest = Depends(), db: Session = Depends(get_db)):
    """根据条件搜索企业用户列表

    可以根据以下条件进行搜索:
    - keyword: 关键词，用于全文搜索
    - name: 企业名称
    - phone: 手机号
    - status: 企业状态
    """
    skip = max(0, request.page - 1) * request.pageSize
    limit = request.pageSize
    enterprises = enterprise_dao.search(
        session=db,
        keyword=request.keyword,
        name=request.name,
        company_name=request.company_name,
        phone=request.phone,
        status=request.status,
        skip=skip,
        limit=limit
    )
    return {
        "code": 200,
        "message": "搜索企业用户列表成功",
        "data": enterprises
    }


@router.get("/enterprise/list", response_model=Dict[str, Any])
def get_enterprise_list(
        db: Session = Depends(get_db)
):
    """获取企业列表

    Args:
        db: 数据库会话
    Returns:
        包含企业列表和总数的字典
    """
    # 获取企业列表
    logger.info(f"获取企业列表 ...... ")
    enterprises = enterprise_dao.get_list(session=db)
    logger.info(f"获取企业列表成功")

    # 将SQLAlchemy模型对象转换为可序列化的字典
    enterprise_list = []
    for enterprise in enterprises:
        enterprise_dict = {
            "company_id": enterprise.id,
            "username": enterprise.username,
            "company_name": enterprise.company_name,
            "business_license": enterprise.business_license,
            "phone": enterprise.phone,
            "email": enterprise.email,
            "address": enterprise.address,
            "status": enterprise.status.value if hasattr(enterprise.status, 'value') else enterprise.status,
            "register_time": enterprise.register_time.isoformat() if enterprise.register_time else None,
            "created_at": enterprise.created_at.isoformat() if enterprise.created_at else None,
            "updated_at": enterprise.updated_at.isoformat() if enterprise.updated_at else None
        }
        enterprise_list.append(enterprise_dict)

    # 格式化响应
    result = {
        "code": 200,
        "msg": "获取企业列表成功",
        "results": enterprise_list,
        "count": len(enterprise_list)
    }

    return result


@router.get("/enterprise/", response_model=EnterpriseListResponse)
def get_enterprises(skip: int = 0, limit: int = 10, page: int = 0, pageSize: int = 10, db: Session = Depends(get_db)):
    """获取企业用户列表"""
    skip = max(0, page - 1) * pageSize
    limit = pageSize
    enterprises = enterprise_dao.get_list(session=db, skip=skip, limit=limit)
    return {
        "code": 200,
        "message": "获取企业用户列表成功",
        "data": enterprises
    }


@router.get("/enterprise/list/by/personal/{user_id}", response_model=Dict[str, Any])
def get_enterprises_by_personal_user(
        user_id: int,
        page: int = Query(1, description="页码，从1开始"),
        pageSize: int = Query(10, description="每页数量"),
        db: Session = Depends(get_db)
):
    """
    根据个人用户ID获取关联的企业列表

    Args:
        user_id: 个人用户ID
        page: 页码，默认为1
        pageSize: 每页数量，默认为10
        db: 数据库会话

    Returns:
        包含企业列表和总数的分页响应
    """
    # 检查个人用户是否存在
    db_user = personal_user_dao.get(session=db, user_id=user_id)
    if db_user is None:
        logger.warning(f"获取企业列表失败 - 个人用户不存在 - 用户ID: {user_id}")
        return {"code": 404, "message": "个人用户不存在", "data": {"list": [], "total": 0}}

    # 计算分页参数
    skip = max(0, page - 1) * pageSize
    limit = pageSize

    # 获取企业列表和总数
    enterprises = enterprise_user_relation_dao.get_enterprises_by_user(
        session=db,
        user_id=user_id,
        skip=skip,
        limit=limit
    )
    total = enterprise_user_relation_dao.count_enterprises_by_user(session=db, user_id=user_id)

    # 格式化返回数据
    enterprise_list = []
    for enterprise in enterprises:
        enterprise_dict = {
            "id": enterprise.id,
            "username": enterprise.username,
            "company_name": enterprise.company_name,
            "status": enterprise.status.value if hasattr(enterprise.status, 'value') else enterprise.status
        }
        enterprise_list.append(enterprise_dict)

    logger.info(f"获取企业列表成功 - 个人用户ID: {user_id} - 总数: {total}")

    return {
        "code": 200,
        "message": "获取企业列表成功",
        "data": {
            "list": enterprise_list,
            "total": total
        }
    }


@router.get("/enterprise/name-list/")
def get_enterprise_name_list(
        keyword: Optional[str] = Query(None, description="搜索关键字，用于模糊匹配企业名称"),
        db: Session = Depends(get_db)
):
    """
    根据关键字模糊搜索企业名称，返回企业ID和公司名称的列表

    Args:
        keyword: 搜索关键字，用于模糊匹配企业名称
        db: 数据库会话

    Returns:
        包含企业ID和公司名称的列表
        格式: {"code": 200, "message": "获取企业名称列表成功", "data": [{"id": 1, "company_name": "微软"}, {"id": 2, "company_name": "谷歌"}]}
    """
    try:
        if not keyword:
            return {
                "code": 200,
                "message": "获取企业名称列表成功",
                "data": []
            }

        result = enterprise_dao.get_name_list_by_keyword(session=db, keyword=keyword)

        return {
            "code": 200,
            "message": "获取企业名称列表成功",
            "data": result
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取企业名称列表失败: {str(e)}"
        )


@router.get("/enterprise/{enterprise_id}/users", response_model=List[PersonalUserResponse])
def get_enterprise_users(enterprise_id: int, db: Session = Depends(get_db)):
    """获取企业用户关联的所有个人用户"""
    db_enterprise = enterprise_dao.get(session=db, enterprise_id=enterprise_id)
    if db_enterprise is None:
        raise HTTPException(status_code=404, detail="企业用户不存在")
    return enterprise_dao.get_users(session=db, enterprise_id=enterprise_id)


@router.get("/enterprise/{enterprise_id}", response_model=EnterpriseDetailFormattedResponse)
def get_enterprise(enterprise_id: int, db: Session = Depends(get_db)):
    """获取企业用户信息"""
    enterprise_data = enterprise_dao.get_with_users(session=db, enterprise_id=enterprise_id)
    if enterprise_data is None:
        raise HTTPException(status_code=404, detail="企业用户不存在")
    return {
        "code": 200,
        "message": "获取企业用户成功",
        "data": enterprise_data
    }


# 企业用户接口
@router.post("/enterprise/", response_model=EnterpriseCreateResponse, status_code=status.HTTP_201_CREATED)
def create_enterprise(enterprise: EnterpriseCreate, db: Session = Depends(get_db)):
    """创建企业用户"""
    db_enterprise = enterprise_dao.create(session=db, enterprise=enterprise)
    return {
        "code": 200,
        "message": "创建企业用户成功",
        "data": db_enterprise
    }


# 带账户创建的企业用户接口
@router.post("/enterprise/with-accounts/",
             response_model=Dict[str, Any],
             status_code=status.HTTP_201_CREATED)
def create_enterprise_with_accounts(enterprise: EnterpriseCreate, db: Session = Depends(get_db)):
    """
    创建企业用户并同时创建关联账户（普通账户和赠送账户）
    """
    try:
        enterprise_user, accounts = user_service.create_enterprise(db, enterprise)
        # 使用字典直接序列化，避免Pydantic验证错误
        return {
            "enterprise": {
                "id": enterprise_user.id,
                "username": enterprise_user.username,
                "company_name": enterprise_user.company_name,
                "business_license": enterprise_user.business_license,
                "phone": enterprise_user.phone,
                "email": enterprise_user.email,
                "address": enterprise_user.address,
                "status": enterprise_user.status.value if hasattr(enterprise_user.status,
                                                                  'value') else enterprise_user.status,
                "register_time": enterprise_user.register_time.isoformat() if enterprise_user.register_time else None,
                "created_at": enterprise_user.created_at.isoformat() if enterprise_user.created_at else None,
                "updated_at": enterprise_user.updated_at.isoformat() if enterprise_user.updated_at else None
            },
            "regular_account": {
                "id": accounts["regular_account"].id,
                "user_id": accounts["regular_account"].user_id,
                "balance": accounts["regular_account"].balance,
                "status": accounts["regular_account"].status.value if hasattr(accounts["regular_account"].status,
                                                                              'value') else accounts[
                    "regular_account"].status,
                "created_at": accounts["regular_account"].created_at.isoformat() if accounts[
                    "regular_account"].created_at else None,
                "updated_at": accounts["regular_account"].updated_at.isoformat() if accounts[
                    "regular_account"].updated_at else None
            },
            "gift_account": {
                "id": accounts["gift_account"].id,
                "user_id": accounts["gift_account"].user_id,
                "balance": accounts["gift_account"].balance,
                "gift_amount": accounts["gift_account"].gift_amount,
                "status": accounts["gift_account"].status.value if hasattr(accounts["gift_account"].status,
                                                                           'value') else accounts[
                    "gift_account"].status,
                "created_at": accounts["gift_account"].created_at.isoformat() if accounts[
                    "gift_account"].created_at else None,
                "updated_at": accounts["gift_account"].updated_at.isoformat() if accounts[
                    "gift_account"].updated_at else None
            }
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"创建企业用户失败：{str(e)}"
        )


@router.put("/enterprise/{enterprise_id}", response_model=EnterpriseUpdateResponse)
def update_enterprise(enterprise_id: int, enterprise: EnterpriseUpdate, db: Session = Depends(get_db)):
    """更新企业用户"""
    db_enterprise = enterprise_dao.update(session=db, enterprise_id=enterprise_id, enterprise=enterprise)
    if db_enterprise is None:
        raise HTTPException(status_code=404, detail="企业用户不存在")
    return {
        "code": 200,
        "message": "更新企业用户成功",
        "data": db_enterprise
    }


# @router.delete("/enterprise/{enterprise_id}", status_code=status.HTTP_204_NO_CONTENT)
# def delete_enterprise(enterprise_id: int, db: Session = Depends(get_db)):
#     """删除企业用户"""
#     success = enterprise_dao.delete(session=db, enterprise_id=enterprise_id)
#     if not success:
#         raise HTTPException(status_code=404, detail="企业用户不存在")
#     return {"message": "企业用户删除成功"}


@router.post("/enterprise/status/", status_code=status.HTTP_200_OK)
def batch_set_enterprise_status(data: EnterpriseBatchStatusUpdate, db: Session = Depends(get_db)):
    """批量设置企业用户状态"""
    if not data.enterprise_ids:
        raise HTTPException(status_code=400, detail="企业ID列表不能为空")

    affected_count = enterprise_dao.batch_set_status(
        session=db,
        enterprise_ids=data.enterprise_ids,
        status=data.status
    )

    return {
        "code": 200,
        "message": f"成功更新{affected_count}个企业的状态",
        "data": {
            "affected_count": affected_count
        }
    }
