import logging

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from starlette import status

from app.core.deps import get_db
from app.dao.user import personal_user_dao, enterprise_dao, enterprise_user_relation_dao
from app.schemas.user import *
from app.schemas.user import PersonalUserCreate, PersonalUserUpdate, PersonalUserSearchRequest, \
    PersonalUserListResponse, PersonalUserSearchResponse, PersonalUserCreateResponse, \
    PersonalUserUpdateResponse, PersonalUserBatchStatusUpdate, EnterpriseResponse
from app.service.user import user_service

# 创建日志器
logger = logging.getLogger(__name__)

router = APIRouter()


# 个人用户接口
@router.get("/personal/search/", response_model=PersonalUserSearchResponse)
def search_personal_users(request: PersonalUserSearchRequest = Depends(), db: Session = Depends(get_db)):
    """根据条件搜索个人用户列表

    可以根据以下条件进行搜索:
    - keyword: 关键词，用于全文搜索
    - name: 用户名/真实姓名
    - phone: 手机号
    - status: 用户状态
    """
    skip = max(0, request.page - 1) * request.pageSize
    limit = request.pageSize
    users = personal_user_dao.search(
        session=db,
        keyword=request.keyword,
        name=request.name,
        phone=request.phone,
        status=request.status,
        skip=skip,
        limit=limit
    )
    return {
        "code": 200,
        "message": "搜索个人用户列表成功",
        "data": users
    }


@router.get("/personal/search/name", response_model=UserNameSearchResponse)
def search_personal_users_by_name(
        name: str,
        db: Session = Depends(get_db)
):
    """
    根据用户名进行模糊搜索
    """
    users = personal_user_dao.search_by_name(db, name)
    return {
        "code": 200,
        "message": "搜索成功",
        "data": users
    }


@router.get("/personal/", response_model=PersonalUserListResponse)
def get_personal_users(skip: int = 0, limit: int = 10, page: int = 0, pageSize: int = 10,
                       db: Session = Depends(get_db)):
    """获取个人用户列表"""
    skip = max(0, page - 1) * pageSize
    limit = pageSize
    users = personal_user_dao.get_list(session=db, skip=skip, limit=limit)
    return {
        "code": 200,
        "message": "获取个人用户列表成功",
        "data": users
    }


@router.get("/personal/list/by/enterprise/{enterprise_id}", response_model=Dict[str, Any])
def get_personal_users_by_enterprise(
        enterprise_id: int,
        page: int = Query(1, description="页码，从1开始"),
        pageSize: int = Query(10, description="每页数量"),
        db: Session = Depends(get_db)
):
    """
    根据企业ID获取关联的个人用户列表

    Args:
        enterprise_id: 企业ID
        page: 页码，默认为1
        pageSize: 每页数量，默认为10
        db: 数据库会话

    Returns:
        包含个人用户列表和总数的分页响应
    """
    # 检查企业是否存在
    db_enterprise = enterprise_dao.get(session=db, enterprise_id=enterprise_id)
    if db_enterprise is None:
        logger.warning(f"获取个人用户列表失败 - 企业不存在 - 企业ID: {enterprise_id}")
        return {"code": 404, "message": "企业不存在", "data": {"list": [], "total": 0}}

    # 计算分页参数
    skip = max(0, page - 1) * pageSize
    limit = pageSize

    # 获取个人用户列表和总数
    users = enterprise_user_relation_dao.get_users_by_enterprise(
        session=db,
        enterprise_id=enterprise_id,
        skip=skip,
        limit=limit,
        status=None
    )
    total = enterprise_user_relation_dao.count_users_by_enterprise(session=db, enterprise_id=enterprise_id)

    # 格式化返回数据
    user_list = []
    for user in users:
        # 获取该用户在企业中的关联关系信息
        relation = enterprise_user_relation_dao.get_by_enterprise_and_user(
            session=db,
            enterprise_id=enterprise_id,
            user_id=user.id,
            status=None
        )

        user_dict = {
            "id": user.id,
            "username": user.username,
            "real_name": user.real_name,
            "nick_name": user.username,  # 使用username作为nick_name
            "phone": user.phone,
            "status": user.status.value if hasattr(user.status, 'value') else user.status,
            "is_admin": relation.is_admin if relation else False,
            "relation_status": relation.relation_status.value if relation and hasattr(relation.relation_status, 'value') else (relation.relation_status if relation else None)
        }
        user_list.append(user_dict)

    logger.info(f"获取个人用户列表成功 - 企业ID: {enterprise_id} - 总数: {total}")

    return {
        "code": 200,
        "message": "获取个人用户列表成功",
        "data": {
            "list": user_list,
            "total": total
        }
    }


@router.get("/personal/{user_id}/enterprises", response_model=List[EnterpriseResponse])
def get_personal_user_enterprises(user_id: int, db: Session = Depends(get_db)):
    """获取个人用户关联的所有企业"""
    db_user = personal_user_dao.get(session=db, user_id=user_id)
    if db_user is None:
        raise HTTPException(status_code=404, detail="个人用户不存在")
    return personal_user_dao.get_enterprises(session=db, user_id=user_id)


@router.get("/personal/{user_id}", response_model=PersonalUserDetailFormattedResponse)
def get_personal_user(user_id: int, db: Session = Depends(get_db)):
    """获取个人用户详情"""
    user_data = personal_user_dao.get_with_enterprises(session=db, user_id=user_id)
    if user_data is None:
        raise HTTPException(status_code=404, detail="个人用户不存在")
    return {
        "code": 200,
        "message": "获取个人用户详情成功",
        "data": user_data
    }


@router.post("/personal/", response_model=PersonalUserCreateResponse, status_code=status.HTTP_201_CREATED)
def create_personal_user(user: PersonalUserCreate, db: Session = Depends(get_db)):
    """创建个人用户"""
    db_user = personal_user_dao.create(session=db, user=user)
    return {
        "code": 200,
        "message": "创建个人用户成功",
        "data": db_user
    }


# 带账户创建的个人用户接口
@router.post("/personal/with-accounts/",
             response_model=Dict[str, Any],
             status_code=status.HTTP_201_CREATED)
def create_personal_user_with_accounts(user: PersonalUserCreate, db: Session = Depends(get_db)):
    """
    创建个人用户并同时创建关联账户（普通账户和赠送账户）
    """
    try:
        personal_user, accounts = user_service.create_personal_user(db, user)
        # 使用字典直接序列化，避免Pydantic验证错误
        return {
            "user": {
                "id": personal_user.id,
                "username": personal_user.username,
                "phone": personal_user.phone,
                "email": personal_user.email,
                "address": personal_user.address,
                "real_name": personal_user.real_name,
                "id_card": personal_user.id_card,
                "status": personal_user.status.value if hasattr(personal_user.status,
                                                                'value') else personal_user.status,
                "register_time": personal_user.register_time.isoformat() if personal_user.register_time else None,
                "created_at": personal_user.created_at.isoformat() if personal_user.created_at else None,
                "updated_at": personal_user.updated_at.isoformat() if personal_user.updated_at else None
            },
            "regular_account": {
                "id": accounts["regular_account"].id,
                "user_id": accounts["regular_account"].user_id,
                "balance": accounts["regular_account"].balance,
                "status": accounts["regular_account"].status.value if hasattr(accounts["regular_account"].status,
                                                                              'value') else accounts[
                    "regular_account"].status,
                "created_at": accounts["regular_account"].created_at.isoformat() if accounts[
                    "regular_account"].created_at else None,
                "updated_at": accounts["regular_account"].updated_at.isoformat() if accounts[
                    "regular_account"].updated_at else None
            },
            "gift_account": {
                "id": accounts["gift_account"].id,
                "user_id": accounts["gift_account"].user_id,
                "balance": accounts["gift_account"].balance,
                "gift_amount": accounts["gift_account"].gift_amount,
                "status": accounts["gift_account"].status.value if hasattr(accounts["gift_account"].status,
                                                                           'value') else accounts[
                    "gift_account"].status,
                "created_at": accounts["gift_account"].created_at.isoformat() if accounts[
                    "gift_account"].created_at else None,
                "updated_at": accounts["gift_account"].updated_at.isoformat() if accounts[
                    "gift_account"].updated_at else None
            }
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"创建个人用户失败：{str(e)}"
        )


@router.put("/personal/{user_id}", response_model=PersonalUserUpdateResponse)
def update_personal_user(user_id: int, user: PersonalUserUpdate, db: Session = Depends(get_db)):
    """更新个人用户信息"""
    db_user = personal_user_dao.update(session=db, user_id=user_id, user=user)
    if db_user is None:
        raise HTTPException(status_code=404, detail="个人用户不存在")
    return {
        "code": 200,
        "message": "更新个人用户成功",
        "data": db_user
    }


# @router.delete("/personal/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
# def delete_personal_user(user_id: int, db: Session = Depends(get_db)):
#     """删除个人用户"""
#     success = personal_user_dao.delete(session=db, user_id=user_id)
#     if not success:
#         raise HTTPException(status_code=404, detail="用户不存在")
#     return {"message": "用户删除成功"}


# @router.delete("/personal/", status_code=status.HTTP_204_NO_CONTENT)
# def delete_personal_users(data: PersonalUserDeleteRequest, db: Session = Depends(get_db)):
#     """批量删除个人用户"""
#     success = personal_user_dao.delete_many(session=db, user_ids=data.ids)
#     if not success:
#         raise HTTPException(status_code=404, detail="用户不存在")
#     return {"message": "批量删除用户成功"}


@router.post("/personal/status/", status_code=status.HTTP_200_OK)
def batch_set_personal_user_status(data: PersonalUserBatchStatusUpdate, db: Session = Depends(get_db)):
    """批量设置个人用户状态"""
    if not data.user_ids:
        raise HTTPException(status_code=400, detail="用户ID列表不能为空")

    affected_count = personal_user_dao.batch_set_status(
        session=db,
        user_ids=data.user_ids,
        status=data.status
    )

    return {
        "code": 200,
        "message": f"成功更新{affected_count}个用户的状态",
        "data": {
            "affected_count": affected_count
        }
    }
