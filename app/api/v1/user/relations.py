import logging  # 添加日志模块导入
from typing import Dict, Optional, List, Any

from fastapi import APIRouter, Depends, HTTPException, Form
from redis import Redis
from sqlalchemy.orm import Session
from starlette import status

from app.core.config import settings
from app.core.deps import get_db
from app.core.security import get_password_hash
from app.dao.user import personal_user_dao, enterprise_dao, enterprise_user_relation_dao
from app.schemas.user import *
from app.schemas.user import (
    EnterpriseUserCreateRelationResponse,
    EnterpriseUserGetRelationResponse,
    EnterpriseUserUpdateRelationResponse)

# 创建日志器
logger = logging.getLogger(__name__)

router = APIRouter()


# 企业用户关联接口`
@router.get("/relation/", response_model=List[EnterpriseUserRelationResponse])
def get_relations(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取所有企业用户关联列表"""
    return enterprise_user_relation_dao.get_list(session=db, skip=skip, limit=limit)


@router.get("/relation/{relation_id}", response_model=EnterpriseUserGetRelationResponse)
def get_relation(relation_id: int, db: Session = Depends(get_db)):
    """获取企业用户关联信息"""
    db_relation = enterprise_user_relation_dao.get(session=db, relation_id=relation_id)
    if db_relation is None:
        raise HTTPException(status_code=404, detail="企业用户关联不存在")
    return {
        "code": 200,
        "message": "获取企业用户关联成功",
        "data": db_relation
    }


@router.get("/enterprise/{enterprise_id}/personal/{user_id}", response_model=EnterpriseUserGetRelationResponse)
def get_relation_by_enterprise_and_user(enterprise_id: int, user_id: int, db: Session = Depends(get_db)):
    """通过企业ID和用户ID获取关联"""
    relation = enterprise_user_relation_dao.get_by_enterprise_and_user(
        session=db, enterprise_id=enterprise_id, user_id=user_id)
    if relation is None:
        raise HTTPException(status_code=404, detail="企业用户关联不存在")
    return {
        "code": 200,
        "message": "获取企业用户关联成功",
        "data": relation
    }


@router.post("/relation/", response_model=EnterpriseUserCreateRelationResponse, status_code=status.HTTP_201_CREATED)
def create_relation(relation: EnterpriseUserRelationCreate, db: Session = Depends(get_db)):
    """创建企业用户关联"""
    # 检查企业和个人用户是否存在
    enterprise = enterprise_dao.get(session=db, enterprise_id=relation.enterprise_id)
    if enterprise is None:
        raise HTTPException(status_code=404, detail="企业用户不存在")

    user = personal_user_dao.get(session=db, user_id=relation.personal_user_id)
    if user is None:
        raise HTTPException(status_code=404, detail="个人用户不存在")

    # 检查关联是否已存在
    existing = enterprise_user_relation_dao.get_by_enterprise_and_user(
        session=db, enterprise_id=relation.enterprise_id, user_id=relation.personal_user_id)
    if existing:
        raise HTTPException(status_code=400, detail="企业用户关联已存在")

    db_relation = enterprise_user_relation_dao.create(session=db, relation=relation)
    # 构建符合响应模型的返回数据
    response_data = {
        "id": db_relation.id,
        "enterprise_id": db_relation.enterprise_id,
        "personal_user_id": db_relation.personal_user_id,
        "is_admin": db_relation.is_admin,
        "relation_status": db_relation.relation_status,
        "remark": db_relation.remark,
        "created_at": db_relation.created_at,
        "updated_at": db_relation.updated_at
    }

    return EnterpriseUserCreateRelationResponse(
        code=201,
        message="创建企业用户关联成功",
        data=response_data
    )


@router.post("/enterprise/{enterprise_id}/personal/{user_id}", response_model=EnterpriseUserCreateRelationResponse)
def create_relation_by_enterprise_and_user(
        enterprise_id: int,
        user_id: int,
        is_admin: bool = Form(False, description="是否为管理员"),
        password: Optional[str] = Form(None, description="用户密码"),
        remark: Optional[str] = Form(None, description="备注"),
        db: Session = Depends(get_db)
):
    """
    通过企业ID和用户ID创建关联关系

    Args:
        enterprise_id: 企业ID
        user_id: 个人用户ID
        is_admin: 是否为管理员
        password: 用户密码(可选)
        remark: 备注(可选)
        db: 数据库会话

    Returns:
        EnterpriseUserCreateRelationResponse: 创建结果响应
    """
    # 检查企业和个人用户是否存在
    enterprise = enterprise_dao.get(session=db, enterprise_id=enterprise_id)
    if enterprise is None:
        logger.warning(f"创建企业用户关联失败 - 企业不存在 - 企业ID: {enterprise_id}")
        raise HTTPException(status_code=404, detail="企业用户不存在")

    user = personal_user_dao.get(session=db, user_id=user_id)
    if user is None:
        logger.warning(f"创建企业用户关联失败 - 个人用户不存在 - 用户ID: {user_id}")
        raise HTTPException(status_code=404, detail="个人用户不存在")

    # 检查关联是否已存在
    existing = enterprise_user_relation_dao.get_by_enterprise_and_user(
        session=db, enterprise_id=enterprise_id, user_id=user_id)
    if existing:
        logger.warning(f"创建企业用户关联失败 - 关联已存在 - 企业ID: {enterprise_id}, 用户ID: {user_id}")
        raise HTTPException(status_code=400, detail="企业用户关联已存在")

    # 如果提供了密码，进行哈希处理
    if password:
        hashed_password = get_password_hash(password)
    else:
        hashed_password = None

    # 创建关联关系
    relation_data = EnterpriseUserRelationCreate(
        enterprise_id=enterprise_id,
        personal_user_id=user_id,
        is_admin=is_admin,
        relation_status=Status.ACTIVE,
        password=hashed_password,
        remark=remark
    )

    try:
        db_relation = enterprise_user_relation_dao.create(session=db, relation=relation_data)
        # 构建符合响应模型的返回数据
        response_data = {
            "id": db_relation.id,
            "enterprise_id": db_relation.enterprise_id,
            "personal_user_id": db_relation.personal_user_id,
            "is_admin": db_relation.is_admin,
            "relation_status": db_relation.relation_status,
            "remark": db_relation.remark,
            "created_at": db_relation.created_at,
            "updated_at": db_relation.updated_at
        }

        logger.info(f"创建企业用户关联成功 - 企业ID: {enterprise_id}, 用户ID: {user_id}")
        return EnterpriseUserCreateRelationResponse(
            code=200,
            message="创建企业用户关联成功",
            data=response_data
        )
    except Exception as e:
        db.rollback()
        logger.error(f"创建企业用户关联失败 - 企业ID: {enterprise_id}, 用户ID: {user_id} - 错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建企业用户关联失败: {str(e)}")


@router.put("/relation/{relation_id}", response_model=EnterpriseUserUpdateRelationResponse)
def update_relation(relation_id: int, relation: EnterpriseUserRelationUpdate, db: Session = Depends(get_db)):
    """更新企业用户关联"""
    db_relation = enterprise_user_relation_dao.update(session=db, relation_id=relation_id, relation=relation)
    if db_relation is None:
        raise HTTPException(status_code=404, detail="企业用户关联不存在")
    return {
        "code": 200,
        "message": "更新企业用户关联成功",
        "data": db_relation
    }


@router.put("/enterprise/{enterprise_id}/personal/{user_id}", response_model=EnterpriseUserUpdateRelationResponse)
def update_relation_by_enterprise_and_user(
        enterprise_id: int,
        user_id: int,
        relation_update: EnterpriseUserRelationUpdate,
        db: Session = Depends(get_db)
):
    """
    通过企业ID和用户ID更新关联关系

    Args:
        enterprise_id: 企业ID
        user_id: 个人用户ID
        relation_update: 关联关系更新数据
        db: 数据库会话

    Returns:
        EnterpriseUserUpdateRelationResponse: 更新结果响应
    """
    # 获取关联关系
    relation = enterprise_user_relation_dao.get_by_enterprise_and_user(
        session=db, enterprise_id=enterprise_id, user_id=user_id, status=None)

    if relation is None:
        logger.warning(f"更新企业用户关联失败 - 关联不存在 - 企业ID: {enterprise_id}, 用户ID: {user_id}")
        raise HTTPException(status_code=404, detail="企业用户关联不存在")

    try:
        # 记录更新前的状态用于日志
        logger.info(f"更新企业用户关联 - 企业ID: {enterprise_id}, 用户ID: {user_id}, 更新数据: {relation_update.model_dump(exclude_unset=True)}")

        # 更新关联关系
        db_relation = enterprise_user_relation_dao.update(
            session=db, relation_id=relation.id, relation=relation_update)

        if db_relation is None:
            logger.error(f"更新企业用户关联失败 - 企业ID: {enterprise_id}, 用户ID: {user_id}")
            raise HTTPException(status_code=500, detail="更新企业用户关联失败")

        logger.info(f"更新企业用户关联成功 - 企业ID: {enterprise_id}, 用户ID: {user_id}, 新状态: is_admin={db_relation.is_admin}, relation_status={db_relation.relation_status}")
        return {
            "code": 200,
            "message": "更新企业用户关联成功",
            "data": db_relation
        }
    except Exception as e:
        db.rollback()
        logger.error(f"更新企业用户关联失败 - 企业ID: {enterprise_id}, 用户ID: {user_id} - 错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新企业用户关联失败: {str(e)}")


@router.delete("/relation/{relation_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_relation(relation_id: int, db: Session = Depends(get_db)):
    """删除企业用户关联"""
    success = enterprise_user_relation_dao.delete(session=db, relation_id=relation_id)
    if not success:
        raise HTTPException(status_code=404, detail="企业用户关联不存在")
    return {"message": "企业用户关联删除成功"}


@router.delete("/enterprise/{enterprise_id}/personal/{user_id}", response_model=Dict[str, Any])
def delete_relation_by_enterprise_and_user(enterprise_id: int, user_id: int, db: Session = Depends(get_db)):
    """
    通过企业ID和用户ID删除关联关系

    Args:
        enterprise_id: 企业ID
        user_id: 个人用户ID
        db: 数据库会话

    Returns:
        Dict: 删除结果响应
    """
    # 获取关联关系
    relation = enterprise_user_relation_dao.get_by_enterprise_and_user(
        session=db, enterprise_id=enterprise_id, user_id=user_id)

    if relation is None:
        logger.warning(f"删除企业用户关联失败 - 关联不存在 - 企业ID: {enterprise_id}, 用户ID: {user_id}")
        raise HTTPException(status_code=404, detail="企业用户关联不存在")

    # 删除关联关系
    success = enterprise_user_relation_dao.delete(session=db, relation_id=relation.id)
    if not success:
        logger.error(f"删除企业用户关联失败 - 企业ID: {enterprise_id}, 用户ID: {user_id}")
        raise HTTPException(status_code=500, detail="删除企业用户关联失败")

    logger.info(f"删除企业用户关联成功 - 企业ID: {enterprise_id}, 用户ID: {user_id}")
    return {
        "code": 200,
        "message": "删除企业用户关联成功",
        "data": {}
    }
