from typing import List, Dict, Any, Optional
import logging  # 添加日志模块导入
from datetime import datetime
import random
from typing import Dict

from fastapi import APIRouter, Depends, HTTPException, <PERSON><PERSON>, <PERSON>er, Query, Request, Form
from sqlalchemy.orm import Session
from starlette import status
from redis import Redis

from app.core.config import settings
from app.core.deps import get_db
from app.core.security import verify_password, oauth2_scheme, create_access_token, verify_token, get_password_hash
from app.dao.admin import admin_dao
from app.dao.user import user_dao, personal_user_dao, enterprise_dao, enterprise_user_relation_dao
from app.schemas.user import *
from app.schemas.user import (
    EnterpriseUserCreateRelationResponse,
    EnterpriseUserGetRelationResponse,
    EnterpriseUserUpdateRelationResponse)
from app.schemas.user import PersonalUserCreate, PersonalUserUpdate, PersonalUserResponse, PersonalUserDetailResponse, \
    PersonalUserSearchRequest, PersonalUserListResponse, PersonalUserSearchResponse, PersonalUserCreateResponse, \
    PersonalUserUpdateResponse, PersonalUserDeleteRequest, PersonalUserBatchStatusUpdate, EnterpriseCreate, \
    EnterpriseUpdate, EnterpriseResponse, EnterpriseDetailResponse, EnterpriseSearchRequest, EnterpriseListResponse, \
    EnterpriseSearchResponse, EnterpriseCreateResponse, \
    EnterpriseUpdateResponse, EnterpriseBatchStatusUpdate
from app.service.user import user_service
from app.core.security import verify_password, oauth2_scheme, create_access_token, verify_token, get_password_hash
from app.core.config import settings
from app.models.enum import Status
from app.utils.common import send_sms
from app.dao.admin import admin_dao
from app.utils.common import get_current_time

# 创建日志器
logger = logging.getLogger(__name__)
# 创建Redis连接
redis_client = Redis.from_url(settings.REDIS_URL)

router = APIRouter()


# 用户密码登录
@router.get("/passwd/login", response_model=UserLoginResponse)
def user_passwd_login(phone: str, passwd: str, company_id: int, company_type: int = 1, db: Session = Depends(get_db)):
    """用户密码登录"""
    if not all([phone, passwd]):
        logger.warning(f"登录参数错误 - 管理员手机号: {phone}")
        return UserLoginResponse(code=400, msg="参数错误", data=None)

    # 当选择签约公司时(company_type=1)，检查company_id
    if company_type == 1 and not company_id:
        logger.warning(f"登录参数错误 - 缺少公司ID - 管理员手机号: {phone}")
        return UserLoginResponse(code=400, msg="参数错误：请选择企业", data=None)

    # 根据公司类型进行不同的验证逻辑
    if company_type == 1:
        # 签约公司逻辑 - 使用个人用户和企业关系表
        personal_user = personal_user_dao.get_by_phone(db, phone)
        if not personal_user:
            logger.warning(f"用户不存在 - 管理员手机号: {phone}")
            return UserLoginResponse(code=400, msg="用户不存在", data=None)

        logger.info(f"user: {personal_user}")
        logger.info(f"user.id: {personal_user.id}")
        logger.info(f"company_id: {company_id}")

        enterprise_user_relation_user = enterprise_user_relation_dao.get_by_enterprise_and_user(db, company_id,
                                                                                                personal_user.id)
        if not enterprise_user_relation_user:
            logger.warning(f"用户不存在 - 管理员手机号: {phone}")
            return UserLoginResponse(code=400, msg="用户不存在", data=None)

        if enterprise_user_relation_user.password is None:
            logger.warning(f"密码错误 - 管理员手机号: {phone}")
            return UserLoginResponse(code=400, msg="密码错误", data=None)

        logger.info(f"enterprise_user_relation_user: {enterprise_user_relation_user.is_admin}")
        if not enterprise_user_relation_user.is_admin:
            logger.warning(f"用户状态错误 - 管理员手机号: {phone}")
            return UserLoginResponse(code=400, msg="用户状态错误", data=None)

        # 验证密码
        if not verify_password(passwd, enterprise_user_relation_user.password):
            logger.warning(f"密码错误 - 管理员手机号: {phone}")
            return UserLoginResponse(code=400, msg="密码错误", data=None)

        # 创建令牌
        access_token = create_access_token(
            data={
                "user_id": personal_user.id,
                "company_id": company_id,
                "company_type": company_type,
                "email": personal_user.email,
                "phone": personal_user.phone
            }
        )

        # 格式化用户信息
        user_info = {
            "company_id": company_id,
            "company_type": company_type,
            "email": personal_user.email,
            "identity": "",
            "phone": personal_user.phone,
            "state": 0,
            "types": 2,  # 签约公司用户类型为2
            "user_id": personal_user.id,
            "user_name": personal_user.username,
            "access_token": access_token
        }

    else:
        # 乙禾公司逻辑 - 使用admin表
        admin = admin_dao.get_by_phone(db, phone)
        if not admin:
            logger.warning(f"管理员不存在 - 手机号: {phone}")
            return UserLoginResponse(code=400, msg="用户不存在", data=None)

        logger.info(f"admin: {admin}")
        logger.info(f"admin.id: {admin.id}")

        # 验证密码
        if not verify_password(passwd, admin.password):
            logger.warning(f"密码错误 - 乙禾公司管理员手机号: {phone}")
            return UserLoginResponse(code=400, msg="密码错误", data=None)

        # 验证状态
        if admin.status != Status.ACTIVE:
            logger.warning(f"管理员状态异常 - 手机号: {phone}")
            return UserLoginResponse(code=400, msg="管理员状态异常", data=None)

        # 更新最后登录时间
        admin.last_login_time = get_current_time()
        db.commit()

        # 创建令牌
        access_token = create_access_token(
            data={
                "admin_id": admin.id,
                "company_id": 0,  # 乙禾公司固定为0
                "company_type": company_type,
                "email": admin.email,
                "phone": admin.phone
            }
        )

        # 格式化用户信息
        user_info = {
            "company_id": 0,  # 乙禾公司固定为0
            "company_type": company_type,
            "email": admin.email if admin.email else "",
            "identity": "",
            "phone": admin.phone,
            "state": 0,
            "types": 3,  # 乙禾公司用户类型为3
            "user_id": admin.id,
            "user_name": admin.name,
            "access_token": access_token
        }

    logger.info(f"用户登录成功 - 手机号: {phone} - 公司类型: {company_type}")

    return UserLoginResponse(
        code=200,
        msg="用户登录成功",
        data=user_info
    )


# 获取用户信息
@router.get("/info", response_model=UserLoginResponse)
async def get_user_info(
        token: str = Depends(oauth2_scheme),
        db: Session = Depends(get_db)
):
    """获取用户信息"""
    # 从token解析用户信息
    logger.info(f"token: {token}")

    try:
        # 从token中解析payload
        payload = verify_token(token)
        logger.info(f"payload: {payload}")

        company_type = payload.get("company_type", 1)

        if company_type == 1:
            # 签约公司用户
            user_id = payload.get("user_id")
            company_id = payload.get("company_id")

            if not user_id or not company_id:
                logger.warning(f"Token中缺少用户ID或企业ID")
                return UserLoginResponse(code=400, msg="无效的令牌", data=None)

            # 获取用户信息
            current_user = personal_user_dao.get(db, user_id)
            if not current_user:
                logger.warning(f"用户不存在 - 用户ID: {user_id}")
                return UserLoginResponse(code=400, msg="用户不存在", data=None)

            logger.info(f"current_user: {current_user}")
            logger.info(f"user_id: {user_id}")
            logger.info(f"company_id: {company_id}")

            # 验证用户与企业的关联
            enterprise_user_relation = enterprise_user_relation_dao.get_by_enterprise_and_user(db, company_id, user_id)
            if not enterprise_user_relation:
                logger.warning(f"用户与企业关联不存在 - 用户ID: {user_id}, 企业ID: {company_id}")
                return UserLoginResponse(code=400, msg="用户与企业关联不存在", data=None)

            # 验证用户是否为管理员
            if not enterprise_user_relation.is_admin:
                logger.warning(f"用户不是管理员 - 用户ID: {user_id}")
                return UserLoginResponse(code=400, msg="用户权限不足", data=None)

            # 格式化用户信息
            user_info = {
                "company_id": company_id,
                "company_type": company_type,
                "email": current_user.email,
                "identity": "",
                "phone": current_user.phone,
                "state": 0,
                "types": 2,  # 签约公司用户类型为2
                "user_id": current_user.id,
                "user_name": current_user.username
            }

            logger.info(f"获取用户信息成功 - 用户ID: {user_id}")
        else:
            # 乙禾公司管理员
            admin_id = payload.get("admin_id")

            if not admin_id:
                logger.warning(f"Token中缺少管理员ID")
                return UserLoginResponse(code=400, msg="无效的令牌", data=None)

            # 获取管理员信息
            admin = admin_dao.get(db, admin_id)
            if not admin:
                logger.warning(f"管理员不存在 - 管理员ID: {admin_id}")
                return UserLoginResponse(code=400, msg="管理员不存在", data=None)

            # 验证管理员状态
            if admin.status != Status.ACTIVE:
                logger.warning(f"管理员状态异常 - 管理员ID: {admin_id}")
                return UserLoginResponse(code=400, msg="管理员状态异常", data=None)

            # 格式化用户信息
            user_info = {
                "company_id": 0,  # 乙禾公司固定为0
                "company_type": company_type,
                "email": admin.email if admin.email else "",
                "identity": "",
                "phone": admin.phone,
                "state": 0,
                "types": 3,  # 乙禾公司用户类型为3
                "user_id": admin.id,
                "user_name": admin.name
            }

            logger.info(f"获取管理员信息成功 - 管理员ID: {admin_id}")

        return UserLoginResponse(
            code=200,
            msg="获取用户信息成功",
            data=user_info
        )
    except Exception as e:
        logger.error(f"获取用户信息失败: {str(e)}")
        return UserLoginResponse(code=400, msg="获取用户信息失败", data=None)


@router.get("/send-validate-code", response_model=Dict[str, Any])
async def send_validate_code(
        phone: str = Query(..., description="用户手机号"),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    发送验证码

    Args:
        phone: 用户手机号
        db: 数据库会话

    Returns:
        Dict: 包含发送结果的响应
    """
    if not phone:
        logger.warning(f"发送验证码参数错误 - 手机号为空")
        return {"code": 400, "msg": "参数错误"}

    # 查询用户是否存在
    personal_user = personal_user_dao.get_by_phone(db, phone)
    if not personal_user:
        logger.warning(f"发送验证码失败 - 手机号不存在或用户状态异常: {phone}")
        return {"code": 400, "msg": "手机号不存在或用户状态异常"}

    # 生成验证码
    validate_code = ''.join(random.choices('1234567890', k=6))
    validate_code = '123456'

    # 发送验证码
    # if send_sms(phone, validate_code):
    if personal_user:
        logger.info(f"验证码发送成功: {validate_code}")
        # 将验证码保存到Redis
        # redis_client.set(phone, validate_code, ex=settings.SMS_TIMEOUT)
        return {"code": 200, "msg": "验证码发送成功"}
    else:
        logger.error(f"验证码发送失败: {phone}")
        return {"code": 500, "msg": "验证码发送失败"}


# 用户通过验证码登录
@router.get("/valid/login", response_model=UserLoginResponse)
async def valid_user_login(
        phone: str = Query(..., description="用户手机号"),
        valid_code: str = Query(..., description="验证码"),
        company_id: int = Query(..., description="企业ID"),
        company_type: int = Query(1, description="公司类型：1-签约公司，2-乙禾公司"),
        db: Session = Depends(get_db)
) -> UserLoginResponse:
    """
    用户通过验证码登录

    Args:
        phone: 用户手机号
        valid_code: 验证码
        company_id: 企业ID
        company_type: 公司类型：1-签约公司，2-乙禾公司
        db: 数据库会话

    Returns:
        Dict: 包含登录结果和用户信息的响应
    """
    if not all([phone, valid_code]):
        logger.warning(f"登录参数错误 - 手机号: {phone}")
        return {"code": 400, "msg": "参数错误"}

    # 当选择签约公司时(company_type=1)，检查company_id
    if company_type == 1 and not company_id:
        logger.warning(f"登录参数错误 - 缺少公司ID - 手机号: {phone}")
        return UserLoginResponse(code=400, msg="参数错误：请选择企业", data=None)

    # 从Redis获取验证码
    # bytes_code = redis_client.get(phone)
    # if not isinstance(bytes_code, bytes):
    #     logger.warning(f"验证码已过期 - 手机号: {phone}")
    #     return {"code": 400, "msg": "验证码已过期"}
    # phone_code = bytes_code.decode()
    phone_code = '123456'  # 测试用验证码

    # 验证验证码
    if phone_code != valid_code:
        logger.warning(f"验证码错误 - 手机号: {phone}")
        return UserLoginResponse(code=400, msg="验证码错误", data=None)

    # 根据公司类型进行不同的验证逻辑
    if company_type == 1:
        # 签约公司逻辑
        personal_user = personal_user_dao.get_by_phone(db, phone)
        if not personal_user:
            logger.warning(f"用户不存在 - 手机号: {phone}")
            return {"code": 400, "msg": "用户不存在"}

        enterprise_user_relation_user = enterprise_user_relation_dao.get_by_enterprise_and_user(db, company_id,
                                                                                                personal_user.id)
        if not enterprise_user_relation_user:
            logger.warning(f"用户不存在 - 管理员手机号: {phone}")
            return UserLoginResponse(code=400, msg="用户不存在", data=None)

        logger.info(f"enterprise_user_relation_user: {enterprise_user_relation_user.is_admin}")
        if not enterprise_user_relation_user.is_admin:
            logger.warning(f"用户状态错误 - 管理员手机号: {phone}")
            return UserLoginResponse(code=400, msg="用户状态错误", data=None)

        # 创建令牌
        access_token = create_access_token(
            data={
                "user_id": personal_user.id,
                "company_id": company_id,
                "company_type": company_type,
                "email": personal_user.email,
                "phone": personal_user.phone
            }
        )

        # 格式化用户信息
        user_info = {
            "company_id": company_id,
            "company_type": company_type,
            "email": personal_user.email,
            "identity": "",
            "phone": personal_user.phone,
            "state": 0,
            "types": 2,  # 签约公司用户类型为2
            "user_id": personal_user.id,
            "user_name": personal_user.username,
            "access_token": access_token
        }
    else:
        # 乙禾公司逻辑
        admin = admin_dao.get_by_phone(db, phone)
        if not admin:
            logger.warning(f"管理员不存在 - 手机号: {phone}")
            return UserLoginResponse(code=400, msg="用户不存在", data=None)

        # 验证状态
        if admin.status != Status.ACTIVE:
            logger.warning(f"管理员状态异常 - 手机号: {phone}")
            return UserLoginResponse(code=400, msg="管理员状态异常", data=None)

        # 更新最后登录时间
        admin.last_login_time = get_current_time()
        db.commit()

        # 创建令牌
        access_token = create_access_token(
            data={
                "admin_id": admin.id,
                "company_id": 0,  # 乙禾公司固定为0
                "company_type": company_type,
                "email": admin.email,
                "phone": admin.phone
            }
        )

        # 格式化用户信息
        user_info = {
            "company_id": 0,  # 乙禾公司固定为0
            "company_type": company_type,
            "email": admin.email if admin.email else "",
            "identity": "",
            "phone": admin.phone,
            "state": 0,
            "types": 3,  # 乙禾公司用户类型为3
            "user_id": admin.id,
            "user_name": admin.name,
            "access_token": access_token
        }

    logger.info(f"用户登录成功 - 手机号: {phone} - 公司类型: {company_type}")

    return UserLoginResponse(
        code=200,
        msg="用户登录成功",
        data=user_info
    )


# 用户修改个人密码
@router.post("/update/user/passwd", response_model=dict)
async def update_user_passwd(
        phone: str = Form(...),
        valid_code: str = Form(...),
        passwd: str = Form(...),
        db: Session = Depends(get_db)
):
    """
    用户修改个人密码

    Args:
        request: 请求对象
        phone: 用户手机号
        valid_code: 验证码
        passwd: 新密码
        db: 数据库会话

    Returns:
        Dict: 修改密码结果响应
    """
    # 参数检查
    if not all([phone, valid_code, passwd]):
        logger.warning(f"修改密码参数错误 - 手机号: {phone}")
        return {"code": 400, "msg": "参数错误"}

    # 从Redis获取验证码
    # bytes_code = redis_client.get(phone)
    # if not isinstance(bytes_code, bytes):
    #     logger.warning(f"验证码已过期 - 手机号: {phone}")
    #     return {"code": 400, "msg": "验证码已过期"}
    # phone_code = bytes_code.decode()
    phone_code = '123456'  # 测试用验证码

    # 验证码比对
    if phone_code != valid_code:
        logger.warning(f"验证码错误 - 手机号: {phone}")
        return {"code": 400, "msg": "验证码错误"}

    try:
        # 更新用户信息
        hashed_password = get_password_hash(passwd)
        user = personal_user_dao.get_by_phone(db, phone)
        if not user:
            logger.warning(f"用户不存在 - 手机号: {phone}")
            return {"code": 400, "msg": "用户不存在"}

        # 更新用户密码
        personal_user_dao.update_password(db, user.id, hashed_password)

        logger.info(f"密码修改成功 - 手机号: {phone}")
        return {"code": 200, "msg": "密码修改成功"}
    except Exception as e:
        db.rollback()
        logger.error(f"数据错误: {str(e)}")
        return {"code": 500, "msg": "数据错误"}


# 用户基础接口
@router.get("/", response_model=List[UserResponse])
def get_users(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """获取所有基础用户列表"""
    return user_dao.get_list(session=db, skip=skip, limit=limit)


@router.get("/{user_id}", response_model=UserResponse)
def get_user(user_id: int, db: Session = Depends(get_db)):
    """获取基础用户信息"""
    db_user = user_dao.get(session=db, user_id=user_id)
    if db_user is None:
        raise HTTPException(status_code=404, detail="用户不存在")
    return db_user


@router.put("/{user_id}", response_model=UserResponse)
def update_user(user_id: int, user: UserUpdate, db: Session = Depends(get_db)):
    """更新基础用户"""
    db_user = user_dao.update(session=db, user_id=user_id, user=user)
    if db_user is None:
        raise HTTPException(status_code=404, detail="用户不存在")
    return db_user


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_user(user_id: int, db: Session = Depends(get_db)):
    """删除基础用户"""
    success = user_dao.delete(session=db, user_id=user_id)
    if not success:
        raise HTTPException(status_code=404, detail="用户不存在")
    return {"message": "用户删除成功"}
