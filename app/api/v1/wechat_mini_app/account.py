# -*- coding: utf-8 -*-
# 账户模块

from fastapi import APIRouter, Depends, HTTPException, Header, File, UploadFile, Request
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import re

from app.dao.account import account_transaction_dao, account_dao
from app.models.enum import Status
from app.service.wechat_miniapp.wx_reservation import reservation_service
from app.core.config import settings
from app.service.wechat_miniapp.wx_service import WechatService
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.service.user import UserService
from app.service.wechat_miniapp.wx_order import WXOrderService
from app.models.user import PersonalUser
from app.dao.rule import rule_dao
from app.models.order import Order
from app.core.deps import get_current_user, get_db
from app.service.wechat_miniapp.wx_account import AccountService
import logging
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from app.schemas.user import PersonalUserCreateWX
import random
import string
import os
from pathlib import Path

from app.utils.common import get_phone_number, product_wx_token, allowed_file, secure_filename
from app.models.account import AccountType, TransactionType
from app.schemas.order import OrderUpdate
from app.models.order import PaymentStatus, OrderType, PaymentMethod
from app.dao.order import order_dao, order_item_dao
from app.service.wechat_miniapp.wx_service import wechat_service
from app.service.payment import payment_service
from app.dao.reservation import reservation_request_dao
from app.models.reservation import ReservationStatus
from app.models.order import OrderStatus, OrderItem
from app.dao.wx_payment import wx_payment_dao, wx_payment_service
from app.models.order import WxPaymentRecord, WxRefundRecord, WxRefundStatus, WxPaymentStatus
from app.schemas.account import AccountTransactionCreate
from app.utils.common import get_current_time
from app.dao.user import personal_user_dao
from app.dao.product import product_dao
from app.service.order import order_service
from app.utils.logger import logger
from app.dao.rule import rule_item_dao
from app.service.revervation import reservation_service as revervation_service
from app.dao.reservation import biz_reservation_request_dao
from app.schemas.reservation import BizReservationRequestCreate
from app.dao.menu import menu_dao
from app.models.reservation import ReservationType
from app.models.rule import DiningReservationRule, RuleItem, RuleScope, RuleType, RuleOrderType
from app.models.product import MealType
from app.models.reservation import BizReservationRequest
from app.dao.user import enterprise_user_relation_dao, enterprise_dao
from app.dao.admin import admin_dao
from app.service.pricing import pricing_service
from app.schemas.order import OrderItemBase
from app.events.deps import EventBusDep
from app.events.models import OrderEvent, OrderEventAction
from app.core.scheduler import send_order_change_reminders_task

router = APIRouter()


class FeedbackRequest(BaseModel):
    text: str


@router.post("/feedback-text")
async def insert_feedback(
        task_info: dict,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    处理用户反馈提交

    Args:
        task_info: 用户反馈内容
        token: 用户token
        db: 数据库会话

    Returns:
        Dict: 包含处理结果的响应
    """
    try:
        # 验证用户token
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"token无效或已过期: {token[:10]}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "code": 401}
            )

        # 验证反馈内容
        if not task_info["text"]:
            raise HTTPException(
                status_code=400,
                detail={"message": "反馈内容不能为空", "code": 400}
            )

        logger.info(f"收到用户反馈: {task_info}")

        # 这里需要实现保存反馈到数据库的逻辑
        # feedback_data = {
        #     "user_id": user.id,
        #     "content": feedback.text,
        #     "created_at": datetime.now()
        # }
        # WeChatUserService.save_feedback(db, feedback_data)

        return {
            "message": "反馈提交成功",
            "code": 200
        }

    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"处理反馈提交时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"message": "提交失败", "code": 500}
        )


@router.get("/account/transactions")
async def get_account_transactions(token: Optional[str] = Header(None), db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    获取账户的所有流水

    Args:
        token: 用户token
        db: 数据库会话

    Returns:
        Dict: 包含用户账单信息的响应
    """
    logger.info(f"开始验证token: {token[:10]}..." if token else "token为空")

    if not token:
        logger.warning("请求中未提供token")
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    user = WeChatUserService.verify_token(db, token)

    if not user:
        logger.warning(f"token无效或已过期: {token[:10]}...")
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    logger.info(f"token验证成功，用户ID: {user.id}")
    user_info = WeChatUserService.get_user_info(db, user.wechat_id)
    logger.debug(f"获取到的用户信息: {user_info}")

    # 获取用户的所有账户
    accounts = account_dao.get_by_user_id(db, user_info["id"])
    logger.info(f"查询到的账户数量: {len(accounts)}")

    # 筛选 REGULAR 类型的账户
    regular_accounts = [account for account in accounts if
                        account.type == AccountType.REGULAR and account.status == Status.ACTIVE]

    # 获取所有账户的交易记录
    account_transactions_list = []
    account = regular_accounts[0]
    transactions = account_transaction_dao.get_by_account_id(db, account.id)

    # 获取企业为用户支付的交易记录
    # 查询与该用户相关的所有订单
    user_orders = order_dao.get_by_user_all(db, user_info["id"])
    logger.info(f"查询到的用户订单数量: {len(user_orders)}")

    # 筛选出企业支付的订单ID
    enterprise_paid_order_ids = [order.id for order in user_orders
                                 if order.payment_method == PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE
                                 and order.status in [OrderStatus.PAID, OrderStatus.REFUNDED,
                                                      OrderStatus.REFUNDED_PARTIAL]]

    # 查询这些订单关联的企业账户交易记录
    enterprise_transactions = []
    if enterprise_paid_order_ids:
        enterprise_transactions = account_transaction_dao.get_by_order_ids(db, enterprise_paid_order_ids)
        logger.info(f"查询到的企业交易记录数量: {len(enterprise_transactions)}")

    # 合并个人账户和企业账户的交易记录
    all_transactions = transactions + enterprise_transactions

    # 按交易时间正序排序
    sorted_transactions = sorted(all_transactions, key=lambda x: x.transaction_time)

    # 计算每次交易后的余额
    current_balance = 0
    balance_map = {}  # 用于存储每笔交易ID对应的余额

    for transaction in sorted_transactions:
        # 只计算用户自己账户的余额变化
        if transaction.account_id == account.id:
            # 根据交易类型更新余额
            order_object = order_dao.get(db, transaction.order_id)

            if transaction.transaction_type in [TransactionType.DEPOSIT, TransactionType.WITHDRAW,
                                                TransactionType.CONSUME, TransactionType.TRANSFER]:
                current_balance += transaction.amount
            elif transaction.transaction_type == TransactionType.REFUND:
                if order_object.payment_method == PaymentMethod.WECHAT_PAY and order_object.type == OrderType.RESERVATION:  # 预订的微信支付,退款时
                    pass
                else:
                    current_balance += transaction.amount

            # 记录每笔交易后的余额
            balance_map[transaction.id] = current_balance
        else:
            # 企业账户交易不影响用户自己的余额
            balance_map[transaction.id] = current_balance

    # 再次获取交易记录并按时间倒序排序用于显示
    for transaction in sorted(all_transactions, key=lambda x: x.transaction_time, reverse=True):
        # 创建交易类型的映射
        transaction_type_mapping = {
            "none": 0,  # 无交易
            "payment": 0,  # 支付：支付订单或交易
            "deposit": 1,  # 充值：向账户存入资金
            "withdraw": 0,  # 提现：从账户提取资金
            "transfer": 3,  # 转账：账户间资金转移
            "consume": 0,  # 消费：使用账户资金进行支付
            "refund": 2  # 退款：退回已支付的资金
        }

        # 使用映射获取交易类型的整数值
        transaction_type_int = transaction_type_mapping.get(transaction.transaction_type.value, 0)
        order_object = order_dao.get(db, transaction.order_id)
        description = transaction.description

        if transaction_type_int == 0:
            description_mark = "消费"
        elif transaction_type_int == 1:
            description_mark = "充值"
        elif transaction_type_int == 2:
            description_mark = "退款"
        elif transaction_type_int == 3:
            description_mark = "转账"
        else:
            description_mark = ""

        # 特殊处理企业账户交易
        is_enterprise_transaction = transaction.account_id != account.id

        if order_object:
            if is_enterprise_transaction:
                # 企业账户支付
                description = f"{description} (企业账户支付)"
            elif order_object.payment_method == PaymentMethod.WECHAT_PAY:
                description = f"{description} (微信支付 - {description_mark})"
            elif order_object.payment_method == PaymentMethod.ALIPAY:
                description = f"{description} (支付宝支付 - {description_mark})"
            elif order_object.payment_method == PaymentMethod.ACCOUNT_BALANCE:
                description = f"{description} (个人账户 - {description_mark})"
            elif order_object.payment_method == PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE:
                description = f"{description} (企业帐户 - {description_mark})"
            elif order_object.payment_method == PaymentMethod.CASH:
                description = f"{description} (现金 - {description_mark})"
            elif order_object.payment_method == PaymentMethod.BANK_TRANSFER:
                description = f"{description} (银行帐户 - {description_mark})"
            else:
                description = f"{description} {description_mark}"

        tmp_data = {
            "id": transaction.id,
            "description": description,
            "transaction_type": transaction_type_int,
            "amount": round(transaction.amount, 2),
            "order_id": transaction.order_id,
            "order_no": order_object.order_no if order_object else None,
            "status": 1,  # 假设所有记录都是有效的
            "balance": round(balance_map.get(transaction.id, 0), 2),  # 使用预先计算的每笔交易后的余额
            "date": transaction.transaction_time.strftime("%Y-%m-%d %H:%M:%S"),
            "is_enterprise_transaction": is_enterprise_transaction  # 标记是否为企业账户交易
        }
        # 只显示个人账户交易明细
        # try:
        #     if order_object.payment_method == PaymentMethod.ACCOUNT_BALANCE:
        #         account_transactions_list.append(tmp_data)
        # except Exception as e:
        #     logger.error(e)
        account_transactions_list.append(tmp_data)

    # 已按交易时间倒序排序

    return {
        "message": "获取成功",
        "status": 200,
        "account_transactions_list": account_transactions_list,
        "count": len(account_transactions_list)
    }


@router.post("/create-recharge-order")
async def create_recharge_order(
        task_info: dict,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    创建充值订单

    Args:
        task_info: 充值订单信息
        token: 用户token
        db: 数据库会话

    Returns:
        Dict: 包含处理结果的响应
    """
    logger.info(f"开始创建充值订单，接收到的参数: task_info={task_info}")

    # 验证用户token
    logger.info(f"开始验证用户token: {token[:10] if token else 'None'}")
    user = WeChatUserService.verify_token(db, token)
    if not user:
        logger.warning(f"token无效或已过期: {token[:10]}...")
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "code": 401}
        )
    logger.info(f"用户token验证成功，用户ID: {user.id}, 微信ID: {user.wechat_id}")

    # 验证充值金额
    if not task_info["amount"]:
        logger.error("充值金额为空")
        raise HTTPException(
            status_code=400,
            detail={"message": "充值金额不能为空", "code": 400}
        )

    logger.info(f"充值金额验证通过: {task_info['amount']}")

    # 生成订单号
    logger.info(f"开始生成充值订单，用户ID: {user.id}, 金额: {task_info['amount']}")
    order = WXOrderService.create_recharge_order(db, user.id, task_info["amount"])

    # 检查订单是否创建成功
    if not order:
        logger.error("订单创建失败")
        raise HTTPException(
            status_code=500,
            detail={"message": "订单创建失败", "code": 500}
        )
    logger.info(f"订单创建成功，订单号: {order.order_no}")

    # 生成充值订单支付参数
    logger.info("开始生成微信支付参数")
    try:
        # task_info["amount"] = 0.01 # 测试用
        pay_params = wechat_service.create_jsapi_payment(
            user.wechat_id,
            order.order_no,
            task_info["amount"],
            "账户充值"
        )
        logger.info(f"微信支付参数生成成功: {pay_params}")
    except Exception as e:
        logger.error(f"生成微信支付参数失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"message": f"生成支付参数失败: {str(e)}", "code": 500}
        )

    return {
        "message": "创建成功",
        "status": 200,
        "payParams": pay_params
    }


@router.get("/account/balance")
async def get_user_balance(
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取用户余额

    Args:
        token: 用户令牌
        db: 数据库会话

    Returns:
        用户余额信息
    """
    logger.info(f"获取用户余额")

    try:
        # 验证用户token
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"token无效或已过期: {token[:10]}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        user_id = user.id
        logger.info(f"用户token验证成功，用户ID: {user_id}")

        # 获取用户余额
        balance = account_dao.get_user_balance(db, user_id)
        logger.info(f"用户余额: {balance}")

        return {
            "message": "获取余额成功",
            "status": 200,
            "data": {
                "balance": balance
            }
        }
    except Exception as e:
        logger.error(f"获取用户余额失败: {str(e)}", exc_info=True)
        return {
            "message": f"获取余额失败: {str(e)}",
            "status": 500
        }
