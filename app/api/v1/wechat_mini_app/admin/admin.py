# 提供小程序管理员模块API

from typing import Dict, Any, Optional

from fastapi import Depends, HTTPException, Header, APIRouter
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.dao.admin import admin_dao, get_admin_permissions
from app.dao.user import personal_user_dao
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.utils.logger import logger

router = APIRouter()


@router.get("/is_system_admin")
async def is_system_admin(token: Optional[str] = Header(None), db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    检查用户是否为系统管理员
    """
    if not token:
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    user = WeChatUserService.verify_token(db, token)
    if not user:
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    # 获取个人用户信息
    personal_user = personal_user_dao.get(db, user.id)
    if not personal_user:
        return {
            "status": 200,
            "message": "获取成功",
            "data": False
        }
    logger.info("个人用户信息: %s", personal_user.phone)
    # 检查用户是否有miniapp:manage权限
    is_system_admin = False

    # 检查用户是否有管理员账号
    admin = admin_dao.get_by_phone(db, personal_user.phone)
    if admin:
        # 检查是否有miniapp:manage权限
        roles = admin.roles
        logger.info("管理员角色: %s", roles)
        if roles:
            for role in roles:
                logger.info("角色ID: %s, 角色名称: %s", role.id, role.name)
                # 检查角色的权限
                permissions = role.permissions
                logger.info("角色权限: %s", permissions)
                for permission in permissions:
                    logger.info("权限代码: %s", permission.code)
                    if permission.code == "miniapp:manage":
                        is_system_admin = True
                        break
                if is_system_admin:
                    break
    logger.info("系统管理员状态: %s", is_system_admin)
    return {
        "status": 200,
        "message": "获取成功",
        "data": is_system_admin
    }
