from sqlalchemy.orm import Session

from app.dao.admin import admin_dao
from app.dao.user import personal_user_dao
from app.service.wechat_miniapp.wx_user import WeChatUserService


def verify_admin_permission(token: str, db: Session, permission_code:str = "miniapp:manage") -> bool:
    """
    验证用户是否有管理员权限

    Args:
        token: 用户token
        db: 数据库会话
        permission_code: 所需要的管理员权限代码，默认为"miniapp:manage"

    Returns:
        bool: 是否有管理员权限
    """
    if not token:
        return False

    # 验证token
    user = WeChatUserService.verify_token(db, token)
    if not user:
        return False

    # 获取个人用户信息
    personal_user = personal_user_dao.get(db, user.id)
    if not personal_user:
        return False

    # 检查用户是否有管理员账号和miniapp:manage权限
    admin = admin_dao.get_by_phone(db, personal_user.phone)
    if not admin:
        return False

    # 检查是否有miniapp:manage权限
    roles = admin.roles
    if roles:
        for role in roles:
            permissions = role.permissions
            for permission in permissions:
                if permission.code == permission_code:
                    return True

    return False

