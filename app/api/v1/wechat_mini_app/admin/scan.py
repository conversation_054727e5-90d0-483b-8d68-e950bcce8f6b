from typing import Dict, Any, Optional

from fastapi import Depends, HTTPException, Header, APIRouter
from fastapi.params import Body
from sqlalchemy.orm import Session

from app.api.v1.wechat_mini_app.admin.common import verify_admin_permission
from app.core.deps import get_db
from app.service.verify import verify_service
from app.utils.logger import logger

router = APIRouter()


@router.post("/scan")
async def scan(
        token: Optional[str] = Header(None),
        data:str =Body(...) ,
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    小程序管理员扫码核销

    需要管理员权限（miniapp:manage）
    """
    # 验证管理员权限
    if not verify_admin_permission(token, db):
        logger.warning("用户无管理员权限或token无效")
        raise HTTPException(
            status_code=403,
            detail={"message": "无权限访问", "status": 403}
        )

    try:
        # 使用原有的预订报表服务
        success, message = verify_service.verify_and_write_off(data, db, mode="MINIAPP")

        if success:
            response = {
                "status": 200,
                "message": message,
                "data": ""
            }
        else:
            response = {
                "status": 400,
                "message": message,
                "data": ""
            }

        return response

    except Exception as e:
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail={"message": "扫码失败", "status": 500}
        )
