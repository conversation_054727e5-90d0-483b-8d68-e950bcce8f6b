from typing import Dict, Any

from fastapi import Depends, APIRouter
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.core.config import settings
from app.dao.content import article_dao
from app.schemas.content import ArticleInDB

router = APIRouter()


@router.get("/published/articles", response_model=Dict[str, Any])
async def get_published_articles(
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取所有已发布的文章
    按照sort_order升序排列
    """
    try:
        articles = article_dao.get_published_articles(session=db)
        # 将 SQLAlchemy 模型转换为 Pydantic 模型并添加 BASE_URL
        articles_data = []
        for article in articles:
            article_dict = ArticleInDB.model_validate(article).model_dump()
            # 为图片字段添加 BASE_URL
            if article_dict.get('image'):
                article_dict['image'] = settings.BASE_URL + article_dict['image']
            if article_dict.get('thumbnail'):
                article_dict['thumbnail'] = settings.BASE_URL + article_dict['thumbnail']
            if article_dict.get('ad_image'):
                article_dict['ad_image'] = settings.BASE_URL + article_dict['ad_image']
            articles_data.append(article_dict)
        
        return {
            "status": 200,
            "message": "获取已发布文章列表成功",
            "data": articles_data
        }
    except Exception as e:
        return {
            "status": 500,
            "message": f"获取已发布文章列表失败：{str(e)}",
            "data": None
        }


@router.get("/published/{article_id}", response_model=Dict[str, Any])
async def get_published_article(
        article_id: int,
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    根据ID获取已发布的文章
    """
    try:
        article = article_dao.get_published_article(session=db, article_id=article_id)
        if not article:
            return {
                "status": 404,
                "message": "文章不存在或未发布",
                "data": None
            }
        # 将 SQLAlchemy 模型转换为 Pydantic 模型并添加 BASE_URL
        article_dict = ArticleInDB.model_validate(article).model_dump()
        # 为图片字段添加 BASE_URL
        if article_dict.get('image'):
            article_dict['image'] = settings.BASE_URL + article_dict['image']
        if article_dict.get('thumbnail'):
            article_dict['thumbnail'] = settings.BASE_URL + article_dict['thumbnail']
        if article_dict.get('ad_image'):
            article_dict['ad_image'] = settings.BASE_URL + article_dict['ad_image']
            
        return {
            "status": 200,
            "message": "获取文章详情成功",
            "data": {
                "article": article_dict
            }
        }
    except Exception as e:
        return {
            "status": 500,
            "message": f"获取文章详情失败：{str(e)}",
            "data": None
        }
