# -*- coding: utf-8 -*-
# 用户认证模块


from fastapi import APIRouter, Depends, HTTPException, Header, File, UploadFile, Request
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import re

from app.dao.account import account_transaction_dao, account_dao
from app.models.enum import Status
from app.service.wechat_miniapp.wx_reservation import reservation_service
from app.core.config import settings
from app.service.wechat_miniapp.wx_service import WechatService
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.service.user import UserService
from app.service.wechat_miniapp.wx_order import WXOrderService
from app.models.user import PersonalUser
from app.dao.rule import rule_dao
from app.models.order import Order
from app.core.deps import get_current_user, get_db
from app.service.wechat_miniapp.wx_account import AccountService
import logging
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from app.schemas.user import PersonalUserCreateWX
import random
import string
import os
from pathlib import Path

from app.utils.common import get_phone_number, product_wx_token, allowed_file, secure_filename
from app.models.account import AccountType, TransactionType
from app.schemas.order import OrderUpdate
from app.models.order import PaymentStatus, OrderType, PaymentMethod
from app.dao.order import order_dao, order_item_dao
from app.service.wechat_miniapp.wx_service import wechat_service
from app.service.payment import payment_service
from app.dao.reservation import reservation_request_dao
from app.models.reservation import ReservationStatus
from app.models.order import OrderStatus, OrderItem
from app.dao.wx_payment import wx_payment_dao, wx_payment_service
from app.models.order import WxPaymentRecord, WxRefundRecord, WxRefundStatus, WxPaymentStatus
from app.schemas.account import AccountTransactionCreate
from app.utils.common import get_current_time
from app.dao.user import personal_user_dao
from app.dao.product import product_dao
from app.service.order import order_service
from app.utils.logger import logger
from app.dao.rule import rule_item_dao
from app.service.revervation import reservation_service as revervation_service
from app.dao.reservation import biz_reservation_request_dao
from app.schemas.reservation import BizReservationRequestCreate
from app.dao.menu import menu_dao
from app.models.reservation import ReservationType
from app.models.rule import DiningReservationRule, RuleItem, RuleScope, RuleType, RuleOrderType
from app.models.product import MealType
from app.models.reservation import BizReservationRequest
from app.dao.user import enterprise_user_relation_dao, enterprise_dao
from app.dao.admin import admin_dao
from app.service.pricing import pricing_service
from app.schemas.order import OrderItemBase
from app.events.deps import EventBusDep
from app.events.models import OrderEvent, OrderEventAction
from app.core.scheduler import send_order_change_reminders_task

router = APIRouter()


class WxLoginRequest(BaseModel):
    code: str
    phone_code: str
    type: int


class UpdateUserInfoRequest(BaseModel):
    nickname: Optional[str] = Field(None, alias="nickName")
    avatar_url: Optional[str] = Field(None, alias="avatarUrl")
    wechat_id: Optional[str] = Field(None, alias="openid")


class MsgStatusUpdate(BaseModel):
    msg_status: bool


@router.post("/auth")
async def verify_token(token: Optional[str] = Header(None), db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    验证token是否有效
    """
    logger.info(f"开始验证token: {token[:10]}..." if token else "token为空")

    if not token:
        logger.warning("请求中未提供token")
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    user = WeChatUserService.verify_token(db, token)

    if not user:
        logger.warning(f"token无效或已过期: {token[:10]}...")
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    logger.info(f"token验证成功，用户ID: {user.id}")
    user_info = WeChatUserService.get_user_info(db, user.wechat_id)
    logger.debug(f"获取到的用户信息: {user_info}")

    return {
        "message": "已登录",
        "status": 200,
        "userInfo": user_info,
        "token": token
    }


@router.post("/monitor-token")
async def monitor_token(request: WxLoginRequest, db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    微信小程序验证是否新用户
    """
    logger.info(f"monitor-token，检查是否注册: {request.code}")
    session_info = WeChatUserService.get_session_info(request.code)
    logger.info(f"monitor-token，获取 session_info: {session_info}")

    if not session_info or 'openid' not in session_info:
        raise HTTPException(status_code=400, detail="获取session_info失败")

    wechat_id = session_info['openid']
    logger.info(f"monitor-token，获取到用户 openid: {wechat_id}")

    user = WeChatUserService.get_user_info(db, wechat_id)
    logger.info(f"monitor-token，获取到用户信息: {user}")
    if user:
        logger.info(f"monitor-token，已注册: {session_info}")
        return {"status": 200}
    else:
        logger.info(f"monitor-token，未注册: {session_info}")
        return {"status": 400}


@router.post("/login")
async def login(request: WxLoginRequest, db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    微信小程序登录，未注册则进行注册登录
    """

    session_info = WeChatUserService.get_session_info(request.code)
    logger.info(f"login，获取 openid: {session_info}")
    if not session_info or 'openid' not in session_info:
        raise HTTPException(status_code=400, detail="登录失败")

    wechat_id = session_info['openid']
    logger.info(f"login，获取到用户 openid: {wechat_id}")

    user = WeChatUserService.get_user_info(db, wechat_id)
    token, token_expiry = product_wx_token()

    if user:
        logger.info(f"login，获取用户信息成功: {user}")
    else:
        logger.info(f"login，获取用户信息为空")

        phone = get_phone_number(request.phone_code, settings.WECHAT_APPID, settings.WECHAT_SECRET)
        logger.info(f"login，进行创建用户，获取手机号：{phone}")
        if not phone:
            raise HTTPException(status_code=400, detail="手机号解密失败")

        # 判断是否已注册
        personal_object = personal_user_dao.get_by_phone(db, phone)
        if personal_object:
            logger.info(f"login，手机号已注册，人工导入账户进行绑定用户")
            # 写入最新 wechat_id
            insert_wechat_id_state = personal_user_dao.update_wechat_id(db, personal_object.id, wechat_id)
            if insert_wechat_id_state:
                logger.info(f"login，人工导入账户进行绑定用户成功")
            else:
                logger.info(f"login，人工导入账户进行绑定用户失败")
        else:
            logger.info(f"login，手机号未注册，进行创建用户")

            # 生成随机用户名和昵称
            random_string = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
            nickname = f"用户_{random_string}"

            personal_user_dict = {
                "username": phone,
                "status": 1,
                "phone": phone,
                "email": None,
                "address": None,
                "real_name": None,
                "id_card": None,
                "password": phone,
                "wechat_id": wechat_id,
                "nickname": nickname
            }

            logger.info(f"login，创建用户 PersonalUser 信息: {personal_user_dict}")

            personal_user_data = PersonalUserCreateWX(**personal_user_dict)
            create_dict = UserService.wx_create_personal_user(db, personal_user_data)

            if create_dict:
                personal_user_object = create_dict[0]
                logger.info(f"用户信息，用户ID: {personal_user_object}")

    # 获取完整的用户信息
    user_info = WeChatUserService.get_user_info(db, wechat_id)
    user_info['token'] = token
    user_info['token_expiry'] = token_expiry

    # 更新数据库中的 token 信息
    WeChatUserService.update_token(db, wechat_id, token, token_expiry)

    # 写入 unionid
    if session_info.get('unionid', None):
        personal_user_dao.update_unionid(db, user_info['id'], session_info['unionid'])
        logger.info(f"用户信息，写入unionid: {session_info['unionid']}, 用户ID: {user_info['id']}, 微信ID: {wechat_id}")
    else:
        logger.info("微信开放平台未提供 unionid 值！")
        logger.info(f"用户信息，未写入unionid, 用户ID: {user_info['id']}, 微信ID: {wechat_id}")

    return {
        "token": token,
        "status": 200,
        "message": "登录成功",
        "userInfo": user_info
    }


@router.get("/user")
async def get_user_info(token: Optional[str] = Header(None), db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    获取用户信息
    """
    if not token:
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    user = WeChatUserService.verify_token(db, token)
    if not user:
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    user_info = WeChatUserService.get_user_info(db, user.wechat_id)

    return {
        "token": token,
        "status": 200,
        "message": "获取成功",
        "userInfo": user_info
    }


@router.post("/user-info")
async def update_user_info(task_info: dict, token: Optional[str] = Header(None), db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    更新用户信息
    """
    user = WeChatUserService.verify_token(db, token)
    if not user:
        logger.warning(f"token无效或已过期: {token[:10]}...")
        return {
            "message": "未登录",
            "status": 401,
            "userInfo": None
        }
    logger.info(task_info["userInfo"])

    if "avatarUrl" in task_info["userInfo"]:
        task_info["userInfo"].pop("avatarUrl")

    user_info = UpdateUserInfoRequest(**task_info["userInfo"])
    logger.info(user_info)

    if WeChatUserService.update_user_info(db, user_info):
        user_info = WeChatUserService.get_user_info(db, user.wechat_id)
        return {
            "message": "更新成功",
            "status": 200,
            "userInfo": user_info
        }
    else:
        return {
            "message": "更新失败",
            "status": 400,
            "userInfo": user_info
        }


@router.post("/upload-avatar")
async def upload_avatar(
        avatar: UploadFile = File(..., alias="avatar"),
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    上传头像并且更新用户信息

    Args:
        avatar: 上传的头像文件
        token: 用户token
        db: 数据库会话

    Returns:
        Dict: 包含上传结果的字典
    """
    # 验证token
    user = WeChatUserService.verify_token(db, token)
    if not user:
        logger.warning(f"token无效或已过期: {token[:10]}...")
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    # 验证文件类型
    if not allowed_file(avatar.filename):
        raise HTTPException(
            status_code=400,
            detail={"message": "不支持的文件类型", "status": 400}
        )

    try:
        # 生成安全的文件名
        ext = Path(avatar.filename).suffix
        filename = secure_filename(f"{user.wechat_id}_{int(datetime.now().timestamp())}{ext}")

        # 创建上传目录
        upload_folder = os.path.join(settings.UPLOAD_FOLDER, 'avatars')
        os.makedirs(upload_folder, exist_ok=True)

        # 文件保存路径
        file_path = os.path.join(upload_folder, filename)

        # 保存文件
        contents = await avatar.read()
        with open(file_path, 'wb') as f:
            f.write(contents)

        # 生成相对路径
        relative_path = f"/static/uploads/avatars/{filename}"

        # 更新用户头像URL
        user_info = UpdateUserInfoRequest(**{
            "avatarUrl": relative_path,
            "openid": user.wechat_id
        })
        logger.info(f"准备更新用户头像，参数：{user_info.dict()}")
        update_result = WeChatUserService.update_user_info(db, user_info)
        logger.info(f"更新用户头像结果：{update_result}")
        if not update_result:
            raise HTTPException(
                status_code=400,
                detail={"message": "更新用户头像失败", "status": 400}
            )

        # 生成完整URL
        full_avatar_url = f"{settings.BASE_URL}{relative_path}"

        return {
            "message": "头像上传成功",
            "status": 200,
            "avatarUrl": full_avatar_url
        }

    except Exception as e:
        logger.error(f"头像上传错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"message": f"头像上传失败: {str(e)}", "status": 500}
        )


@router.post("/update-msg-state")
async def update_msg_status(task_info: dict, token: Optional[str] = Header(None), db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    更新用户消息订阅状态

    Args:
        task_info: 消息状态更新请求体
        token: 当前登录用户
        db: 数据库

    Returns:
        Dict: 包含更新结果的响应
    """

    try:
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"token无效或已过期: {token[:10]}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        msg_state = task_info["msg_status"]

        if WeChatUserService.update_msg_status(db, user.id, msg_state):
            if msg_state == 1:
                gzh_str = "，记得关注公众号哦！"
            else:
                gzh_str = "，已关闭消息推送。"
            return {
                "message": "更新成功" + gzh_str,
                "status": 200,
                "msg_status": msg_state
            }
        raise HTTPException(
            status_code=400,
            detail={"message": "更新失败", "status": 400}
        )
    except Exception as e:
        logger.error(f"更新消息状态错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"message": f"更新失败: {str(e)}", "status": 500}
        )
