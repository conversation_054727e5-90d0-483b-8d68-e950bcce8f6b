# -*- coding: utf-8 -*-
# 商务餐模块


from fastapi import APIRouter, Depends, HTTPException, Header, File, UploadFile, Request
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import re

from app.dao.account import account_transaction_dao, account_dao
from app.models.enum import Status
from app.service.wechat_miniapp.wx_reservation import reservation_service
from app.core.config import settings
from app.service.wechat_miniapp.wx_service import WechatService
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.service.user import UserService
from app.service.wechat_miniapp.wx_order import WXOrderService
from app.models.user import PersonalUser
from app.dao.rule import rule_dao
from app.models.order import Order
from app.core.deps import get_current_user, get_db
from app.service.wechat_miniapp.wx_account import AccountService
import logging
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from app.schemas.user import PersonalUserCreateWX
import random
import string
import os
from pathlib import Path

from app.utils.common import get_phone_number, product_wx_token, allowed_file, secure_filename
from app.models.account import AccountType, TransactionType
from app.schemas.order import OrderUpdate
from app.models.order import PaymentStatus, OrderType, PaymentMethod
from app.dao.order import order_dao, order_item_dao
from app.service.wechat_miniapp.wx_service import wechat_service
from app.service.payment import payment_service
from app.dao.reservation import reservation_request_dao
from app.models.reservation import ReservationStatus
from app.models.order import OrderStatus, OrderItem
from app.dao.wx_payment import wx_payment_dao, wx_payment_service
from app.models.order import WxPaymentRecord, WxRefundRecord, WxRefundStatus, WxPaymentStatus
from app.schemas.account import AccountTransactionCreate
from app.utils.common import get_current_time
from app.dao.user import personal_user_dao
from app.dao.product import product_dao
from app.service.order import order_service
from app.utils.logger import logger
from app.dao.rule import rule_item_dao
from app.service.revervation import reservation_service as revervation_service
from app.dao.reservation import biz_reservation_request_dao
from app.schemas.reservation import BizReservationRequestCreate
from app.dao.menu import menu_dao
from app.models.reservation import ReservationType
from app.models.rule import DiningReservationRule, RuleItem, RuleScope, RuleType, RuleOrderType
from app.models.product import MealType
from app.models.reservation import BizReservationRequest
from app.dao.user import enterprise_user_relation_dao, enterprise_dao
from app.dao.admin import admin_dao
from app.service.pricing import pricing_service
from app.schemas.order import OrderItemBase
from app.events.deps import EventBusDep
from app.events.models import OrderEvent, OrderEventAction
from app.core.scheduler import send_order_change_reminders_task

router = APIRouter()


@router.post("/biz-order/create")
async def create_biz_order(
        task_info: dict,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """创建商务餐订单"""
    logger.info(f"创建商务餐订单，接收到的参数: {task_info}")

    try:
        # 验证用户token
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"token无效或已过期: {token[:10]}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        user_id = user.id
        logger.info(f"用户token验证成功，用户ID: {user_id}")

        # 验证必填参数
        required_fields = ['store_id',
                           'booking_date', 'booking_time', 'people_count', 'items']

        for field in required_fields:
            if field not in task_info or not task_info[field]:
                logger.error(f"参数错误，缺少必填字段: {field}")
                return {
                    "message": f"缺少必填参数: {field}",
                    "status": 400
                }

        # 验证手机号格式
        phone_pattern = r'(^$)|^1[3-9]\d{9}$'
        if not re.match(phone_pattern, task_info['contact_phone']):
            logger.error(f"手机号格式错误: {task_info['contact_phone']}")
            return {
                "message": "手机号格式错误",
                "status": 400
            }

        # 获取规则和规则项（商务餐rule只能有一个，rule_item可以有多个）
        rule = rule_dao.get_by_scope_and_order_type(db, RuleScope.ORDER, RuleOrderType.BIZ_DINING)
        logger.info(f"获取的规则: {rule}")

        if not rule:
            logger.error("商务餐预订规则不存在")
            return {
                "message": "商务餐预订规则不存在",
                "status": 500
            }

        # 获取规则项(商务餐rule只能有一个，rule_item限制为一个)
        # rule_item = rule_item_dao.get_by_rule_id(db, rule.id)
        # logger.info(f"获取的规则项: {rule_item}")

        rule_items = rule_item_dao.get_by_rule(db, rule.id)
        logger.info(f"获取的规则项列表: {rule_items}")

        if not rule_items:
            logger.error("商务餐预订规则项不存在")
            return {
                "message": "商务餐预订规则项不存在",
                "status": 500
            }

        # 处理预约日期时间
        booking_date = task_info['booking_date'].replace('年', '-').replace('月', '-').replace('日', '')
        booking_time = task_info['booking_time'].replace('时', ':').replace('分', '')

        # 合并日期和时间
        booking_datetime_str = f"{booking_date} {booking_time}"
        try:
            booking_datetime = datetime.strptime(booking_datetime_str, "%Y-%m-%d %H:%M")
            # 检查是否是过去的时间
            if booking_datetime < datetime.now():
                return {
                    "message": "抱歉！预约时间不能是过去的时间",
                    "status": 400
                }

            # 检查如果是当天预约，只允许在9:30之前创建
            today = datetime.now().date()
            booking_date_obj = booking_datetime.date()
            current_time = datetime.now().time()
            cutoff_time = datetime.strptime("09:30", "%H:%M").time()

            if booking_date_obj == today and current_time > cutoff_time:
                logger.info(f"预约当天就餐，且当前时间大于 9:30，不允许创建")
                onsite_state = task_info.get("onsite_state", None)
                if onsite_state == "onsite":
                    logger.info(f"临时扫码预约商务餐，允许创建")
                else:
                    return {
                        "message": "抱歉！预约当天就餐只能在 9:30 之前创建",
                        "status": 400
                    }

            # 补充商务餐的 rule_item 规则，报餐时间是否在允许的时间内
            valid_rule_item = None
            available_time_slots = []
            now = datetime.now()
            current_date = now.date()

            for rule_item in rule_items:
                # 首先检查预订日期是否在允许的范围内（根据generated_count）
                days_count = 5 if rule_item.generated_count is None else rule_item.generated_count

                # 计算预订日期与当前日期的差值（天数）
                booking_date_obj = booking_datetime.date()
                days_diff = (booking_date_obj - current_date).days

                # 检查是否超出可预订的天数范围
                if days_diff < 0 or days_diff > days_count:
                    logger.info(f"预订日期 {booking_date_obj} 超出可预订范围，最多可预订未来 {days_count} 天")
                    continue

                # 检查预订日期是否是允许的星期几
                weekday_part = rule_item.start_time_cron_str.split()[4]
                booking_weekday = booking_datetime.weekday()  # 0-6表示周一到周日

                # 转换cron表达式中的星期几为Python的星期几
                allowed_weekdays = []
                if weekday_part == "*":
                    allowed_weekdays = list(range(7))  # 所有天都允许
                else:
                    for day in weekday_part.split(','):
                        if day == "0":  # cron中0表示周日
                            allowed_weekdays.append(6)  # Python中6是周日
                        else:
                            allowed_weekdays.append(int(day) - 1)  # 其他天减1

                # 检查预订日期是否是允许的星期几
                if booking_weekday not in allowed_weekdays:
                    logger.info(f"预订日期 {booking_date_obj} 不是允许的星期几，允许的星期几: {allowed_weekdays}")
                    continue

                # 然后检查预订时间是否在规则允许的时间范围内
                start_cron_parts = rule_item.start_time_cron_str.split()
                end_cron_parts = rule_item.end_time_cron_str.split()

                # 获取规则允许的开始和结束时间
                rule_start_hour = int(start_cron_parts[1])  # 从cron表达式获取小时
                rule_start_minute = int(start_cron_parts[0])  # 从cron表达式获取分钟
                rule_end_hour = int(end_cron_parts[1])
                rule_end_minute = int(end_cron_parts[0])

                # 获取预订的就餐时间
                booking_hour = booking_datetime.hour
                booking_minute = booking_datetime.minute

                # 检查预订时间是否在规则允许的时间范围内
                is_time_allowed = False
                if (booking_hour > rule_start_hour or
                    (booking_hour == rule_start_hour and booking_minute >= rule_start_minute)) and \
                        (booking_hour < rule_end_hour or
                         (booking_hour == rule_end_hour and booking_minute <= rule_end_minute)):
                    is_time_allowed = True

                # 如果预订时间不在允许范围内，跳过此规则项
                if not is_time_allowed:
                    logger.info(
                        f"预订时间 {booking_hour}:{booking_minute} 不在规则允许的时间范围 {rule_start_hour}:{rule_start_minute}-{rule_end_hour}:{rule_end_minute} 内")
                    continue

                # 最后检查预订截止时间
                if rule_item.order_deadline is not None:
                    # 计算预订截止时间
                    deadline_date = booking_datetime
                    # 将分钟数转换为小时和分钟
                    hours = rule_item.order_deadline // 60
                    minutes = rule_item.order_deadline % 60
                    logger.info(f"预订截止时间 - 小时: {hours}, 分钟: {minutes}")

                    # 创建预订的截止时间
                    order_deadline_time = deadline_date - timedelta(hours=hours, minutes=minutes)
                    logger.info(f"就餐开始时间: {deadline_date}")
                    logger.info(f"预订截止时间: {order_deadline_time}")

                    # 格式化时间段
                    time_slot = f"{rule_start_hour:02d}:{rule_start_minute:02d}-{rule_end_hour:02d}:{rule_end_minute:02d}"

                    if now <= order_deadline_time:
                        # 找到满足条件的规则项
                        valid_rule_item = rule_item
                        logger.info(f"找到满足条件的规则项: {rule_item.id}")
                        available_time_slots.append({
                            "rule_item_id": rule_item.id,
                            "time_slot": time_slot,
                            "deadline": order_deadline_time.strftime("%Y-%m-%d %H:%M")
                        })

            # 如果没有找到满足条件的规则项，返回错误信息和可用时段
            if not valid_rule_item:
                logger.info(f"该时段不提供商务餐服务")
                # 获取可用时段，使用规则项的generated_count参数
                future_slots = []

                for rule_item in rule_items:
                    if rule_item.start_time_cron_str and rule_item.end_time_cron_str:
                        # 使用规则项的generated_count，如果未设置则默认为5天
                        days_count = 5 if rule_item.generated_count is None else rule_item.generated_count

                        # 获取规则允许的开始和结束时间
                        start_cron_parts = rule_item.start_time_cron_str.split()
                        end_cron_parts = rule_item.end_time_cron_str.split()

                        start_hour = int(start_cron_parts[1])
                        start_minute = int(start_cron_parts[0])
                        end_hour = int(end_cron_parts[1])
                        end_minute = int(end_cron_parts[0])

                        # 格式化时间段
                        time_slot = f"{start_hour:02d}:{start_minute:02d}-{end_hour:02d}:{end_minute:02d}"

                        # 获取允许的星期几（从cron表达式的第5部分）
                        weekday_part = start_cron_parts[4]
                        allowed_weekdays = []

                        if weekday_part == "*":
                            # 如果是*，表示所有天都允许
                            allowed_weekdays = list(range(7))  # 0-6表示周一到周日
                        else:
                            # 否则解析具体允许的天
                            for day in weekday_part.split(','):
                                # cron表达式中0表示周日，1-6表示周一到周六
                                # 而Python中0-6表示周一到周日，所以需要转换
                                if day == "0":  # 周日
                                    allowed_weekdays.append(6)  # Python中6是周日
                                else:
                                    allowed_weekdays.append(int(day) - 1)  # 其他天减1

                        # 生成未来days_count天的可用时段
                        current_date = now.date()
                        days_checked = 0
                        days_added = 0

                        while days_added < days_count and days_checked < 30:  # 最多检查30天，避免无限循环
                            days_checked += 1
                            future_date = current_date + timedelta(days=days_checked)

                            # 检查这一天是否是允许的星期几
                            weekday = future_date.weekday()  # 0-6表示周一到周日

                            if weekday in allowed_weekdays:
                                # 如果是允许的星期几，添加到可用时段
                                future_slots.append({
                                    "date": future_date.strftime("%Y-%m-%d"),
                                    "time_slot": time_slot
                                })
                                days_added += 1

                # 按日期排序
                future_slots.sort(key=lambda x: x["date"])

                return {
                    "message": "抱歉！该时段不提供商务餐服务，请选择以下可用时段",
                    "status": 400,
                    "available_slots": future_slots
                }

            # 使用找到的有效规则项
            rule_item = valid_rule_item

        except ValueError:
            logger.error(f"日期时间格式错误: {booking_datetime_str}")
            return {
                "message": "日期时间格式错误",
                "status": 400
            }

        # 解析预订日期和时间
        start_hour, start_minute = booking_time.split(':')
        end_hour = str(int(start_hour) + 3)  # 默认用餐时长3小时
        end_minute = start_minute

        # 生成预订时间段格式：YYMMDDHHMM_YYMMDDHHMM
        # booking_date_obj已经在前面定义，不需要再次解析booking_date
        reservation_period = (
                booking_date_obj.strftime("%y%m%d") + start_hour.zfill(2) + start_minute.zfill(2) + "_" +
                booking_date_obj.strftime("%y%m%d") + end_hour.zfill(2) + end_minute.zfill(2)
        )

        # 生成dining_start_time和dining_end_time
        # 直接使用已经解析好的booking_datetime，只修改小时和分钟
        dining_start_time = booking_datetime.replace(
            hour=int(start_hour),
            minute=int(start_minute)
        )

        # 计算结束时间，默认用餐时长3小时
        end_hour_int = int(start_hour) + 3
        # 如果结束时间超过21点，则截断到21点
        if end_hour_int > 21:
            end_hour_int = 21

        end_hour = str(end_hour_int)
        logger.info(end_hour)
        # 使用replace方法修改小时和分钟
        dining_end_time = dining_start_time.replace(
            hour=end_hour_int,
            minute=int(end_minute)
        )

        rule_data = {
            "rule_id": rule.id,
            "rule_item_id": rule_item.id,
            "dining_start_time": dining_start_time,
            "dining_end_time": dining_end_time,
            "reservation_period": reservation_period,
            "task_info": task_info
        }
        logger.info(f"创建的规则数据: {rule_data}")

        products = []
        for booking in task_info['items']:
            products.append(
                {
                    "product_id": booking["dish_id"],
                    "quantity": booking["quantity"],
                    "reservation_requests": [
                        {
                            "rule_id": rule_item.id,
                            "rule_item_id": rule_item.id,
                            "dining_start_time": dining_start_time,
                            "dining_end_time": dining_end_time,
                            "reservation_period": reservation_period
                        }
                    ]
                }
            )
        logger.info(f"创建商品列表: {products}")

        # 创建订单
        order = order_service.create_order(db, user_id, products, rule_data)
        order_id = order.id
        order_no = order.order_no
        order_payable_amount = order.payable_amount
        logger.info(f"创建订单: {order}")
        logger.info(f"创建的订单号: {order_no}")
        logger.info(f"创建的订单金额: {order_payable_amount}")

        # 获取用户余额
        user_balance = account_dao.get_user_balance(db, user_id)
        logger.info(f"用户余额: {user_balance}")

        return {
            'order_no': order_no,
            'order_id': order_id,
            'payable_amount': order_payable_amount,
            'user_balance': user_balance
        }
    except Exception as e:
        logger.error(f"创建商务餐订单失败: {str(e)}", exc_info=True)
        db.rollback()
        return {
            "message": f"创建订单失败: {str(e)}",
            "status": 500
        }


# 获取商务餐订单详情
@router.get("/biz-order/detail")
async def get_biz_order_detail(
        order_id: int,
        source_mark: str = None,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取商务餐订单详情

    Args:
        order_id: 订单ID
        source_mark: 来源标记，用于判断是否可以查看订单详情
        token: 用户令牌
        db: 数据库会话

    Returns:
        订单详情数据
    """
    logger.info(f"获取商务餐订单详情，订单ID: {order_id}")

    try:
        # 验证用户token
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"token无效或已过期: {token[:10]}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        user_id = user.id
        logger.info(f"用户token验证成功，用户ID: {user_id}")

        # 获取订单信息
        order = order_dao.get(db, order_id)
        if not order:
            logger.error(f"订单不存在，订单ID: {order_id}")
            return {
                "message": "订单不存在",
                "status": 404
            }
        order_id = order.id
        order_no = order.order_no
        order_is_modified = order.is_modified
        order_status_text = order.status.name
        order_status_value = order.status.value
        order_payment_status_text = order.payment_status.name
        order_payment_status_value = order.payment_status.value
        order_created_at = order.created_at
        order_payment_method = order.payment_method
        order_payment_method_text = order.payment_method.name
        order_payment_method_value = order.payment_method.value

        # 获取订单项
        order_items = order_item_dao.get_by_order(db, order_id)

        # 获取商务餐预订信息
        reservation_request = None
        for req in order.reservation_requests:
            if req.type == ReservationType.BIZ_DINING_RESERVATION:
                reservation_request = req
                break

        if not reservation_request:
            logger.error(f"未找到商务餐预订信息，订单ID: {order_id}")
            return {
                "message": "未找到商务餐预订信息",
                "status": 404
            }

        store_info = {
            "id": 1,  # 使用固定值1替代内置函数id
            "name": "乙禾素食餐厅",
            "address": "广州市黄埔区黄埔大道东976号港航中心二期B座1601房",
            "rating": "4.9",
            "ratingCount": "1024",
            "image": f"{settings.BASE_URL}/static/images/202506101801712.jpg"
        }

        # 解析预约时间
        reservation_period = reservation_request.reservation_period  # 格式: 2505091000_2505091200
        start_time_str, end_time_str = reservation_period.split('_')
        start_time = datetime.strptime(f"20{start_time_str}", "%Y%m%d%H%M")

        # 获取系统运营管理员
        # 根据 user 的手机号 去 admin 表中查询是否是管理员
        user_phone = user.phone
        admin_status = admin_dao.get_by_phone(db, user_phone)

        is_server_admin = False
        if admin_status:
            logger.info("是管理员")
            # 检查是否有miniapp:server权限
            roles = admin_status.roles
            logger.info("管理员角色: %s", roles)
            if roles:
                for role in roles:
                    logger.info("角色ID: %s, 角色名称: %s", role.id, role.name)
                    # 检查角色的权限
                    permissions = role.permissions
                    logger.info("角色权限: %s", permissions)
                    for permission in permissions:
                        logger.info("权限代码: %s", permission.code)
                        if permission.code == "miniapp:server":
                            is_server_admin = True
                            break
                    if is_server_admin:
                        break
            logger.info("系统管理员状态: %s", is_server_admin)
        else:
            logger.info("不是管理员")
            is_server_admin = False

        user_mark = ""
        enterprise_id = None
        enterprise_data = None
        now = datetime.now()

        logger.info(f"source_mark: {source_mark}")
        logger.info(f"start_time: {start_time}")
        logger.info(f"datetime.now(): {now}")
        if not source_mark:
            # （加菜）订单当天9:30分前，允许查看
            if start_time.date() > now.date():
                user_mark = "user"
                logger.info(f"订单未来时间，允许查看，订单ID: {order_id}")
            else:
                if start_time.date() == now.date():
                    if now.hour < 9 and now.minute < 30:
                        user_mark = "user"
                        logger.info(f"订单当天9:30分前，允许查看，订单ID: {order_id}")
                    else:
                        logger.info(f"订单当天9:30分后，不允许查看，订单ID: {order_id}")
                        if is_server_admin:
                            logger.info(f"订单当天9:30分后，不允许查看，是服务员可继续加菜，订单ID: {order_id}")
                            # 如果是管理员，则允许查看
                            user_mark = "admin"
                        else:
                            logger.info(f"订单已超过可修改时间段，不是服务员，不允许加菜，订单ID: {order_id}")
                            # 如果不是管理员，则不允许查看
                            return {
                                "message": "订单已超过可更改时间段，如需帮助请联系客服！",
                                "status": 403
                            }
                else:
                    logger.info(f"订单已超过可修改时间段，不是服务员，不允许加菜，订单ID: {order_id}")
                    return {
                        "message": "订单已超过可更改时间段，如需帮助请联系客服！",
                        "status": 403
                    }

        # 验证订单所属用户
        if is_server_admin:
            # 系统运营者可查看所有订单
            logger.info(f"系统运营者可查看所有订单，运营者ID: {admin_status.id}")
            user_mark = "admin"
        else:
            # 1.如果是企业支付，就允许该企业的所有用户查看，否则只允许订单所属用户查看；备注：需要看支付订单的支付企业ID来确定是否是该企业的订单
            # 2.如果是个人支付，就允许订单所属用户查看
            if order.user_id == user_id:
                # 订单所属用户可以查看
                logger.info(f"订单所属用户可以查看，用户ID: {user_id}")
                user_mark = "user"
            elif order.payment_method == PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE:
                # 企业支付的订单，检查用户是否属于该企业
                # 获取用户关联的所有企业
                user_enterprise_relations = enterprise_user_relation_dao.get_by_personal_user_id(db, user_id)
                user_enterprise_ids = [relation.enterprise_id for relation in user_enterprise_relations]

                # 获取订单的企业ID（通过支付记录获取）
                account_transactions = account_transaction_dao.get_by_order_id(db, order_id)
                if not account_transactions or not account_transactions[0].account_id:
                    logger.error(f"找不到订单的支付交易记录，订单ID: {order_id}")
                    return {
                        "message": "无权访问此订单",
                        "status": 403
                    }

                # 获取支付企业的账户ID和企业ID
                enterprise_account_list = account_dao.get_by_id(db, account_transactions[0].account_id)
                if not enterprise_account_list or len(enterprise_account_list) == 0:
                    logger.error(f"找不到企业账户")
                    return {
                        "message": "获取企业列表成功",
                        "status": 200,
                        "data": []
                    }

                # 获取列表中的第一个账户
                enterprise_account = enterprise_account_list[0]
                enterprise_id = enterprise_account.user_id
                enterprise = enterprise_dao.get(db, enterprise_id)

                if enterprise_id not in user_enterprise_ids:
                    logger.error(f"用户不属于支付企业，用户ID: {user_id}")
                    return {
                        "message": "无权访问此订单",
                        "status": 403
                    }
                logger.info(f"用户属于支付企业，用户ID: {user_id}, 企业ID: {enterprise_id}")
                user_mark = "enterprise"
                enterprise_data = {
                    'id': enterprise.id,
                    'company_name': enterprise.company_name,
                    'phone': enterprise.phone,
                    'email': enterprise.email,
                    'address': enterprise.address
                }
            else:
                # 非订单所属用户且非企业支付订单，无权访问
                logger.error(f"无权访问此订单，用户ID: {user_id}，订单所属用户ID: {order.user_id}")
                return {
                    "message": "无权访问此订单",
                    "status": 403
                }

        # 如果是企业支付，则获取该企业名称和ID
        pay_enterprise_id = None
        if order_payment_method == PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE:
            # 获取order_id的流水
            account_transactions = account_transaction_dao.get_by_order_id(db, order_id)
            if not account_transactions:
                logger.error(f"找不到订单的支付交易记录，订单ID: {order_id}")
            else:
                pay_enterprise_id = account_transactions[0].account.user_id
        # 构建订单项数据
        items_data = []
        for item in order_items:
            items_data.append({
                "dish_id": item.product_id,
                "name": item.product.name,
                "price": float(item.price),
                "quantity": item.quantity,
                "image": item.product.image,
                "status": item.status.value,
            })
        # 构建订单详情数据
        order_detail = {
            "order_id": order_id,
            "order_no": order_no,
            "is_modified": order_is_modified,
            "status": order_status_value,
            "status_text": order_status_text,
            "payment_status": order_payment_status_value,
            "payment_status_text": order_payment_status_text,
            "payment_method": order_payment_method,
            "payment_method_text": order_payment_method_text,
            "payment_method_value": order_payment_method_value,
            "total_amount": float(order.total_amount),
            "actual_amount_paid": float(order.actual_amount_paid),
            "booking_date": start_time.strftime("%Y年%m月%d日"),
            "booking_time": start_time.strftime("%H时%M分"),
            "people_count": reservation_request.persons,
            "contact_name": reservation_request.name,
            "contact_phone": reservation_request.phone,
            "remark": reservation_request.remark,
            "created_at": order_created_at.strftime("%Y-%m-%d %H:%M:%S"),
            "store_info": store_info,
            "items": items_data,
            "enterprise_id": enterprise_id if user_mark == "enterprise" else None,  # 如果需要企业ID，可以在这里添加
            "enterprise_data": enterprise_data,
            "pay_enterprise_id": pay_enterprise_id,
            "user_mark": user_mark
        }

        return {
            "message": "获取成功",
            "status": 200,
            "data": order_detail
        }

    except Exception as e:
        logger.error(f"获取商务餐订单详情失败: {str(e)}", exc_info=True)
        return {
            "message": f"获取订单详情失败: {str(e)}",
            "status": 500
        }


# 根据商务餐订单进行修改订单
@router.post("/biz-order/update")
async def update_biz_order(
        task_info: dict,
        event_bus: EventBusDep,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    根据商务餐订单进行补充订单项，订单ID: {order_id}
    """
    order_id = task_info.get("order_id")
    logger.info(f"根据商务餐订单进行补充订单项，订单ID: {order_id}")
    logger.info(f"接收到的参数: {task_info}")

    try:
        # 验证用户token
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"token无效或已过期: {token[:10]}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        user_id = user.id
        logger.info(f"用户token验证成功，用户ID: {user_id}")

        # 获取订单信息
        order = order_dao.get(db, order_id)
        if not order:
            logger.error(f"订单不存在，订单ID: {order_id}")
            return {
                "message": "订单不存在",
                "status": 404
            }
        order_id = order.id
        order_no = order.order_no

        # 获取原始订单项
        original_order_items = order_item_dao.get_by_order(db, order_id)

        # 获取变更项目
        changed_items = task_info.get("changed_items", [])
        logger.info(f"订单变更项目: {changed_items}")

        # 处理变更项目
        if changed_items:
            for item in changed_items:
                dish_id = item.get("dish_id")
                quantity = item.get("quantity")
                change_type = item.get("change_type")

                # 查找原始订单项
                original_item = next((oi for oi in original_order_items if str(oi.product_id) == str(dish_id)), None)

                if change_type == "add":
                    # 新增订单项
                    logger.info(f"新增订单项: dish_id={dish_id}, quantity={quantity}")
                    if not original_item:
                        # 获取商品信息
                        product = product_dao.get(db, dish_id)
                        if not product:
                            logger.error(f"商品不存在，商品ID: {dish_id}")
                            continue
                        subtotal = product.price * quantity

                        # 订单项计价
                        final_price, payable_amount, remark = pricing_service.product_pricing(db, dish_id,
                                                                                              product.price, quantity)
                        # 生成订单项 - 使用SQLAlchemy模型而不是Pydantic模型
                        new_order_item = OrderItem(
                            order_id=order_id,
                            product_id=dish_id,
                            quantity=quantity,
                            unpaid_quantity=quantity,
                            price=product.price,
                            subtotal=subtotal,
                            final_price=final_price,
                            payable_amount=payable_amount,
                            pricing_remark="商务餐预订订单，新增订单项。" + remark,
                            status=OrderStatus.PENDING
                        )

                        db.add(new_order_item)
                    elif original_item.status == OrderStatus.CANCELLED:
                        # 恢复已取消的订单项
                        logger.info(f"恢复已取消订单项: dish_id={dish_id}, quantity={quantity}")
                        product = product_dao.get(db, dish_id)
                        if not product:
                            logger.error(f"商品不存在，商品ID: {dish_id}")
                            continue

                        subtotal = product.price * quantity
                        # 订单项计价
                        final_price, payable_amount, remark = pricing_service.product_pricing(db, dish_id,
                                                                                              product.price, quantity)

                        # 更新订单项
                        original_item.quantity = quantity
                        original_item.unpaid_quantity = quantity
                        original_item.subtotal = subtotal
                        original_item.final_price = final_price
                        original_item.payable_amount = payable_amount
                        original_item.pricing_remark = "商务餐预订订单，恢复已取消订单项。" + remark
                        original_item.status = OrderStatus.PENDING  # 将状态从CANCELLED改为PENDING

                elif change_type == "update" and original_item:
                    # 更新订单项数量和金额
                    logger.info(
                        f"更新订单项: dish_id={dish_id}, old_quantity={original_item.quantity}, new_quantity={quantity}")
                    
                    # 计算单项金额变化
                    old_amount = original_item.quantity * original_item.price
                    new_amount = quantity * original_item.price
                    amount_diff = new_amount - old_amount
                    tmp_quantity = original_item.quantity

                    # 更新数量
                    original_item.quantity = quantity

                    # 更新金额
                    original_item.subtotal = new_amount  # 更新小计金额

                    # 重新计算最终价格和应付金额
                    final_price, payable_amount, remark = pricing_service.product_pricing(db, dish_id,
                                                                                          original_item.price, quantity)
                    original_item.final_price = final_price
                    original_item.payable_amount = payable_amount

                    logger.info(f"该订单项总数量: {tmp_quantity}, 新的总数量: {quantity}，未支付数量: {original_item.unpaid_quantity}")

                    if quantity > tmp_quantity:
                        # 新增订单项数量
                        original_item.unpaid_quantity = (quantity - tmp_quantity) + original_item.unpaid_quantity
                        original_item.status = OrderStatus.PARTIAL_PAID
                    elif quantity < tmp_quantity:
                        # 减少订单项数量
                        original_item.unpaid_quantity = original_item.unpaid_quantity - (tmp_quantity - quantity)
                        if original_item.unpaid_quantity <= 0:
                            original_item.status = OrderStatus.PAID
                    logger.info(f"该订单项未支付数量: {original_item.unpaid_quantity}")

                    # 记录金额变化
                    logger.info(
                        f"订单项金额更新: dish_id={dish_id}, old_amount={old_amount}, new_amount={new_amount}, diff={amount_diff}")

                elif change_type == "delete" and original_item:
                    # 删除订单项（标记为已取消）
                    logger.info(f"删除订单项: dish_id={dish_id}, quantity={original_item.quantity}")
                    original_item.unpaid_quantity = 0
                    original_item.status = OrderStatus.CANCELLED

            # 发送订单变更提醒消息
            #send_order_change_reminders_task(order_id, "已加菜")

        else:
            logger.info(f"订单项无变动")

        # 提交数据库更改
        db.commit()

        return {
            "message": "更新成功",
            "status": 200,
            "data": {
                "order_id": order_id,
                "order_no": order_no
            }
        }

    except Exception as e:
        db.rollback()
        logger.error(f"更新商务餐订单失败: {str(e)}", exc_info=True)
        return {
            "message": f"更新订单失败: {str(e)}",
            "status": 500
        }


# 根据商务餐订单进行支付
@router.post("/biz-order/pay")
async def pay_biz_order(
        task_info: dict,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    根据商务餐订单进行支付
    """
    try:
        logger.info(f"开始处理商务餐订单支付，接收到的参数: {task_info}")

        # 验证用户token
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"token无效或已过期: {token[:10]}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        user_id = user.id
        logger.info(f"用户token验证成功，用户ID: {user_id}")

        # 获取订单信息
        order_id = task_info.get("order_id")
        if not order_id:
            logger.error("订单ID为空")
            return {
                "message": "订单ID不能为空",
                "status": 400
            }

        order = order_dao.get(db, order_id)
        if not order:
            logger.error(f"订单不存在，订单ID: {order_id}")
            return {
                "message": "订单不存在",
                "status": 404
            }
        order_id = order.id
        order_no = order.order_no
        order_status = order.status
        order_is_modified = order.is_modified
        order_payment_status = order.payment_status

        # 检查订单状态，是否为已支付状态，is_modified 表示是否为修改过的订单（修改过的订单可以支付）
        if order_status != OrderStatus.PAID and not order_is_modified:
            logger.error(f"订单状态不正确，当前状态: {order_status}")
            return {
                "message": "订单状态不正确，无法支付",
                "status": 400
            }

        if order_payment_status != PaymentStatus.PAID and not order_is_modified:
            logger.error(f"订单支付状态不正确，当前状态: {order_payment_status}")
            return {
                "message": "订单已支付或正在处理中",
                "status": 400
            }

        # 计算未支付金额
        order_items = order_item_dao.get_by_order(db, order_id)
        current_items_amount = 0
        for item in order_items:
            if item.status != OrderStatus.CANCELLED and str(item.product_id) != '100' and str(item.product_id) != '200':
                current_items_amount += item.price * item.quantity

        total_amount = float(order.total_amount) if order.total_amount else 0
        unpaid_amount = current_items_amount - total_amount
        payable_amount = max(0, unpaid_amount)

        if payable_amount <= 0:
            logger.info(f"订单无需支付，应付金额: {payable_amount}")
            # 如果是修改后的订单且无需额外支付，直接标记为已支付
            order.is_modified = True
            order.last_modified_at = datetime.now()
            order.current_status = "订单已修改，金额小于原金额，无需支付"
            db.commit()

            return {
                "message": "支付成功!",
                "status": 200
            }

        payment_method = task_info.get("payment_method")
        if not payment_method:
            logger.error("支付方式为空")
            return {
                "message": "支付方式不能为空",
                "status": 400
            }

        # 处理不同的支付方式
        if payment_method == "wxpay":
            # 微信支付
            # 最小支付金额为0.01元
            logger.info(f"未调整最小支付金额时，金额: {payable_amount}")
            payable_amount = max(0.01, round(payable_amount, 2))
            logger.info(f"调整最小支付金额后，金额: {payable_amount}")

            # 更新订单支付方式
            order_dao.update_payment_method(db, order_id, PaymentMethod.WECHAT_PAY)

            # 为修改后的订单生成新的支付订单号
            # modified_order_no = order_no
            # 为修改后的订单生成新的支付订单号，格式为：原订单号_时间戳
            modified_order_no = f"{order_no}_{int(datetime.now().timestamp())}"
            logger.info(f"修改后的订单生成新的支付订单号: {modified_order_no}")

            # 生成微信支付参数
            try:
                class_wechat_service = WechatService()
                pay_params = class_wechat_service.create_jsapi_payment(
                    user.wechat_id,
                    modified_order_no,  # 使用新生成的订单号
                    payable_amount,
                    "商务餐订单支付，补差额：" + str(payable_amount)
                )

                if not pay_params:
                    logger.error("微信支付参数生成失败，返回为空")
                    return {
                        "message": "生成支付参数失败，请检查微信支付配置",
                        "status": 500
                    }

                logger.info(f"微信支付参数生成成功: {pay_params}")

                # 创建支付记录
                wx_payment_service.create_payment_record(
                    db,
                    order_id,
                    modified_order_no,  # 使用新生成的订单号
                    user.wechat_id,
                    payable_amount,
                    "商务餐订单支付，补差额：" + str(payable_amount),
                    pay_params.get("package", "").replace("prepay_id=", ""),
                    pay_params
                )

                return {
                    "message": "支付参数生成成功",
                    "status": 200,
                    "data": pay_params
                }
            except Exception as e:
                logger.error(f"生成微信支付参数失败: {str(e)}")
                return {
                    "message": f"生成支付参数失败: {str(e)}",
                    "status": 500
                }

        elif payment_method == "balance":
            # 个人账户余额支付
            logger.info(f"使用个人账户余额支付，金额: {payable_amount}")

            # 检查用户余额
            accounts = account_dao.get_by_user_id(db, user.id)
            if not accounts:
                logger.error("用户账户不存在")
                return {
                    "message": "用户账户不存在",
                    "status": 400
                }

            regular_account = None
            for account in accounts:
                if account.type == AccountType.REGULAR:
                    regular_account = account

            if not regular_account:
                logger.error("用户普通账户不存在")
                return {
                    "message": "用户普通账户不存在",
                    "status": 400
                }

            if regular_account.balance < payable_amount:
                logger.error(f"用户余额不足，余额: {regular_account.balance}，需要: {payable_amount}")
                return {
                    "message": "余额不足",
                    "status": 400
                }

            # 执行支付
            try:
                # 更新账户余额
                regular_account.balance -= payable_amount

                # 创建支付交易记录
                transaction = AccountTransactionCreate(
                    account_id=regular_account.id,
                    order_id=order_id,
                    transaction_type=TransactionType.CONSUME,
                    amount=-payable_amount,
                    description=f"商务餐订单修改后差额支付：{order_no}",
                    transaction_time=get_current_time()
                )
                account_transaction_dao.create(db, transaction)

                # 重新获取订单对象，避免使用可能已过期的对象
                order = order_dao.get(db, order_id)
                if not order:
                    db.rollback()
                    logger.error("订单不存在或已被删除")
                    return {
                        "message": "订单不存在或已被删除",
                        "status": 404
                    }

                # 更新订单状态
                order.is_modified = True
                order.last_modified_at = datetime.now()
                order.current_status = "商务餐订单支付，个人账户进行补差额：" + str(payable_amount)

                # 更新订单总金额
                order.total_amount = current_items_amount
                order.actual_amount_paid = order.total_amount

                # 同步更新订单项状态
                update_order_items_payment_status(db, order_id)
                db.commit()

                logger.info(f"个人账户余额支付成功，金额: {payable_amount}")

                send_order_change_reminders_task(order_id, "已支付")

                return {
                    "message": "支付成功",
                    "status": 200
                }
            except Exception as e:
                db.rollback()
                logger.error(f"个人账户余额支付失败: {str(e)}")
                return {
                    "message": f"支付失败: {str(e)}",
                    "status": 500
                }

        elif payment_method == "biz_enterprise":
            # 企业支付
            enterprise_id = task_info.get("enterprise_id")
            if not enterprise_id:
                logger.error("企业ID为空")
                return {
                    "message": "企业ID不能为空",
                    "status": 400
                }

            # 检查用户与企业是否存在有效的关系
            relation = enterprise_user_relation_dao.get_by_enterprise_and_user(
                db, enterprise_id, user.id
            )

            if not relation or relation.relation_status != Status.ACTIVE:
                logger.error(f"用户与企业不存在有效关系，用户ID: {user.id}，企业ID: {enterprise_id}")
                return {
                    "message": "用户与企业不存在有效关系",
                    "status": 400
                }

            # 执行支付
            try:
                # 获取企业账户
                enterprise_accounts = account_dao.get_by_user_id(db, enterprise_id)
                enterprise_account = next((acc for acc in enterprise_accounts if acc.type == AccountType.REGULAR), None)

                if not enterprise_account:
                    raise ValueError("企业账户不存在")

                if enterprise_account.balance < payable_amount:
                    return {
                        "message": "企业账户余额不足",
                        "status": 400
                    }

                # 更新企业账户余额
                enterprise_account.balance -= payable_amount

                # 创建支付交易记录
                transaction = AccountTransactionCreate(
                    account_id=enterprise_account.id,
                    order_id=order_id,
                    transaction_type=TransactionType.CONSUME,
                    amount=-payable_amount,
                    description=f"商务餐订单修改后差额支付：{order_no}",
                    transaction_time=get_current_time()
                )
                account_transaction_dao.create(db, transaction)

                # 重新获取订单对象，避免使用可能已过期的对象
                order = order_dao.get(db, order_id)
                if not order:
                    db.rollback()
                    logger.error("订单不存在或已被删除")
                    return {
                        "message": "订单不存在或已被删除",
                        "status": 404
                    }

                # 更新订单状态
                order.is_modified = True
                order.last_modified_at = datetime.now()
                order.current_status = "商务餐订单支付，企业账户进行补差额：" + str(payable_amount)

                # 更新订单总金额
                order.total_amount = current_items_amount
                order.actual_amount_paid = order.total_amount

                # 同步更新订单项状态
                update_order_items_payment_status(db, order_id)
                db.commit()

                logger.info(f"企业账户支付成功，金额: {payable_amount}")

                send_order_change_reminders_task(order_id, "已支付")

                return {
                    "message": "支付成功",
                    "status": 200
                }
            except Exception as e:
                db.rollback()
                logger.error(f"企业账户支付失败: {str(e)}")
                return {
                    "message": f"支付失败: {str(e)}",
                    "status": 500
                }
        else:
            logger.error(f"不支持的支付方式: {payment_method}")
            return {
                "message": "不支持的支付方式",
                "status": 400
            }

    except Exception as e:
        logger.error(f"处理商务餐订单支付失败: {str(e)}", exc_info=True)
        return {
            "message": f"处理支付失败: {str(e)}",
            "status": 500
        }


def update_order_items_payment_status(db: Session, order_id: int):
    """
    根据支付金额更新订单项状态
    
    Args:
        db: 数据库会话
        order_id: 订单ID
    """
    try:
        # 获取订单的所有订单项
        order_items = order_item_dao.get_by_order(db, order_id)
        
        # 按应付金额排序，优先支付金额较小的订单项
        unpaid_items = [
            item for item in order_items 
            if item.status == OrderStatus.PENDING or item.status == OrderStatus.PARTIAL_PAID
            and str(item.product_id) not in ['100', '200']  # 排除特殊商品
        ]
        for item in unpaid_items:
            logger.info(f"订单项 {item.id} ，支付状态: {item.status}")
            item.status = OrderStatus.PAID
            item.unpaid_quantity = 0
            logger.info(f"订单项 {item.id} ，支付成功，支付状态: {item.status}")

    except Exception as e:
        logger.error(f"更新订单项支付状态失败: {str(e)}")
        raise
