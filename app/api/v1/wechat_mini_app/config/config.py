# 提供小程序配置API

from fastapi import APIRouter
from app.utils.logger import logger
from app.core.config import settings
from fastapi import Depends

router = APIRouter()


@router.get("/get_config")
async def get_config():
    logger.info("get_config")
    logger.info(settings.RULE_ITEMS_MEAL_TYPE)
    logger.info(settings.PERSONAL_PAYMENT_FIXED_AMOUNT)

    # 如果配置为空，提供默认配置
    default_config = {
        'BREAKFAST': 0, 
        'LUNCH': 1,           # 默认启用午餐
        'AFTERNOON_TEA': 0, 
        'DINNER': 1,          # 默认启用晚餐
        'NIGHT_SNACK': 0
    }
    
    # 如果settings.RULE_ITEMS_MEAL_TYPE为空，使用默认配置
    config = settings.RULE_ITEMS_MEAL_TYPE if settings.RULE_ITEMS_MEAL_TYPE else default_config
    
    return config

@router.get("/get_personal_payment_fixed_amount")
async def get_personal_payment_fixed_amount():
    return settings.PERSONAL_PAYMENT_FIXED_AMOUNT


@router.get("/get_open_enterprise_list")
async def get_open_enterprise_list():
    return settings.OPEN_ENTERPRISE_LIST