# -*- coding: utf-8 -*-
# 优惠券模块

from fastapi import APIRouter, Depends, HTTPException, Header
from typing import Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.service.coupon import coupon_service
from app.dao.order import order_dao
from app.utils.logger import logger
from app.schemas.coupon import CouponPricingRequest, CouponPricingResponse

router = APIRouter()


class GetUserCouponsRequest(BaseModel):
    """获取用户优惠券列表请求模型"""
    order_id: int = Field(..., description="订单ID")


@router.post("/user-available-coupons")
async def get_user_available_coupons(
    request: GetUserCouponsRequest,
    token: Optional[str] = Header(None),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    根据订单和用户获取用户优惠券列表
    
    Args:
        request: 包含订单ID的请求体
        token: 用户认证token
        db: 数据库会话
        
    Returns:
        Dict[str, Any]: 包含三个优惠券列表的响应：
        - available: 有效可用的优惠券列表
        - valid_but_unavailable: 有效但当前不可用的优惠券列表  
        - used: 已经使用的优惠券列表
    """
    try:
        # 验证token
        if not token:
            logger.warning("获取用户优惠券列表：未提供token")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        # 验证用户
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"获取用户优惠券列表：token无效或已过期: {token[:10]}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        # 获取订单信息
        order = order_dao.get(db, request.order_id)
        if not order:
            logger.warning(f"获取用户优惠券列表：订单不存在, order_id: {request.order_id}")
            raise HTTPException(
                status_code=404,
                detail={"message": "订单不存在", "status": 404}
            )

        # 验证订单是否属于当前用户
        if order.user_id != user.id:
            logger.warning(f"获取用户优惠券列表：订单不属于当前用户, order_id: {request.order_id}, user_id: {user.id}, order_user_id: {order.user_id}")
            raise HTTPException(
                status_code=403,
                detail={"message": "无权限访问此订单", "status": 403}
            )

        # 调用优惠券服务获取优惠券列表
        logger.info(f"开始获取用户优惠券列表: user_id={user.id}, order_id={request.order_id}")
        coupon_lists = coupon_service.get_user_available_coupons(db, order, user)

        # 构建响应数据
        response_data = {
            "status": 200,
            "message": "获取成功",
            "data": {
                "available_coupons": coupon_lists["available"],
                "valid_but_unavailable_coupons": coupon_lists["valid_but_unavailable"],
                "used_coupons": coupon_lists["used"],
                "summary": {
                    "available_count": len(coupon_lists["available"]),
                    "valid_but_unavailable_count": len(coupon_lists["valid_but_unavailable"]),
                    "used_count": len(coupon_lists["used"]),
                    "total_count": (
                        len(coupon_lists["available"]) + 
                        len(coupon_lists["valid_but_unavailable"]) + 
                        len(coupon_lists["used"])
                    )
                },
                "order_info": {
                    "order_id": order.id,
                    "order_no": order.order_no,
                    "total_amount": order.total_amount,
                    "payable_amount": order.payable_amount,
                    "status": order.status.value,
                    "payment_status": order.payment_status.value
                }
            }
        }

        logger.info(f"用户优惠券列表获取成功: user_id={user.id}, order_id={request.order_id}, "
                   f"可用={len(coupon_lists['available'])}, "
                   f"不可用={len(coupon_lists['valid_but_unavailable'])}, "
                   f"已使用={len(coupon_lists['used'])}")

        return response_data

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"获取用户优惠券列表失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"message": f"获取优惠券列表失败: {str(e)}", "status": 500}
        )


@router.get("/user-coupons/{order_id}")
async def get_user_coupons_by_order_id(
    order_id: int,
    token: Optional[str] = Header(None),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    根据订单ID获取用户优惠券列表 (GET方式)
    
    Args:
        order_id: 订单ID
        token: 用户认证token
        db: 数据库会话
        
    Returns:
        Dict[str, Any]: 包含三个优惠券列表的响应
    """
    # 构建请求对象并调用POST接口的逻辑
    request = GetUserCouponsRequest(order_id=order_id)
    return await get_user_available_coupons(request, token, db)


@router.get("/user-coupons")
async def get_user_coupons(
    token: Optional[str] = Header(None),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取用户的所有优惠券列表（不依赖订单）
    
    Args:
        token: 用户认证token
        db: 数据库会话
        
    Returns:
        Dict[str, Any]: 包含三个优惠券列表的响应：
        - available: 在有效期内且可使用的优惠券列表
        - expired: 未在有效期内的优惠券列表（包括已过期和未到期的）
        - used: 已经使用的优惠券列表
    """
    try:
        # 验证token
        if not token:
            logger.warning("获取用户优惠券列表：未提供token")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        # 验证用户
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"获取用户优惠券列表：token无效或已过期: {token[:10]}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        # 调用优惠券服务获取优惠券列表
        logger.info(f"开始获取用户优惠券列表: user_id={user.id}")
        coupon_lists = coupon_service.get_user_coupons(db, user)

        # 构建响应数据
        response_data = {
            "status": 200,
            "message": "获取成功",
            "data": {
                "available_coupons": coupon_lists["available"],
                "expired_coupons": coupon_lists["expired"],
                "used_coupons": coupon_lists["used"],
                "summary": {
                    "available_count": len(coupon_lists["available"]),
                    "expired_count": len(coupon_lists["expired"]),
                    "used_count": len(coupon_lists["used"]),
                    "total_count": (
                        len(coupon_lists["available"]) + 
                        len(coupon_lists["expired"]) + 
                        len(coupon_lists["used"])
                    )
                },
                "user_info": {
                    "user_id": user.id,
                    "username": user.username,
                    "phone": user.phone
                }
            }
        }

        logger.info(f"用户优惠券列表获取成功: user_id={user.id}, "
                   f"可用={len(coupon_lists['available'])}, "
                   f"过期/未生效={len(coupon_lists['expired'])}, "
                   f"已使用={len(coupon_lists['used'])}")

        return response_data

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"获取用户优惠券列表失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"message": f"获取优惠券列表失败: {str(e)}", "status": 500}
        )


@router.get("/user-coupons-summary")
async def get_user_coupons_summary(
    token: Optional[str] = Header(None),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取用户优惠券统计概览（不依赖特定订单）
    
    Args:
        token: 用户认证token
        db: 数据库会话
        
    Returns:
        Dict[str, Any]: 用户优惠券统计信息
    """
    try:
        # 验证token
        if not token:
            logger.warning("获取用户优惠券统计：未提供token")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        # 验证用户
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"获取用户优惠券统计：token无效或已过期: {token[:10]}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        # 获取用户的优惠券统计
        from app.dao.coupon import coupon_usage_record_dao
        from app.models.coupon import CouponUsageStatus
        
        # 获取有效优惠券数量
        valid_count = db.query(coupon_usage_record_dao.model).filter(
            coupon_usage_record_dao.model.user_id == user.id,
            coupon_usage_record_dao.model.status == CouponUsageStatus.VALID
        ).count()
        
        # 获取已使用优惠券数量
        used_count = db.query(coupon_usage_record_dao.model).filter(
            coupon_usage_record_dao.model.user_id == user.id,
            coupon_usage_record_dao.model.status == CouponUsageStatus.USED
        ).count()
        
        # 获取过期优惠券数量
        expired_count = db.query(coupon_usage_record_dao.model).filter(
            coupon_usage_record_dao.model.user_id == user.id,
            coupon_usage_record_dao.model.status == CouponUsageStatus.EXPIRED
        ).count()

        response_data = {
            "status": 200,
            "message": "获取成功",
            "data": {
                "summary": {
                    "valid_count": valid_count,
                    "used_count": used_count,
                    "expired_count": expired_count,
                    "total_count": valid_count + used_count + expired_count
                },
                "user_info": {
                    "user_id": user.id,
                    "username": user.username,
                    "phone": user.phone
                }
            }
        }

        logger.info(f"用户优惠券统计获取成功: user_id={user.id}, "
                   f"有效={valid_count}, 已使用={used_count}, 过期={expired_count}")

        return response_data

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"获取用户优惠券统计失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"message": f"获取优惠券统计失败: {str(e)}", "status": 500}
        )


@router.post("/coupon-pricing")
async def coupon_pricing(
    request: CouponPricingRequest,
    token: Optional[str] = Header(None),
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    基于优惠券的计价接口

    根据用户选择的产品和优惠券，计算最终的订单金额和优惠详情

    Args:
        request: 计价请求，包含用户ID、产品组合、优惠券使用记录ID列表
        token: 用户认证token
        db: 数据库会话

    Returns:
        Dict[str, Any]: 计价结果，包含订单信息、优惠信息、优惠券列表等
    """
    try:
        # 验证token
        if not token:
            logger.warning("优惠券计价：未提供token")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        # 验证用户
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"优惠券计价：token无效或已过期: {token[:10]}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        # 验证用户ID是否匹配
        if request.user_id != user.id:
            logger.warning(f"优惠券计价：用户ID不匹配, token_user_id: {user.id}, request_user_id: {request.user_id}")
            raise HTTPException(
                status_code=403,
                detail={"message": "用户ID不匹配", "status": 403}
            )

        # 验证请求参数
        if not request.products:
            raise HTTPException(
                status_code=400,
                detail={"message": "产品列表不能为空", "status": 400}
            )

        # 调用优惠券计价服务
        logger.info(f"开始优惠券计价: user_id={request.user_id}, "
                   f"products_count={len(request.products)}, "
                   f"coupons_count={len(request.coupon_usage_record_ids)}")

        pricing_result = coupon_service.coupon_pricing(
            session=db,
            user_id=request.user_id,
            products=request.products,
            coupon_usage_record_ids=request.coupon_usage_record_ids
        )

        # 构建响应数据
        response_data = {
            "status": 200,
            "message": "计价成功",
            "data": pricing_result
        }

        logger.info(f"优惠券计价成功: user_id={request.user_id}, "
                   f"total_amount={pricing_result['pricing_result']['order']['total_amount']}, "
                   f"payable_amount={pricing_result['pricing_result']['order']['payable_amount']}, "
                   f"total_discount={pricing_result['pricing_result']['discount']['total_discount']}")

        return response_data

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except ValueError as e:
        # 业务逻辑验证失败
        logger.warning(f"优惠券计价验证失败: {str(e)}")
        raise HTTPException(
            status_code=400,
            detail={"message": str(e), "status": 400}
        )
    except Exception as e:
        logger.error(f"优惠券计价失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"message": f"计价失败: {str(e)}", "status": 500}
        )
