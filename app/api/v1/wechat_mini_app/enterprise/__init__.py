from fastapi import APIRouter
from .auth import router as auth_router
from .user import router as user_router
from .account import router as account_router
from .statistic import router as statistic_router

router = APIRouter()

router.include_router(auth_router)
router.include_router(user_router)
router.include_router(account_router, prefix="/account")
router.include_router(statistic_router, prefix="/statistic")

__all__ = ["router"]
