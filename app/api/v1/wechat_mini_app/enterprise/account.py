from typing import Dict, Any, Optional
from datetime import datetime

from fastapi import Depends, HTTPException, Header, APIRouter, Query
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.dao.user import enterprise_user_relation_dao
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.service.account import AccountService

router = APIRouter()
account_service = AccountService()


@router.get("/balance")
async def get_account_balance(
        enterprise_id: int = Query(..., description="企业ID"),
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取企业账户余额
    包括regular_account和gift_account的余额信息
    需要验证当前用户是该企业的管理员
    """
    if not token:
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    user = WeChatUserService.verify_token(db, token)
    if not user:
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    # 验证当前用户是否为该企业的管理员
    relation = enterprise_user_relation_dao.get_by_enterprise_and_user(db, enterprise_id, user.id)
    if not relation or not relation.is_admin:
        raise HTTPException(
            status_code=403,
            detail={"message": "权限不足，您不是该企业的管理员", "status": 403}
        )

    try:
        # 获取企业账户信息
        account_data = account_service.get_enterprise_accounts(db, enterprise_id)
        
        # 提取账户余额信息
        account_info = account_data["data"]["account_info"]
        enterprise_info = account_data["data"]["enterprise_info"]
        
        return {
            "token": token,
            "status": 200,
            "message": "获取成功",
            "data": {
                "enterprise_id": enterprise_id,
                "enterprise_name": enterprise_info.get("company_name"),
                "regular_account": {
                    "balance": account_info["regular_account"]["balance"]
                },
                "gift_account": {
                    "balance": account_info["gift_account"]["balance"],
                    "gift_amount": account_info["gift_account"]["gift_amount"]
                }
            }
        }

    except ValueError as e:
        return {
            "token": token,
            "status": 404,
            "message": str(e),
            "data": None
        }
    except Exception as e:
        return {
            "token": token,
            "status": 500,
            "message": f"获取账户余额失败：{str(e)}",
            "data": None
        }


@router.get("/transactions")
async def search_account_transactions(
        enterprise_id: int = Query(..., description="企业ID"),
        start_time: Optional[str] = Query(None, description="开始时间，格式：YYYY-MM-DD HH:MM:SS"),
        end_time: Optional[str] = Query(None, description="结束时间，格式：YYYY-MM-DD HH:MM:SS"),
        skip: int = Query(0, description="跳过条数"),
        limit: int = Query(20, description="返回条数"),
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    搜索企业账户交易记录
    根据时间范围获取企业所有账户的交易记录列表
    需要验证当前用户是该企业的管理员
    """
    if not token:
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    user = WeChatUserService.verify_token(db, token)
    if not user:
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    # 验证当前用户是否为该企业的管理员
    relation = enterprise_user_relation_dao.get_by_enterprise_and_user(db, enterprise_id, user.id)
    if not relation or not relation.is_admin:
        raise HTTPException(
            status_code=403,
            detail={"message": "权限不足，您不是该企业的管理员", "status": 403}
        )

    try:
        # 解析时间参数
        start_datetime = None
        end_datetime = None
        
        if start_time:
            try:
                start_datetime = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                try:
                    # 如果只有日期，默认从当天00:00:00开始
                    start_datetime = datetime.strptime(start_time, "%Y-%m-%d")
                except ValueError:
                    return {
                        "token": token,
                        "status": 400,
                        "message": "开始时间格式错误，请使用 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS 格式",
                        "data": None
                    }
        
        if end_time:
            try:
                end_datetime = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                try:
                    # 如果只有日期，默认到当天23:59:59结束
                    end_datetime = datetime.strptime(end_time + " 23:59:59", "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    return {
                        "token": token,
                        "status": 400,
                        "message": "结束时间格式错误，请使用 YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS 格式",
                        "data": None
                    }

        # 验证时间范围
        if start_datetime and end_datetime and start_datetime > end_datetime:
            return {
                "token": token,
                "status": 400,
                "message": "开始时间不能晚于结束时间",
                "data": None
            }

        # 获取企业账户交易记录
        result = account_service.search_enterprise_transactions(
            db,
            enterprise_id=enterprise_id,
            start_time=start_datetime,
            end_time=end_datetime,
            skip=skip,
            limit=limit
        )
        
        # 格式化交易时间
        transaction_list = result["data"]["list"]
        for transaction in transaction_list:
            if transaction.get("transaction_time"):
                transaction["transaction_time"] = transaction["transaction_time"].strftime("%Y-%m-%d %H:%M:%S")
        
        return {
            "token": token,
            "status": 200,
            "message": "获取成功",
            "data": {
                "total": result["data"]["total"],
                "transactions": transaction_list
            }
        }

    except ValueError as e:
        return {
            "token": token,
            "status": 404,
            "message": str(e),
            "data": None
        }
    except Exception as e:
        return {
            "token": token,
            "status": 500,
            "message": f"获取交易记录失败：{str(e)}",
            "data": None
        }

