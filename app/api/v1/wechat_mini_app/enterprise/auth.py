from typing import Dict, Any, Optional

from fastapi import Depends, HTTPException, <PERSON><PERSON>, APIRouter, Query
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.dao.user import enterprise_user_relation_dao
from app.service.wechat_miniapp.wx_user import WeChatUserService

router = APIRouter()


@router.get("/is_enterprise_admin")
async def is_enterprise_admin(token: Optional[str] = Header(None), db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    获取用户信息
    """
    if not token:
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    user = WeChatUserService.verify_token(db, token)
    if not user:
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    # user_info = WeChatUserService.get_user_info(db, user.wechat_id)

    rels = enterprise_user_relation_dao.get_list_by_personal_user_id(db, user.id)
    if not rels:
        return {
            "token": token,
            "status": 200,
            "message": "获取成功",
            "data": []
        }
    managed_enterprise_list = []
    for rel in rels:
        if rel.is_admin:
            managed_enterprise_list.append(
                {
                    "enterprise_id": rel.enterprise_id,
                    "enterprise_name": rel.enterprise.company_name
                }
            )

    return {
        "token": token,
        "status": 200,
        "message": "获取成功",
        "data": managed_enterprise_list
    }
