# 提供小程序企业统计报表模块API

from datetime import datetime
from typing import Dict, Any, Optional, List

from fastapi import Depends, HTTPException, Header, APIRouter, Query
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.dao.user import enterprise_user_relation_dao, enterprise_dao
from app.schemas.reservation import (
    ReservationOrderReportItem,
    ReservationReportRequest, ReservationReportItem
)
from app.service.report import report_service
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.utils.logger import logger

router = APIRouter()


def verify_enterprise_admin_permission(token: str, enterprise_id: int, db: Session) -> tuple[bool, List[int]]:
    """
    验证用户是否为指定企业的管理员，并返回该企业的所有用户ID列表
    
    Args:
        token: 用户token
        enterprise_id: 企业ID
        db: 数据库会话
        
    Returns:
        tuple[bool, List[int]]: (是否有管理员权限, 企业用户ID列表)
    """
    if not token:
        return False, []

    # 验证token
    user = WeChatUserService.verify_token(db, token)
    if not user:
        return False, []

    # 验证当前用户是否为该企业的管理员
    relation = enterprise_user_relation_dao.get_by_enterprise_and_user(db, enterprise_id, user.id)
    if not relation or not relation.is_admin:
        return False, []

    # 获取企业下所有用户的ID列表
    enterprise_users = enterprise_user_relation_dao.get_users_by_enterprise(db, enterprise_id, skip=0, limit=10000)
    user_ids = [user.id for user in enterprise_users]
    
    return True, user_ids


@router.post("/reservation_report")
async def get_reservation_report_for_enterprise(
        enterprise_id: int = Query(..., description="企业ID"),
        request: Optional[ReservationReportRequest] = None,
        token: Optional[str] = Header(None),
        phone: Optional[str] = Query(None, description="手机号"),
        real_name: Optional[str] = Query(None, description="姓名"),
        nick_name: Optional[str] = Query(None, description="微信昵称"),
        status: Optional[List[str]] = Query(None, description="状态，可以多选"),
        dining_start_time: Optional[str] = Query(None, description="就餐开始时间 (YYYY-MM-DD HH:MM:SS)"),
        dining_end_time: Optional[str] = Query(None, description="就餐结束时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_start_time: Optional[str] = Query(None, description="预订开始时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_end_time: Optional[str] = Query(None, description="预订结束时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_period: Optional[str] = Query(None, description="预订时段"),
        payment_enterprise: Optional[str] = Query(None, description="支付企业"),
        page: Optional[int] = Query(1, description="页码"),
        page_size: Optional[int] = Query(None, description="每页记录数"),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    企业管理员获取预订报表数据

    需要企业管理员权限，只返回该企业作为支付企业的预订记录

    查询条件：
    - 预订时间范围
    - 预订时段
    - 手机号
    - 姓名
    - 微信昵称
    - 状态
    - 支付企业（自动设置为当前企业）
    - 分页参数

    返回结果包含：
    "用户真名、手机号、产品、份数、费用、预订状态、预订时段、预订时间"
    """
    # 验证企业管理员权限并获取企业用户ID列表
    has_permission, user_ids = verify_enterprise_admin_permission(token, enterprise_id, db)
    if not has_permission:
        logger.warning(f"用户无企业管理员权限或token无效，企业ID: {enterprise_id}")
        raise HTTPException(
            status_code=403,
            detail={"message": "无权限访问", "status": 403}
        )

    # 获取企业信息，用于限定支付企业
    enterprise = enterprise_dao.get(db, enterprise_id)
    if not enterprise:
        logger.warning(f"企业不存在，企业ID: {enterprise_id}")
        raise HTTPException(
            status_code=404,
            detail={"message": "企业不存在", "status": 404}
        )

    params = {}

    # 首先处理request body中的参数
    if request:
        request_params = request.model_dump(exclude_none=True)
        logger.info(f"从request body获取参数: {request_params}")
        params.update(request_params)

    # 处理时间参数
    dining_start_time_to_use = dining_start_time if dining_start_time else None
    if not dining_start_time_to_use and 'dining_start_time' in params:
        # 如果query中没有，但body中有，则使用body中的值
        dining_start_time_to_use = params['dining_start_time'].strftime("%Y-%m-%d %H:%M:%S") if isinstance(
            params['dining_start_time'], datetime) else str(params['dining_start_time'])
        logger.info(f"使用body中的dining_start_time: {dining_start_time_to_use}")

    if dining_start_time_to_use:
        try:
            if isinstance(dining_start_time_to_use, str):
                params['dining_start_time'] = datetime.strptime(dining_start_time_to_use, "%Y-%m-%d %H:%M:%S")
            else:
                params['dining_start_time'] = dining_start_time_to_use
            logger.info(f"解析就餐开始时间成功: {params['dining_start_time']}")
        except ValueError:
            logger.error(f"就餐开始时间格式错误: {dining_start_time_to_use}")
            raise HTTPException(
                status_code=400,
                detail={"message": "无效的就餐开始时间格式，请使用 YYYY-MM-DD HH:MM:SS", "status": 400}
            )

    dining_end_time_to_use = dining_end_time if dining_end_time else None
    if not dining_end_time_to_use and 'dining_end_time' in params:
        dining_end_time_to_use = params['dining_end_time'].strftime("%Y-%m-%d %H:%M:%S") if isinstance(
            params['dining_end_time'], datetime) else str(params['dining_end_time'])
        logger.info(f"使用body中的dining_end_time: {dining_end_time_to_use}")

    if dining_end_time_to_use:
        try:
            if isinstance(dining_end_time_to_use, str):
                params['dining_end_time'] = datetime.strptime(dining_end_time_to_use, "%Y-%m-%d %H:%M:%S")
            else:
                params['dining_end_time'] = dining_end_time_to_use
            logger.info(f"解析就餐结束时间成功: {params['dining_end_time']}")
        except ValueError:
            logger.error(f"就餐结束时间格式错误: {dining_end_time_to_use}")
            raise HTTPException(
                status_code=400,
                detail={"message": "无效的就餐结束时间格式，请使用 YYYY-MM-DD HH:MM:SS", "status": 400}
            )

    reservation_start_time_to_use = reservation_start_time if reservation_start_time else None
    if not reservation_start_time_to_use and 'reservation_start_time' in params:
        reservation_start_time_to_use = params['reservation_start_time'].strftime("%Y-%m-%d %H:%M:%S") if isinstance(
            params['reservation_start_time'], datetime) else str(params['reservation_start_time'])
        logger.info(f"使用body中的reservation_start_time: {reservation_start_time_to_use}")

    if reservation_start_time_to_use:
        try:
            if isinstance(reservation_start_time_to_use, str):
                params['reservation_start_time'] = datetime.strptime(reservation_start_time_to_use, "%Y-%m-%d %H:%M:%S")
            else:
                params['reservation_start_time'] = reservation_start_time_to_use
            logger.info(f"解析预订开始时间成功: {params['reservation_start_time']}")
        except ValueError:
            logger.error(f"预订开始时间格式错误: {reservation_start_time_to_use}")
            raise HTTPException(
                status_code=400,
                detail={"message": "无效的预订开始时间格式，请使用 YYYY-MM-DD HH:MM:SS", "status": 400}
            )

    reservation_end_time_to_use = reservation_end_time if reservation_end_time else None
    if not reservation_end_time_to_use and 'reservation_end_time' in params:
        reservation_end_time_to_use = params['reservation_end_time'].strftime("%Y-%m-%d %H:%M:%S") if isinstance(
            params['reservation_end_time'], datetime) else str(params['reservation_end_time'])
        logger.info(f"使用body中的reservation_end_time: {reservation_end_time_to_use}")

    if reservation_end_time_to_use:
        try:
            if isinstance(reservation_end_time_to_use, str):
                params['reservation_end_time'] = datetime.strptime(reservation_end_time_to_use, "%Y-%m-%d %H:%M:%S")
            else:
                params['reservation_end_time'] = reservation_end_time_to_use
            logger.info(f"解析预订结束时间成功: {params['reservation_end_time']}")
        except ValueError:
            logger.error(f"预订结束时间格式错误: {reservation_end_time_to_use}")
            raise HTTPException(
                status_code=400,
                detail={"message": "无效的预订结束时间格式，请使用 YYYY-MM-DD HH:MM:SS", "status": 400}
            )

    # 处理其他参数（query参数优先级高于body参数）
    def set_param_with_priority(param_name, query_value, log_name):
        if query_value is not None:
            params[param_name] = query_value
            logger.info(f"设置{log_name}(来自query): {query_value}")
        elif param_name in params:
            logger.info(f"使用{log_name}(来自body): {params[param_name]}")
        # 如果都没有，params中也不会有这个key

    set_param_with_priority('reservation_period', reservation_period, '预订时段')
    set_param_with_priority('phone', phone, '手机号')
    set_param_with_priority('real_name', real_name, '真实姓名')
    set_param_with_priority('nick_name', nick_name, '微信昵称')
    set_param_with_priority('status', status, '状态')
    set_param_with_priority('page', page, '页码')
    set_param_with_priority('page_size', page_size, '每页大小')

    if params["page_size"] is None:
        params["page_size"] = 10

    # 强制设置支付企业为当前企业，忽略传入的payment_enterprise参数
    params["payment_enterprise"] = enterprise.company_name
    logger.info(f"设置支付企业为当前企业: {enterprise.company_name}")

    # 移除用户ID列表过滤参数，改为通过支付企业过滤
    # params["user_ids"] = user_ids

    try:
        # 使用原有的预订报表服务，通过支付企业过滤
        reservation_list, total_count = report_service.get_reservation_report_raw(
            db=db,
            **params
        )

        data = {
            "list": [ReservationReportItem(**item) for item in reservation_list],
            "total": total_count,
        }

        logger.info(f"企业统计-预订报表查询成功，企业ID: {enterprise_id}，返回 {len(data['list'])} 条记录")

        response = {
            "status": 200,
            "message": "获取成功",
            "data": data
        }

        return response

    except Exception as e:
        import traceback
        logger.error(f"企业统计-预订报表查询失败，企业ID: {enterprise_id}，详细错误信息: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail={"message": "查询失败", "status": 500}
        )


@router.post("/reservation_order_report")
async def get_reservation_order_report_for_enterprise(
        enterprise_id: int = Query(..., description="企业ID"),
        request: Optional[ReservationReportRequest] = None,
        token: Optional[str] = Header(None),
        phone: Optional[str] = Query(None, description="手机号"),
        real_name: Optional[str] = Query(None, description="姓名"),
        nick_name: Optional[str] = Query(None, description="微信昵称"),
        status: Optional[List[str]] = Query(None, description="状态，可以多选"),
        dining_start_time: Optional[str] = Query(None, description="就餐开始时间 (YYYY-MM-DD HH:MM:SS)"),
        dining_end_time: Optional[str] = Query(None, description="就餐结束时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_start_time: Optional[str] = Query(None, description="预订开始时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_end_time: Optional[str] = Query(None, description="预订结束时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_period: Optional[str] = Query(None, description="预订时段"),
        payment_enterprise: Optional[str] = Query(None, description="支付企业"),
        page: Optional[int] = Query(1, description="页码"),
        page_size: Optional[int] = Query(None, description="每页记录数"),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    企业管理员获取预订订单报表数据，关联biz_reservation_requests和orders表
    
    需要企业管理员权限，只返回该企业作为支付企业的预订记录
    
    查询条件：
    - 就餐时间范围
    - 预订时间范围
    - 预订时段
    - 手机号
    - 姓名
    - 微信昵称
    - 状态
    - 支付企业（自动设置为当前企业）
    - 分页参数
    
    返回结果包含：
    - 预订信息（reservation_requests + biz_reservation_requests）
    - 订单信息（orders）
    - 订单项信息（order_items）
    - 用户信息
    """
    logger.info(f"企业管理员请求预订订单报表，企业ID: {enterprise_id}，token: {token[:10] if token else 'None'}...")

    # 验证企业管理员权限并获取企业用户ID列表
    has_permission, user_ids = verify_enterprise_admin_permission(token, enterprise_id, db)
    if not has_permission:
        logger.warning(f"用户无企业管理员权限或token无效，企业ID: {enterprise_id}")
        raise HTTPException(
            status_code=403,
            detail={"message": "无权限访问", "status": 403}
        )

    # 获取企业信息，用于限定支付企业
    enterprise = enterprise_dao.get(db, enterprise_id)
    if not enterprise:
        logger.warning(f"企业不存在，企业ID: {enterprise_id}")
        raise HTTPException(
            status_code=404,
            detail={"message": "企业不存在", "status": 404}
        )

    # 处理参数
    params = {}

    if request:
        request_params = request.model_dump(exclude_none=True)
        logger.info(f"从request body获取参数: {request_params}")
        params.update(request_params)

    if dining_start_time:
        try:
            params['dining_start_time'] = datetime.strptime(dining_start_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail={"message": "无效的就餐开始时间格式，请使用 YYYY-MM-DD HH:MM:SS", "status": 400}
            )

    if dining_end_time:
        try:
            params['dining_end_time'] = datetime.strptime(dining_end_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail={"message": "无效的就餐结束时间格式，请使用 YYYY-MM-DD HH:MM:SS", "status": 400}
            )

    if reservation_start_time:
        try:
            params['reservation_start_time'] = datetime.strptime(reservation_start_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail={"message": "无效的预订开始时间格式，请使用 YYYY-MM-DD HH:MM:SS", "status": 400}
            )

    if reservation_end_time:
        try:
            params['reservation_end_time'] = datetime.strptime(reservation_end_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail={"message": "无效的预订结束时间格式，请使用 YYYY-MM-DD HH:MM:SS", "status": 400}
            )

    if reservation_period:
        params['reservation_period'] = reservation_period
    if phone:
        params['phone'] = phone
    if real_name:
        params['real_name'] = real_name
    if nick_name:
        params['nick_name'] = nick_name
    if status:
        params['status'] = status
    if page:
        params['page'] = page
    if page_size:
        params['page_size'] = page_size
    else:
        params["page_size"] = 10

    # 强制设置支付企业为当前企业，忽略传入的payment_enterprise参数
    params["payment_enterprise"] = enterprise.company_name
    logger.info(f"设置支付企业为当前企业: {enterprise.company_name}")

    # 移除用户ID列表过滤参数，改为通过支付企业过滤
    # params["user_ids"] = user_ids

    try:
        # 使用新的预订订单报表服务，通过支付企业过滤
        reservation_list, total_count = report_service.get_reservation_order_report_raw(
            db=db,
            **params
        )

        data = {
            "list": [ReservationOrderReportItem(**item) for item in reservation_list],
            "total": total_count,
        }

        logger.info(f"企业管理员预订订单报表查询成功，企业ID: {enterprise_id}，返回 {len(reservation_list)} 条记录")

        return {
            "status": 200,
            "message": "获取成功",
            "data": data
        }

    except Exception as e:
        logger.error(f"企业管理员预订订单报表查询失败，企业ID: {enterprise_id}，错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"message": "查询失败", "status": 500}
        )


@router.post("/reservation_report/download_excel")
async def download_reservation_report_excel_for_enterprise(
        enterprise_id: int = Query(..., description="企业ID"),
        request: Optional[ReservationReportRequest] = None,
        token: Optional[str] = Header(None),
        phone: Optional[str] = Query(None, description="手机号"),
        real_name: Optional[str] = Query(None, description="姓名"),
        nick_name: Optional[str] = Query(None, description="微信昵称"),
        status: Optional[List[str]] = Query(None, description="状态，可以多选"),
        dining_start_time: Optional[str] = Query(None, description="就餐开始时间 (YYYY-MM-DD HH:MM:SS)"),
        dining_end_time: Optional[str] = Query(None, description="就餐结束时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_start_time: Optional[str] = Query(None, description="预订开始时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_end_time: Optional[str] = Query(None, description="预订结束时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_period: Optional[str] = Query(None, description="预订时段"),
        db: Session = Depends(get_db)
):
    """
    企业管理员下载预订报表Excel文件

    需要企业管理员权限，只包含该企业作为支付企业的预订记录

    查询条件：
    - 预订时间范围
    - 预订时段
    - 手机号
    - 姓名
    - 微信昵称
    - 状态
    - 支付企业（自动设置为当前企业）

    返回Excel文件
    """
    logger.info(f"企业管理员请求下载预订报表Excel，企业ID: {enterprise_id}，token: {token[:10] if token else 'None'}...")

    # 验证企业管理员权限并获取企业用户ID列表
    has_permission, user_ids = verify_enterprise_admin_permission(token, enterprise_id, db)
    if not has_permission:
        logger.warning(f"用户无企业管理员权限或token无效，企业ID: {enterprise_id}")
        raise HTTPException(
            status_code=403,
            detail={"message": "无权限访问", "status": 403}
        )

    # 获取企业信息，用于限定支付企业
    enterprise = enterprise_dao.get(db, enterprise_id)
    if not enterprise:
        logger.warning(f"企业不存在，企业ID: {enterprise_id}")
        raise HTTPException(
            status_code=404,
            detail={"message": "企业不存在", "status": 404}
        )

    # 处理参数
    params = {}
    # 首先处理request body中的参数
    if request:
        request_params = request.model_dump(exclude_none=True)
        logger.info(f"从request body获取参数: {request_params}")
        params.update(request_params)

    if dining_start_time:
        try:
            params['dining_start_time'] = datetime.strptime(dining_start_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail={"message": "无效的就餐开始时间格式，请使用 YYYY-MM-DD HH:MM:SS", "status": 400}
            )

    if dining_end_time:
        try:
            params['dining_end_time'] = datetime.strptime(dining_end_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail={"message": "无效的就餐结束时间格式，请使用 YYYY-MM-DD HH:MM:SS", "status": 400}
            )

    if reservation_start_time:
        try:
            params['reservation_start_time'] = datetime.strptime(reservation_start_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail={"message": "无效的预订开始时间格式，请使用 YYYY-MM-DD HH:MM:SS", "status": 400}
            )

    if reservation_end_time:
        try:
            params['reservation_end_time'] = datetime.strptime(reservation_end_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail={"message": "无效的预订结束时间格式，请使用 YYYY-MM-DD HH:MM:SS", "status": 400}
            )

    if reservation_period:
        params['reservation_period'] = reservation_period
    if phone:
        params['phone'] = phone
    if real_name:
        params['real_name'] = real_name
    if nick_name:
        params['nick_name'] = nick_name
    if status:
        params['status'] = status

    # 强制设置支付企业为当前企业，忽略传入的payment_enterprise参数
    params["payment_enterprise"] = enterprise.company_name
    logger.info(f"设置支付企业为当前企业: {enterprise.company_name}")

    # 移除用户ID列表过滤参数，改为通过支付企业过滤
    # params["user_ids"] = user_ids

    try:
        # 生成Excel文件，通过支付企业过滤
        excel_file = report_service.get_reservation_report_excel(
            db=db,
            **params
        )

        # 设置文件名，包含当前时间
        current_time = datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f"enterprise_reservation_report_{enterprise_id}_{current_time}.xlsx"

        logger.info(f"企业管理员预订报表Excel生成成功，企业ID: {enterprise_id}，文件名: {filename}")

        # 返回文件流
        return StreamingResponse(
            excel_file,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except Exception as e:
        logger.error(f"企业管理员预订报表Excel生成失败，企业ID: {enterprise_id}，错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"message": "Excel生成失败", "status": 500}
        )


@router.post("/reservation_order_report/download_excel")
async def download_reservation_order_report_excel_for_enterprise(
        enterprise_id: int = Query(..., description="企业ID"),
        request: Optional[ReservationReportRequest] = None,
        token: Optional[str] = Header(None),
        phone: Optional[str] = Query(None, description="手机号"),
        real_name: Optional[str] = Query(None, description="姓名"),
        nick_name: Optional[str] = Query(None, description="微信昵称"),
        status: Optional[List[str]] = Query(None, description="状态，可以多选"),
        dining_start_time: Optional[str] = Query(None, description="就餐开始时间 (YYYY-MM-DD HH:MM:SS)"),
        dining_end_time: Optional[str] = Query(None, description="就餐结束时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_start_time: Optional[str] = Query(None, description="预订开始时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_end_time: Optional[str] = Query(None, description="预订结束时间 (YYYY-MM-DD HH:MM:SS)"),
        reservation_period: Optional[str] = Query(None, description="预订时段"),
        db: Session = Depends(get_db)
):
    """
    企业管理员下载预订订单报表Excel文件，使用合并单元格显示预订信息和订单项

    需要企业管理员权限，只包含该企业用户的预订记录

    查询条件：
    - 就餐时间范围
    - 预订时间范围
    - 预订时段
    - 手机号
    - 姓名
    - 微信昵称
    - 状态
    - 支付企业

    返回Excel文件，包含：
    - 预订信息（合并单元格显示）
    - 订单信息（合并单元格显示）
    - 订单项详情（每行一个订单项）
    """
    logger.info(f"企业管理员请求下载预订订单报表Excel，企业ID: {enterprise_id}，token: {token[:10] if token else 'None'}...")

    # 验证企业管理员权限并获取企业用户ID列表
    has_permission, user_ids = verify_enterprise_admin_permission(token, enterprise_id, db)
    if not has_permission:
        logger.warning(f"用户无企业管理员权限或token无效，企业ID: {enterprise_id}")
        raise HTTPException(
            status_code=403,
            detail={"message": "无权限访问", "status": 403}
        )

    if not user_ids:
        logger.info(f"企业 {enterprise_id} 没有关联用户")
        # 即使没有数据也要返回空的Excel文件
        user_ids = []

    # 处理参数
    params = {}
    # 首先处理request body中的参数
    if request:
        request_params = request.model_dump(exclude_none=True)
        logger.info(f"从request body获取参数: {request_params}")
        params.update(request_params)

    if dining_start_time:
        try:
            params['dining_start_time'] = datetime.strptime(dining_start_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail={"message": "无效的就餐开始时间格式，请使用 YYYY-MM-DD HH:MM:SS", "status": 400}
            )

    if dining_end_time:
        try:
            params['dining_end_time'] = datetime.strptime(dining_end_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail={"message": "无效的就餐结束时间格式，请使用 YYYY-MM-DD HH:MM:SS", "status": 400}
            )

    if reservation_start_time:
        try:
            params['reservation_start_time'] = datetime.strptime(reservation_start_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail={"message": "无效的预订开始时间格式，请使用 YYYY-MM-DD HH:MM:SS", "status": 400}
            )

    if reservation_end_time:
        try:
            params['reservation_end_time'] = datetime.strptime(reservation_end_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail={"message": "无效的预订结束时间格式，请使用 YYYY-MM-DD HH:MM:SS", "status": 400}
            )

    if reservation_period:
        params['reservation_period'] = reservation_period
    if phone:
        params['phone'] = phone
    if real_name:
        params['real_name'] = real_name
    if nick_name:
        params['nick_name'] = nick_name
    if status:
        params['status'] = status

    # 强制设置支付企业为当前企业，忽略传入的payment_enterprise参数
    params["payment_enterprise"] = enterprise.company_name
    logger.info(f"设置支付企业为当前企业: {enterprise.company_name}")

    # 移除用户ID列表过滤参数，改为通过支付企业过滤
    # params["user_ids"] = user_ids

    try:
        # 生成Excel文件，通过支付企业过滤
        excel_file = report_service.get_reservation_order_report_excel(
            db=db,
            **params
        )

        # 设置文件名，包含当前时间
        current_time = datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f"enterprise_reservation_order_report_{enterprise_id}_{current_time}.xlsx"

        logger.info(f"企业管理员预订订单报表Excel生成成功，企业ID: {enterprise_id}，文件名: {filename}")

        # 返回文件流
        return StreamingResponse(
            excel_file,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except Exception as e:
        logger.error(f"企业管理员预订订单报表Excel生成失败，企业ID: {enterprise_id}，错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"message": "Excel生成失败", "status": 500}
        ) 