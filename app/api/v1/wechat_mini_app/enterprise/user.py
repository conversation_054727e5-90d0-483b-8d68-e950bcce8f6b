from typing import Dict, Any, Optional

from fastapi import Depends, HTTPException, Header, APIRouter, Query
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.dao.user import enterprise_user_relation_dao, personal_user_dao
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.schemas.user import PersonalUserCreate, EnterpriseUserRelationCreate
from app.models.enum import Status

router = APIRouter()


@router.get("/users/search")
async def search_users(
        enterprise_id: int = Query(..., description="企业ID"),
        keyword: Optional[str] = Query(None, description="搜索关键字，可搜索用户名、手机号、真实姓名等"),
        phone: Optional[str] = Query(None, description="手机号"),
        username: Optional[str] = Query(None, description="用户名"),
        skip: int = Query(0, description="跳过条数"),
        limit: int = Query(20, description="返回条数"),
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    搜索企业下的用户信息
    需要验证当前用户是该企业的管理员
    """
    if not token:
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    user = WeChatUserService.verify_token(db, token)
    if not user:
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    # 验证当前用户是否为该企业的管理员
    relation = enterprise_user_relation_dao.get_by_enterprise_and_user(db, enterprise_id, user.id)
    if not relation or not relation.is_admin:
        raise HTTPException(
            status_code=403,
            detail={"message": "权限不足，您不是该企业的管理员", "status": 403}
        )

    # 搜索企业下的用户
    result = enterprise_user_relation_dao.search_users_by_enterprise(
        db,
        enterprise_id=enterprise_id,
        keyword=keyword,
        phone=phone,
        username=username,
        skip=skip,
        limit=limit
    )

    return {
        "token": token,
        "status": 200,
        "message": "获取成功",
        "data": {
            "total": result["total"],
            "users": result["list"]
        }
    }


@router.post("/users/add")
async def add_user(
        enterprise_id: int = Query(..., description="企业ID"),
        username: str = Query(..., description="用户真实姓名"),
        phone: str = Query(..., description="手机号"),
        is_admin: bool = Query(False, description="是否为管理员"),
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    为企业添加用户
    需要验证当前操作用户是该企业的管理员
    """
    if not token:
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    # 验证当前操作用户
    current_user = WeChatUserService.verify_token(db, token)
    if not current_user:
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    # 验证当前用户是否为该企业的管理员
    relation = enterprise_user_relation_dao.get_by_enterprise_and_user(db, enterprise_id, current_user.id)
    if not relation or not relation.is_admin:
        raise HTTPException(
            status_code=403,
            detail={"message": "权限不足，您不是该企业的管理员", "status": 403}
        )

    try:
        # 检查待创建用户是否已经存在（通过手机号检查）
        existing_user = personal_user_dao.get_by_phone(db, phone)
        
        if existing_user:
            # 用户已存在，检查是否已经与该企业有关联
            existing_relation = enterprise_user_relation_dao.get_by_enterprise_and_user(
                db, enterprise_id, existing_user.id
            )
            if existing_relation:
                return {
                    "token": token,
                    "status": 400,
                    "message": "该用户已经是企业成员",
                    "data": {
                        "user_id": existing_user.id,
                        "username": existing_user.real_name,
                        "phone": existing_user.phone
                    }
                }
            
            # 用户存在但与企业无关联，创建关联关系
            relation_data = EnterpriseUserRelationCreate(
                enterprise_id=enterprise_id,
                personal_user_id=existing_user.id,
                is_admin=is_admin,
                relation_status=Status.ACTIVE
            )
            new_relation = enterprise_user_relation_dao.create(db, relation_data)
            
            return {
                "token": token,
                "status": 200,
                "message": "用户已添加到企业",
                "data": {
                    "user_id": existing_user.id,
                    "username": existing_user.real_name,
                    "phone": existing_user.phone,
                    "is_admin": new_relation.is_admin,
                    "is_new_user": False
                }
            }
        else:
            # 用户不存在，创建新的个人用户
            # 注意：username参数对应PersonalUser的real_name，phone对应username和phone
            user_create_data = PersonalUserCreate(
                username=phone,  # PersonalUser的username字段用手机号
                phone=phone,     # PersonalUser的phone字段
                real_name=username,  # PersonalUser的real_name字段用传入的username参数
                status=Status.ACTIVE
            )
            
            new_user = personal_user_dao.create(db, user_create_data)
            
            # 创建企业用户关联关系
            relation_data = EnterpriseUserRelationCreate(
                enterprise_id=enterprise_id,
                personal_user_id=new_user.id,
                is_admin=is_admin,
                relation_status=Status.ACTIVE
            )
            new_relation = enterprise_user_relation_dao.create(db, relation_data)
            
            return {
                "token": token,
                "status": 200,
                "message": "用户创建成功并已添加到企业",
                "data": {
                    "user_id": new_user.id,
                    "username": new_user.real_name,
                    "phone": new_user.phone,
                    "is_admin": new_relation.is_admin,
                    "is_new_user": True
                }
            }
            
    except Exception as e:
        db.rollback()
        return {
            "token": token,
            "status": 500,
            "message": f"操作失败：{str(e)}",
            "data": None
        }


@router.put("/users/update")
async def update_user(
        enterprise_id: int = Query(..., description="企业ID"),
        user_id: int = Query(..., description="待更新的用户ID"),
        real_name: Optional[str] = Query(None, description="用户真实姓名"),
        is_admin: Optional[bool] = Query(None, description="是否为管理员"),
        status: Optional[Status] = Query(None, description="用户状态(1/0)"),
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    更新企业用户信息
    只允许更新real_name、is_admin、status字段
    需要验证当前操作用户是该企业的管理员
    """
    if not token:
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    # 验证当前操作用户
    current_user = WeChatUserService.verify_token(db, token)
    if not current_user:
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    # 验证当前用户是否为该企业的管理员
    current_relation = enterprise_user_relation_dao.get_by_enterprise_and_user(db, enterprise_id, current_user.id)
    if not current_relation or not current_relation.is_admin:
        raise HTTPException(
            status_code=403,
            detail={"message": "权限不足，您不是该企业的管理员", "status": 403}
        )

    try:
        # 检查待更新的用户是否存在
        target_user = personal_user_dao.get(db, user_id)
        if not target_user:
            return {
                "token": token,
                "status": 404,
                "message": "用户不存在",
                "data": None
            }

        # 检查待更新用户是否为该企业成员
        target_relation = enterprise_user_relation_dao.get_by_enterprise_and_user(db, enterprise_id, user_id)
        if not target_relation:
            return {
                "token": token,
                "status": 404,
                "message": "该用户不是企业成员",
                "data": None
            }

        # 构建更新数据
        update_data = {}
        relation_update_data = {}

        # 更新real_name（个人用户字段）
        if real_name is not None:
            from app.schemas.user import PersonalUserUpdate
            user_update = PersonalUserUpdate(real_name=real_name)
            updated_user = personal_user_dao.update(db, user_id, user_update)
            if not updated_user:
                raise Exception("更新用户real_name失败")

        # 更新is_admin（企业关联关系字段）
        if is_admin is not None:
            relation_update_data["is_admin"] = is_admin

        # 更新status（企业关联关系字段）
        if status is not None:
            relation_update_data["relation_status"] = status

        # 更新企业关联关系
        if relation_update_data:
            from app.schemas.user import EnterpriseUserRelationUpdate
            relation_update = EnterpriseUserRelationUpdate(**relation_update_data)
            updated_relation = enterprise_user_relation_dao.update(db, target_relation.id, relation_update)
            if not updated_relation:
                raise Exception("更新企业关联关系失败")

        # 获取更新后的用户信息
        updated_user = personal_user_dao.get(db, user_id)
        updated_relation = enterprise_user_relation_dao.get_by_enterprise_and_user(db, enterprise_id, user_id)

        return {
            "token": token,
            "status": 200,
            "message": "用户信息更新成功",
            "data": {
                "user_id": updated_user.id,
                "username": updated_user.real_name,
                "phone": updated_user.phone,
                "is_admin": updated_relation.is_admin if updated_relation else False,
                "status": updated_relation.relation_status.value if updated_relation and hasattr(updated_relation.relation_status, 'value') else updated_relation.relation_status if updated_relation else None
            }
        }

    except Exception as e:
        db.rollback()
        return {
            "token": token,
            "status": 500,
            "message": f"更新失败：{str(e)}",
            "data": None
        }


@router.post("/update/status")
async def update_status(
        enterprise_id: int = Query(..., description="企业ID"),
        user_ids: list[int] = Query(..., description="用户ID列表"),
        status: Status = Query(..., description="用户状态(1/0)"),
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    批量更新企业用户状态
    需要验证当前操作用户是该企业的管理员
    """
    if not token:
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    # 验证当前操作用户
    current_user = WeChatUserService.verify_token(db, token)
    if not current_user:
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    # 验证当前用户是否为该企业的管理员
    current_relation = enterprise_user_relation_dao.get_by_enterprise_and_user(db, enterprise_id, current_user.id)
    if not current_relation or not current_relation.is_admin:
        raise HTTPException(
            status_code=403,
            detail={"message": "权限不足，您不是该企业的管理员", "status": 403}
        )

    if not user_ids:
        return {
            "token": token,
            "status": 400,
            "message": "用户ID列表不能为空",
            "data": None
        }

    try:
        # 验证所有用户都是该企业的成员
        valid_user_ids = []
        for user_id in user_ids:
            relation = enterprise_user_relation_dao.get_by_enterprise_and_user(db, enterprise_id, user_id)
            if relation:
                valid_user_ids.append(user_id)

        if not valid_user_ids:
            return {
                "token": token,
                "status": 404,
                "message": "没有找到属于该企业的用户",
                "data": None
            }

        # 批量更新企业用户关联关系状态
        from app.models.user import EnterpriseUserRelation
        affected_count = db.query(EnterpriseUserRelation).filter(
            EnterpriseUserRelation.enterprise_id == enterprise_id,
            EnterpriseUserRelation.personal_user_id.in_(valid_user_ids)
        ).update(
            {EnterpriseUserRelation.relation_status: status},
            synchronize_session='fetch'
        )
        
        db.commit()

        return {
            "token": token,
            "status": 200,
            "message": f"成功更新{affected_count}个用户的状态",
            "data": {
                "affected_count": affected_count,
                "updated_user_ids": valid_user_ids,
                "invalid_user_ids": [uid for uid in user_ids if uid not in valid_user_ids]
            }
        }

    except Exception as e:
        db.rollback()
        return {
            "token": token,
            "status": 500,
            "message": f"批量更新状态失败：{str(e)}",
            "data": None
        }
