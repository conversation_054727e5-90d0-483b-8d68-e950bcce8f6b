# -*- coding: utf-8 -*-
# 菜单模块


from fastapi import APIRouter, Depends, HTTPException, Header, File, UploadFile, Request
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import re

from app.dao.account import account_transaction_dao, account_dao
from app.models.enum import Status
from app.service.wechat_miniapp.wx_reservation import reservation_service
from app.core.config import settings
from app.service.wechat_miniapp.wx_service import WechatService
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.service.user import UserService
from app.service.wechat_miniapp.wx_order import WXOrderService
from app.models.user import PersonalUser
from app.dao.rule import rule_dao
from app.models.order import Order
from app.core.deps import get_current_user, get_db
from app.service.wechat_miniapp.wx_account import AccountService
import logging
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from app.schemas.user import PersonalUserCreateWX
import random
import string
import os
from pathlib import Path

from app.utils.common import get_phone_number, product_wx_token, allowed_file, secure_filename
from app.models.account import AccountType, TransactionType
from app.schemas.order import OrderUpdate
from app.models.order import PaymentStatus, OrderType, PaymentMethod
from app.dao.order import order_dao, order_item_dao
from app.service.wechat_miniapp.wx_service import wechat_service
from app.service.payment import payment_service
from app.dao.reservation import reservation_request_dao
from app.models.reservation import ReservationStatus
from app.models.order import OrderStatus, OrderItem
from app.dao.wx_payment import wx_payment_dao, wx_payment_service
from app.models.order import WxPaymentRecord, WxRefundRecord, WxRefundStatus, WxPaymentStatus
from app.schemas.account import AccountTransactionCreate
from app.utils.common import get_current_time
from app.dao.user import personal_user_dao
from app.dao.product import product_dao
from app.service.order import order_service
from app.utils.logger import logger
from app.dao.rule import rule_item_dao
from app.service.revervation import reservation_service as revervation_service
from app.dao.reservation import biz_reservation_request_dao
from app.schemas.reservation import BizReservationRequestCreate
from app.dao.menu import menu_dao
from app.models.reservation import ReservationType
from app.models.rule import DiningReservationRule, RuleItem, RuleScope, RuleType, RuleOrderType
from app.models.product import MealType
from app.models.reservation import BizReservationRequest
from app.dao.user import enterprise_user_relation_dao, enterprise_dao
from app.dao.admin import admin_dao
from app.service.pricing import pricing_service
from app.schemas.order import OrderItemBase
from app.events.deps import EventBusDep
from app.events.models import OrderEvent, OrderEventAction
from app.core.scheduler import send_order_change_reminders_task

router = APIRouter()


@router.get("/menu/list")
async def get_menu_list(
        product_id: int = None,
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取菜单列表
    Args:
        product_id: 产品ID
        db: 数据库会话
    Returns:
        Dict: 包含菜单列表信息的响应
    """
    try:
        logger.info(f"开始获取菜单列表，产品ID: {product_id}")

        if not product_id:
            logger.error("产品ID为空")
            return {
                "message": "产品ID不能为空",
                "status": 400
            }

        # 获取菜单列表
        menu_list = product_dao.get_contents_by_product(db, product_id)
        logger.info(f"获取到的菜单列表: {menu_list}")

        # 将SQLAlchemy模型对象转换为可序列化的字典列表
        serialized_menu_list = [
            {
                "id": item.id,
                "name": item.name,
                "content": item.content,
                "image": settings.BASE_URL + item.image,
                "thumbnail": settings.BASE_URL + item.thumbnail,
                "sort_order": item.sort_order,
                "status": item.status.value if hasattr(item, "status") and item.status else None,
                "type": item.type.value if hasattr(item, "type") and item.type else None,
                "created_at": item.created_at.isoformat() if hasattr(item, "created_at") and item.created_at else None,
                "updated_at": item.updated_at.isoformat() if hasattr(item, "updated_at") and item.updated_at else None
            } for item in menu_list
        ]

        return {
            "message": "获取菜单列表成功",
            "status": 200,
            "data": serialized_menu_list
        }
    except Exception as e:
        logger.error(f"获取菜单列表失败: {str(e)}")
        return {
            "message": "获取菜单列表失败",
            "status": 500
        }


# 获取商家信息 (商务餐)
@router.get("/store/detail")
async def get_store_detail(
        id: int,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取商家详细信息 (商务餐)"""
    logger.info(f"获取商家信息，ID: {id}")

    try:
        # 验证token（可选）
        # if token:
        #     user = WeChatUserService.verify_token(db, token)
        #     if not user:
        #         logger.warning(f"token无效或已过期: {token[:10]}...")

        # 使用mock数据，由于没有商家/店铺表
        store_data = {
            "id": id,
            "name": "乙禾素食餐厅",
            "address": "广州市黄埔区黄埔大道东976号港航中心二期B座1601房",
            "rating": "4.9",
            "ratingCount": "1024",
            "image": f"{settings.BASE_URL}/static/images/202506101801712.jpg"
        }

        return {
            "message": "获取成功",
            "status": 200,
            "data": store_data
        }

    except Exception as e:
        logger.error(f"获取商家信息失败: {str(e)}")
        return {
            "message": "获取商家信息失败",
            "status": 500,
            "data": None
        }


# 获取菜单分类 (商务餐)
@router.get("/menu/categories")
async def get_menu_categories(
        store_id: int,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取商家的菜单分类 (商务餐)"""
    logger.info(f"获取菜单分类，商家ID: {store_id}")

    try:
        # 验证token（可选）
        # if token:
        #     user = WeChatUserService.verify_token(db, token)

        # 使用产品分类
        from app.models.product import ProductCategory
        categories = db.query(ProductCategory).filter(ProductCategory.status == Status.ACTIVE).all()

        # 转换为响应格式
        categories_data = [
            {
                "id": str(category.id),
                "name": category.name,
                "sort_order": getattr(category, "sort_order", 0) or 0
            } for category in categories
        ]

        # 按排序顺序排序
        categories_data.sort(key=lambda x: x["sort_order"])

        return {
            "message": "获取成功",
            "status": 200,
            "data": categories_data
        }

    except Exception as e:
        logger.error(f"获取菜单分类失败: {str(e)}")
        return {
            "message": "获取菜单分类失败",
            "status": 500,
            "data": []
        }


# 获取菜品列表 (商务餐)
@router.get("/menu/dishes")
async def get_menu_dishes(
        category_id: str,
        page_no: int = 1,
        page_size: int = 20,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取指定分类的菜品列表 (商务餐)"""
    logger.info(f"获取菜品列表，分类ID: {category_id}, 页码: {page_no}, 每页数量: {page_size}")

    try:
        # 验证token（可选）
        # if token:
        #     user = WeChatUserService.verify_token(db, token)

        # 计算偏移量
        skip = (page_no - 1) * page_size

        # 获取菜品列表
        if category_id == '0':
            # 特殊处理"所有菜品"分类
            products = product_dao.get_list(db, skip=skip, limit=page_size)
        else:
            # 指定分类的菜品
            products = product_dao.get_by_category(db, int(category_id), skip=skip, limit=page_size)

        # 计算总数
        if category_id == '0':
            total = product_dao.count(db)
        else:
            total = len(products)  # 简单计算

        # 转换为响应格式
        dishes_data = []
        for product in products:
            if hasattr(product,
                       'status') and product.status == Status.ACTIVE and product.meal_type == MealType.BUSINESS:
                product_image = getattr(product, "image", None)
                if product_image:
                    image_url = settings.BASE_URL + product_image
                else:
                    image_url = f"{settings.BASE_URL}/static/images/default-dish.png"

                dishes_data.append({
                    "id": str(product.id),
                    "name": product.name,
                    "description": getattr(product, "description", "") or "",
                    "price": float(product.price),
                    "sales": getattr(product, "sales", 0) or 0,
                    "image": image_url,
                    "category_id": str(getattr(product, "category_id", "0"))
                })

        return {
            "message": "获取成功",
            "status": 200,
            "data": {
                "list": dishes_data,
                "total": total,
                "page_no": page_no,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
        }

    except Exception as e:
        logger.error(f"获取菜品列表失败: {str(e)}")
        return {
            "message": "获取菜品列表失败",
            "status": 500,
            "data": {
                "list": [],
                "total": 0,
                "page_no": page_no,
                "page_size": page_size,
                "total_pages": 0
            }
        }
