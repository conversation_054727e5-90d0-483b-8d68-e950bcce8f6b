import hashlib
import datetime
import requests
from typing import Dict, Any, Optional
from fastapi import APIR<PERSON>er, Header, Request, Depends, HTTPException
from fastapi.responses import PlainTextResponse, JSONResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.core.config import settings
from app.core.deps import get_db
from app.models.user import PersonalUser
from app.utils.logger import logger

# 创建路由器
router = APIRouter()


class MsgStatusUpdate(BaseModel):
    msg_status: int


class TestMessageRequest(BaseModel):
    openid: str


# 验证微信服务器请求并处理消息推送
@router.get("/revice")
async def verify_wechat_server(
    signature: str = "",
    timestamp: str = "",
    nonce: str = "",
    echostr: str = "",
):
    """验证微信服务器请求"""
    token = settings.WECHAT_TOKEN
    temp_list = [token, timestamp, nonce]
    temp_list.sort()
    temp_str = ''.join(temp_list)
    calculated_signature = hashlib.sha1(temp_str.encode('utf-8')).hexdigest()

    if calculated_signature == signature:
        logger.info("微信服务器验证成功")
        return PlainTextResponse(content=echostr)
    else:
        logger.info("微信服务器验证失败")
        return PlainTextResponse(content="验证失败", status_code=403)


@router.post("/revice")
async def process_wechat_message(request: Request):
    """处理微信服务器推送的消息"""
    try:
        xml_data = await request.body()
        if not xml_data:
            return PlainTextResponse(content="消息为空", status_code=400)

        # 这里可以添加消息签名验证
        # TODO: 添加签名验证逻辑

        # 返回成功响应
        # 注意：微信服务器要求返回 "success"，否则会重试
        return PlainTextResponse(content="success")

    except Exception as e:
        logger.error(f"处理消息推送错误: {str(e)}")
        return PlainTextResponse(content="处理失败", status_code=500)


def send_subscribe_message(openid: str, template_id: str, data: Dict[str, Any]) -> bool:
    """
    发送订阅消息
    :param openid: 用户的openid
    :param template_id: 订阅消息模板ID
    :param data: 要发送的数据
    """
    try:
        # 获取access_token
        token_url = f"{settings.WECHAT_OFFICIAL_DOMAIN}/cgi-bin/token?grant_type=client_credential&appid={settings.WECHAT_APPID}&secret={settings.WECHAT_SECRET}"
        token_response = requests.get(token_url)
        access_token = token_response.json().get('access_token')

        if not access_token:
            print("获取access_token失败")
            return False

        # 发送订阅消息的URL
        url = f"{settings.WECHAT_OFFICIAL_DOMAIN}/cgi-bin/message/subscribe/send?access_token={access_token}"

        # 构建请求数据
        message_data = {
            "touser": openid,
            "template_id": template_id,
            "page": "pages/topic/topic",  # 点击消息后跳转的页面
            "data": data
        }

        # 发送请求
        response = requests.post(url, json=message_data)
        result = response.json()

        if result.get('errcode') == 0:
            print(f"发送订阅消息成功: {result}")
            return True
        else:
            if result.get('errcode') == 43101:
                print(f"非长期订阅，无法发送")
                return True
            else:
                print(f"发送订阅消息失败: {result}")
                return False

    except Exception as e:
        print(f"发送订阅消息异常: {str(e)}")
        return False


@router.post("/send-test-message")
async def send_test_message(request: TestMessageRequest):
    """
    测试发送订阅消息的接口
    """
    try:
        openid = request.openid

        if not openid:
            return JSONResponse(
                content={'error': '缺少openid参数', 'status': 400},
                status_code=400
            )

        # 订阅消息模板ID
        template_id = 'uMsj6o9VGLA7t6Dv1EVbq_xbCBmu2sncEldk8KuFNmM'

        # 构建消息数据（根据你的模板格式调整）
        message_data = {
            # "thing1": {"value": "Robby，您好！"},  # 根据实际模板格式修改
            "thing2": {"value": "今天午餐的菜谱已开放，请尽快完成点餐"},
            "time1": {"value": datetime.datetime.now().strftime("%Y-%m-%d %H:%M")},
        }

        # 发送消息
        success = send_subscribe_message(openid, template_id, message_data)

        if success:
            return {
                'message': '发送成功',
                'status': 200
            }
        else:
            return JSONResponse(
                content={'message': '发送失败', 'status': 500},
                status_code=500
            )

    except Exception as e:
        print(f"发送测试消息错误: {str(e)}")
        return JSONResponse(
            content={'error': str(e), 'status': 500},
            status_code=500
        )


@router.post("/update-msg-status")
async def update_msg_status(
    request: MsgStatusUpdate,
    token: Optional[str] = Header(None),
    db: Session = Depends(get_db)
):
    """更新用户消息订阅状态"""
    logger.info("收到更新消息订阅状态请求")

    if not token:
        raise HTTPException(status_code=401, detail="未登录")

    try:
        # 查询用户
        mini_app_user = db.query(PersonalUser).filter(PersonalUser.token == token).first()
        if not mini_app_user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 直接更新消息状态，不检查微信订阅状态
        mini_app_user.msg_status = request.msg_status
        db.commit()

        logger.info(f"用户 {mini_app_user.id} 的消息状态已更新为 {request.msg_status}")

        return {
            'message': '更新成功',
            'status': 200,
            'msg_status': request.msg_status
        }

    except Exception as e:
        logger.error(f"更新消息状态错误: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
