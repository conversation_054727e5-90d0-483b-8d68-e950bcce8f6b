# 提供小程序管理员模块API

from fastapi import APIRouter, Depends, HTTPException, Header, File, UploadFile, Request
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import re

from app.dao.account import account_transaction_dao, account_dao
from app.models.enum import Status
from app.service.wechat_miniapp.wx_reservation import reservation_service
from app.core.config import settings
from app.service.wechat_miniapp.wx_service import WechatService
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.service.user import UserService
from app.service.wechat_miniapp.wx_order import WXOrderService
from app.models.user import PersonalUser
from app.dao.rule import rule_dao, rule_item_dao
from app.models.order import Order
from app.core.deps import get_current_user, get_db
from app.service.wechat_miniapp.wx_account import AccountService
import logging
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from app.schemas.user import PersonalUserCreateWX
import random
import string
import os
from pathlib import Path

from app.utils.common import get_phone_number, product_wx_token, allowed_file, secure_filename
from app.models.account import AccountType, TransactionType
from app.schemas.order import OrderUpdate
from app.models.order import PaymentStatus, OrderType, PaymentMethod
from app.dao.order import order_dao, order_item_dao
from app.service.wechat_miniapp.wx_service import wechat_service
from app.service.payment import payment_service
from app.dao.reservation import reservation_request_dao
from app.models.reservation import ReservationStatus
from app.models.order import OrderStatus, OrderItem
from app.dao.wx_payment import wx_payment_dao, wx_payment_service
from app.models.order import WxPaymentRecord, WxRefundRecord, WxRefundStatus, WxPaymentStatus
from app.schemas.account import AccountTransactionCreate
from app.utils.common import get_current_time
from app.dao.user import personal_user_dao
from app.dao.product import product_dao
from app.service.order import order_service
from app.utils.logger import logger
from app.dao.rule import rule_item_dao
from app.service.revervation import reservation_service as revervation_service
from app.dao.reservation import biz_reservation_request_dao
from app.schemas.reservation import BizReservationRequestCreate
from app.dao.menu import menu_dao
from app.models.reservation import ReservationType
from app.models.rule import DiningReservationRule, RuleItem, RuleScope, RuleType, RuleOrderType
from app.models.product import MealType
from app.models.reservation import BizReservationRequest
from app.dao.user import enterprise_user_relation_dao, enterprise_dao
from app.dao.admin import admin_dao
from app.service.pricing import pricing_service
from app.schemas.order import OrderItemBase
from app.events.deps import EventBusDep
from app.events.models import OrderEvent, OrderEventAction
from app.core.scheduler import send_order_change_reminders_task
from app.callback.speaker import send_request

router = APIRouter()


# 新增：核销请求模型
class VerifyOrderRequest(BaseModel):
    order_no: str


def _filter_today_slots_and_find_best(date_list: List[Dict[str, Any]], type: str, user_id: int, db: Session, unfiltered_date_list: List[Dict[str, Any]] = None) -> Dict[str, Any]:
    """过滤当天时段并找到最佳推荐时段
    
    Args:
        date_list: 过滤后的日期列表（用于显示可用时段）
        type: 预约类型
        user_id: 用户ID
        db: 数据库会话
        unfiltered_date_list: 未过滤的日期列表（用于检查现有预订记录）
        
    Returns:
        Dict: 包含当天日期和推荐时段的信息
    """
    today = datetime.now().date()
    today_str = today.strftime('%Y-%m-%d')
    current_time = datetime.now()
    
    logger.info(f"当前日期: {today_str}, 当前时间: {current_time}")
    
    # 查找今天的日期数据（过滤后的）
    today_date_item = None
    for date_item in date_list:
        if date_item['date'] == today_str:
            today_date_item = date_item
            break
    
    if today_date_item:
        logger.info(f"今天({today_str})有可用的预约时段: {today_date_item}")
    else:
        logger.info(f"今天({today_str})没有可用的预约时段")
        return {
            "today_date_item": None,
            "recommended_slot": None,
            "available_slots": [],
            "status": "no_meal_today"  # 今天没有餐次
        }
    
    # 直接检查用户是否已有预订记录（仅对员工餐）
    if type == 'employee' and unfiltered_date_list:
        request_date_list = [today]
        logger.info(f"检查用户 {user_id} 在 {request_date_list} 的预订记录")

        # 使用未过滤的数据来检查现有预订记录
        unfiltered_today_date_item = None
        for date_item in unfiltered_date_list:
            if date_item['date'] == today_str:
                unfiltered_today_date_item = date_item
                break

        if unfiltered_today_date_item:
            # 检查用户是否已有任何餐食类型的预订记录
            has_any_reservation = False
            for time_slot in unfiltered_today_date_item.get('timeSlots', []):
                # 获取时间段对应的餐食类型
                rule_item = rule_item_dao.get(db, time_slot["rule_item_id"])
                meal_type = rule_item.meal_type if rule_item else None

                if meal_type:
                    logger.info(f"检查餐食类型: {meal_type.value}")
                    has_enterprise_order, _ = order_dao.check_enterprise_order_in_day_by_meal_type(
                        db, user_id, request_date_list, meal_type
                    )
                    if has_enterprise_order:
                        logger.info(f"用户已有 {meal_type.value} 类型的预订记录")
                        has_any_reservation = True
                        break

            if has_any_reservation:
                logger.info("用户已有预订记录")
                return {
                    "today_date_item": today_date_item,
                    "recommended_slot": None,
                    "available_slots": [],
                    "status": "existing_reservation"  # 已有预订记录
                }

    # 获取所有可用的时段，并且只显示当前时间在预约开始时间和结束时间之间的时段
    available_slots = []
    future_slots = []  # 未来时段（未开餐）

    # 使用过滤后的数据来判断时段
    all_slots = today_date_item.get('timeSlots', [])

    for slot in all_slots:
        if slot.get('can_select', False) and not slot.get('disabled', True) and slot.get('available_capacity', 0) > 0:
            # 检查当前时间是否在预约时段内
            try:
                start_time_str = slot.get('start_time', '')
                end_time_str = slot.get('end_time', '')

                if start_time_str and end_time_str:
                    start_time = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M")
                    end_time = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M")

                    # 当前时间在预约时段内
                    if start_time <= current_time <= end_time:
                        available_slots.append(slot)
                        logger.info(f"时段 {slot.get('time')} 在当前时间范围内且可用，添加到可用列表")
                    # 当前时间在预约时段之前（未开餐）
                    elif current_time < start_time:
                        future_slots.append(slot)
                        logger.info(f"时段 {slot.get('time')} 在当前时间之前，添加到未来列表")
                    else:
                        logger.info(f"时段 {slot.get('time')} 已过期，跳过")
                else:
                    logger.warning(f"时段缺少开始或结束时间: {slot}")
            except (ValueError, KeyError) as e:
                logger.warning(f"解析时段时间失败: {e}, slot: {slot}")
                continue
    
    logger.info(f"当前时间范围内的可用时段数量: {len(available_slots)}")
    logger.info(f"未来时段数量: {len(future_slots)}")

    # 如果有可用时段，返回正常状态
    if available_slots:
        # 按开始时间排序
        available_slots.sort(key=lambda x: x.get('start_time', ''))
        logger.info(f"排序后的可用时段: {available_slots}")

        # 选择第一个作为推荐
        recommended_slot = available_slots[0] if available_slots else None
        logger.info(f"推荐时段: {recommended_slot.get('time') if recommended_slot else '无'}")

        return {
            "today_date_item": today_date_item,
            "recommended_slot": recommended_slot,
            "available_slots": available_slots,
            "status": "available"  # 有可用时段
        }

    # 如果没有可用时段但有未来时段，说明未开餐
    if future_slots:
        logger.info("当前时间范围内没有可用时段，但有未来时段，说明未开餐")
        return {
            "today_date_item": today_date_item,
            "recommended_slot": None,
            "available_slots": [],
            "status": "not_open_yet"  # 未开餐
        }

    # 如果既没有可用时段也没有未来时段，可能是已有预订记录
    logger.info("当前时间范围内没有可用时段，也没有未来时段")
    return {
        "today_date_item": today_date_item,
        "recommended_slot": None,
        "available_slots": [],
        "status": "existing_reservation"  # 已有预订记录
    }


def _find_best_time_slot(available_slots: List[Dict[str, Any]], current_time: datetime) -> Optional[Dict[str, Any]]:
    """根据当前时间找到最佳时段
    
    Args:
        available_slots: 可用时段列表
        current_time: 当前时间
        
    Returns:
        Optional[Dict]: 最佳时段，如果没有则返回None
    """
    if not available_slots:
        return None
    
    current_timestamp = current_time.timestamp()
    
    # 策略1: 找当前时间在范围内的时段
    for slot in available_slots:
        try:
            start_time_str = slot.get('start_time', '')
            end_time_str = slot.get('end_time', '')
            
            # 处理不同的时间格式
            if start_time_str and end_time_str:
                try:
                    # 尝试解析ISO格式
                    start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
                    end_time = datetime.fromisoformat(end_time_str.replace('Z', '+00:00'))
                except ValueError:
                    # 如果ISO格式失败，尝试其他格式
                    start_time = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M")
                    end_time = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M")
                
                if start_time.timestamp() <= current_timestamp <= end_time.timestamp():
                    logger.info(f"找到当前时间范围内的时段: {slot.get('time')}")
                    return slot
        except (ValueError, KeyError) as e:
            logger.warning(f"解析时段时间失败: {e}, slot: {slot}")
            continue
    
    # 策略2: 找下一个未来的时段
    future_slots = []
    for slot in available_slots:
        try:
            start_time_str = slot.get('start_time', '')
            if start_time_str:
                try:
                    start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
                except ValueError:
                    start_time = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M")
                
                if start_time.timestamp() > current_timestamp:
                    future_slots.append((slot, start_time.timestamp() - current_timestamp))
        except (ValueError, KeyError) as e:
            logger.warning(f"解析时段时间失败: {e}, slot: {slot}")
            continue
    
    if future_slots:
        # 按时间差排序，选择最近的未来时段
        future_slots.sort(key=lambda x: x[1])
        best_slot = future_slots[0][0]
        logger.info(f"找到最近的未来时段: {best_slot.get('time')}")
        return best_slot
    
    # 策略3: 如果都没有找到，返回第一个可用时段
    logger.info(f"返回第一个可用时段: {available_slots[0].get('time')}")
    return available_slots[0]


@router.get("/onsite/config")
async def get_onsite_config(
        type: str,  # 预约类型
        source: Optional[str] = None,  # 添加source参数，默认为None
        meal_type: Optional[str] = None,  # 添加meal_type参数，默认为None
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """获取预约配置
    Args:
        type: 预约类型
        source: 请求来源（topic或admin）
        meal_type: 餐类
        token: 用户token
        db: 数据库会话

    Returns:
        Dict: 包含预约配置信息的响应
    """
    logger.info(f"开始获取预约配置")
    logger.info(f"请求来源: {source}")  # 记录请求来源
    
    user = WeChatUserService.verify_token(db, token)
    if not user:
        logger.warning(f"token无效或已过期: {token[:10]}...")
        raise HTTPException(
            status_code=401,
            detail={"message": "未登录", "status": 401}
        )

    logger.info(f"用户token验证成功，用户ID: {user.id}")
    logger.info(f"预约类型: {type}")
    user_id = user.id

    qr_type = "static"
    logger.info(f"二维码类型: {qr_type}")

    # 传递source参数给service层
    result = reservation_service.get_reservation_info(db, type, user_id, source=source, meal_type=meal_type, qr_type=qr_type)

    logger.info(f"现场点餐配置: {result}")

    # 在这里做筛选过滤
    if source == 'admin' and result.get('code') == 200:
        logger.info("管理员来源请求，进行当天时段过滤和智能推荐")
        
        data = result.get('data', {})
        original_date_list = data.get('dateList', [])
        
        # 获取未过滤的数据用于检查现有预订记录
        unfiltered_result = reservation_service.get_reservation_info(db, type, user_id, source=source, meal_type=meal_type, qr_type=qr_type, filter_existing_reservations=False)
        unfiltered_data = unfiltered_result.get('data', {})
        unfiltered_date_list = unfiltered_data.get('dateList', [])

        # 过滤当天时段并找到最佳推荐
        today_result = _filter_today_slots_and_find_best(original_date_list, type, user_id, db, unfiltered_date_list)
        logger.info(f"今天({today_result['today_date_item']['date_str'] if today_result['today_date_item'] else '无'})的时段过滤结果: {today_result}")

        today_date_item = today_result['today_date_item']
        recommended_slot = today_result['recommended_slot']
        available_slots = today_result['available_slots']
        status = today_result.get('status', 'unknown')  # 获取状态信息

        # 只返回当天的数据
        if today_date_item:
            # 更新当天时段为可用时段
            today_date_item['timeSlots'] = available_slots
            final_date_list = [today_date_item]
            
            # 如果有推荐时段，添加推荐标记
            if recommended_slot:
                for slot in today_date_item['timeSlots']:
                    if slot.get('rule_item_id') == recommended_slot.get('rule_item_id'):
                        slot['is_recommended'] = True
                        break
            
            logger.info(f"当天可用时段数量: {len(available_slots)}")
            logger.info(f"推荐时段: {recommended_slot.get('time') if recommended_slot else '无'}")
        else:
            final_date_list = []
            logger.info("今天没有可用的预约时段")
        
        # 更新返回的数据
        data['dateList'] = final_date_list
        
        # 如果有推荐时段，添加推荐信息
        if recommended_slot:
            data['recommended_slot'] = recommended_slot
            data['today_date'] = today_date_item['date_str'] if today_date_item else None
        
        # 添加状态信息
        data['status'] = status
        logger.info(f"返回状态: {status}")

        # 更新结果
        result['data'] = data
        
        logger.info(f"最终返回数据 - 日期数量: {len(final_date_list)}, 是否有推荐: {bool(recommended_slot)}, 状态: {status}")
    else:
        logger.info(f"非管理员来源或请求失败，保持原有数据结构")

    return result


@router.post("/onsite/verify")
async def verify_order(
        request: VerifyOrderRequest,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """核销现场自助餐订单
    
    Args:
        request: 核销请求，包含订单号
        token: 用户token
        db: 数据库会话

    Returns:
        Dict: 核销结果
    """
    try:
        logger.info(f"开始核销现场自助餐订单，订单号: {request.order_no}")
        
        # 验证用户token
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"token无效或已过期: {token[:10] if token else 'None'}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        logger.info(f"用户token验证成功，用户ID: {user.id}")
        
        # 根据订单号查找订单
        order = order_dao.get_by_order_no(db, request.order_no)
        if not order:
            logger.warning(f"订单不存在: {request.order_no}")
            return {
                "status": 404,
                "message": "订单不存在",
                "code": 404
            }
        order_id = order.id
        order_type = order.type
        order_status = order.status
        logger.info(f"找到订单，订单ID: {order_id}, 订单类型: {order_type.value}, 当前状态: {order_status.value}")
        
        # 检查订单是否已支付
        if order_status.value not in ['paid', 'partial_paid']:
            logger.warning(f"订单未支付，无法核销: {request.order_no}, 支付状态: {order_status.value}")
            return {
                "status": 400,
                "message": "订单未支付，无法核销",
                "code": 400
            }
        
        # 获取订单项
        order_items = order_item_dao.get_by_order(db, order_id)
        if not order_items:
            logger.warning(f"订单没有订单项: {request.order_no}")
            return {
                "status": 400,
                "message": "订单数据异常，没有订单项",
                "code": 400
            }
        
        order_item_id = order_items[0].id  # 获取第一个订单项ID
        
        # 获取预约记录
        reservation_request = reservation_request_dao.get_by_order_item_id_and_user_id(db, order_item_id, user.id)
        if not reservation_request:
            logger.warning(f"订单没有预约记录: {request.order_no}")
            return {
                "status": 400,
                "message": "订单数据异常，没有预约记录",
                "code": 400
            }
        
        # 开始事务操作
        try:
            # 更新预订订单状态
            logger.info("开始更新预订订单状态为已验证")
            reservation_request.status = ReservationStatus.VERIFIED
            order_item_id = reservation_request.order_item_id
            
            # 更新所有订单项状态
            logger.info("开始更新订单项状态为已核销")
            order_item = order_item_dao.get_by_order_item_id(db, order_item_id)
            if not order_item:
                logger.error(f"未找到订单项: {order_item_id}")
            order_item.status = OrderStatus.VERIFIED
            # 提交事务
            db.commit()
            logger.info(f"订单自动核销成功: {request.order_no}")

            try:
                send_request("静态码临时点餐，核销成功")
                logger.info("静态码临时点餐，核销成功")
            except Exception as e:
                logger.error(f"发送语音消息失败: {str(e)}")

            return {
                "status": 200,
                "message": "订单自动核销成功",
                "code": 200,
                "data": {
                    "order_no": request.order_no,
                    "status": "已核销"
                }
            }
            
        except Exception as e:
            # 发生错误时回滚事务
            logger.error(f"自动核销过程中发生错误: {str(e)}")
            db.rollback()
            return {
                "status": 500,
                "message": f"核销失败: {str(e)}",
                "code": 500
            }
            
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"自动核销订单时发生异常: {str(e)}")
        logger.exception("详细异常信息")
        return {
            "status": 500,
            "message": f"自动核销订单时发生异常: {str(e)}",
            "code": 500
        }
