# -*- coding: utf-8 -*-
# 分账支付模块


from fastapi import APIRouter, Depends, HTTPException, Header, File, UploadFile, Request
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import re

from app.dao.account import account_transaction_dao, account_dao
from app.models.enum import Status
from app.service.wechat_miniapp.wx_reservation import reservation_service
from app.core.config import settings
from app.service.wechat_miniapp.wx_service import WechatService
from app.service.wechat_miniapp.wx_user import WeChatUserService
from app.service.user import UserService
from app.service.wechat_miniapp.wx_order import WXOrderService
from app.models.user import PersonalUser
from app.dao.rule import rule_dao
from app.models.order import Order
from app.core.deps import get_current_user, get_db
from app.service.wechat_miniapp.wx_account import AccountService
import logging
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from app.schemas.user import PersonalUserCreateWX
import random
import string
import os
from pathlib import Path

from app.utils.common import get_phone_number, product_wx_token, allowed_file, secure_filename
from app.models.account import AccountType, TransactionType
from app.schemas.order import OrderUpdate
from app.models.order import PaymentStatus, OrderType, PaymentMethod
from app.dao.order import order_dao, order_item_dao
from app.service.wechat_miniapp.wx_service import wechat_service
from app.service.payment import payment_service
from app.dao.reservation import reservation_request_dao
from app.models.reservation import ReservationStatus
from app.models.order import OrderStatus, OrderItem
from app.dao.wx_payment import wx_payment_dao, wx_payment_service
from app.models.order import WxPaymentRecord, WxRefundRecord, WxRefundStatus, WxPaymentStatus
from app.schemas.account import AccountTransactionCreate
from app.utils.common import get_current_time
from app.dao.user import personal_user_dao
from app.dao.product import product_dao
from app.service.order import order_service
from app.utils.logger import logger
from app.dao.rule import rule_item_dao
from app.service.revervation import reservation_service as revervation_service
from app.dao.reservation import biz_reservation_request_dao
from app.schemas.reservation import BizReservationRequestCreate
from app.dao.menu import menu_dao
from app.models.reservation import ReservationType
from app.models.rule import DiningReservationRule, RuleItem, RuleScope, RuleType, RuleOrderType
from app.models.product import MealType
from app.models.reservation import BizReservationRequest
from app.dao.user import enterprise_user_relation_dao, enterprise_dao
from app.dao.admin import admin_dao
from app.service.pricing import pricing_service
from app.schemas.order import OrderItemBase
from app.events.deps import EventBusDep
from app.events.models import OrderEvent, OrderEventAction
from app.core.scheduler import send_order_change_reminders_task

router = APIRouter()


@router.get("/enterprise/list")
async def get_user_enterprise_list(
        order_id: Optional[int] = None,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    获取订单关联的企业信息

    Args:
        order_id: 订单ID，查询该订单是否为企业支付
        token: 用户令牌
        db: 数据库会话

    Returns:
        企业信息列表，如果不是企业支付则返回空列表
    """
    logger.info(f"获取订单关联的企业信息，订单ID: {order_id}")

    try:
        # 验证用户token
        user = WeChatUserService.verify_token(db, token)
        if not user:
            logger.warning(f"token无效或已过期: {token[:10]}...")
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        # 如果没有提供订单ID，直接返回空列表
        if not order_id:
            return {
                "message": "获取企业列表成功",
                "status": 200,
                "data": []
            }

        # 获取订单信息
        order = order_dao.get(db, order_id)
        if not order:
            logger.error(f"订单不存在，订单ID: {order_id}")
            return {
                "message": "订单不存在",
                "status": 404
            }

        # 检查订单的支付方式是否为企业支付
        if order.payment_method != PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE:
            logger.info(f"订单支付方式不是企业支付，订单ID: {order_id}")
            return {
                "message": "获取企业列表成功，订单不是企业支付，不提供企业信息",
                "status": 200,
                "data": []
            }

        # 获取订单的企业ID（通过支付记录获取）
        account_transactions = account_transaction_dao.get_by_order_id(db, order_id)
        if not account_transactions or not account_transactions[0].account_id:
            logger.error(f"找不到订单的支付交易记录，订单ID: {order_id}")
            return {
                "message": "获取企业列表成功",
                "status": 200,
                "data": []
            }

        # 获取支付企业的账户ID和企业ID
        enterprise_account_list = account_dao.get_by_id(db, account_transactions[0].account_id)
        if not enterprise_account_list or len(enterprise_account_list) == 0:
            logger.error(f"找不到企业账户")
            return {
                "message": "获取企业列表成功",
                "status": 200,
                "data": []
            }

        # 获取列表中的第一个账户
        enterprise_account = enterprise_account_list[0]
        enterprise_id = enterprise_account.user_id
        enterprise = enterprise_dao.get(db, enterprise_id)

        if not enterprise or enterprise.status != Status.ACTIVE:
            logger.error(f"企业不存在或状态不活跃，企业ID: {enterprise_id}")
            return {
                "message": "获取企业列表成功",
                "status": 200,
                "data": []
            }

        # 构建企业数据
        enterprise_data = {
            "id": enterprise.id,
            "company_name": enterprise.company_name,
            "phone": enterprise.phone,
            "email": enterprise.email,
            "address": enterprise.address
        }

        logger.info(f"订单使用企业支付，企业ID: {enterprise_id}")
        return {
            "message": "获取企业列表成功",
            "status": 200,
            "data": [enterprise_data]
        }

    except Exception as e:
        logger.error(f"获取企业列表失败: {str(e)}", exc_info=True)
        return {
            "message": f"获取企业列表失败: {str(e)}",
            "status": 500
        }


@router.post("/order/pay/split")
async def create_split_payment(
        task_info: dict,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    创建分次支付订单（企业支付 + 个人支付）
    """
    try:
        logger.info(f"开始创建分次支付，接收到的参数: {task_info}")

        # 验证用户token
        user = WeChatUserService.verify_token(db, token)
        if not user:
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        # 验证必要参数
        order_no = task_info.get("order_no")
        enterprise_id = task_info.get("enterprise_id")
        total_amount = task_info.get("amount")

        if not all([order_no, enterprise_id, total_amount]):
            raise HTTPException(
                status_code=400,
                detail={"message": "缺少必要参数", "status": 400}
            )

        # 检查总金额是否适合分次支付
        if total_amount <= settings.PERSONAL_PAYMENT_FIXED_AMOUNT:
            raise HTTPException(
                status_code=400,
                detail={"message": "订单金额过小，请使用个人支付", "status": 400}
            )

        # 获取订单
        order = order_dao.get_by_order_no(db, order_no)
        if not order:
            raise HTTPException(
                status_code=404,
                detail={"message": "订单不存在", "status": 404}
            )

        order_id = order.id
        order_payment_status = order.payment_status

        # 检查订单状态
        if order_payment_status != PaymentStatus.UNPAID:
            raise HTTPException(
                status_code=400,
                detail={"message": "订单状态不允许支付", "status": 400}
            )

        # 计算分次支付金额
        personal_payment_amount = settings.PERSONAL_PAYMENT_FIXED_AMOUNT  # 个人需要支付的固定金额
        enterprise_payment_amount = round(total_amount - personal_payment_amount, 2)  # 处理浮点数精度问题

        if enterprise_payment_amount <= 0:
            raise HTTPException(
                status_code=400,
                detail={"message": "订单金额不足以进行分次支付，请使用个人支付", "status": 400}
            )

        # 第一步：更新订单为分次支付模式（但不改变支付状态）
        order_dao.update_split_payment_info(
            db,
            order_id,
            enterprise_payment_amount,
            personal_payment_amount
        )

        logger.info(f"开始企业支付，金额: {enterprise_payment_amount}")

        # 第二步：进行企业支付
        payment_info = {
            "payment_method": PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE,
            "enterprise_id": enterprise_id,
            "amount": enterprise_payment_amount
        }

        enterprise_payment_result = payment_service.pay_order_partially(
            db, order_id, payment_info
        )

        if not enterprise_payment_result:
            raise HTTPException(
                status_code=500,
                detail={"message": "企业支付失败", "status": 500}
            )

        logger.info(f"企业支付成功，等待个人支付: {personal_payment_amount}")

        return {
            "message": "企业支付成功，请继续个人支付",
            "status": 200,
            "enterprise_paid_amount": enterprise_payment_amount,
            "remaining_amount": personal_payment_amount,
            "order_status": "partial_paid"
        }

    except Exception as e:
        logger.error(f"分次支付失败: {str(e)}")
        return {
            "message": f"分次支付失败: {str(e)}",
            "status": 500
        }


@router.post("/order/pay/complete")
async def complete_split_payment(
        task_info: dict,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    完成分次支付的个人部分
    """
    try:
        logger.info(f"开始完成个人支付，接收到的参数: {task_info}")

        # 验证用户token
        user = WeChatUserService.verify_token(db, token)
        if not user:
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        # 验证必要参数
        order_no = task_info.get("order_no")
        payment_method = task_info.get("paymentMethod")

        if not all([order_no, payment_method]):
            raise HTTPException(
                status_code=400,
                detail={"message": "缺少必要参数", "status": 400}
            )

        # 获取订单
        order = order_dao.get_by_order_no(db, order_no)
        if not order:
            raise HTTPException(
                status_code=404,
                detail={"message": "订单不存在", "status": 404}
            )

        order_id = order.id
        order_payment_status = order.payment_status

        # 检查订单是否为部分支付状态
        if order_payment_status != PaymentStatus.PARTIAL_PAID:
            raise HTTPException(
                status_code=400,
                detail={"message": "订单不在部分支付状态", "status": 400}
            )

        # 获取需要个人支付的金额
        personal_amount = order.requires_personal_payment
        if personal_amount <= 0:
            raise HTTPException(
                status_code=400,
                detail={"message": "无需个人支付", "status": 400}
            )

        # 进行个人支付
        if payment_method == "wxpay":
            # 微信支付
            logger.info("准备微信支付参数")
            try:
                class_wechat_service = WechatService()
                pay_params = class_wechat_service.create_jsapi_payment(
                    user.wechat_id,
                    order_no + "_personal",  # 为个人支付部分生成特殊订单号
                    personal_amount,
                    "订单个人部分支付"
                )

                # 删除这行错误的代码：
                # order_dao.update_payment_method(db, order.id, PaymentMethod.WECHAT_PAY)
                # 分次支付的payment_method应该保持为ENTERPRISE_ACCOUNT_BALANCE

                return {
                    "message": "个人支付参数生成成功",
                    "status": 200,
                    "payParams": pay_params,
                    "payment_amount": personal_amount
                }
            except Exception as e:
                logger.error(f"生成微信支付参数失败: {str(e)}")
                raise HTTPException(
                    status_code=500,
                    detail={"message": f"生成支付参数失败: {str(e)}", "status": 500}
                )

        elif payment_method == "balance":
            # 个人账户支付
            payment_info = {
                "payment_method": PaymentMethod.ACCOUNT_BALANCE,
                "amount": personal_amount
            }

            complete_result = payment_service.complete_split_payment(
                db, order_id, payment_info
            )

            if complete_result:
                # 更新产品库存
                order_service.update_product_stock(db, order_id)

                return {
                    "message": "支付完成",
                    "status": 200
                }
            else:
                raise HTTPException(
                    status_code=500,
                    detail={"message": "个人支付失败", "status": 500}
                )
        else:
            raise HTTPException(
                status_code=400,
                detail={"message": "不支持的支付方式", "status": 400}
            )

    except Exception as e:
        logger.error(f"完成个人支付失败: {str(e)}")
        return {
            "message": f"完成个人支付失败: {str(e)}",
            "status": 500
        }


@router.post("/order/pay/split/cancel")
async def cancel_split_payment(
        task_info: dict,
        token: Optional[str] = Header(None),
        db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    取消分次支付并回退企业支付金额
    """
    try:
        logger.info(f"开始取消分次支付，接收到的参数: {task_info}")

        # 验证用户token
        user = WeChatUserService.verify_token(db, token)
        if not user:
            raise HTTPException(
                status_code=401,
                detail={"message": "未登录", "status": 401}
            )

        order_no = task_info.get("order_no")
        if not order_no:
            raise HTTPException(
                status_code=400,
                detail={"message": "缺少订单号", "status": 400}
            )

        # 获取订单
        order = order_dao.get_by_order_no(db, order_no)
        if not order:
            raise HTTPException(
                status_code=404,
                detail={"message": "订单不存在", "status": 404}
            )

        order_id = order.id
        order_payment_status = order.payment_status

        # 检查订单状态是否为部分支付
        if order_payment_status != PaymentStatus.PARTIAL_PAID:
            raise HTTPException(
                status_code=400,
                detail={"message": "订单不在部分支付状态", "status": 400}
            )

        # 执行回退逻辑
        refund_result = payment_service.refund_split_payment(db, order_id)

        if refund_result:
            return {
                "message": "分次支付已取消，企业支付金额已回退",
                "status": 200
            }
        else:
            raise HTTPException(
                status_code=500,
                detail={"message": "回退失败", "status": 500}
            )

    except Exception as e:
        logger.error(f"取消分次支付失败: {str(e)}")
        return {
            "message": f"取消分次支付失败: {str(e)}",
            "status": 500
        }


async def handle_split_payment_refund(
        db: Session,
        order_id: int,
        total_refund_amount: float,
        out_refund_no: str,
        is_business_dining: bool,
        order_item_id: int
) -> bool:
    """处理混合支付（分账支付）的退款"""
    try:
        order = order_dao.get(db, order_id)
        if not order:
            raise Exception("订单不存在")
        order_id = order.id
        order_enterprise_paid_amount = order.enterprise_paid_amount
        order_personal_paid_amount = order.personal_paid_amount

        logger.info(f"[混合支付退款] 开始处理，订单ID: {order_id}, 退款总额: {total_refund_amount}")

        # 计算各部分退款金额
        enterprise_paid = order_enterprise_paid_amount
        personal_paid = order_personal_paid_amount
        total_paid = enterprise_paid + personal_paid

        if total_paid <= 0:
            raise Exception("订单总支付金额为0，无法退款")

        # 按比例计算退款金额
        enterprise_refund_amount = round((enterprise_paid / total_paid) * total_refund_amount, 2)
        personal_refund_amount = round(total_refund_amount - enterprise_refund_amount, 2)

        logger.info(f"[混合支付退款] 企业退款: {enterprise_refund_amount}, 个人退款: {personal_refund_amount}")

        # 1. 处理企业部分退款
        if enterprise_refund_amount > 0:
            success = await refund_enterprise_payment(
                db, order_id, enterprise_refund_amount, out_refund_no, is_business_dining, order_item_id
            )
            if not success:
                raise Exception("企业部分退款失败")

        # 2. 处理个人部分退款
        if personal_refund_amount > 0:
            success = await refund_personal_payment(
                db, order_id, personal_refund_amount, out_refund_no, is_business_dining, order_item_id
            )
            if not success:
                raise Exception("个人部分退款失败")

        logger.info(f"[混合支付退款] 退款处理完成")
        return True

    except Exception as e:
        logger.error(f"[混合支付退款] 处理失败: {str(e)}")
        return False


async def refund_enterprise_payment(
        db: Session,
        order_id: int,
        refund_amount: float,
        out_refund_no: str,
        is_business_dining: bool,
        order_item_id: int
) -> bool:
    """退款企业支付部分到企业账户"""
    try:
        order = order_dao.get(db, order_id)
        if not order:
            raise Exception("订单不存在")
        order_id = order.id
        order_enterprise_paid_amount = order.enterprise_paid_amount
        logger.info(f"[企业退款] 开始处理，订单ID: {order_id}, 退款金额: {refund_amount}")

        # 使用DAO方法查找企业支付交易记录
        enterprise_transaction = account_transaction_dao.find_matching_enterprise_transaction(
            db, order_id, order_enterprise_paid_amount
        )

        if not enterprise_transaction:
            raise Exception("未找到匹配的企业支付交易记录")

        # 获取企业账户
        enterprise_account = enterprise_transaction.account
        if not enterprise_account:
            raise Exception("企业账户不存在")

        # 退款到企业账户
        enterprise_account.balance += refund_amount

        # 创建企业退款交易记录
        refund_transaction = AccountTransactionCreate(
            account_id=enterprise_account.id,
            order_id=order_id,
            transaction_type=TransactionType.REFUND,
            amount=refund_amount,
            description=f"{'混合支付订单退款(企业部分)' if is_business_dining else '混合支付订单部分退款(企业部分)'}：{order_id}-{order_item_id}",
            transaction_time=get_current_time()
        )
        account_transaction_dao.create(db, refund_transaction)

        logger.info(f"[企业退款] 处理完成，退款金额: {refund_amount}")
        return True

    except Exception as e:
        logger.error(f"[企业退款] 处理失败: {str(e)}")
        return False


async def refund_personal_payment(
        db: Session,
        order_id: int,
        refund_amount: float,
        out_refund_no: str,
        is_business_dining: bool,
        order_item_id: int
) -> bool:
    """退款个人支付部分（账户余额或微信支付）"""
    try:
        order = order_dao.get(db, order_id)
        if not order:
            raise Exception("订单不存在")
        order_id = order.id
        order_no = order.order_no
        order_user_id = order.user_id
        logger.info(f"[个人退款] 开始处理，订单ID: {order_id}, 退款金额: {refund_amount}")

        # 使用DAO方法检查个人支付方式
        personal_wx_payment = wx_payment_dao.get_personal_payment_by_order_no(db, order_no)

        if personal_wx_payment:
            # 个人部分是微信支付，需要调用微信退款
            logger.info(f"[个人退款] 检测到微信支付，调用微信退款，金额: {refund_amount}")

            personal_refund_no = f"{out_refund_no}_personal"
            refund_result = wechat_service.create_refund(
                personal_wx_payment.transaction_id,
                personal_refund_no,
                personal_wx_payment.total_amount,
                refund_amount,
                f"混合支付订单个人部分{'退款' if is_business_dining else '部分商品退款'}"
            )

            if not refund_result:
                raise Exception("个人微信支付退款失败")

            logger.info(f"[个人退款] 微信退款请求成功，等待回调")

        else:
            # 个人部分是账户余额支付
            logger.info(f"[个人退款] 检测到账户余额支付，直接退款到账户")

            # 获取用户账户
            accounts = account_dao.get_by_user_id(db, order_user_id)
            regular_account = next((account for account in accounts if account.type == AccountType.REGULAR), None)

            if not regular_account:
                raise Exception("用户账户不存在")

            # 退款到用户账户
            regular_account.balance += refund_amount

            # 创建个人退款交易记录
            refund_transaction = AccountTransactionCreate(
                account_id=regular_account.id,
                order_id=order_id,
                transaction_type=TransactionType.REFUND,
                amount=refund_amount,
                description=f"{'混合支付订单退款(个人部分)' if is_business_dining else '混合支付订单部分退款(个人部分)'}：{order_id}-{order_item_id}",
                transaction_time=get_current_time()
            )
            account_transaction_dao.create(db, refund_transaction)

        logger.info(f"[个人退款] 处理完成，退款金额: {refund_amount}")
        return True

    except Exception as e:
        logger.error(f"[个人退款] 处理失败: {str(e)}")
        return False
