import logging
from typing import Dict, Any

from fastapi import APIRouter, Depends

from app.schemas.callback import FeieyunCallbackRequest, FeieyunCallbackResponse, AudioEnum
from app.service.verify import verify_service
from app.core.deps import get_current_user, get_db
from sqlalchemy.orm import Session

feieyun_router = APIRouter()
logger = logging.getLogger(__name__)


@feieyun_router.post("/feieyun", response_model=FeieyunCallbackResponse)
async def feieyun_callback(
    request: FeieyunCallbackRequest,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    处理飞鹅云打印机回调
    
    Args:
        request: 飞鹅云回调请求数据
        db: 数据库会话对象
        
    Returns:
        Dict[str, Any]: 回调响应
    """
    try:
        logger.info(f"接收到飞鹅云回调: SN={request.sn}")

        # 调用核销方法，传入数据库会话
        success, _ = verify_service.verify_and_write_off(request.result, db)

        # 根据核销结果返回响应
        if success:
            return {
                "res": "SUCCESS",
                "audio": AudioEnum.WRITE_OFF_SUCCESS
            }
        else:
            return {
                "res": "FAILURE",
                "audio": AudioEnum.WRITE_OFF_FAILURE
            }
    except Exception as e:
        logger.error(f"处理飞鹅云回调出错: {str(e)}")
        return {
            "res": "FAILURE",
            "audio": AudioEnum.FAILURE
        }

