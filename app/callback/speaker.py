# 核心请求地址：https://api.yoyoiot.cn/拼接你的AppID的值/device/control/?sign=拼接你计算得到的签名&拼接时间戳也就是计算签名的时候用到的ts；
# 签名为md5(md5(你的开发者密码)拼接上面的ts时间戳的值)；ts为时间戳获取当前时间戳即可；也就是将开发者密码（AppSecret）进行一次MD5，然后将这个结果拼接上时间戳，再对整个拼接后的字符串做一次MD5
# 请求需要传递两个参数device和order:
# device[字符串]：设备唯一ID，可传多个[用,间隔]，可在控制台可查看，也可通过接口拉取
# order[json字符串]：下发的命令，例如：
# {"power":1}，一般为将通断器的线路接通
# {"power3":0}，一般为将开关或控制器的第3条线路关闭
# {"play:gbk:16":"你好，欢迎光临"}，让语音喇叭播报指定内容
# 相同产品类型的设备，命令相同，不同产品类型的命令，请到每个产品的商品页面查看
# 注意：一定要替换成正式的AppID和AppSecret，再根据实时的时间戳计算签名，请求一定需要device设备ID和order命令

import hashlib
import json
import requests
import time
from urllib.parse import urlencode
from app.utils.logger import logger

def calculate_md5(s):
    return hashlib.md5(s.encode()).hexdigest()

def send_request(data_str):
    app_id = "vtyVWcgruv"  # 替换为实际的 AppID
    app_secret = "yihesu1234567890"  # 替换为实际的 AppSecret
    ts = int(time.time())  # 获取当前时间戳（秒）

    # 计算签名 md5(md5(开发者密码)拼接上面的ts参数)
    sign = calculate_md5(calculate_md5(app_secret) + str(ts))

    # 构建 URL
    base_url = f"https://api.yoyoiot.cn/{app_id}/device/control/"
    params = {
        "sign": sign,
        "ts": ts
    }

    # 构建请求体数据
    device = "585222"  # 替换为实际的设备ID；可传多个[用,间隔]
    order = {"play:gbk:16": data_str}  # 替换为实际的命令

    request_body = {
        "device": device,
        "order": order
    }
    logger.info(f"Request Body: {request_body}")

    # 发送请求
    response = requests.post(
        base_url,
        params=params,
        json=request_body,
        headers={"Content-Type": "application/json"}
    )

    # 输出响应
    logger.info(f"Response Status Code: {response.status_code}")
    logger.info(f"Response Body: {response.text}")
