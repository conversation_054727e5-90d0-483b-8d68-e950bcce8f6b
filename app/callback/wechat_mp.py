import hashlib
import logging
import xml.etree.ElementTree as ET
from typing import Dict, Optional, Any
import base64
import requests
import json
import time

from fastapi import APIRouter, Query, Depends, Body
from fastapi.responses import PlainTextResponse
from starlette.requests import Request
from sqlalchemy.orm import Session
from app.db.session import <PERSON>Local
from pydantic import BaseModel

from app.core.config import settings
from app.utils.logger import logger
from app.core.scheduler import update_user_msg_state
from app.dao.user import personal_user_dao

wechat_mp_router = APIRouter()

# 获取服务号微信TOKEN
#token = settings.WECHAT_TOKEN_MP
# WECHAT_APPID = settings.WECHAT_APPID
# WECHAT_SECRET = settings.WECHAT_SECRET
token = '06VjurCW6i124n'
WECHAT_APPID = 'wxd80832a0c649a7d6'
WECHAT_SECRET = '1686d9024f2e3a41603fcc8a61deb8e9'

# 获取access_token的函数
def get_access_token() -> Optional[str]:
    """
    获取微信公众号的access_token

    Returns:
        Optional[str]: 成功返回access_token，失败返回None
    """
    try:
        token_url = f"{settings.WECHAT_OFFICIAL_DOMAIN}/cgi-bin/token?grant_type=client_credential&appid={WECHAT_APPID}&secret={WECHAT_SECRET}"
        token_response = requests.get(token_url)
        token_result = token_response.json()
        
        if 'access_token' not in token_result:
            logger.error(f"获取微信access_token失败: {token_result}")
            return None
            
        return token_result['access_token']
    except Exception as e:
        logger.error(f"获取access_token异常: {str(e)}")
        return None

# 获取用户UnionID的函数
def get_user_unionid(openid: str) -> Optional[Dict[str, Any]]:
    """
    获取微信用户的UnionID和其他用户信息
    
    Args:
        openid: 用户的OpenID
        
    Returns:
        Optional[Dict[str, Any]]: 成功返回用户信息字典，失败返回None
    """
    try:
        access_token = get_access_token()
        if not access_token:
            return None
            
        # 调用微信接口获取用户信息
        user_info_url = f"{settings.WECHAT_OFFICIAL_DOMAIN}/cgi-bin/user/info?access_token={access_token}&openid={openid}&lang=zh_CN"
        response = requests.get(user_info_url)
        user_info = response.json()
        
        if 'errcode' in user_info:
            logger.error(f"获取用户信息失败: {user_info}")
            return None
            
        logger.info(f"获取到用户信息: {user_info}")
        return user_info
    except Exception as e:
        logger.error(f"获取用户UnionID异常: {str(e)}")
        return None

@wechat_mp_router.get("/verify", response_class=PlainTextResponse)
async def wechat_mp_verify(
    signature: str = Query(..., description="微信加密签名"),
    timestamp: str = Query(..., description="时间戳"),
    nonce: str = Query(..., description="随机数"),
    echostr: str = Query(..., description="随机字符串")
) -> str:
    """
    处理微信服务号服务器配置验证请求
    
    验证流程：
    1. 将token、timestamp、nonce三个参数进行字典序排序
    2. 将三个参数字符串拼接成一个字符串进行sha1加密
    3. 开发者获得加密后的字符串可与signature对比，标识该请求来源于微信
    4. 如果验证成功，原样返回echostr参数内容
    
    Args:
        signature: 微信加密签名
        timestamp: 时间戳
        nonce: 随机数
        echostr: 随机字符串
    
    Returns:
        str: 如果验证成功，返回echostr；否则返回空字符串
    """
    logger.info(f"收到微信服务号验证请求: signature={signature}, timestamp={timestamp}, nonce={nonce}")
    
    # 1. 将token、timestamp、nonce三个参数进行字典序排序
    temp_list = [token, timestamp, nonce]
    temp_list.sort()
    
    # 2. 将三个参数字符串拼接成一个字符串进行sha1加密
    temp_str = ''.join(temp_list)
    sign = hashlib.sha1(temp_str.encode('utf-8')).hexdigest()
    
    # 3. 将加密后的字符串与signature对比
    if sign == signature:
        logger.info("微信服务号验证成功")
        return echostr
    else:
        logger.warning(f"微信服务号验证失败: 计算签名={sign}, 微信签名={signature}")
        return ""


async def parse_xml_message(request: Request) -> Dict[str, Any]:
    """
    解析微信XML消息
    
    Args:
        request: HTTP请求对象
    
    Returns:
        Dict: 解析后的消息字典
    """
    body = await request.body()
    xml_data = body.decode('utf-8')
    logger.info(f"收到微信消息: {xml_data}")
    
    try:
        root = ET.fromstring(xml_data)
        message = {}
        for child in root:
            message[child.tag] = child.text
        return message
    except Exception as e:
        logger.error(f"解析微信XML消息失败: {e}")
        return {}


def generate_text_reply(to_user: str, from_user: str, content: str) -> str:
    """
    生成文本回复消息
    
    Args:
        to_user: 接收方账号（用户OpenID）
        from_user: 开发者微信号
        content: 回复内容
    
    Returns:
        str: 回复消息的XML字符串
    """
    create_time = int(time.time())
    
    return f"""<xml>
    <ToUserName><![CDATA[{to_user}]]></ToUserName>
    <FromUserName><![CDATA[{from_user}]]></FromUserName>
    <CreateTime>{create_time}</CreateTime>
    <MsgType><![CDATA[text]]></MsgType>
    <Content><![CDATA[{content}]]></Content>
    </xml>"""


@wechat_mp_router.post("/message")
async def wechat_mp_message(request: Request):
    """
    处理微信服务号的消息和事件推送
    
    Args:
        request: HTTP请求对象
    
    Returns:
        响应内容
    """
    db = SessionLocal()
    message = await parse_xml_message(request)
    if not message:
        return PlainTextResponse("success")
    
    msg_type = message.get('MsgType')
    from_user = message.get('FromUserName')
    to_user = message.get('ToUserName')
    
    # 记录接收到的消息
    logger.info(f"接收到微信消息: 类型={msg_type}, 发送方={from_user}")
    
    # 根据消息类型处理
    if msg_type == 'text':
        # 文本消息处理
        content = message.get('Content', '')
        logger.info(f"收到文本消息: {content}")
        # content = "取消订阅:eWhzb2JwR3I3V0U4Sk13QUNrS1llT20tUHpWMzlmaw==" or "绑定订阅:eWhzb2JwR3I3V0U4Sk13QUNrS1llT20tUHpWMzlmaw=="
        if content.startswith("取消订阅"):
            try:
                hx_wechat_id = content.split(":")[1]
                # 使用base64模块解码
                str_wechat_id = base64.b64decode(hx_wechat_id).decode('utf-8')
                logger.info(f"解码后的wechat_id: {str_wechat_id}")
                # 检查前三位是否为"yhs"，如果是则去掉，否则保持原样
                if str_wechat_id.startswith("yhs"):
                    wechat_id = str_wechat_id[3:] 
                    logger.info(f"去掉yhs后的wechat_id: {wechat_id}")
                    user = personal_user_dao.get_by_openid(db, wechat_id)
                    if user and user.id:
                        update_user_msg_state(db, user.id, 0, from_user)
                        reply_content = "已取消订阅"
                    else:
                        reply_content = "取消订阅失败, 未找到绑定用户"
                else:
                    reply_content = "取消订阅失败, 未找到绑定用户"
            except Exception as e:
                logger.error(f"取消订阅失败: {e}")
                reply_content = "取消订阅失败, 未找到绑定用户"
        elif content.startswith("绑定订阅"):
            try:
                hx_wechat_id = content.split(":")[1]
                # 使用base64模块解码
                str_wechat_id = base64.b64decode(hx_wechat_id).decode('utf-8')
                logger.info(f"解码后的wechat_id: {str_wechat_id}")
                # 检查前三位是否为"yhs"，如果是则去掉，否则保持原样
                if str_wechat_id.startswith("yhs"):
                    wechat_id = str_wechat_id[3:] 
                    logger.info(f"去掉yhs后的wechat_id: {wechat_id}")
                    user = personal_user_dao.get_by_openid(db, wechat_id)
                    if user and user.id:
                        update_user_msg_state(db, user.id, 1, from_user)
                        reply_content = "已绑定订阅"
                    else:
                        reply_content = "绑定订阅失败, 未找到绑定用户"
                else:
                    reply_content = "绑定订阅失败, 未找到绑定用户"
            except Exception as e:
                logger.error(f"绑定订阅失败: {e}")
                reply_content = "绑定订阅失败, 未找到绑定用户"
        else:
            reply_content = "已收到您的消息: " + content

        return PlainTextResponse(
            generate_text_reply(from_user, to_user, reply_content),
            media_type="application/xml"
        )
    
    elif msg_type == 'image':
        # 图片消息处理
        pic_url = message.get('PicUrl')
        media_id = message.get('MediaId')
        logger.info(f"收到图片消息: PicUrl={pic_url}, MediaId={media_id}")
        
        reply_content = "感谢您发送图片，我们已收到。"
        return PlainTextResponse(
            generate_text_reply(from_user, to_user, reply_content),
            media_type="application/xml"
        )
    
    elif msg_type == 'voice':
        # 语音消息处理
        media_id = message.get('MediaId')
        format_type = message.get('Format')
        logger.info(f"收到语音消息: MediaId={media_id}, Format={format_type}")
        
        reply_content = "感谢您发送语音，我们已收到。"
        return PlainTextResponse(
            generate_text_reply(from_user, to_user, reply_content),
            media_type="application/xml"
        )
    
    elif msg_type == 'video' or msg_type == 'shortvideo':
        # 视频消息处理
        media_id = message.get('MediaId')
        thumb_media_id = message.get('ThumbMediaId')
        logger.info(f"收到视频消息: MediaId={media_id}, ThumbMediaId={thumb_media_id}")
        
        reply_content = "感谢您发送视频，我们已收到。"
        return PlainTextResponse(
            generate_text_reply(from_user, to_user, reply_content),
            media_type="application/xml"
        )
    
    elif msg_type == 'location':
        # 地理位置消息处理
        location_x = message.get('Location_X')
        location_y = message.get('Location_Y')
        scale = message.get('Scale')
        label = message.get('Label')
        logger.info(f"收到地理位置消息: Location_X={location_x}, Location_Y={location_y}, Label={label}")
        
        reply_content = f"您发送的位置是: {label}"
        return PlainTextResponse(
            generate_text_reply(from_user, to_user, reply_content),
            media_type="application/xml"
        )
    
    elif msg_type == 'link':
        # 链接消息处理
        title = message.get('Title')
        description = message.get('Description')
        url = message.get('Url')
        logger.info(f"收到链接消息: Title={title}, Url={url}")
        
        reply_content = f"您发送的链接是: {title}"
        return PlainTextResponse(
            generate_text_reply(from_user, to_user, reply_content),
            media_type="application/xml"
        )
    
    elif msg_type == 'event':
        # 事件消息处理
        event = message.get('Event')
        logger.info(f"收到事件推送: Event={event}")
        
        if event.lower() == 'subscribe':
            # 关注事件
            # 获取用户UnionID和其他信息
            user_info = get_user_unionid(from_user)
            
            if user_info:
                # 获取成功，记录用户信息
                unionid = user_info.get('unionid')
                nickname = user_info.get('nickname')
                subscribe_time = user_info.get('subscribe_time')
                
                logger.info(f"用户关注: OpenID={from_user}, UnionID={unionid}, 昵称={nickname}, 关注时间={subscribe_time}")
                if unionid:
                    # 检查是否小程序用户，是则获取unionid来进行绑定。
                    user = personal_user_dao.get_by_unionid(db, unionid)
                    if user and user.id:
                        update_user_msg_state(db, user.id, 1, from_user)
                        logger.info(f"用户关注，已找到绑定用户: {unionid}, 更新消息状态为1, 更新公众号用户ID为: {from_user}")
                    else:
                        logger.info(f"用户关注，未获取到 unionid 数据, 更新公众号用户ID为: {from_user}")
                else:
                    logger.info(f"用户关注，未找到绑定用户: {unionid}, 更新公众号用户ID为: {from_user}")
                reply_content = f"感谢您的关注，{nickname}！"
            else:
                # 获取失败，返回通用欢迎消息
                logger.warning(f"获取用户信息失败，OpenID={from_user}")
                reply_content = "感谢您的关注！"
                
            return PlainTextResponse(
                generate_text_reply(from_user, to_user, reply_content),
                media_type="application/xml"
            )
        elif event.lower() == 'unsubscribe':
            # 取消关注事件
            logger.info(f"用户 {from_user} 取消了关注")
            return PlainTextResponse("success")
    
    # 默认回复
    return PlainTextResponse("success")


@wechat_mp_router.get("/message", response_class=PlainTextResponse)
async def wechat_mp_message_get(
    signature: str = Query(..., description="微信加密签名"),
    timestamp: str = Query(..., description="时间戳"),
    nonce: str = Query(..., description="随机数"),
    echostr: str = Query(..., description="随机字符串")
) -> str:
    """处理微信服务号消息验证"""
    logger.info(f"收到微信服务号验证请求: signature={signature}, timestamp={timestamp}, nonce={nonce}")
    
    # 1. 将token、timestamp、nonce三个参数进行字典序排序
    temp_list = [token, timestamp, nonce]
    temp_list.sort()
    
    # 2. 将三个参数字符串拼接成一个字符串进行sha1加密
    temp_str = ''.join(temp_list)
    sign = hashlib.sha1(temp_str.encode('utf-8')).hexdigest()
    
    # 3. 将加密后的字符串与signature对比
    if sign == signature:
        logger.info("微信服务号验证成功")
        return echostr
    else:
        logger.warning(f"微信服务号验证失败: 计算签名={sign}, 微信签名={signature}")
        return ""


# 模板消息请求模型
class TemplateMessageRequest(BaseModel):
    openid: str
    template_id: str
    data: Dict[str, Dict[str, str]]
    url: Optional[str] = None
    miniprogram: Optional[Dict[str, str]] = None

def send_template_message(
    openid: str, 
    template_id: str, 
    data: Dict[str, Dict[str, str]], 
    url: Optional[str] = None, 
    miniprogram: Optional[Dict[str, str]] = None
) -> bool:
    """
    发送微信服务号模板消息
    
    Args:
        openid: 用户的OpenID
        template_id: 模板ID
        data: 模板数据，格式为 {"first": {"value": "xxx", "color": "#173177"}, ...}
        url: 点击消息跳转的链接，可选
        miniprogram: 跳转小程序的配置，格式为 {"appid": "xxx", "pagepath": "xxx"}，可选
        
    Returns:
        bool: 是否发送成功
    """
    try:
        # 获取access_token
        access_token = get_access_token()
        if not access_token:
            return False
        
        # 构建请求数据
        message_data = {
            "touser": openid,
            "template_id": template_id,
            "data": data
        }
        
        # 添加可选参数
        if url:
            message_data["url"] = url
            
        if miniprogram:
            message_data["miniprogram"] = miniprogram
        
        # 发送模板消息
        send_url = f"{settings.WECHAT_OFFICIAL_DOMAIN}/cgi-bin/message/template/send?access_token={access_token}"
        response = requests.post(send_url, json=message_data)
        result = response.json()
        
        if result.get('errcode') == 0:
            logger.info(f"模板消息发送成功: {result}")
            return True
        else:
            logger.error(f"模板消息发送失败: {result}")
            return False
            
    except Exception as e:
        logger.error(f"发送模板消息异常: {str(e)}")
        return False

@wechat_mp_router.post("/send_template")
async def send_template_message_api(request: TemplateMessageRequest):
    """
    发送微信服务号模板消息
    
    Args:
        request: 模板消息请求
        
    Returns:
        发送结果
    """
    try:
        # 调用发送模板消息函数
        result = send_template_message(
            request.openid,
            request.template_id,
            request.data,
            request.url,
            request.miniprogram
        )
        
        if result:
            return {"message": "模板消息发送成功", "status": 200}
        else:
            return {"message": "模板消息发送失败", "status": 500}
            
    except Exception as e:
        logger.error(f"发送模板消息API异常: {str(e)}")
        return {"message": f"发送模板消息异常: {str(e)}", "status": 500}

