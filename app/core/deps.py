from typing import Generator, Optional, List
from fastapi import Depends, HTTPException, status, Security, Request
from fastapi.security import OAuth2<PERSON><PERSON>wordBearer, SecurityScopes
from sqlalchemy.orm import Session
from jose import JWTError, jwt
from pydantic import ValidationError

from app.db.session import SessionLocal
from app.models.user import User
from app.models.admin import Admin
from app.core.config import settings
from app.schemas.token import TokenPayload
from app.dao import admin as admin_crud

oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/auth/login",
    scopes={
        "admin": "管理员权限",
        "user": "普通用户权限"
    }
)


def get_db() -> Generator:
    """获取数据库会话"""
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()


async def get_current_user(
        token: str = Depends(oauth2_scheme),
        db: Session = Depends(get_db)
) -> User:
    """获取当前登录用户"""
    authenticate_value = f"Bearer"

    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证凭据",
        headers={"WWW-Authenticate": authenticate_value},
    )

    try:
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        token_scopes = payload.get("scopes", [])
        token_data = TokenPayload(sub=username, scopes=token_scopes)
    except (JWTError, ValidationError):
        raise credentials_exception

    user = db.query(User).filter(User.username == username).first()
    if user is None:
        raise credentials_exception

    # 检查是否有user范围
    if "user" not in token_data.scopes:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足",
            headers={"WWW-Authenticate": authenticate_value},
        )

    return user


async def get_current_admin(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> Admin:
    """获取当前管理员"""
    authenticate_value = f"Bearer"

    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无法验证管理员凭据",
        headers={"WWW-Authenticate": authenticate_value},
    )

    try:
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        admin_id: str = payload.get("sub")
        if admin_id is None:
            raise credentials_exception

        token_scopes = payload.get("scopes", [])
        company_type = payload.get("company_type", 2)  # 默认为乙禾公司(2)
        company_id = payload.get("company_id", 0)      # 默认为乙禾公司ID(0)

        token_data = TokenPayload(
            sub=admin_id,
            scopes=token_scopes,
            company_type=company_type,
            company_id=company_id
        )
    except (JWTError, ValidationError):
        raise credentials_exception

    admin = admin_crud.get_admin(db=db, admin_id=int(admin_id))
    if admin is None:
        raise credentials_exception

    # 检查是否有admin范围
    if "admin" not in token_data.scopes:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="管理员权限不足",
            headers={"WWW-Authenticate": authenticate_value},
        )

    # 为管理员对象添加额外的公司信息
    admin.company_type = token_data.company_type
    admin.company_id = token_data.company_id

    return admin


def check_permissions(required_permissions: List[str]):
    """检查权限依赖"""
    async def permissions_dependency(
        current_admin: Admin = Depends(get_current_admin),
        db: Session = Depends(get_db)
    ):
        # 获取管理员的所有权限
        admin_permissions = admin_crud.get_admin_permissions(db=db, admin_id=current_admin.id)

        # 检查是否具有所有必需的权限
        for permission in required_permissions:
            if permission not in admin_permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足: 需要 {permission} 权限"
                )
        return current_admin
    return permissions_dependency


def get_api_auth_deps(required_permissions=None):
    """
    获取API身份验证依赖项

    参数:
        required_permissions: 可选的权限列表，用于检查用户是否有权访问

    返回:
        通用的身份验证依赖项，可以在路由器上使用
    """
    if required_permissions:
        return [Depends(get_current_admin), Depends(check_permissions(required_permissions))]
    else:
        return [Depends(get_current_admin)]


def require_permissions(required_permissions: List[str]):
    """
    创建一个路由依赖，用于检查用户是否具有所需权限

    用法示例:
    ```
    @router.get("/some-route", dependencies=[Depends(require_permissions(["read:items", "write:items"]))])
    def some_func():
        pass
    ```
    """
    return [Depends(check_permissions(required_permissions))]


def admin_required():
    """
    创建一个仅要求管理员身份的依赖

    用法示例:
    ```
    @router.get("/admin-only-route", dependencies=[Depends(admin_required())])
    def admin_func():
        pass
    ```
    """
    return [Depends(get_current_admin)]


def get_rbac_auth_deps(required_permissions: Optional[List[str]] = None):
    """
    获取带有RBAC权限检查的API身份验证依赖项
    
    参数:
        required_permissions: 可选的权限列表，用于检查用户是否有权访问
    
    返回:
        包含RBAC权限检查的身份验证依赖项
    """
    if required_permissions:
        return [Depends(get_current_admin), Depends(check_permissions(required_permissions))]
    else:
        # 导入RBAC权限检查依赖
        from app.core.rbac import PermissionDep
        return [PermissionDep]


def get_api_auth_deps_with_rbac():
    """
    获取带有自动RBAC权限检查的API身份验证依赖项
    
    返回:
        包含自动权限路径映射检查的身份验证依赖项
    """
    from app.core.rbac import PermissionDep
    return [PermissionDep]
