"""
Event System Core Configuration

This module provides initialization and configuration for the event system.
"""

from contextlib import asynccontextmanager

from app.events.bus import get_event_bus
from app.events.models import SystemEvent, SystemEventAction
from app.utils.logger import logger


async def initialize_event_system() -> None:
    """
    Initialize the event system.
    
    This function should be called during application startup to
    set up the event bus and register default handlers.
    """
    logger.info("Initializing event system...")

    # Get the event bus and start it
    event_bus = get_event_bus()
    await event_bus.start()

    # Register default system event handlers
    await _register_default_handlers()

    # Publish system startup event
    startup_event = SystemEvent(
        action=SystemEventAction.STARTUP,
        component="event_system",
        message="Event system initialized successfully"
    )
    await event_bus.publish(startup_event)

    logger.info("Event system initialized successfully")


async def shutdown_event_system() -> None:
    """
    Shutdown the event system.
    
    This function should be called during application shutdown to
    properly clean up the event bus.
    """
    logger.info("Shutting down event system...")

    # Publish system shutdown event
    event_bus = get_event_bus()
    shutdown_event = SystemEvent(
        action=SystemEventAction.SHUTDOWN,
        component="event_system",
        message="Event system shutting down"
    )
    await event_bus.publish(shutdown_event)

    # Stop the event bus
    await event_bus.stop()

    logger.info("Event system shutdown complete")


async def _register_default_handlers() -> None:
    """Register default event handlers for system events."""
    from app.events.handlers import event_handler
    from app.events.models import SystemEvent

    @event_handler(SystemEvent)
    async def log_system_events(event: SystemEvent):
        """Default handler to log all system events."""
        log_level = getattr(logger, event.level.lower(), logger.info)
        log_level(f"System Event [{event.action}] {event.component}: {event.message}")

    # Auto-import all event handler modules to register them
    try:
        import app.handlers  # This will trigger auto-import of all handlers
        logger.info("成功自动导入所有事件处理器")
    except ImportError as e:
        logger.warning(f"自动导入事件处理器失败: {e}")
    except Exception as e:
        logger.error(f"自动导入事件处理器失败: {e}")


@asynccontextmanager
async def event_system_lifespan():
    """
    Context manager for event system lifecycle management.
    
    Use this in FastAPI lifespan events to properly initialize
    and shutdown the event system.
    """
    await initialize_event_system()
    try:
        yield
    finally:
        await shutdown_event_system()


# Utility functions for common event operations

async def publish_user_event(
        action: str,
        user_id: int,
        user_type: str = None,
        username: str = None,
        **additional_data
) -> None:
    """
    Utility function to publish user events.
    
    Args:
        action: The user action that occurred
        user_id: ID of the user
        user_type: Type of user (optional)
        username: Username (optional)
        **additional_data: Additional event data
    """
    from app.events.models import UserEvent, UserEventAction

    event = UserEvent(
        action=UserEventAction(action),
        user_id=user_id,
        user_type=user_type,
        username=username,
        additional_data=additional_data or None,
        source="user_service"
    )

    event_bus = get_event_bus()
    await event_bus.publish(event)


async def publish_order_event(
        action: str,
        order_id: int,
        order_no: str = None,
        user_id: int = None,
        amount: float = None,
        status: str = None,
        payment_status: str = None,
        **additional_data
) -> None:
    """
    Utility function to publish order events.
    
    Args:
        action: The order action that occurred
        order_id: ID of the order
        order_no: Order number (optional)
        user_id: ID of the user who owns the order (optional)
        amount: Order amount (optional)
        status: Order status (optional)
        payment_status: Payment status (optional)
        **additional_data: Additional event data
    """
    from app.events.models import OrderEvent, OrderEventAction

    event = OrderEvent(
        action=OrderEventAction(action),
        order_id=order_id,
        order_no=order_no,
        user_id=user_id,
        amount=amount,
        status=status,
        payment_status=payment_status,
        additional_data=additional_data or None,
        source="order_service"
    )

    event_bus = get_event_bus()
    await event_bus.publish(event)


async def publish_notification_event(
        action: str,
        channel: str,
        content: str,
        recipient_id: int = None,
        recipient_type: str = None,
        template_id: str = None,
        subject: str = None,
        priority: int = 1,
        **data
) -> None:
    """
    Utility function to publish notification events.
    
    Args:
        action: The notification action
        channel: Notification channel (email, sms, wechat, push)
        content: Notification content
        recipient_id: ID of the recipient (optional)
        recipient_type: Type of recipient (optional)
        template_id: Template ID (optional)
        subject: Notification subject (optional)
        priority: Priority level (1-4)
        **data: Additional notification data
    """
    from app.events.models import NotificationEvent, NotificationEventAction

    event = NotificationEvent(
        action=NotificationEventAction(action),
        recipient_id=recipient_id,
        recipient_type=recipient_type,
        channel=channel,
        template_id=template_id,
        subject=subject,
        content=content,
        data=data or None,
        priority=priority,
        source="notification_service"
    )

    event_bus = get_event_bus()
    await event_bus.publish(event)
