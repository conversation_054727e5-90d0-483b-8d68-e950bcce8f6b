"""
RBAC权限控制模块
实现基于角色的访问控制(Role-Based Access Control)
"""
from typing import List, Dict, Optional, Callable, Any
from functools import wraps
from fastapi import HTTPException, Request, Depends
from sqlalchemy.orm import Session
from app.core.deps import get_current_admin, get_db
from app.dao.admin import get_admin_permissions
from app.models.admin import Admin
import re


class PermissionManager:
    """权限管理器"""
    
    def __init__(self):
        # API路径到权限码的映射
        self._permission_mapping: Dict[str, Dict[str, str]] = {}
        # 排除权限检查的路径模式
        self._excluded_patterns: List[str] = [
            r'^/api/v1/auth/.*',  # 认证相关接口
            r'^/api/v1/wx/.*',    # 微信小程序接口
        ]
        self._init_permission_mapping()
    
    def _init_permission_mapping(self):
        """初始化权限映射"""
        # 管理员模块权限
        self._permission_mapping['/api/v1/admin'] = {
            'GET': 'admin:read',
            'POST': 'admin:create',
            'PUT': 'admin:update', 
            'DELETE': 'admin:delete'
        }
        
        # 角色模块权限
        self._permission_mapping['/api/v1/admin/roles'] = {
            'GET': 'role:read',
            'POST': 'role:create',
            'PUT': 'role:update',
            'DELETE': 'role:delete'
        }
        
        # 权限模块权限
        self._permission_mapping['/api/v1/admin/permissions'] = {
            'GET': 'permission:read',
            'POST': 'permission:create', 
            'PUT': 'permission:update',
            'DELETE': 'permission:delete'
        }
        
        # 用户模块权限
        self._permission_mapping['/api/v1/user'] = {
            'GET': 'user:read',
            'POST': 'user:create',
            'PUT': 'user:update',
            'DELETE': 'user:delete'
        }
        
        # 商品模块权限
        self._permission_mapping['/api/v1/product'] = {
            'GET': 'product:read',
            'POST': 'product:create',
            'PUT': 'product:update',
            'DELETE': 'product:delete'
        }
        
        # 商品分类权限
        self._permission_mapping['/api/v1/category'] = {
            'GET': 'category:read',
            'POST': 'category:create',
            'PUT': 'category:update',
            'DELETE': 'category:delete'
        }
        
        # 内容模块权限
        self._permission_mapping['/api/v1/content'] = {
            'GET': 'content:read',
            'POST': 'content:create',
            'PUT': 'content:update',
            'DELETE': 'content:delete'
        }
        
        # 文件模块权限
        self._permission_mapping['/api/v1/file'] = {
            'GET': 'file:read',
            'POST': 'file:upload',
            'DELETE': 'file:delete'
        }
        
        # 价格模块权限
        self._permission_mapping['/api/v1/pricing'] = {
            'GET': 'pricing:read',
            'POST': 'pricing:create',
            'PUT': 'pricing:update',
            'DELETE': 'pricing:delete'
        }
        
        # 规则模块权限
        self._permission_mapping['/api/v1/rule'] = {
            'GET': 'rule:read',
            'POST': 'rule:create',
            'PUT': 'rule:update',
            'DELETE': 'rule:delete'
        }
        
        # 菜单模块权限
        self._permission_mapping['/api/v1/menu'] = {
            'GET': 'menu:read',
            'POST': 'menu:create',
            'PUT': 'menu:update',
            'DELETE': 'menu:delete'
        }
        
        # 标签模块权限
        self._permission_mapping['/api/v1/tag'] = {
            'GET': 'tag:read',
            'POST': 'tag:create',
            'PUT': 'tag:update',
            'DELETE': 'tag:delete'
        }
        
        # 订单模块权限
        self._permission_mapping['/api/v1/order'] = {
            'GET': 'order:read',
            'POST': 'order:create',
            'PUT': 'order:update',
            'PATCH': 'order:update',
            'DELETE': 'order:delete'
        }
        
        # 账户模块权限
        self._permission_mapping['/api/v1/account'] = {
            'GET': 'account:read',
            'POST': 'account:create',
            'PUT': 'account:update',
            'DELETE': 'account:delete'
        }
        
        # 预订模块权限
        self._permission_mapping['/api/v1/reservation'] = {
            'GET': 'reservation:read',
            'POST': 'reservation:create',
            'PUT': 'reservation:update',
            'DELETE': 'reservation:delete'
        }
        
        self._permission_mapping['/api/v1/buffet-reservation'] = {
            'GET': 'buffet_reservation:read',
            'POST': 'buffet_reservation:create',
            'PUT': 'buffet_reservation:update',
            'DELETE': 'buffet_reservation:delete'
        }
        
        self._permission_mapping['/api/v1/biz-reservation'] = {
            'GET': 'biz_reservation:read',
            'POST': 'biz_reservation:create',
            'PUT': 'biz_reservation:update',
            'DELETE': 'biz_reservation:delete'
        }
        
        # 审批模块权限
        self._permission_mapping['/api/v1/approval'] = {
            'GET': 'approval:read',
            'POST': 'approval:create',
            'PUT': 'approval:update',
            'DELETE': 'approval:delete'
        }
        
        # 支付模块权限
        self._permission_mapping['/api/v1/payment'] = {
            'GET': 'payment:read',
            'POST': 'payment:create'
        }
        
        # 报表模块权限
        self._permission_mapping['/api/v1/report'] = {
            'GET': 'report:read'
        }
        
        # 赠送模块权限
        self._permission_mapping['/api/v1/gift'] = {
            'GET': 'gift:read',
            'POST': 'gift:create',
            'PUT': 'gift:update',
            'DELETE': 'gift:delete'
        }
        
        # 优惠券模块权限
        self._permission_mapping['/api/v1/coupon'] = {
            'GET': 'coupon:read',
            'POST': 'coupon:create',
            'PUT': 'coupon:update',
            'DELETE': 'coupon:delete'
        }
    
    def is_path_excluded(self, path: str) -> bool:
        """检查路径是否排除权限检查"""
        for pattern in self._excluded_patterns:
            if re.match(pattern, path):
                return True
        return False
    
    def get_required_permission(self, path: str, method: str) -> Optional[str]:
        """根据路径和HTTP方法获取所需权限"""
        if self.is_path_excluded(path):
            return None
            
        # 寻找最匹配的路径
        matched_path = None
        for api_path in self._permission_mapping.keys():
            if path.startswith(api_path):
                if matched_path is None or len(api_path) > len(matched_path):
                    matched_path = api_path
        
        if matched_path and method.upper() in self._permission_mapping[matched_path]:
            return self._permission_mapping[matched_path][method.upper()]
        
        return None
    
    def add_permission_mapping(self, path: str, method_permissions: Dict[str, str]):
        """添加权限映射"""
        self._permission_mapping[path] = method_permissions
    
    def check_user_permission(self, admin_permissions: List[str], required_permission: str) -> bool:
        """检查用户是否具有所需权限"""
        return required_permission in admin_permissions


# 全局权限管理器实例
permission_manager = PermissionManager()


def require_permission(permission_code: str):
    """
    权限装饰器，用于标记路由需要的权限
    
    Args:
        permission_code: 权限代码
    
    Usage:
        @require_permission("product:create")
        @router.post("/products")
        def create_product():
            pass
    """
    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取依赖注入的参数
            admin = kwargs.get('current_admin')
            db = kwargs.get('db')
            
            if admin and db:
                admin_permissions = get_admin_permissions(db, admin.id)
                if not permission_manager.check_user_permission(admin_permissions, permission_code):
                    raise HTTPException(
                        status_code=403,
                        detail=f"权限不足，需要权限: {permission_code}"
                    )
            
            return await func(*args, **kwargs) if hasattr(func, '__await__') else func(*args, **kwargs)
        return wrapper
    return decorator


async def check_api_permission(
    request: Request,
    current_admin: Admin = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """
    API权限检查依赖
    
    Args:
        request: FastAPI请求对象
        current_admin: 当前管理员
        db: 数据库会话
    
    Raises:
        HTTPException: 当权限不足时抛出403异常
    """
    path = str(request.url.path)
    method = request.method
    
    # 检查是否为排除路径
    if permission_manager.is_path_excluded(path):
        return current_admin
    
    # 获取所需权限
    required_permission = permission_manager.get_required_permission(path, method)
    
    if required_permission:
        # 获取管理员权限
        admin_permissions = get_admin_permissions(db, current_admin.id)
        
        # 检查权限
        if not permission_manager.check_user_permission(admin_permissions, required_permission):
            raise HTTPException(
                status_code=403,
                detail=f"权限不足，无法访问 {method} {path}，需要权限: {required_permission}"
            )
    
    return current_admin


# 权限依赖，用于路由级别的权限控制
PermissionDep = Depends(check_api_permission) 