from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
import asyncio

from app.db.session import SessionLocal
from app.utils.logger import logger
from app.models.order import Order, OrderStatus
from app.models.user import PersonalUser
from app.models.reservation import ReservationRequest, ReservationStatus, ReservationType
from app.api.v1.wechat_mini_app.message import send_subscribe_message
from app.tasks.notifications.buffet import send_daily_buffet_statistic_notification
from app.core.config import settings

# 创建调度器实例
scheduler = AsyncIOScheduler()

# 定时任务：读取数据库中的订单信息
async def read_orders_task():
    """
    定时读取数据库中的订单信息
    此任务会统计各状态的订单数量并记录到日志
    """
    logger.info(f"开始执行定时任务：读取订单信息 - {datetime.now()}")
    
    db = SessionLocal()
    try:
        # 获取所有订单
        orders = db.query(Order).all()
        
        # 统计各状态订单数量
        status_counts = {}
        for status in OrderStatus:
            status_counts[status.value] = 0
            
        for order in orders:
            if order.status in status_counts:
                status_counts[order.status.value] += 1
        
        # 记录统计结果
        logger.info(f"订单统计结果: 总订单数: {len(orders)}")
        for status, count in status_counts.items():
            logger.info(f"状态 {status}: {count} 个订单")
            
        # 查询最近创建的5个订单
        recent_orders = db.query(Order).order_by(Order.created_at.desc()).limit(5).all()
        logger.info("最近创建的订单:")
        for order in recent_orders:
            logger.info(f"订单ID: {order.id}, 订单号: {order.order_no}, 状态: {order.status.value}, 创建时间: {order.created_at}")
            
    except Exception as e:
        logger.error(f"定时任务执行出错: {str(e)}")
    finally:
        db.close()

# 更新用户消息状态的函数
def update_user_msg_state(db: Session, user_id: int, msg_status: int, from_user: str) -> bool:
    """
    将用户的消息订阅状态设置为0
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        
    Returns:
        bool: 更新是否成功
    """
    try:
        user = db.query(PersonalUser).filter(PersonalUser.id == user_id).first()
        if user:
            user.msg_status = msg_status
            if msg_status == 1:
                user.gzh_openid = from_user
            db.commit()
            logger.info(f"已将用户 {user_id} 的消息订阅状态更新为{msg_status}")
            return True
        else:
            logger.warning(f"未找到用户 {user_id}")
            return False
    except Exception as e:
        logger.error(f"更新用户 {user_id} 消息状态时出错: {str(e)}")
        db.rollback()
        return False

# 新增定时任务：发送预订提醒消息
async def send_reservation_reminders_task():
    """
    发送预订提醒消息
    1. 获取 PersonalUser 表 msg_status 为 1 的用户 id 和 wechat_id
    2. 获取 ReservationRequest 表中这些用户第二天有预订且状态为 PAID_FULL 的订单
    3. 发送提醒消息
    4. 发送后将用户的 msg_status 设置为 0

    注意：周五和周六不发送提醒
    """
    # 检查是否启用通知功能
    if not settings.ENABLE_NOTIFICATION:
        logger.info("通知功能已禁用，跳过发送预订提醒消息")
        return
    
    logger.info(f"开始执行定时任务：发送预订提醒消息 - {datetime.now()}")
    
    # 检查今天是否为周五或周六（weekday返回0-6，分别代表周一到周日）
    today_weekday = datetime.now().weekday()
    if today_weekday in [4, 5]:  # 4是周五，5是周六
        logger.info("今天是周五或周六，跳过发送预订提醒")
        return

    db = SessionLocal()
    try:
        # 1. 获取 msg_status 为 1 的用户
        users_with_notifications = db.query(PersonalUser).filter(
            PersonalUser.msg_status == 1,
            PersonalUser.wechat_id.isnot(None)  # 确保有微信ID
        ).all()
        
        if not users_with_notifications:
            logger.info("没有找到需要发送通知的用户")
            return
            
        logger.info(f"找到 {len(users_with_notifications)} 个需要发送通知的用户")

        from app.callback.wechat_mp import send_template_message

        # 计算明天的日期范围
        tomorrow = datetime.now() + timedelta(days=1)
        tomorrow_start = datetime(tomorrow.year, tomorrow.month, tomorrow.day, 0, 0, 0)
        tomorrow_end = datetime(tomorrow.year, tomorrow.month, tomorrow.day, 23, 59, 59)
        
        # 订阅消息模板ID
        template_id = 'uMsj6o9VGLA7t6Dv1EVbq_xbCBmu2sncEldk8KuFNmM'
        
        # 遍历需要通知的用户
        for user in users_with_notifications:
            # 2. 查询用户明天的预订
            reservations = db.query(ReservationRequest).filter(
                ReservationRequest.user_id == user.id,
                ReservationRequest.status == ReservationStatus.PAID_FULL,
                ReservationRequest.type != ReservationType.BIZ_DINING_RESERVATION,
                ReservationRequest.dining_start_time >= tomorrow_start,
                ReservationRequest.dining_start_time <= tomorrow_end
            ).all()
            
            if reservations:
                logger.info(f"用户 {user.id} 明天有预订，跳过！")
                continue
            if not user.gzh_openid:
                logger.info(f"用户 {user.id} 无公众号（服务号）用户ID，跳过！")
                continue
                
            logger.info(f"用户 {user.id} 明天无预订记录，进行提醒！")
            
            # 3. 发送提醒消息
            try:
                # 构建消息数据（小程序模块）
                # message_data = {
                #     "thing2": {"value": "您明天无预订记录，请于19:00前预订免得错过预订！"},
                #     "time1": {"value": datetime.now().strftime("%Y-%m-%d %H:%M")},
                # }
                # # 发送消息
                # success = send_subscribe_message(user.wechat_id, template_id, message_data)
                # if success:
                #     logger.info(f"成功发送预订提醒给用户 {user.id}，微信ID: {user.wechat_id}")
                #     # 提醒用户后，将消息订阅设置为0
                #     # update_user_msg_state(db, user.id, 0, '')
                # else:
                #     logger.warning(f"发送预订提醒给用户 {user.id} 失败")

                # 构建消息数据（公众号模块）
                template_id = '5NI0dKaf0Z2Wjz9sNgT4OidQjFTATqrrUYXbF89znvw'
                now_time = datetime.now()
                now_time = datetime(now_time.year, now_time.month, now_time.day, 19, 0, 0)
                now_time = now_time.strftime("%Y-%m-%d %H:%M")
                message_data = {
                    "thing16": {"value": "乙禾素伙伴们!明天需用餐的记得预约哦!"},
                    "time33": {"value": now_time},
                }
                miniprogram = {
                    "appid": "wxc58a034f610866c5",
                    "pagepath": "pages/topic/topic"
                }
                # 发送消息
                success = send_template_message(user.gzh_openid, template_id, message_data, url=None, miniprogram=miniprogram)
                if success:
                    logger.info(f"成功发送预订提醒给用户 {user.id}，公众号用户ID: {user.gzh_openid}")
                else:
                    logger.warning(f"发送预订提醒给用户 {user.id} 失败")
            except Exception as e:
                logger.error(f"发送预订提醒给用户 {user.id} 时出错: {str(e)}")
                
    except Exception as e:
        logger.error(f"预订提醒定时任务执行出错: {str(e)}")
    finally:
        db.close()

# 获取服务员，对应的微信ID列表
def get_waiter_list():
    """
    获取服务员，对应的微信ID列表
    """
    from app.dao.admin import get_notify_admins

    db = SessionLocal()
    waiter_list = get_notify_admins(db)
    logger.info(f"服务员列表: {waiter_list}")
    return waiter_list


def send_order_change_reminders_task(order_id: int, mark_text: str):
    """
    发送商务餐订单商品修改提醒消息
    """
    
    # 检查是否启用通知功能
    if not settings.ENABLE_NOTIFICATION:
        logger.info("通知功能已禁用，跳过发送订单变更提醒消息")
        return
    
    from app.callback.wechat_mp import send_template_message
    logger.info(f"开始执行定时任务：发送订单变更提醒消息 - {datetime.now()}")
    mark_text = "商务餐:{} {}，请核对！".format(order_id, mark_text)
    logger.info(f"mark_text: {mark_text}")

    db = SessionLocal()
    try:
        # 构建消息数据（公众号模块）
        template_id = '5NI0dKaf0Z2Wjz9sNgT4OidQjFTATqrrUYXbF89znvw'
        now_time = datetime.now()
        now_time = now_time.strftime("%Y-%m-%d %H:%M")
        message_data = {
            "thing16": {"value": mark_text},
            "time33": {"value": now_time},
        }
        miniprogram = {
            "appid": "wxc58a034f610866c5",
            "pagepath": "pages/booking_business_edit/booking_business_edit?order_id={}".format(order_id)
        }
        # 发送消息
        waiter_list = get_waiter_list()
        logger.info(f"服务员列表: {waiter_list}")
        for waiter_user in waiter_list:
            success = send_template_message(waiter_user.contact, template_id, message_data, url=None, miniprogram=miniprogram)
            if success:
                logger.info(f"成功发送商务餐订单商品修改提醒给用户 {waiter_user.contact}")
            else:
                logger.warning(f"发送商务餐订单商品修改提醒给用户 {waiter_user.contact} 失败")
    except Exception as e:
        logger.error(f"发送商务餐订单商品修改提醒时出错: {str(e)}")
    finally:
        db.close()


# 添加定时任务到调度器
def setup_scheduler():
    """
    设置并启动调度器
    """
    
    # 每天下午5点发送预订提醒
    scheduler.add_job(
        send_reservation_reminders_task,
        CronTrigger(hour=17, minute=30),
        id="send_reservation_reminders",
        name="每日预订提醒",
        replace_existing=True
    )

    scheduler.add_job(
        send_daily_buffet_statistic_notification,
        CronTrigger(hour=9, minute=30),
        args=("lunch", "today"),
        id="daily_lunch_buffet_statistic_task",
        name="当日自助餐午餐预订统计",
        replace_existing=True
    )

    scheduler.add_job(
        send_daily_buffet_statistic_notification,
        CronTrigger(hour=15, minute=00),
        args=("dinner", "today"),
        id="daily_dinner_buffet_statistic_task",
        name="当日自助餐晚餐预订统计",
        replace_existing=True
    )

    scheduler.add_job(
        send_daily_buffet_statistic_notification,
        CronTrigger(hour=19, minute=00),
        args=("lunch", "tomorrow"),
        id="daily_lunch_buffet_next_day_statistic_task",
        name="次日自助餐午餐预订统计",
        replace_existing=True
    )

    scheduler.add_job(
        send_daily_buffet_statistic_notification,
        CronTrigger(hour=19, minute=2),
        args=("dinner", "tomorrow"),
        id="daily_dinner_buffet_next_day_statistic_task",
        name="次日自助餐晚餐预订统计",
        replace_existing=True
    )

    logger.info("定时任务已设置")
    
    return scheduler
