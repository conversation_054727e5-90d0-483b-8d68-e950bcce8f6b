from typing import List, Optional

from sqlalchemy.orm import Session

from app.dao.base import DAO
from app.models.account import Account, RegularAccount, GiftAccount, PointsAccount, MemberAccount, AccountTransaction, \
    AccountType, TransactionType, WechatAccount
from app.schemas.account import (
    AccountCreate, AccountUpdate,
    RegularAccountCreate, RegularAccountUpdate,
    GiftAccountCreate, GiftAccountUpdate,
    PointsAccountCreate, PointsAccountUpdate,
    MemberAccountCreate, MemberAccountUpdate,
    AccountTransactionCreate, AccountTransactionUpdate, WechatAccountCreate, WechatAccountUpdate
)


class AccountDAO(DAO):
    """账户DAO类"""

    def __init__(self):
        super().__init__(Account)

    def create(self, session: Session, account: AccountCreate) -> Account:
        """创建账户"""
        account_data = account.model_dump()
        return super().create(session, **account_data)

    def get(self, session: Session, account_id: int) -> Optional[Account]:
        """获取账户"""
        return super().get(session, account_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[Account]:
        """获取账户列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def get_by_user_id(self, session: Session, user_id: int) -> List[Account]:
        """根据用户ID获取账户列表"""
        return session.query(self.model).filter(Account.user_id == user_id).all()

    def update(self, session: Session, account_id: int, account: AccountUpdate) -> Optional[Account]:
        """更新账户"""
        account_data = account.model_dump(exclude_unset=True)
        # 确保不更新账户类型
        if 'type' in account_data:
            del account_data['type']
        return super().update(session, account_id, **account_data)

    def delete(self, session: Session, account_id: int) -> bool:
        """删除账户"""
        return super().delete(session, account_id)

    def get_user_balance(self, session: Session, user_id: int) -> float:
        """获取用户余额"""
        regular_account = session.query(RegularAccount).filter(RegularAccount.user_id == user_id).first()
        if regular_account:
            return regular_account.balance
        return 0

    def get_by_id(self, session: Session, account_id: int) -> List[Account]:
        """根据账户ID获取账户列表"""
        return session.query(self.model).filter(Account.id == account_id).all()


class RegularAccountDAO(DAO):
    """普通账户DAO类"""

    def __init__(self):
        super().__init__(RegularAccount)

    def create(self, session: Session, account: RegularAccountCreate) -> RegularAccount:
        """创建普通账户"""
        account_data = account.model_dump()
        return super().create(session, **account_data)

    def get(self, session: Session, account_id: int) -> Optional[RegularAccount]:
        """获取普通账户"""
        return super().get(session, account_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[RegularAccount]:
        """获取普通账户列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def get_by_user_id(self, session: Session, user_id: int) -> List[RegularAccount]:
        """根据用户ID获取普通账户列表"""
        return session.query(self.model).filter(RegularAccount.user_id == user_id).all()

    def update(self, session: Session, account_id: int, account: RegularAccountUpdate) -> Optional[RegularAccount]:
        """更新普通账户"""
        account_data = account.model_dump(exclude_unset=True)
        # 确保不更新账户类型
        if 'type' in account_data:
            del account_data['type']
        return super().update(session, account_id, **account_data)

    def delete(self, session: Session, account_id: int) -> bool:
        """删除普通账户"""
        return super().delete(session, account_id)


class GiftAccountDAO(DAO):
    """赠送账户DAO类"""

    def __init__(self):
        super().__init__(GiftAccount)

    def create(self, session: Session, account: GiftAccountCreate) -> GiftAccount:
        """创建赠送账户"""
        account_data = account.model_dump()
        return super().create(session, **account_data)

    def get(self, session: Session, account_id: int) -> Optional[GiftAccount]:
        """获取赠送账户"""
        return super().get(session, account_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[GiftAccount]:
        """获取赠送账户列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def get_by_user_id(self, session: Session, user_id: int) -> List[GiftAccount]:
        """根据用户ID获取赠送账户列表"""
        return session.query(self.model).filter(GiftAccount.user_id == user_id).all()

    def update(self, session: Session, account_id: int, account: GiftAccountUpdate) -> Optional[GiftAccount]:
        """更新赠送账户"""
        account_data = account.model_dump(exclude_unset=True)
        # 确保不更新账户类型
        if 'type' in account_data:
            del account_data['type']
        return super().update(session, account_id, **account_data)

    def delete(self, session: Session, account_id: int) -> bool:
        """删除赠送账户"""
        return super().delete(session, account_id)


class PointsAccountDAO(DAO):
    """积分账户DAO类"""

    def __init__(self):
        super().__init__(PointsAccount)

    def create(self, session: Session, account: PointsAccountCreate) -> PointsAccount:
        """创建积分账户"""
        account_data = account.model_dump()
        return super().create(session, **account_data)

    def get(self, session: Session, account_id: int) -> Optional[PointsAccount]:
        """获取积分账户"""
        return super().get(session, account_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[PointsAccount]:
        """获取积分账户列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def get_by_user_id(self, session: Session, user_id: int) -> List[PointsAccount]:
        """根据用户ID获取积分账户列表"""
        return session.query(self.model).filter(PointsAccount.user_id == user_id).all()

    def update(self, session: Session, account_id: int, account: PointsAccountUpdate) -> Optional[PointsAccount]:
        """更新积分账户"""
        account_data = account.model_dump(exclude_unset=True)
        # 确保不更新账户类型
        if 'type' in account_data:
            del account_data['type']
        return super().update(session, account_id, **account_data)

    def delete(self, session: Session, account_id: int) -> bool:
        """删除积分账户"""
        return super().delete(session, account_id)


class MemberAccountDAO(DAO):
    """会员账户DAO类"""

    def __init__(self):
        super().__init__(MemberAccount)

    def create(self, session: Session, account: MemberAccountCreate) -> MemberAccount:
        """创建会员账户"""
        account_data = account.model_dump()
        return super().create(session, **account_data)

    def get(self, session: Session, account_id: int) -> Optional[MemberAccount]:
        """获取会员账户"""
        return super().get(session, account_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[MemberAccount]:
        """获取会员账户列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def get_by_user_id(self, session: Session, user_id: int) -> List[MemberAccount]:
        """根据用户ID获取会员账户列表"""
        return session.query(self.model).filter(MemberAccount.user_id == user_id).all()

    def update(self, session: Session, account_id: int, account: MemberAccountUpdate) -> Optional[MemberAccount]:
        """更新会员账户"""
        account_data = account.model_dump(exclude_unset=True)
        # 确保不更新账户类型
        if 'type' in account_data:
            del account_data['type']
        return super().update(session, account_id, **account_data)

    def delete(self, session: Session, account_id: int) -> bool:
        """删除会员账户"""
        return super().delete(session, account_id)


class WechatAccountDAO(DAO):
    """普通账户DAO类"""

    def __init__(self):
        super().__init__(WechatAccount)

    def create(self, session: Session, account: WechatAccountCreate) -> WechatAccount:
        """创建普通账户"""
        account_data = account.model_dump()
        return super().create(session, **account_data)

    def get(self, session: Session, account_id: int) -> Optional[WechatAccount]:
        """获取普通账户"""
        return super().get(session, account_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[WechatAccount]:
        """获取普通账户列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def get_by_user_id(self, session: Session, user_id: int) -> List[WechatAccount]:
        """根据用户ID获取普通账户列表"""
        return session.query(self.model).filter(WechatAccount.user_id == user_id).all()

    def update(self, session: Session, account_id: int, account: WechatAccountUpdate) -> Optional[WechatAccount]:
        """更新普通账户"""
        account_data = account.model_dump(exclude_unset=True)
        # 确保不更新账户类型
        if 'type' in account_data:
            del account_data['type']
        return super().update(session, account_id, **account_data)

    def delete(self, session: Session, account_id: int) -> bool:
        """删除普通账户"""
        return super().delete(session, account_id)


class AccountTransactionDAO(DAO):
    """账户流水DAO类"""

    def __init__(self):
        super().__init__(AccountTransaction)

    def create(self, session: Session, transaction: AccountTransactionCreate) -> AccountTransaction:
        """创建账户流水"""
        transaction_data = transaction.model_dump()
        return super().create(session, **transaction_data)

    def get(self, session: Session, transaction_id: int) -> Optional[AccountTransaction]:
        """获取账户流水"""
        return super().get(session, transaction_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[AccountTransaction]:
        """获取账户流水列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def get_by_account_id(self, session: Session, account_id: int) -> List[AccountTransaction]:
        """根据账户ID获取账户流水列表"""
        return session.query(self.model).filter(AccountTransaction.account_id == account_id).all()

    def get_by_order_id(self, session: Session, order_id: int) -> List[AccountTransaction]:
        """根据订单ID获取账户流水列表"""
        return session.query(self.model).filter(AccountTransaction.order_id == order_id).all()

    def update(self, session: Session, transaction_id: int, transaction: AccountTransactionUpdate) -> Optional[AccountTransaction]:
        """更新账户流水"""
        transaction_data = transaction.model_dump(exclude_unset=True)
        return super().update(session, transaction_id, **transaction_data)

    def delete(self, session: Session, transaction_id: int) -> bool:
        """删除账户流水"""
        return super().delete(session, transaction_id)

    def get_by_order_ids(self, db: Session, order_ids: List[int]) -> List[AccountTransaction]:
        """通过订单ID列表查询交易记录"""
        return db.query(AccountTransaction).filter(AccountTransaction.order_id.in_(order_ids)).all()

    def get_by_order_id(self, session: Session, order_id: int) -> List[AccountTransaction]:
        """根据订单ID获取账户"""
        return session.query(AccountTransaction).filter(AccountTransaction.order_id == order_id).all()

    def get_enterprise_payment_transactions(self, session: Session, order_id: int) -> List[AccountTransaction]:
        """获取订单的企业支付交易记录"""
        return session.query(AccountTransaction).filter(
            AccountTransaction.order_id == order_id,
            AccountTransaction.transaction_type == TransactionType.PAYMENT,
            AccountTransaction.amount < 0  # 支出记录
        ).all()

    def find_matching_enterprise_transaction(self, session: Session, order_id: int, enterprise_paid_amount: float) -> Optional[AccountTransaction]:
        """查找匹配的企业支付交易记录"""
        payment_transactions = self.get_enterprise_payment_transactions(session, order_id)

        for transaction in payment_transactions:
            if abs(transaction.amount) == enterprise_paid_amount:
                return transaction
        return None


# 创建DAO实例
account_dao = AccountDAO()
regular_account_dao = RegularAccountDAO()
gift_account_dao = GiftAccountDAO()
points_account_dao = PointsAccountDAO()
member_account_dao = MemberAccountDAO()
wechat_account_dao = WechatAccountDAO()
account_transaction_dao = AccountTransactionDAO()