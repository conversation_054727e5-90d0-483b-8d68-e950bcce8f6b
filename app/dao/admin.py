from typing import List, Optional

from sqlalchemy import or_
from sqlalchemy.orm import Session

from app.dao.base import DAO
from app.models.admin import Admin, Role, Permission
from app.schemas.admin import (
    AdminCreate, AdminUpdate,
    RoleCreate, RoleUpdate,
    PermissionCreate, PermissionUpdate
)
from app.messager.core.message import MessageRecipient


class AdminDAO(DAO):
    """管理员DAO类"""

    def __init__(self):
        super().__init__(Admin)

    def create(self, session: Session, admin: AdminCreate) -> Admin:
        """创建管理员"""
        from app.core.security import get_password_hash
        admin_data = admin.model_dump()
        if "password" in admin_data:
            admin_data["password"] = get_password_hash(admin_data["password"])
        return super().create(session, **admin_data)

    def get(self, session: Session, admin_id: int) -> Optional[Admin]:
        """获取管理员"""
        return super().get(session, admin_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[Admin]:
        """获取管理员列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, admin_id: int, admin: AdminUpdate) -> Optional[Admin]:
        """更新管理员"""
        from app.core.security import get_password_hash
        admin_data = admin.model_dump(exclude_unset=True)
        if "password" in admin_data and admin_data["password"]:
            admin_data["password"] = get_password_hash(admin_data["password"])
        return super().update(session, admin_id, **admin_data)

    def delete(self, session: Session, admin_id: int) -> bool:
        """删除管理员"""
        return super().delete(session, admin_id)
        
    def add_role(self, session: Session, admin_id: int, role_id: int) -> Optional[Admin]:
        """为管理员添加角色"""
        admin = self.get(session, admin_id)
        role = session.query(Role).filter(Role.id == role_id).first()
        
        if admin and role:
            admin.roles.append(role)
            session.commit()
            session.refresh(admin)
            return admin
        return None
        
    def remove_role(self, session: Session, admin_id: int, role_id: int) -> Optional[Admin]:
        """移除管理员的角色"""
        admin = self.get(session, admin_id)
        role = session.query(Role).filter(Role.id == role_id).first()
        
        if admin and role and role in admin.roles:
            admin.roles.remove(role)
            session.commit()
            session.refresh(admin)
            return admin
        return None
        
    def get_roles(self, session: Session, admin_id: int) -> List[Role]:
        """获取管理员所有角色"""
        admin = self.get(session, admin_id)
        if admin:
            return admin.roles
        return []

    def get_by_phone(self, session: Session, phone: str):
        """根据手机号获取管理员"""
        return session.query(Admin).filter(Admin.phone == phone).first()

    def search(self, session: Session, keyword: str = None, skip: int = 0, limit: int = 100) -> dict:
        """
        根据关键字搜索管理员列表及总数
        
        Args:
            session (Session): 数据库会话
            keyword (str, optional): 搜索关键字，可搜索管理员姓名、用户名、手机号、邮箱等
            skip (int, optional): 分页起始位置. Defaults to 0.
            limit (int, optional): 分页大小. Defaults to 100.
            
        Returns:
            dict: 包含 'total'（搜索结果总数）和 'list'（分页后的管理员列表）的字典
        """
        query = session.query(self.model)

        # 如果提供了关键字，则添加过滤条件
        if keyword:
            search_keyword = f"%{keyword}%"
            query = query.filter(
                or_(
                    self.model.name.ilike(search_keyword),
                    self.model.username.ilike(search_keyword),
                    self.model.phone.ilike(search_keyword),
                    self.model.email.ilike(search_keyword)
                )
            )

        # 查询总数
        total = query.count()
        # 查询分页后的管理员列表
        items = query.order_by(self.model.id.desc()).offset(skip).limit(limit).all()

        return {
            "total": total,
            "list": items
        }

    def batch_update_status(self, session: Session, admin_ids: List[int], status: int) -> bool:
        """批量更新管理员状态"""
        try:
            # 将状态转换为Status枚举
            from app.models.enum import Status
            status_enum = Status.ACTIVE if status == 1 else Status.INACTIVE
            
            # 批量更新
            session.query(self.model).filter(
                self.model.id.in_(admin_ids)
            ).update(
                {"status": status_enum},
                synchronize_session=False
            )
            session.commit()
            return True
        except Exception:
            session.rollback()
            return False

    def batch_delete(self, session: Session, admin_ids: List[int]) -> bool:
        """批量删除管理员"""
        try:
            session.query(self.model).filter(
                self.model.id.in_(admin_ids)
            ).delete(synchronize_session=False)
            session.commit()
            return True
        except Exception:
            session.rollback()
            return False


class RoleDAO(DAO):
    """角色DAO类"""

    def __init__(self):
        super().__init__(Role)

    def create(self, session: Session, role: RoleCreate) -> Role:
        """创建角色"""
        role_data = role.model_dump()
        return super().create(session, **role_data)

    def get(self, session: Session, role_id: int) -> Optional[Role]:
        """获取角色"""
        return super().get(session, role_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[Role]:
        """获取角色列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, role_id: int, role: RoleUpdate) -> Optional[Role]:
        """更新角色"""
        role_data = role.model_dump(exclude_unset=True)
        return super().update(session, role_id, **role_data)

    def delete(self, session: Session, role_id: int) -> bool:
        """删除角色"""
        return super().delete(session, role_id)
        
    def add_permission(self, session: Session, role_id: int, permission_id: int) -> Optional[Role]:
        """为角色添加权限"""
        role = self.get(session, role_id)
        permission = session.query(Permission).filter(Permission.id == permission_id).first()
        
        if role and permission:
            role.permissions.append(permission)
            session.commit()
            session.refresh(role)
            return role
        return None
        
    def remove_permission(self, session: Session, role_id: int, permission_id: int) -> Optional[Role]:
        """移除角色的权限"""
        role = self.get(session, role_id)
        permission = session.query(Permission).filter(Permission.id == permission_id).first()
        
        if role and permission and permission in role.permissions:
            role.permissions.remove(permission)
            session.commit()
            session.refresh(role)
            return role
        return None
        
    def get_permissions(self, session: Session, role_id: int) -> List[Permission]:
        """获取角色所有权限"""
        role = self.get(session, role_id)
        if role:
            return role.permissions
        return []

    def search(self, session: Session, keyword: str = None, skip: int = 0, limit: int = 100) -> dict:
        """
        根据关键字搜索角色列表及总数
        
        Args:
            session (Session): 数据库会话
            keyword (str, optional): 搜索关键字，可搜索角色名称、描述等
            skip (int, optional): 分页起始位置. Defaults to 0.
            limit (int, optional): 分页大小. Defaults to 100.
            
        Returns:
            dict: 包含 'total'（搜索结果总数）和 'list'（分页后的角色列表）的字典
        """
        query = session.query(self.model)

        # 如果提供了关键字，则添加过滤条件
        if keyword:
            search_keyword = f"%{keyword}%"
            query = query.filter(
                or_(
                    self.model.name.ilike(search_keyword),
                    self.model.description.ilike(search_keyword)
                )
            )

        # 查询总数
        total = query.count()
        # 查询分页后的角色列表
        items = query.order_by(self.model.id.desc()).offset(skip).limit(limit).all()

        return {
            "total": total,
            "list": items
        }

    def batch_update_status(self, session: Session, role_ids: List[int], status: int) -> bool:
        """批量更新角色状态"""
        try:
            # 将状态转换为Status枚举
            from app.models.enum import Status
            status_enum = Status.ACTIVE if status == 1 else Status.INACTIVE
            
            # 批量更新
            session.query(self.model).filter(
                self.model.id.in_(role_ids)
            ).update(
                {"status": status_enum},
                synchronize_session=False
            )
            session.commit()
            return True
        except Exception:
            session.rollback()
            return False

    def batch_delete(self, session: Session, role_ids: List[int]) -> bool:
        """批量删除角色"""
        try:
            session.query(self.model).filter(
                self.model.id.in_(role_ids)
            ).delete(synchronize_session=False)
            session.commit()
            return True
        except Exception:
            session.rollback()
            return False


class PermissionDAO(DAO):
    """权限DAO类"""

    def __init__(self):
        super().__init__(Permission)

    def create(self, session: Session, permission: PermissionCreate) -> Permission:
        """创建权限"""
        permission_data = permission.model_dump()
        return super().create(session, **permission_data)

    def get(self, session: Session, permission_id: int) -> Optional[Permission]:
        """获取权限"""
        return super().get(session, permission_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[Permission]:
        """获取权限列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, permission_id: int, permission: PermissionUpdate) -> Optional[Permission]:
        """更新权限"""
        permission_data = permission.model_dump(exclude_unset=True)
        return super().update(session, permission_id, **permission_data)

    def delete(self, session: Session, permission_id: int) -> bool:
        """删除权限"""
        return super().delete(session, permission_id)

    def search(self, session: Session, keyword: str = None, skip: int = 0, limit: int = 100) -> dict:
        """
        根据关键字搜索权限列表及总数
        
        Args:
            session (Session): 数据库会话
            keyword (str, optional): 搜索关键字，可搜索权限名称、权限代码、描述等
            skip (int, optional): 分页起始位置. Defaults to 0.
            limit (int, optional): 分页大小. Defaults to 100.
            
        Returns:
            dict: 包含 'total'（搜索结果总数）和 'list'（分页后的权限列表）的字典
        """
        query = session.query(self.model)

        # 如果提供了关键字，则添加过滤条件
        if keyword:
            search_keyword = f"%{keyword}%"
            query = query.filter(
                or_(
                    self.model.name.ilike(search_keyword),
                    self.model.code.ilike(search_keyword),
                    self.model.description.ilike(search_keyword),
                    self.model.permission_type.ilike(search_keyword)
                )
            )

        # 查询总数
        total = query.count()
        # 查询分页后的权限列表
        items = query.order_by(self.model.id.desc()).offset(skip).limit(limit).all()

        return {
            "total": total,
            "list": items
        }

    def batch_update_status(self, session: Session, permission_ids: List[int], status: int) -> bool:
        """批量更新权限状态"""
        try:
            # 将状态转换为Status枚举
            from app.models.enum import Status
            status_enum = Status.ACTIVE if status == 1 else Status.INACTIVE
            
            # 批量更新
            session.query(self.model).filter(
                self.model.id.in_(permission_ids)
            ).update(
                {"status": status_enum},
                synchronize_session=False
            )
            session.commit()
            return True
        except Exception:
            session.rollback()
            return False

    def batch_delete(self, session: Session, permission_ids: List[int]) -> bool:
        """批量删除权限"""
        try:
            session.query(self.model).filter(
                self.model.id.in_(permission_ids)
            ).delete(synchronize_session=False)
            session.commit()
            return True
        except Exception:
            session.rollback()
            return False


# 创建DAO实例
admin_dao = AdminDAO()
role_dao = RoleDAO()
permission_dao = PermissionDAO()

# 认证相关函数
def get_admin_by_username(db: Session, username: str) -> Optional[Admin]:
    """根据用户名获取管理员"""
    return db.query(Admin).filter(Admin.username == username).first()

def get_admin(db: Session, admin_id: int) -> Optional[Admin]:
    """根据ID获取管理员"""
    return admin_dao.get(db, admin_id)

def create_admin(db: Session, admin: AdminCreate) -> Admin:
    """创建管理员"""
    return admin_dao.create(db, admin)

def update_admin(db: Session, admin_id: int, admin: AdminUpdate) -> Optional[Admin]:
    """更新管理员"""
    return admin_dao.update(db, admin_id, admin)

def get_admin_permissions(db: Session, admin_id: int) -> List[str]:
    """获取管理员所有权限的code列表"""
    admin = get_admin(db, admin_id)
    if not admin:
        return []
    
    permissions = []
    for role in admin.roles:
        for permission in role.permissions:
            permissions.append(permission.code)
    
    return list(set(permissions))  # 去重


def get_notify_admins(db: Session) -> List[MessageRecipient]:
    """
    获取所有具有"miniapp:notify"权限的admin，并根据其手机号，获取对应的personal_users中的gzh_openid
    返回一个MessageRecipient数组

    Args:
        db: 数据库会话

    Returns:
        List[MessageRecipient]: MessageRecipient数组，其中id为admin的id，name为admin的name，contact为对应的gzh_openid
    """
    from app.dao.user import personal_user_dao

    try:
        # 获取所有管理员
        all_admins = db.query(Admin).all()

        notify_users = []
        required_permission = "miniapp:notify"

        for admin in all_admins:
            # 检查管理员是否有miniapp:notify权限
            has_permission = False

            for role in admin.roles:
                for permission in role.permissions:
                    if permission.code == required_permission:
                        has_permission = True
                        break
                if has_permission:
                    break

            if has_permission:
                # 获取对应的personal_user信息
                personal_user = personal_user_dao.get_by_phone(db, admin.phone) if admin.phone else None

                # 只有当personal_user存在且有gzh_openid时才添加到结果中
                if personal_user and personal_user.gzh_openid:
                    recipient = MessageRecipient(
                        id=str(admin.id),
                        name=admin.name,
                        contact=personal_user.gzh_openid
                    )
                    notify_users.append(recipient)

        return notify_users

    except Exception as e:
        # 记录错误但不抛出异常，返回空列表
        return []
