from datetime import datetime
from typing import List, Optional

from sqlalchemy.orm import Session

from app.dao.base import DAO
from app.models.approval import Approval, ApprovalStatus
from app.schemas.approval import ApprovalCreate, ApprovalUpdate
from app.utils.common import get_current_time


class ApprovalDAO(DAO):
    """审批DAO类"""

    def __init__(self):
        super().__init__(Approval)

    def create(self, session: Session, approval: ApprovalCreate) -> Approval:
        """创建审批请求"""
        approval_data = approval.model_dump()
        return super().create(session, **approval_data)

    def get(self, session: Session, approval_id: int) -> Optional[Approval]:
        """获取审批请求"""
        return super().get(session, approval_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[Approval]:
        """获取审批请求列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, approval_id: int, approval: ApprovalUpdate) -> Optional[Approval]:
        """更新审批请求"""
        approval_data = approval.model_dump(exclude_unset=True)
        return super().update(session, approval_id, **approval_data)

    def delete(self, session: Session, approval_id: int) -> bool:
        """删除审批请求"""
        return super().delete(session, approval_id)

    def get_by_enterprise(self, session: Session, enterprise_id: int, skip: int = 0, limit: int = 100) -> List[Approval]:
        """获取企业的所有审批请求"""
        return session.query(self.model).filter(self.model.enterprise_id == enterprise_id).offset(skip).limit(limit).all()

    def get_by_applicant(self, session: Session, applicant_id: int, skip: int = 0, limit: int = 100) -> List[Approval]:
        """获取申请人的所有审批请求"""
        return session.query(self.model).filter(self.model.applicant_id == applicant_id).offset(skip).limit(limit).all()

    def get_by_approver(self, session: Session, approver_id: int, skip: int = 0, limit: int = 100) -> List[Approval]:
        """获取审批人审批的所有请求"""
        return session.query(self.model).filter(self.model.approver_id == approver_id).offset(skip).limit(limit).all()

    def get_by_reservation_request(self, session: Session, reservation_request_id: int) -> Optional[Approval]:
        """获取预订请求关联的审批"""
        return session.query(self.model).filter(
            self.model.reservation_request_id == reservation_request_id
        ).first()

    def get_by_status(self, session: Session, status: ApprovalStatus, skip: int = 0, limit: int = 100) -> List[Approval]:
        """获取指定状态的审批请求"""
        return session.query(self.model).filter(self.model.status == status).offset(skip).limit(limit).all()

    def approve(self, session: Session, approval_id: int, approver_id: int, comment: Optional[str] = None) -> Optional[Approval]:
        """审批通过"""
        approval = self.get(session, approval_id)
        if approval and approval.status == ApprovalStatus.PENDING:
            approval.status = ApprovalStatus.APPROVED
            approval.approver_id = approver_id
            approval.comment = comment
            approval.approval_time = get_current_time()
            session.commit()
            session.refresh(approval)
            return approval
        return None

    def reject(self, session: Session, approval_id: int, approver_id: int, comment: Optional[str] = None) -> Optional[Approval]:
        """审批拒绝"""
        approval = self.get(session, approval_id)
        if approval and approval.status == ApprovalStatus.PENDING:
            approval.status = ApprovalStatus.REJECTED
            approval.approver_id = approver_id
            approval.comment = comment
            approval.approval_time = get_current_time()
            session.commit()
            session.refresh(approval)
            return approval
        return None


# 创建DAO实例
approval_dao = ApprovalDAO() 