from typing import Any, Optional, List, Dict

from sqlalchemy import inspect, and_, or_
from sqlalchemy.orm import Session


class DAO:
    def __init__(self, model):
        self.model = model
        self.primary_key = inspect(self.model).primary_key[0].name

    def create(self, session: Session, **kwargs) -> Any:
        """
        创建新记录
        :param session: 数据库会话
        :param kwargs: 记录字段
        :return: 创建的实例
        """
        instance = self.model(**kwargs)
        session.add(instance)
        session.commit()
        session.refresh(instance)
        return instance

    def get(self, session: Session, record_id: Any) -> Optional[Any]:
        """
        根据主键获取单个记录
        :param session: 数据库会话
        :param record_id: 主键值
        :return: 记录实例或None
        """
        return session.query(self.model).filter(
            getattr(self.model, self.primary_key) == record_id
        ).first()

    def get_all(self, session: Session) -> List[Any]:
        """
        获取所有记录
        :param session: 数据库会话
        :return: 记录列表
        """
        return session.query(self.model).all()

    def filter(self, session: Session, **filters) -> List[Any]:
        """
        根据条件过滤记录
        :param session: 数据库会话
        :param filters: 过滤条件
        :return: 符合条件的记录列表
        """
        return session.query(self.model).filter_by(**filters).all()

    def update(self, session: Session, record_id: Any, **kwargs) -> Optional[Any]:
        """
        根据主键更新记录
        :param session: 数据库会话
        :param record_id: 主键值
        :param kwargs: 要更新的字段
        :return: 更新后的实例或None
        """
        instance = self.get(session, record_id)
        if instance:
            for key, value in kwargs.items():
                setattr(instance, key, value)
            session.commit()
            session.refresh(instance)
        return instance

    def delete(self, session: Session, record_id: Any) -> bool:
        """
        根据主键删除记录
        :param session: 数据库会话
        :param record_id: 主键值
        :return: 是否删除成功
        """
        instance = self.get(session, record_id)
        if instance:
            session.delete(instance)
            session.commit()
            return True
        return False

    def bulk_create(
            self,
            session: Session,
            data_list: List[Dict[str, Any]],
            commit: bool = True,
            batch_size: int = 500
    ) -> List[Any]:
        """
        批量创建记录
        :param session: 数据库会话
        :param data_list: 字典列表，每个字典表示一条记录
        :param commit: 是否立即提交事务
        :param batch_size: 每批处理数量，默认500
        :return: 创建的实例列表
        """
        instances = []
        for i in range(0, len(data_list), batch_size):
            batch = data_list[i:i + batch_size]
            # 使用 bulk_insert_mappings 进行批量插入
            session.bulk_insert_mappings(self.model, batch)
            if commit:
                session.commit()
                # 获取插入的记录
                pk_column = getattr(self.model, self.primary_key)
                # 对于自增ID，我们需要通过其他字段来查找记录
                if not any(data.get(pk_column) for data in batch):
                    # 使用其他字段组合来查找记录
                    conditions = []
                    for data in batch:
                        conditions.append(and_(
                            *[getattr(self.model, k) == v for k, v in data.items()]
                        ))
                    batch_instances = session.query(self.model).filter(or_(*conditions)).all()
                else:
                    # 如果有主键，直接使用主键查找
                    inserted_ids = [data[pk_column] for data in batch if pk_column in data]
                    batch_instances = session.query(self.model).filter(
                        pk_column.in_(inserted_ids)
                    ).all()
                instances.extend(batch_instances)
        return instances

    def bulk_update(
            self,
            session: Session,
            updates: List[Dict[str, Any]],
            commit: bool = True,
            batch_size: int = 500
    ) -> int:
        """
        批量更新多条记录（不同主键不同更新值）
        :param session: 数据库会话
        :param updates: 包含主键和更新值的字典列表
        :param commit: 是否立即提交事务
        :param batch_size: 每批处理数量，默认500
        :return: 更新的记录数
        """
        total_updated = 0
        for i in range(0, len(updates), batch_size):
            batch = updates[i:i + batch_size]
            # 确保每条记录都包含主键
            valid_updates = [
                {self.primary_key: u[self.primary_key], **{k: v for k, v in u.items() if k != self.primary_key}}
                for u in batch
                if self.primary_key in u
            ]
            if valid_updates:
                session.bulk_update_mappings(self.model, valid_updates)
                if commit:
                    session.commit()
                total_updated += len(valid_updates)
        return total_updated

    def bulk_update_by_ids(
            self,
            session: Session,
            ids: List[Any],
            values: Dict[str, Any],
            commit: bool = True,
            batch_size: int = 500
    ) -> int:
        """
        根据主键列表批量更新相同字段值
        :param session: 数据库会话
        :param ids: 主键值列表
        :param values: 要更新的字段字典
        :param commit: 是否立即提交事务
        :param batch_size: 每批处理数量，默认500
        :return: 更新的记录数
        """
        total_updated = 0
        for i in range(0, len(ids), batch_size):
            batch_ids = ids[i:i + batch_size]
            # 构建更新映射
            update_mappings = [{self.primary_key: id, **values} for id in batch_ids]
            session.bulk_update_mappings(self.model, update_mappings)
            if commit:
                session.commit()
            total_updated += len(batch_ids)
        return total_updated

    def bulk_update_by_filter(
            self,
            session: Session,
            values: Dict[str, Any],
            filters: Dict[str, Any],
            commit: bool = True,
            batch_size: int = 500
    ) -> int:
        """
        根据条件批量更新记录
        :param session: 数据库会话
        :param values: 要更新的字段字典
        :param filters: 过滤条件
        :param commit: 是否立即提交事务
        :param batch_size: 每批处理数量，默认500
        :return: 更新的记录数
        """
        # 获取符合条件的记录
        instances = session.query(self.model).filter_by(**filters).all()
        total_updated = 0

        for i in range(0, len(instances), batch_size):
            batch = instances[i:i + batch_size]
            # 构建更新映射
            mappings = [
                {self.primary_key: getattr(instance, self.primary_key), **values}
                for instance in batch
            ]
            session.bulk_update_mappings(self.model, mappings)
            if commit:
                session.commit()
            total_updated += len(batch)
        return total_updated
