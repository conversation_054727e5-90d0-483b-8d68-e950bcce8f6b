from typing import List, Optional

from sqlalchemy import and_
from sqlalchemy.orm import Session

from app.dao.base import DAO
from app.models.content import Content, ContentType
from app.models.file import content_file_relation
from app.models.product import Product
from app.schemas.content import ContentCreate, ContentUpdate
from app.dao.file import file_dao
from app.models.content import Content, ContentType, Article, Dish
from app.models.file import content_file_relation, File
from app.models.product import Product
from app.schemas.content import ContentCreate, ContentUpdate, ArticleCreate, ArticleUpdate, ArticleWithFiles, DishCreate, DishUpdate, \
    DishWithFiles


class ContentDAO(DAO):
    """内容 DAO 类"""

    def __init__(self):
        super().__init__(Content)

    def create(self, session: Session, content: ContentCreate) -> Content:
        """创建内容"""
        content_data = content.model_dump()
        return super().create(session, **content_data)

    def get(self, session: Session, content_id: int) -> Optional[Content]:
        """获取内容"""
        return super().get(session, content_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[Content]:
        """获取内容列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def get_by_product(self, session: Session, product_id: int) -> List[Content]:
        """获取产品相关内容"""
        return session.query(self.model).filter(
            self.model.products.any(Product.id == product_id)
        ).all()

    def get_by_content_type(self, session: Session, content_type: ContentType) -> List[Content]:
        """根据内容类型获取内容"""
        return session.query(self.model).filter(
            self.model.type == content_type
        ).all()

    def update(self, session: Session, content_id: int, content: ContentUpdate) -> Optional[Content]:
        """更新内容"""
        content_data = content.model_dump(exclude_unset=True)
        return super().update(session, content_id, **content_data)

    def delete(self, session: Session, content_id: int) -> bool:
        """删除内容"""
        return super().delete(session, content_id)

    def add_files(self, session: Session, file_ids: List[int], content_id: int) -> dict:
        """批量将文件绑定到内容

        Returns:
            dict: 包含成功和失败的文件ID列表
        """
        content = session.query(Content).filter(Content.id == content_id).first()
        if not content:
            return {"success": [], "failed": file_ids}

        success_ids = []
        failed_ids = []

        for file_id in file_ids:
            file = file_dao.get(session, file_id)
            if not file:
                failed_ids.append(file_id)
                continue

            # 检查是否已经绑定
            existing = session.query(content_file_relation).filter(
                and_(
                    content_file_relation.c.file_id == file_id,
                    content_file_relation.c.content_id == content_id
                )
            ).first()

            if not existing:
                content.files.append(file)
                success_ids.append(file_id)
            else:
                # 已经绑定过，也视为成功
                success_ids.append(file_id)

        session.commit()
        return {"success": success_ids, "failed": failed_ids}

    def remove_files(self, session: Session, file_ids: List[int], content_id: int) -> dict:
        """批量将文件从内容解绑

        Returns:
            dict: 包含成功和失败的文件ID列表
        """
        content = session.query(Content).filter(Content.id == content_id).first()
        if not content:
            return {"success": [], "failed": file_ids}

        success_ids = []
        failed_ids = []

        for file_id in file_ids:
            file = file_dao.get(session, file_id)
            if not file:
                failed_ids.append(file_id)
                continue

            # 检查是否已经绑定
            existing = session.query(content_file_relation).filter(
                and_(
                    content_file_relation.c.file_id == file_id,
                    content_file_relation.c.content_id == content_id
                )
            ).first()

            if existing:
                # 移除关联关系
                file.contents.remove(content)
                success_ids.append(file_id)
            else:
                # 未绑定，也视为成功
                success_ids.append(file_id)

        session.commit()
        return {"success": success_ids, "failed": failed_ids}

    def get_files(self, session: Session, content_id: int) -> List[File]:
        """获取内容关联的文件列表"""
        content = session.query(Content).filter(Content.id == content_id).first()
        if not content:
            return []
        return content.files

    def batch_set_status(self, session: Session, content_ids: List[int], status) -> int:
        """
        批量将指定内容的状态设置为有效

        Args:
            session (Session): 数据库会话
            content_ids (List[int]): 要更新的内容 ID 列表

        Returns:
            int: 更新的记录数
        """
        result = session.query(Content).filter(Content.id.in_(content_ids)).update(
            {Content.status: status},
            synchronize_session='fetch'
        )
        session.commit()
        return result


# 创建 DAO 实例
content_dao = ContentDAO()


class ArticleDAO(DAO):
    """文章 DAO 类"""

    def __init__(self):
        super().__init__(Article)

    def create(self, session: Session, article: ArticleCreate) -> Article:
        """创建文章"""
        article_data = article.model_dump()
        article_obj = Article(**article_data)
        session.add(article_obj)
        session.commit()
        session.refresh(article_obj)
        return article_obj

    def get(self, session: Session, article_id: int) -> Optional[Article]:
        """获取文章"""
        return session.query(Article).filter(Article.id == article_id).first()

    def get_with_files(self, session: Session, article_id: int) -> Optional[ArticleWithFiles]:
        """获取文章包含文件"""
        article = session.query(Article).filter(Article.id == article_id).first()
        if not article:
            return None
        article.files = file_dao.get_by_content(session, article_id)
        return article

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> dict:
        """
        获取文章列表及总数

        Returns:
            dict: 包含 'total'（文章总数）和 'list'（分页后的文章列表）的字典
        """
        # 查询文章总数
        total = session.query(Article).count()
        # 查询分页后的文章列表
        items = session.query(Article).order_by(Article.id.desc()).offset(skip).limit(limit).all()
        return {
            "total": total,
            "list": items
        }

    def search(self, session: Session, keyword: str = None, skip: int = 0, limit: int = 100) -> dict:
        """
        根据关键字搜索文章列表及总数

        Args:
            session (Session): 数据库会话
            keyword (str, optional): 搜索关键字，可搜索文章名称、内容或摘要
            skip (int, optional): 分页起始位置. Defaults to 0.
            limit (int, optional): 分页大小. Defaults to 100.

        Returns:
            dict: 包含 'total'（搜索结果总数）和 'list'（分页后的文章列表）的字典
        """
        query = session.query(Article)

        # 如果提供了关键字，则添加过滤条件
        if keyword:
            search_keyword = f"%{keyword}%"
            query = query.filter(
                Article.name.ilike(search_keyword) |
                Article.content.ilike(search_keyword) |
                Article.summary.ilike(search_keyword)
            )

        # 查询总数
        total = query.count()
        # 查询分页后的文章列表
        items = query.order_by(Article.id.desc()).offset(skip).limit(limit).all()

        return {
            "total": total,
            "list": items
        }

    def get_by_product(self, session: Session, product_id: int) -> List[Article]:
        """获取产品相关文章"""
        return session.query(Article).filter(
            Article.products.any(Product.id == product_id)
        ).all()

    def update(self, session: Session, article_id: int, article: ArticleUpdate) -> Optional[Article]:
        """更新文章"""
        db_article = session.query(Article).filter(Article.id == article_id).first()
        if not db_article:
            return None

        article_data = article.model_dump(exclude_unset=True)
        for key, value in article_data.items():
            setattr(db_article, key, value)

        session.commit()
        session.refresh(db_article)
        return db_article

    def delete(self, session: Session, article_id: int) -> bool:
        """删除文章"""
        db_article = session.query(Article).filter(Article.id == article_id).first()
        if not db_article:
            return False

        session.delete(db_article)
        session.commit()
        return True

    def delete_many(self, session: Session, article_ids: List[int]) -> bool:
        """批量删除文章"""
        db_articles = session.query(Article).filter(Article.id.in_(article_ids)).all()
        if not db_articles:
            return False
        for db_article in db_articles:
            session.delete(db_article)
        session.commit()
        return True

    def get_published_articles(self, session: Session) -> List[Article]:
        """获取所有已发布的文章，按sort_order升序排列"""
        from app.models.enum import Status
        return session.query(Article).filter(
            Article.status == Status.ACTIVE
        ).order_by(Article.sort_order.asc()).all()

    def get_published_article(self, session: Session, article_id: int) -> Optional[Article]:
        """根据ID获取已发布的文章"""
        from app.models.enum import Status
        return session.query(Article).filter(
            Article.id == article_id,
            Article.status == Status.ACTIVE
        ).first()


class DishDAO(DAO):
    """菜品 DAO 类"""

    def __init__(self):
        super().__init__(Dish)

    def create(self, session: Session, dish: DishCreate) -> Dish:
        """创建菜品"""
        dish_data = dish.model_dump()
        dish_obj = Dish(**dish_data)
        session.add(dish_obj)
        session.commit()
        session.refresh(dish_obj)
        return dish_obj

    def get(self, session: Session, dish_id: int) -> Optional[Dish]:
        """获取菜品"""
        return session.query(Dish).filter(Dish.id == dish_id).first()

    def get_with_files(self, session: Session, dish_id: int) -> Optional[DishWithFiles]:
        """获取菜品"""
        dish = session.query(Dish).filter(Dish.id == dish_id).first()
        if not dish:
            return None
        dish.files = file_dao.get_by_content(session, dish_id)
        return dish

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> dict:
        """
        获取菜品列表及总数

        Returns:
            dict: 包含 'total'（菜品总数）和 'items'（分页后的菜品列表）的字典
        """
        # 查询菜品总数
        total = session.query(Dish).count()
        # 查询分页后的菜品列表
        items = session.query(Dish).order_by(Dish.id.desc()).offset(skip).limit(limit).all()
        return {
            "total": total,
            "list": items
        }

    def search(self, session: Session, keyword: str = None, skip: int = 0, limit: int = 100) -> dict:
        """
        根据关键字搜索菜品列表及总数
        
        Args:
            session (Session): 数据库会话
            keyword (str, optional): 搜索关键字，可搜索菜品名称或描述
            skip (int, optional): 分页起始位置. Defaults to 0.
            limit (int, optional): 分页大小. Defaults to 100.
            
        Returns:
            dict: 包含 'total'（搜索结果总数）和 'list'（分页后的菜品列表）的字典
        """
        query = session.query(Dish)

        # 如果提供了关键字，则添加过滤条件
        if keyword:
            search_keyword = f"%{keyword}%"
            query = query.filter(
                Dish.name.ilike(search_keyword) |
                Dish.content.ilike(search_keyword)
            )

        # 查询总数
        total = query.count()
        # 查询分页后的菜品列表
        items = query.order_by(Dish.id.desc()).offset(skip).limit(limit).all()

        return {
            "total": total,
            "list": items
        }

    def get_by_product(self, session: Session, product_id: int) -> List[Dish]:
        """获取产品相关菜品"""
        return session.query(Dish).filter(
            Dish.products.any(Product.id == product_id)
        ).all()

    def update(self, session: Session, dish_id: int, dish: DishUpdate) -> Optional[Dish]:
        """更新菜品"""
        db_dish = session.query(Dish).filter(Dish.id == dish_id).first()
        if not db_dish:
            return None

        dish_data = dish.model_dump(exclude_unset=True)
        for key, value in dish_data.items():
            setattr(db_dish, key, value)

        session.commit()
        session.refresh(db_dish)
        return db_dish

    def delete(self, session: Session, dish_id: int) -> bool:
        """删除菜品"""
        db_dish = session.query(Dish).filter(Dish.id == dish_id).first()
        if not db_dish:
            return False

        session.delete(db_dish)
        session.commit()
        return True

    def delete_many(self, session: Session, dish_ids: List[int]) -> bool:
        """批量删除菜品"""
        db_dishes = session.query(Dish).filter(Dish.id.in_(dish_ids)).all()
        if not db_dishes:
            return False
        for db_dish in db_dishes:
            session.delete(db_dish)
        session.commit()
        return True


# 创建 DAO 实例
article_dao = ArticleDAO()
dish_dao = DishDAO()
