from typing import List, Optional

from sqlalchemy.orm import Session

from app.dao.base import DAO
from app.models.content import Content
from app.models.file import File, FileType
from app.schemas.file import FileCreate, FileUpdate


class FileDAO(DAO):
    """文件 DAO 类"""

    def __init__(self):
        super().__init__(File)

    def create(self, session: Session, file: FileCreate) -> File:
        """创建文件"""
        file_data = file.model_dump()
        return super().create(session, **file_data)

    def get(self, session: Session, file_id: int) -> Optional[File]:
        """获取文件"""
        return super().get(session, file_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[File]:
        """获取文件列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def get_by_file_type(self, session: Session, file_type: FileType) -> List[File]:
        """根据文件类型获取文件"""
        return session.query(self.model).filter(
            self.model.file_type == file_type
        ).all()

    def get_by_content(self, session: Session, content_id: int) -> List[File]:
        """获取内容相关文件"""
        return session.query(self.model).filter(
            self.model.contents.any(Content.id == content_id)
        ).all()

    def update(self, session: Session, file_id: int, file: FileUpdate) -> Optional[File]:
        """更新文件"""
        file_data = file.model_dump(exclude_unset=True)
        return super().update(session, file_id, **file_data)

    def delete(self, session: Session, file_id: int) -> bool:
        """删除文件"""
        return super().delete(session, file_id)


file_dao = FileDAO()
