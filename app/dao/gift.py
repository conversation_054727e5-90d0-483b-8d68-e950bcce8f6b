from operator import and_
from typing import List, Optional, Dict, Any

from sqlalchemy.orm import Session, joinedload, selectinload

from app.dao.base import DAO
from app.models.enum import Status
from app.models.gift import (
    GiftRule, OrderGiftRule, GiftRuleType,
    OrdGiftRuleOrdProdRel, OrdGiftRuleGiftProdRel
)
from app.models.order import OrderType
from app.models.product import Product
from app.schemas.gift import (
    GiftRuleCreate, GiftRuleUpdate,
    OrderGiftRuleCreate, OrderGiftRuleUpdate,
    OrdGiftRuleOrdProdRelCreate, OrdGiftRuleOrdProdRelUpdate,
    OrdGiftRuleGiftProdRelCreate, OrdGiftRuleGiftProdRelUpdate
)


class GiftRuleDAO(DAO):
    """赠送规则 DAO 类"""

    def __init__(self):
        super().__init__(GiftRule)

    def create(self, session: Session, gift_rule: GiftRuleCreate) -> GiftRule:
        """创建赠送规则"""
        gift_rule_data = gift_rule.model_dump()
        return super().create(session, **gift_rule_data)

    def get(self, session: Session, gift_rule_id: int) -> Optional[GiftRule]:
        """获取赠送规则"""
        return super().get(session, gift_rule_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[GiftRule]:
        """获取赠送规则列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def get_active_gift_rules(self, session: Session, skip: int = 0, limit: int = 100) -> List[GiftRule]:
        """获取活跃赠送规则列表"""
        return session.query(self.model).filter(
            self.model.status == Status.ACTIVE
        ).offset(skip).limit(limit).all()

    def search(self, session: Session, keyword: Optional[str] = None,
              name: Optional[str] = None, status: Optional[Status] = None,
              gift_type: Optional[GiftRuleType] = None,
              skip: int = 0, limit: int = 100) -> Dict[str, Any]:
        """
        根据条件搜索赠送规则
        
        Args:
            session: 数据库会话
            keyword: 关键词，用于模糊搜索
            name: 规则名称，用于模糊搜索
            status: 规则状态
            gift_type: 赠送规则类型
            skip: 分页偏移量
            limit: 分页大小
            
        Returns:
            Dict: 包含赠送规则列表和总数的字典
        """
        query = session.query(self.model)
        
        # 应用过滤条件
        if keyword:
            query = query.filter(self.model.name.like(f"%{keyword}%"))
        
        if name:
            query = query.filter(self.model.name.like(f"%{name}%"))
            
        if status is not None:
            query = query.filter(self.model.status == status)
            
        if gift_type is not None:
            query = query.filter(self.model.type == gift_type)
            
        # 获取总数
        total = query.count()
        
        # 应用分页
        gift_rules = query.order_by(self.model.id.desc()).offset(skip).limit(limit).all()
        
        return {
            "list": gift_rules,
            "total": total
        }

    def update(self, session: Session, gift_rule_id: int, gift_rule: GiftRuleUpdate) -> Optional[GiftRule]:
        """更新赠送规则"""
        gift_rule_data = gift_rule.model_dump(exclude_unset=True)
        return super().update(session, gift_rule_id, **gift_rule_data)

    def delete(self, session: Session, gift_rule_id: int) -> bool:
        """删除赠送规则"""
        return super().delete(session, gift_rule_id)

    def to_dict(self, gift_rule: GiftRule) -> dict:
        """将赠送规则转换为字典"""
        return {
            "id": gift_rule.id,
            "name": gift_rule.name,
            "description": gift_rule.description,
            "start_time": gift_rule.start_time,
            "end_time": gift_rule.end_time,
            "is_mutual_exclusive": gift_rule.is_mutual_exclusive,
            "status": gift_rule.status,
            "type": gift_rule.type,
            "created_at": gift_rule.created_at,
            "updated_at": gift_rule.updated_at,
        }


class OrderGiftRuleDAO(DAO):
    """订单赠送规则 DAO 类"""

    def __init__(self):
        super().__init__(OrderGiftRule)

    def create(self, session: Session, order_gift_rule: OrderGiftRuleCreate) -> OrderGiftRule:
        """创建订单赠送规则"""
        # 从输入数据中提取订单商品和赠送商品ID列表
        order_products = order_gift_rule.order_products
        gift_products = order_gift_rule.gift_products
        
        # 移除不属于模型的字段
        order_gift_rule_data = order_gift_rule.model_dump(exclude={'order_products', 'gift_products'})
        
        # 创建订单赠送规则
        order_gift_rule_obj = super().create(session, **order_gift_rule_data)
        
        # 创建订单商品关联
        for product_id in order_products:
            rel = OrdGiftRuleOrdProdRel(
                gift_rule_condition_id=order_gift_rule_obj.id,
                order_product_id=product_id,
                quantity=1  # 默认数量为1
            )
            session.add(rel)
        
        # 创建赠送商品关联
        for product_id in gift_products:
            rel = OrdGiftRuleGiftProdRel(
                gift_rule_result_id=order_gift_rule_obj.id,
                gift_product_id=product_id,
                quantity=1  # 默认数量为1
            )
            session.add(rel)
        
        session.commit()
        return order_gift_rule_obj

    def get(self, session: Session, order_gift_rule_id: int) -> Optional[OrderGiftRule]:
        """获取订单赠送规则"""
        import logging

        logger = logging.getLogger(__name__)
        
        rule = session.query(self.model)\
            .options(
                selectinload(OrderGiftRule.order_product_rels).selectinload(OrdGiftRuleOrdProdRel.order_product),
                selectinload(OrderGiftRule.gift_product_rels).selectinload(OrdGiftRuleGiftProdRel.gift_product)
            )\
            .filter(self.model.id == order_gift_rule_id)\
            .first()
            
        if rule:
            # 记录关联数据的加载情况
            logger.info(f"Order product rels: {rule.order_product_rels}")
            for rel in rule.order_product_rels:
                logger.info(f"Order product: {rel.order_product}")
                
            logger.info(f"Gift product rels: {rule.gift_product_rels}")
            for rel in rule.gift_product_rels:
                logger.info(f"Gift product: {rel.gift_product}")
                
        return rule

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[OrderGiftRule]:
        """获取订单赠送规则列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def get_active_order_gift_rules(self, session: Session, skip: int = 0, limit: int = 100) -> List[OrderGiftRule]:
        """获取活跃订单赠送规则列表"""
        return session.query(self.model).filter(
            self.model.status == Status.ACTIVE
        ).offset(skip).limit(limit).all()

    def search(self, session: Session, keyword: Optional[str] = None,
              name: Optional[str] = None, status: Optional[Status] = None,
              order_type: Optional[OrderType] = None,
              skip: int = 0, limit: int = 100) -> Dict[str, Any]:
        """
        根据条件搜索订单赠送规则
        
        Args:
            session: 数据库会话
            keyword: 关键词，用于模糊搜索
            name: 规则名称，用于模糊搜索
            status: 规则状态
            order_type: 订单类型
            skip: 分页偏移量
            limit: 分页大小
            
        Returns:
            Dict: 包含订单赠送规则列表和总数的字典
        """
        query = session.query(self.model)
        
        # 应用过滤条件
        if keyword:
            query = query.filter(self.model.name.like(f"%{keyword}%"))
        
        if name:
            query = query.filter(self.model.name.like(f"%{name}%"))
            
        if status is not None:
            query = query.filter(self.model.status == status)
            
        if order_type is not None:
            query = query.filter(self.model.order_type == order_type)
            
        # 获取总数
        total = query.count()
        
        # 应用分页
        order_gift_rules = query.order_by(self.model.id.desc()).offset(skip).limit(limit).all()
        
        return {
            "list": order_gift_rules,
            "total": total
        }

    def update(self, session: Session, order_gift_rule_id: int, 
              order_gift_rule: OrderGiftRuleUpdate) -> Optional[OrderGiftRule]:
        """更新订单赠送规则"""
        order_gift_rule_data = order_gift_rule.model_dump(exclude_unset=True)
        return super().update(session, order_gift_rule_id, **order_gift_rule_data)

    def delete(self, session: Session, order_gift_rule_id: int) -> bool:
        """删除订单赠送规则"""
        return super().delete(session, order_gift_rule_id)

    def get_order_products_by_rule(self, session: Session, rule_id: int, 
                                  skip: int = 0, limit: int = 100) -> List[OrdGiftRuleOrdProdRel]:
        """获取订单赠送规则关联的订单商品"""
        return session.query(OrdGiftRuleOrdProdRel).filter(
            OrdGiftRuleOrdProdRel.gift_rule_condition_id == rule_id
        ).offset(skip).limit(limit).all()

    def get_gift_products_by_rule(self, session: Session, rule_id: int,
                                 skip: int = 0, limit: int = 100) -> List[OrdGiftRuleGiftProdRel]:
        """获取订单赠送规则关联的赠送商品"""
        return session.query(OrdGiftRuleGiftProdRel).filter(
            OrdGiftRuleGiftProdRel.gift_rule_result_id == rule_id
        ).offset(skip).limit(limit).all()

    def add_order_products(self, session: Session, rule_id: int, 
                          product_quantities: List[Dict[str, int]]) -> dict:
        """批量添加订单商品到赠送规则
        
        Args:
            session: 数据库会话
            rule_id: 赠送规则ID
            product_quantities: 商品数量列表 [{"product_id": 1, "quantity": 2}, ...]
            
        Returns:
            dict: 包含成功和失败的结果
        """
        rule = session.query(OrderGiftRule).filter(OrderGiftRule.id == rule_id).first()
        if not rule:
            return {"success": [], "failed": product_quantities}

        success_items = []
        failed_items = []

        for item in product_quantities:
            product_id = item.get("product_id")
            quantity = item.get("quantity", 1)
            
            # 获取Product实例
            product = session.query(Product).filter(Product.id == product_id).first()
            if not product:
                failed_items.append(item)
                continue

            # 检查是否已经绑定
            existing = session.query(OrdGiftRuleOrdProdRel).filter(
                and_(
                    OrdGiftRuleOrdProdRel.gift_rule_condition_id == rule_id,
                    OrdGiftRuleOrdProdRel.order_product_id == product_id
                )
            ).first()

            if not existing:
                rel = OrdGiftRuleOrdProdRel(
                    gift_rule_condition_id=rule_id,
                    order_product_id=product_id,
                    quantity=quantity
                )
                session.add(rel)
                success_items.append(item)
            else:
                # 更新数量
                existing.quantity = quantity
                success_items.append(item)

        session.commit()
        return {"success": success_items, "failed": failed_items}

    def add_gift_products(self, session: Session, rule_id: int,
                         product_quantities: List[Dict[str, int]]) -> dict:
        """批量添加赠送商品到赠送规则"""
        rule = session.query(OrderGiftRule).filter(OrderGiftRule.id == rule_id).first()
        if not rule:
            return {"success": [], "failed": product_quantities}

        success_items = []
        failed_items = []

        for item in product_quantities:
            product_id = item.get("product_id")
            quantity = item.get("quantity", 1)
            
            # 获取Product实例
            product = session.query(Product).filter(Product.id == product_id).first()
            if not product:
                failed_items.append(item)
                continue

            # 检查是否已经绑定
            existing = session.query(OrdGiftRuleGiftProdRel).filter(
                and_(
                    OrdGiftRuleGiftProdRel.gift_rule_result_id == rule_id,
                    OrdGiftRuleGiftProdRel.gift_product_id == product_id
                )
            ).first()

            if not existing:
                rel = OrdGiftRuleGiftProdRel(
                    gift_rule_result_id=rule_id,
                    gift_product_id=product_id,
                    quantity=quantity
                )
                session.add(rel)
                success_items.append(item)
            else:
                # 更新数量
                existing.quantity = quantity
                success_items.append(item)

        session.commit()
        return {"success": success_items, "failed": failed_items}

    def remove_order_products(self, session: Session, rule_id: int,
                             product_ids: List[int]) -> dict:
        """批量移除订单商品关联"""
        success_ids = []
        failed_ids = []

        for product_id in product_ids:
            existing = session.query(OrdGiftRuleOrdProdRel).filter(
                and_(
                    OrdGiftRuleOrdProdRel.gift_rule_condition_id == rule_id,
                    OrdGiftRuleOrdProdRel.order_product_id == product_id
                )
            ).first()

            if existing:
                session.delete(existing)
                success_ids.append(product_id)
            else:
                failed_ids.append(product_id)

        session.commit()
        return {"success": success_ids, "failed": failed_ids}

    def remove_gift_products(self, session: Session, rule_id: int,
                            product_ids: List[int]) -> dict:
        """批量移除赠送商品关联"""
        success_ids = []
        failed_ids = []

        for product_id in product_ids:
            existing = session.query(OrdGiftRuleGiftProdRel).filter(
                and_(
                    OrdGiftRuleGiftProdRel.gift_rule_result_id == rule_id,
                    OrdGiftRuleGiftProdRel.gift_product_id == product_id
                )
            ).first()

            if existing:
                session.delete(existing)
                success_ids.append(product_id)
            else:
                failed_ids.append(product_id)

        session.commit()
        return {"success": success_ids, "failed": failed_ids}

    def to_dict(self, order_gift_rule: OrderGiftRule) -> dict:
        """将订单赠送规则转换为字典"""
        result = {
            "id": order_gift_rule.id,
            "name": order_gift_rule.name,
            "description": order_gift_rule.description,
            "start_time": order_gift_rule.start_time,
            "end_time": order_gift_rule.end_time,
            "is_mutual_exclusive": order_gift_rule.is_mutual_exclusive,
            "status": order_gift_rule.status,
            "type": order_gift_rule.type,
            "order_type": order_gift_rule.order_type,
            "order_start_time": order_gift_rule.order_start_time,
            "order_end_time": order_gift_rule.order_end_time,
            "order_amount": order_gift_rule.order_amount,
            "gift_amount": order_gift_rule.gift_amount,
            "created_at": order_gift_rule.created_at,
            "updated_at": order_gift_rule.updated_at,
        }
        
        # 添加关联商品
        if hasattr(order_gift_rule, 'order_product_rels') and order_gift_rule.order_product_rels:
            result["order_product_rels"] = [
                ord_gift_rule_ord_prod_rel_dao.to_dict(rel) 
                for rel in order_gift_rule.order_product_rels
            ]
        
        if hasattr(order_gift_rule, 'gift_product_rels') and order_gift_rule.gift_product_rels:
            result["gift_product_rels"] = [
                ord_gift_rule_gift_prod_rel_dao.to_dict(rel)
                for rel in order_gift_rule.gift_product_rels
            ]
        
        return result


class OrdGiftRuleOrdProdRelDAO(DAO):
    """订单赠送规则与订单商品关联 DAO 类"""

    def __init__(self):
        super().__init__(OrdGiftRuleOrdProdRel)

    def create(self, session: Session, rel: OrdGiftRuleOrdProdRelCreate) -> OrdGiftRuleOrdProdRel:
        """创建关联"""
        rel_data = rel.model_dump()
        return super().create(session, **rel_data)

    def get(self, session: Session, rel_id: int) -> Optional[OrdGiftRuleOrdProdRel]:
        """获取关联"""
        return super().get(session, rel_id)

    def update(self, session: Session, rel_id: int, 
              rel: OrdGiftRuleOrdProdRelUpdate) -> Optional[OrdGiftRuleOrdProdRel]:
        """更新关联"""
        rel_data = rel.model_dump(exclude_unset=True)
        return super().update(session, rel_id, **rel_data)

    def delete(self, session: Session, rel_id: int) -> bool:
        """删除关联"""
        return super().delete(session, rel_id)

    def to_dict(self, rel: OrdGiftRuleOrdProdRel) -> dict:
        """将关联转换为字典"""
        return {
            "id": rel.id,
            "gift_rule_condition_id": rel.gift_rule_condition_id,
            "order_product_id": rel.order_product_id,
            "quantity": rel.quantity,
            "name": rel.order_product.name if rel.order_product else None
        }


class OrdGiftRuleGiftProdRelDAO(DAO):
    """订单赠送规则与赠送商品关联 DAO 类"""

    def __init__(self):
        super().__init__(OrdGiftRuleGiftProdRel)

    def create(self, session: Session, rel: OrdGiftRuleGiftProdRelCreate) -> OrdGiftRuleGiftProdRel:
        """创建关联"""
        rel_data = rel.model_dump()
        return super().create(session, **rel_data)

    def get(self, session: Session, rel_id: int) -> Optional[OrdGiftRuleGiftProdRel]:
        """获取关联"""
        return super().get(session, rel_id)

    def update(self, session: Session, rel_id: int,
              rel: OrdGiftRuleGiftProdRelUpdate) -> Optional[OrdGiftRuleGiftProdRel]:
        """更新关联"""
        rel_data = rel.model_dump(exclude_unset=True)
        return super().update(session, rel_id, **rel_data)

    def delete(self, session: Session, rel_id: int) -> bool:
        """删除关联"""
        return super().delete(session, rel_id)

    def to_dict(self, rel: OrdGiftRuleGiftProdRel) -> dict:
        """将关联转换为字典"""
        return {
            "id": rel.id,
            "gift_rule_result_id": rel.gift_rule_result_id,
            "gift_product_id": rel.gift_product_id,
            "quantity": rel.quantity,
            "name": rel.gift_product.name if rel.gift_product else None
        }


# 创建 DAO 实例
gift_rule_dao = GiftRuleDAO()
order_gift_rule_dao = OrderGiftRuleDAO()
ord_gift_rule_ord_prod_rel_dao = OrdGiftRuleOrdProdRelDAO()
ord_gift_rule_gift_prod_rel_dao = OrdGiftRuleGiftProdRelDAO() 