from datetime import datetime
from typing import List, Optional, Dict

from sqlalchemy import and_, or_
from sqlalchemy.orm import Session, joinedload

from app.dao.base import DAO
from app.models.enum import Status
from app.models.menu import Menu
from app.models.content import Content
from app.models.product import Product
from app.models.rule import Rule, DiningReservationRule, RuleType
from app.schemas.menu import MenuCreate, MenuUpdate


class MenuDAO(DAO):
    """菜单 DAO 类"""

    def __init__(self):
        super().__init__(Menu)

    def create(self, session: Session, menu: MenuCreate) -> Menu:
        """创建菜单
        
        Args:
            session: 数据库会话
            menu: 创建菜单的数据
            
        Returns:
            创建的菜单对象
        """
        menu_data = menu.model_dump(exclude={"content_ids"})
        menu_obj = super().create(session, **menu_data)
        
        # 关联内容
        if menu.content_ids:
            contents = session.query(Content).filter(Content.id.in_(menu.content_ids)).all()
            menu_obj.contents = contents
            session.commit()
            session.refresh(menu_obj)
        
        return menu_obj

    def get(self, session: Session, menu_id: int) -> Optional[Menu]:
        """获取菜单
        
        Args:
            session: 数据库会话
            menu_id: 菜单ID
            
        Returns:
            菜单对象或None，包含餐厅预订规则信息（如果关联的规则是餐厅预订规则）
        """
        menu_obj = session.query(self.model).options(
            joinedload(self.model.product),
            joinedload(self.model.rule)
        ).filter(self.model.id == menu_id).first()
        
        if menu_obj and menu_obj.rule_id and menu_obj.rule:
            # 如果菜单关联了规则，且规则是餐厅预订规则，则获取其特有属性
            if menu_obj.rule.type == RuleType.DINING_RESERVATION:
                dining_rule = session.query(DiningReservationRule).filter(
                    DiningReservationRule.id == menu_obj.rule_id
                ).first()
                
                if dining_rule:
                    # 设置规则信息
                    setattr(menu_obj, "rule_id", dining_rule.id)
                    setattr(menu_obj, "rule_name", dining_rule.name)
                    setattr(menu_obj, "rule_status", dining_rule.status)
                    setattr(menu_obj, "rule_type", dining_rule.type.value if dining_rule.type else None)
                    
                    # 设置餐厅预订规则特有属性
                    setattr(menu_obj, "rule_alias", dining_rule.alias)
                    setattr(menu_obj, "dining_start_time_cron_str", dining_rule.dining_start_time_cron_str)
                    setattr(menu_obj, "dining_end_time_cron_str", dining_rule.dining_end_time_cron_str)
                    setattr(menu_obj, "verify_start_time_cron_str", dining_rule.verify_start_time_cron_str)
                    setattr(menu_obj, "verify_end_time_cron_str", dining_rule.verify_end_time_cron_str)
                    setattr(menu_obj, "order_deadline", dining_rule.order_deadline)
                    setattr(menu_obj, "cancellation_deadline", dining_rule.cancellation_deadline)
                    setattr(menu_obj, "is_auto_verify", dining_rule.is_auto_verify)
            else:
                # 如果不是餐厅预订规则，则只设置基本规则信息
                setattr(menu_obj, "rule_id", menu_obj.rule.id)
                setattr(menu_obj, "rule_name", menu_obj.rule.name)
                setattr(menu_obj, "rule_status", menu_obj.rule.status)
                setattr(menu_obj, "rule_type", menu_obj.rule.type.value if menu_obj.rule.type else None)
        
        return menu_obj

    def get_list(self, session: Session, skip: int = 0, limit: int = 100,
                 product_id: Optional[int] = None, rule_id: Optional[int] = None,
                 status: Optional[Status] = None) -> List[Dict]:
        """获取菜单列表
        
        Args:
            session: 数据库会话
            skip: 跳过的记录数
            limit: 限制返回的记录数
            product_id: 按产品ID筛选
            rule_id: 按规则ID筛选
            status: 按状态筛选
            
        Returns:
            菜单列表，包含产品名称和规则名称
        """
        query = session.query(self.model).options(
            joinedload(self.model.product),
            joinedload(self.model.rule)
        )
        
        # 应用筛选条件
        if product_id is not None:
            query = query.filter(self.model.product_id == product_id)
        if rule_id is not None:
            query = query.filter(self.model.rule_id == rule_id)
        if status is not None:
            query = query.filter(self.model.status == status)
            
        items = query.offset(skip).limit(limit).all()
        
        # 构建返回结果，添加product_name和rule_name
        result_list = []
        for item in items:
            menu_data = {
                "id": item.id,
                "name": item.name,
                "description": item.description,
                "status": item.status,
                "created_at": item.created_at,
                "updated_at": item.updated_at,
                "product_id": item.product_id,
                "rule_id": item.rule_id,
                "available_start_time_cron_str": item.available_start_time_cron_str,
                "available_end_time_cron_str": item.available_end_time_cron_str,
                "available_date": item.available_date,
                "product_name": item.product.name if item.product else None,
                "rule_name": item.rule.name if item.rule else None
            }
            result_list.append(menu_data)
        
        return result_list

    def get_rule_data(self, session: Session, rule_id: int) -> Dict:
        """获取规则相关数据，包括DiningReservationRule的属性
        
        Args:
            session: 数据库会话
            rule_id: 规则ID
            
        Returns:
            包含规则及其子类属性的字典
        """
        # 先获取基本规则信息
        rule = session.query(Rule).filter(Rule.id == rule_id).first()
        if not rule:
            return {}
            
        rule_data = {
            "rule_id": rule.id,
            "rule_name": rule.name,
            "rule_status": rule.status,
            "rule_type": rule.type.value if rule.type else None,
        }
        
        # 如果是餐厅预订规则，获取其特有属性
        if rule.type == RuleType.DINING_RESERVATION:
            dining_rule = session.query(DiningReservationRule).filter(DiningReservationRule.id == rule_id).first()
            if dining_rule:
                rule_data.update({
                    "rule_alias": dining_rule.alias,
                    "dining_start_time_cron_str": dining_rule.dining_start_time_cron_str,
                    "dining_end_time_cron_str": dining_rule.dining_end_time_cron_str, 
                    "verify_start_time_cron_str": dining_rule.verify_start_time_cron_str,
                    "verify_end_time_cron_str": dining_rule.verify_end_time_cron_str,
                    "order_deadline": dining_rule.order_deadline,
                    "cancellation_deadline": dining_rule.cancellation_deadline,
                    "is_auto_verify": dining_rule.is_auto_verify
                })
                
        return rule_data

    def search(self, session: Session, keyword: Optional[str] = None, name: Optional[str] = None,
               product_id: Optional[int] = None, rule_id: Optional[int] = None, 
               status: Optional[Status] = None, skip: int = 0, limit: int = 100) -> Dict:
        """搜索菜单
        
        Args:
            session: 数据库会话
            keyword: 搜索关键词，可搜索菜单名称、菜单描述等
            name: 菜单名称（模糊匹配）
            product_id: 按产品ID筛选
            rule_id: 按规则ID筛选
            status: 按状态筛选
            skip: 跳过的记录数
            limit: 限制返回的记录数
            
        Returns:
            dict: 包含 'total'（搜索结果总数）和 'list'（分页后的菜单列表）的字典
        """
        # 使用joinedload预加载关联的产品和规则
        query = session.query(self.model).options(
            joinedload(self.model.product),
            joinedload(self.model.rule)
        )
        
        # 根据关键词搜索
        if keyword:
            search_keyword = f"%{keyword}%"
            query = query.filter(
                or_(
                    self.model.name.ilike(search_keyword),
                    self.model.description.ilike(search_keyword)
                )
            )
            
        # 按菜单名称搜索
        if name:
            search_name = f"%{name}%"
            query = query.filter(self.model.name.ilike(search_name))
            
        # 应用筛选条件
        if product_id is not None:
            query = query.filter(self.model.product_id == product_id)
        if rule_id is not None:
            query = query.filter(self.model.rule_id == rule_id)
        if status is not None:
            query = query.filter(self.model.status == status)
            
        # 获取总数
        total = query.count()
        
        # 获取分页后的列表
        items = query.order_by(self.model.id.desc()).offset(skip).limit(limit).all()
        
        # 提取所有规则ID
        rule_ids = set(item.rule_id for item in items if item.rule_id is not None)
        
        # 一次性查询所有相关DiningReservationRule
        dining_rules = {}
        if rule_ids:
            # 先获取所有规则的基本信息
            rules = session.query(Rule).filter(Rule.id.in_(rule_ids)).all()
            rule_type_map = {rule.id: rule.type for rule in rules}
            
            # 获取所有餐厅预订规则
            dining_rule_ids = [rule_id for rule_id in rule_ids if rule_type_map.get(rule_id) == RuleType.DINING_RESERVATION]
            if dining_rule_ids:
                dining_rule_objs = session.query(DiningReservationRule).filter(
                    DiningReservationRule.id.in_(dining_rule_ids)
                ).all()
                dining_rules = {dr.id: dr for dr in dining_rule_objs}
        
        # 构建返回结果
        result_list = []
        for item in items:
            # 获取基本菜单信息
            menu_data = {
                "id": item.id,
                "name": item.name,
                "description": item.description,
                "status": item.status,
                "created_at": item.created_at,
                "updated_at": item.updated_at,
                "product_id": item.product_id,
                "rule_id": item.rule_id,
                "available_start_time_cron_str": item.available_start_time_cron_str,
                "available_end_time_cron_str": item.available_end_time_cron_str,
                "available_date": item.available_date,
                "product_name": item.product.name if item.product else None,
                "rule_name": item.rule.name if item.rule else None
            }
            
            # 添加规则信息
            if item.rule_id and item.rule:
                rule = item.rule
                rule_data = {
                    "rule_id": rule.id,
                    "rule_name": rule.name,
                    "rule_status": rule.status,
                    "rule_type": rule.type.value if rule.type else None,
                }
                
                # 如果是餐厅预订规则，添加特有属性
                if rule.type == RuleType.DINING_RESERVATION and item.rule_id in dining_rules:
                    dining_rule = dining_rules[item.rule_id]
                    rule_data.update({
                        "rule_alias": dining_rule.alias,
                        "dining_start_time_cron_str": dining_rule.dining_start_time_cron_str,
                        "dining_end_time_cron_str": dining_rule.dining_end_time_cron_str,
                        "verify_start_time_cron_str": dining_rule.verify_start_time_cron_str,
                        "verify_end_time_cron_str": dining_rule.verify_end_time_cron_str,
                        "order_deadline": dining_rule.order_deadline,
                        "cancellation_deadline": dining_rule.cancellation_deadline,
                        "is_auto_verify": dining_rule.is_auto_verify
                    })
                
                menu_data.update(rule_data)
                
            result_list.append(menu_data)
        
        return {
            "total": total,
            "list": result_list
        }

    def count(self, session: Session, product_id: Optional[int] = None,
              rule_id: Optional[int] = None, status: Optional[Status] = None) -> int:
        """获取菜单数量
        
        Args:
            session: 数据库会话
            product_id: 按产品ID筛选
            rule_id: 按规则ID筛选
            status: 按状态筛选
            
        Returns:
            菜单数量
        """
        query = session.query(self.model)
        
        # 应用筛选条件
        if product_id is not None:
            query = query.filter(self.model.product_id == product_id)
        if rule_id is not None:
            query = query.filter(self.model.rule_id == rule_id)
        if status is not None:
            query = query.filter(self.model.status == status)
            
        return query.count()

    def update(self, session: Session, menu_id: int, menu: MenuUpdate) -> Optional[Menu]:
        """更新菜单
        
        Args:
            session: 数据库会话
            menu_id: 菜单ID
            menu: 更新菜单的数据
            
        Returns:
            更新后的菜单对象或None
        """
        menu_obj = self.get(session, menu_id)
        if not menu_obj:
            return None
            
        # 更新基本字段
        menu_data = menu.model_dump(exclude={"content_ids"}, exclude_unset=True)
        for key, value in menu_data.items():
            setattr(menu_obj, key, value)
            
        # 更新内容关联
        if menu.content_ids is not None:
            contents = session.query(Content).filter(Content.id.in_(menu.content_ids)).all()
            menu_obj.contents = contents
            
        session.commit()
        session.refresh(menu_obj)
        return menu_obj

    def update_status(self, session: Session, menu_id: int, status: Status) -> Optional[Menu]:
        """更新菜单状态
        
        Args:
            session: 数据库会话
            menu_id: 菜单ID
            status: 新的状态
            
        Returns:
            更新后的菜单对象或None
        """
        menu_obj = session.query(self.model).filter(self.model.id == menu_id).first()
        if not menu_obj:
            return None
            
        menu_obj.status = status
        session.commit()
        session.refresh(menu_obj)
        return menu_obj

    def delete(self, session: Session, menu_id: int) -> bool:
        """删除菜单
        
        Args:
            session: 数据库会话
            menu_id: 菜单ID
            
        Returns:
            是否删除成功
        """
        return super().delete(session, menu_id)
        
    def get_active_menu_for_date(self, session: Session, product_id: int, target_date: datetime) -> List[Dict]:
        """获取指定日期的有效菜单
        
        Args:
            session: 数据库会话
            product_id: 产品ID
            target_date: 目标日期
            
        Returns:
            有效的菜单列表，包含产品名称和规则名称
        """
        # 查询逻辑：
        # 1. 状态为激活的菜单
        # 2. 产品ID匹配
        # 3. 如果有设置available_date，则必须匹配目标日期
        # 4. 根据available_start_time_cron_str和available_end_time_cron_str判断是否在生效时间范围内
        #    (这部分可能需要在应用层处理，因为cron表达式的解析较为复杂)
        
        target_day = target_date.date()
        query = session.query(self.model).options(
            joinedload(self.model.product),
            joinedload(self.model.rule)
        ).filter(
            and_(
                self.model.status == Status.ACTIVE,
                self.model.product_id == product_id,
                (self.model.available_date.is_(None) | (self.model.available_date == target_day))
            )
        )
        
        items = query.all()
        
        # 提取所有规则ID
        rule_ids = set(item.rule_id for item in items if item.rule_id is not None)
        
        # 一次性查询所有相关DiningReservationRule
        dining_rules = {}
        if rule_ids:
            # 先获取所有规则的基本信息（已经由joinedload加载）
            rule_type_map = {item.rule.id: item.rule.type for item in items if item.rule}
            
            # 获取所有餐厅预订规则
            dining_rule_ids = [rule_id for rule_id in rule_ids if rule_type_map.get(rule_id) == RuleType.DINING_RESERVATION]
            if dining_rule_ids:
                dining_rule_objs = session.query(DiningReservationRule).filter(
                    DiningReservationRule.id.in_(dining_rule_ids)
                ).all()
                dining_rules = {dr.id: dr for dr in dining_rule_objs}
        
        # 构建返回结果
        result_list = []
        for item in items:
            # 获取基本菜单数据
            menu_data = {
                "id": item.id,
                "name": item.name,
                "description": item.description,
                "status": item.status,
                "created_at": item.created_at,
                "updated_at": item.updated_at,
                "product_id": item.product_id,
                "rule_id": item.rule_id,
                "available_start_time_cron_str": item.available_start_time_cron_str,
                "available_end_time_cron_str": item.available_end_time_cron_str,
                "available_date": item.available_date,
                "product_name": item.product.name if item.product else None,
                "rule_name": item.rule.name if item.rule else None
            }
            
            # 添加规则信息
            if item.rule_id and item.rule:
                rule = item.rule
                rule_data = {
                    "rule_id": rule.id,
                    "rule_name": rule.name,
                    "rule_status": rule.status,
                    "rule_type": rule.type.value if rule.type else None,
                }
                
                # 如果是餐厅预订规则，添加特有属性
                if rule.type == RuleType.DINING_RESERVATION and item.rule_id in dining_rules:
                    dining_rule = dining_rules[item.rule_id]
                    rule_data.update({
                        "rule_alias": dining_rule.alias,
                        "dining_start_time_cron_str": dining_rule.dining_start_time_cron_str,
                        "dining_end_time_cron_str": dining_rule.dining_end_time_cron_str,
                        "verify_start_time_cron_str": dining_rule.verify_start_time_cron_str,
                        "verify_end_time_cron_str": dining_rule.verify_end_time_cron_str,
                        "order_deadline": dining_rule.order_deadline,
                        "cancellation_deadline": dining_rule.cancellation_deadline,
                        "is_auto_verify": dining_rule.is_auto_verify
                    })
                
                menu_data.update(rule_data)
                
            result_list.append(menu_data)
        
        # 注意：这里返回的菜单还需要在应用层根据cron表达式进一步筛选
        return result_list

    def get_menu_with_contents(self, session: Session, menu_id: int) -> Optional[Menu]:
        """获取菜单及其关联的内容详情
        
        Args:
            session: 数据库会话
            menu_id: 菜单ID
            
        Returns:
            菜单对象或None，包含详细的content列表、餐厅预订规则信息（如果关联的规则是餐厅预订规则）
        """
        menu_obj = session.query(self.model).options(
            joinedload(self.model.product),
            joinedload(self.model.rule),
            joinedload(self.model.contents).joinedload(Content.files),
            joinedload(self.model.contents).joinedload(Content.tags)
        ).filter(self.model.id == menu_id).first()
        
        if not menu_obj:
            return None
        
        # 设置规则信息（复用原有逻辑）
        if menu_obj.rule_id and menu_obj.rule:
            # 如果菜单关联了规则，且规则是餐厅预订规则，则获取其特有属性
            if menu_obj.rule.type == RuleType.DINING_RESERVATION:
                dining_rule = session.query(DiningReservationRule).filter(
                    DiningReservationRule.id == menu_obj.rule_id
                ).first()
                
                if dining_rule:
                    # 设置规则信息
                    setattr(menu_obj, "rule_id", dining_rule.id)
                    setattr(menu_obj, "rule_name", dining_rule.name)
                    setattr(menu_obj, "rule_status", dining_rule.status)
                    setattr(menu_obj, "rule_type", dining_rule.type.value if dining_rule.type else None)
                    
                    # 设置餐厅预订规则特有属性
                    setattr(menu_obj, "rule_alias", dining_rule.alias)
                    setattr(menu_obj, "dining_start_time_cron_str", dining_rule.dining_start_time_cron_str)
                    setattr(menu_obj, "dining_end_time_cron_str", dining_rule.dining_end_time_cron_str)
                    setattr(menu_obj, "verify_start_time_cron_str", dining_rule.verify_start_time_cron_str)
                    setattr(menu_obj, "verify_end_time_cron_str", dining_rule.verify_end_time_cron_str)
                    setattr(menu_obj, "order_deadline", dining_rule.order_deadline)
                    setattr(menu_obj, "cancellation_deadline", dining_rule.cancellation_deadline)
                    setattr(menu_obj, "is_auto_verify", dining_rule.is_auto_verify)
            else:
                # 如果不是餐厅预订规则，则只设置基本规则信息
                setattr(menu_obj, "rule_id", menu_obj.rule.id)
                setattr(menu_obj, "rule_name", menu_obj.rule.name)
                setattr(menu_obj, "rule_status", menu_obj.rule.status)
                setattr(menu_obj, "rule_type", menu_obj.rule.type.value if menu_obj.rule.type else None)
        
        return menu_obj


# 创建 DAO 实例
menu_dao = MenuDAO() 