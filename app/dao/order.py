from typing import List, Optional
import logging
from sqlalchemy.orm import Session
from datetime import datetime, time, timedelta

from app.dao.base import DAO
from app.models.order import Order, OrderItem, OrderStatus, PaymentStatus, PaymentMethod, OrderType
from app.models.reservation import ReservationType
from app.schemas.order import OrderCreate, OrderUpdate, OrderItemCreate, OrderItemUpdate
from app.models.account import AccountTransaction, TransactionType
from app.models.reservation import ReservationRequest, ReservationStatus
from app.utils.common import create_order_no

# 获取logger
logger = logging.getLogger(__name__)


class OrderDAO(DAO):
    """订单DAO类"""

    def __init__(self):
        super().__init__(Order)

    def create(self, session: Session, order: OrderCreate) -> Order:
        """创建订单"""
        order_data = order.model_dump()
        # 如果包含订单项，需要先移除
        if "items" in order_data:
            items_data = order_data.pop("items")
            order_obj = super().create(session, **order_data)
            order_obj_id = order_obj.id
            # 创建订单项
            for item_data in items_data:
                item_data["order_id"] = order_obj.id
                order_item_dao.create(session, OrderItemCreate(**item_data))
            # session.refresh(order_obj)
            order_obj = self.get(session, order_obj_id)
            return order_obj
        order_data["order_no"] = create_order_no()
        return super().create(session, **order_data)

    def get(self, session: Session, order_id: int) -> Optional[Order]:
        """获取订单"""
        return super().get(session, order_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[Order]:
        """获取订单列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, order_id: int, order: OrderUpdate) -> Optional[Order]:
        """更新订单"""
        order_data = order.model_dump(exclude_unset=True)
        return super().update(session, order_id, **order_data)

    def delete(self, session: Session, order_id: int) -> bool:
        """删除订单"""
        # 先删除关联的订单项
        order_items = session.query(OrderItem).filter(OrderItem.order_id == order_id).all()
        for item in order_items:
            session.delete(item)
        
        # 再删除订单本身
        return super().delete(session, order_id)

    def get_by_user(self, session: Session, user_id: int, skip: int = 0, limit: int = 100) -> List[Order]:
        """获取用户的所有订单"""
        return session.query(self.model).filter(self.model.user_id == user_id).offset(skip).limit(limit).all()

    def get_by_user_all(self, session: Session, user_id: int) -> List[Order]:
        """获取用户的所有订单"""
        return session.query(self.model).filter(self.model.user_id == user_id).all()

    def update_status(self, session: Session, order_id: int, status: OrderStatus) -> Optional[Order]:
        """更新订单状态"""
        order = self.get(session, order_id)
        if order:
            order.status = status
            session.commit()
            # session.refresh(order)
            order = self.get(session, order_id)
            return order
        return None

    def update_payment_status(self, session: Session, order_id: int, payment_status: PaymentStatus, status: OrderStatus) -> Optional[Order]:
        """更新支付状态"""
        order = self.get(session, order_id)
        if order:
            order.payment_status = payment_status
            order.payment_time = datetime.now()
            order.status = status
            session.commit()
            # session.refresh(order)
            order = self.get(session, order_id)
            return order
        return None

    def update_order_type(self, session: Session, order_id: int, order_type: OrderType) -> Optional[Order]:
        """更新订单类型"""
        order = self.get(session, order_id)
        if order:
            order.type = order_type
            session.commit()
            # session.refresh(order)
            order = self.get(session, order_id)
            return order
        return None

    def update_payment_method(self, session: Session, order_id: int, payment_method: PaymentMethod) -> Optional[Order]:
        """更新支付方式"""
        order = self.get(session, order_id)
        if order:
            order.payment_method = payment_method
            session.commit()
            # session.refresh(order)
            order = self.get(session, order_id)
            return order
        return None

    def get_by_order_no(self, session: Session, order_no: str) -> Optional[Order]:
        """根据订单号获取订单

        Args:
            session: 数据库会话
            order_no: 订单号

        Returns:
            Optional[Order]: 订单对象，如果不存在返回 None
        """
        try:
            logger.info(f"尝试查询订单号: {order_no}")
            result = session.query(self.model).filter(self.model.order_no == order_no).first()
            if result:
                logger.info(f"查询成功，订单ID: {result.id}")
            else:
                logger.warning(f"未找到订单号为 {order_no} 的订单")
            return result
        except Exception as e:
            logger.error(f"根据订单号获取订单失败: {str(e)}")
            logger.exception("详细异常信息")
            return None

    def cancel_update_status(self, session: Session, order_id: int, status: OrderStatus) -> Optional[Order]:
        """取消订单,更新订单状态"""
        order = self.get(session, order_id)
        if order:
            order.status = status
            session.commit()
            order = self.get(session, order_id)
            return order
        return None

    def cancel_order(self, session: Session, order_id: int) -> Optional[Order]:
        """取消订单"""
        order = self.get(session, order_id)
        if order:
            order.status = OrderStatus.REFUNDED
            order.payment_status = PaymentStatus.REFUNDED
            session.commit()
            order = self.get(session, order_id)
            return order
        return None

    def check_enterprise_order_in_day(self, session: Session, user_id: int, request_date_list: List[str]) -> bool:
        """
        检查用户在前后七天内是否已有同一企业支付成功的订单

        Args:
            session: 数据库会话
            user_id: 用户ID
            request_date_list: 订单消费日期列表
        Returns:
            bool: 如果七天内已有该企业支付成功的订单则返回True，否则返回False
        """
        try:
            # 获取当前日期
            today = datetime.now().date()
            # 计算前7天的日期
            seven_days_before = today - timedelta(days=7)
            seven_days_before_start = datetime.combine(seven_days_before, time.min)

            reservation_requests = session.query(ReservationRequest).filter(
                ReservationRequest.user_id == user_id,
                ReservationRequest.status == ReservationStatus.PAID_FULL,
                ReservationRequest.created_at >= seven_days_before_start
            ).all()

            logger.info(f"用户{user_id}前七天内预订订单数量: {len(reservation_requests)}")

            for reservation_request in reservation_requests:
                reservation_period = reservation_request.reservation_period
                reservation_period_start = reservation_period.split("_")[0]
                reservation_period_start = '20'+reservation_period_start[0:6]
                reservation_period_start = datetime.strptime(reservation_period_start, '%Y%m%d')
                logger.info(f"预订订单{reservation_request.id}的预订时间: {reservation_period_start}")
                # 01 需求调整：商务餐不占用员工自助餐名额
                if reservation_request.type == ReservationType.BIZ_DINING_RESERVATION:
                    logger.info(f"预订订单{reservation_request.id}是商务餐预订，跳过")
                    continue

                if reservation_period_start.date() in request_date_list:
                    logger.info(f"预订订单{reservation_period_start.date()}的预订时间在{request_date_list}中")
                    # 查询该预订订单的支付类型，区分如果非企业账户支付就跳过
                    # 通过订单项查找对应的订单
                    order_item = reservation_request.order_item
                    if order_item and order_item.order:
                        order_object = order_item.order
                        if order_object and order_object.payment_method == PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE:
                            logger.info(f"订单{order_object.order_no}是企业账户支付")
                            return True, reservation_period_start
                        else:
                            logger.info(f"订单{order_object.order_no if order_object else '未知'}不是企业账户支付，跳过")
                    else:
                        logger.info(f"预订请求{reservation_request.id}没有关联的订单项或订单，跳过")
            return False, None
        except Exception as e:
            logger.error(f"检查企业订单失败: {str(e)}")
            return False, None

    def check_enterprise_order_in_day_by_meal_type(self, session: Session, user_id: int, request_date_list: List[str], meal_type) -> bool:
        """
        检查用户在指定日期是否已有同一企业支付成功的指定餐食类型订单

        Args:
            session: 数据库会话
            user_id: 用户ID
            request_date_list: 订单消费日期列表
            meal_type: 餐食类型（MealType枚举）
        Returns:
            tuple: (bool, datetime) 如果已有该企业指定餐食类型支付成功的订单则返回(True, 预订时间)，否则返回(False, None)
        """
        try:
            from app.models.rule import RuleItem

            # 获取当前日期
            today = datetime.now().date()
            # 计算前7天的日期
            seven_days_before = today - timedelta(days=7)
            seven_days_before_start = datetime.combine(seven_days_before, time.min)

            # 查询用户前七天内已支付成功的预订请求，并关联规则项以获取餐食类型
            reservation_requests = session.query(ReservationRequest).join(
                RuleItem, ReservationRequest.rule_item_id == RuleItem.id
            ).filter(
                ReservationRequest.user_id == user_id,
                ReservationRequest.status == ReservationStatus.PAID_FULL,
                ReservationRequest.created_at >= seven_days_before_start,
                RuleItem.meal_type == meal_type  # 过滤特定餐食类型
            ).all()

            logger.info(f"用户{user_id}前七天内{meal_type.value if meal_type else '未知'}类型预订订单数量: {len(reservation_requests)}")

            for reservation_request in reservation_requests:
                reservation_period = reservation_request.reservation_period
                reservation_period_start = reservation_period.split("_")[0]
                reservation_period_start = '20'+reservation_period_start[0:6]
                reservation_period_start = datetime.strptime(reservation_period_start, '%Y%m%d')
                logger.info(f"预订订单{reservation_request.id}的预订时间: {reservation_period_start}")

                # 商务餐不占用员工自助餐名额
                if reservation_request.type == ReservationType.BIZ_DINING_RESERVATION:
                    logger.info(f"预订订单{reservation_request.id}是商务餐预订，跳过")
                    continue

                if reservation_period_start.date() in request_date_list:
                    logger.info(f"预订订单{reservation_period_start.date()}的预订时间在{request_date_list}中")
                    # 查询该预订订单的支付类型，区分如果非企业账户支付就跳过
                    order_item = reservation_request.order_item
                    if order_item and order_item.order:
                        order_object = order_item.order
                        if order_object and order_object.payment_method == PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE:
                            logger.info(f"订单{order_object.order_no}是企业账户支付，餐食类型: {meal_type.value if meal_type else '未知'}")
                            return True, reservation_period_start
                        else:
                            logger.info(f"订单{order_object.order_no if order_object else '未知'}不是企业账户支付，跳过")
                    else:
                        logger.info(f"预订请求{reservation_request.id}没有关联的订单项或订单，跳过")
            return False, None
        except Exception as e:
            logger.error(f"检查企业订单失败: {str(e)}")
            return False, None

    def update_split_payment_info(self, session: Session, order_id: int, enterprise_amount: float, personal_amount: float) -> Optional[Order]:
        """
        更新订单的分次支付信息

        Args:
            session: 数据库会话
            order_id: 订单ID
            enterprise_amount: 企业支付金额
            personal_amount: 个人需要支付的金额

        Returns:
            Optional[Order]: 更新后的订单对象，如果失败返回None
        """
        try:
            order = self.get(session, order_id)
            if not order:
                logger.error(f"订单不存在: {order_id}")
                return None

            # 更新分次支付相关字段（但不更改支付状态，让pay_order_partially来处理）
            order.requires_personal_payment = round(personal_amount, 2)  # 处理浮点数精度问题
            order.is_split_payment = True
            order.updated_at = datetime.now()

            session.commit()
            logger.info(f"订单 {order_id} 分次支付信息更新成功，个人待支付: {personal_amount}")

            # 重新获取更新后的订单
            order = self.get(session, order_id)
            return order

        except Exception as e:
            logger.error(f"更新订单分次支付信息失败: {str(e)}")
            session.rollback()
            return None

class OrderItemDAO(DAO):
    """订单项DAO类"""

    def __init__(self):
        super().__init__(OrderItem)

    def create(self, session: Session, order_item: OrderItemCreate) -> OrderItem:
        """创建订单项"""
        order_item_data = order_item.model_dump()
        return super().create(session, **order_item_data)

    def get(self, session: Session, order_item_id: int) -> Optional[OrderItem]:
        """获取订单项"""
        return super().get(session, order_item_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[OrderItem]:
        """获取订单项列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, order_item_id: int, order_item: OrderItemUpdate) -> Optional[OrderItem]:
        """更新订单项"""
        order_item_data = order_item.model_dump(exclude_unset=True)
        return super().update(session, order_item_id, **order_item_data)

    def delete(self, session: Session, order_item_id: int) -> bool:
        """删除订单项"""
        return super().delete(session, order_item_id)
    
    def get_by_order(self, session: Session, order_id: int) -> List[OrderItem]:
        """获取订单的所有订单项"""
        return session.query(self.model).filter(self.model.order_id == order_id).all()

    def update_status(self, session: Session, order_id: int, status: OrderStatus) -> Optional[OrderItem]:
        """更新订单状态"""
        order_items = session.query(self.model).filter(self.model.order_id == order_id).all()
        for order_item in order_items:
            order_item.status = status
            session.commit()
        return order_items

    def get_by_order_item_id(self, session: Session, order_item_id: int) -> Optional[OrderItem]:
        """根据订单项ID获取订单项"""
        return session.query(self.model).filter(self.model.id == order_item_id).first()


# 创建DAO实例
order_dao = OrderDAO()
order_item_dao = OrderItemDAO()
