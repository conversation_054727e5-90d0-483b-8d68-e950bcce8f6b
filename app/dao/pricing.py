from typing import List, Optional

from sqlalchemy import and_, or_
from sqlalchemy.orm import Session, joinedload

from app.dao.base import DAO
from app.models.pricing import PricingStrategy, DiscountStrategy, FullReductionStrategy, TimeLimitedStrategy, \
    MemberPriceStrategy, product_pricing_strategy_relation, PricingStrategyScope, PricingStrategyType, \
    BundleStrategy, BundleStrategyProdRel
from app.models.product import Product
from app.schemas.pricing import PricingStrategyCreate, PricingStrategyUpdate, DiscountStrategyCreate, \
    FullReductionStrategyCreate, TimeLimitedStrategyCreate, TimeLimitedStrategyUpdate, MemberPriceStrategyCreate, \
    MemberPriceStrategyUpdate, DiscountStrategyUpdate, FullReductionStrategyUpdate, BundleStrategyCreate, \
    BundleStrategyUpdate


class PricingStrategyDAO(DAO):
    """定价策略DAO类"""

    def __init__(self):
        super().__init__(PricingStrategy)

    def create(self, session: Session, strategy: PricingStrategyCreate) -> PricingStrategy:
        """创建定价策略"""
        strategy_data = strategy.model_dump()
        return super().create(session, **strategy_data)

    def get(self, session: Session, strategy_id: int) -> Optional[PricingStrategy]:
        """获取定价策略"""
        return super().get(session, strategy_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[PricingStrategy]:
        """获取定价策略列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, strategy_id: int, strategy: PricingStrategyUpdate) -> Optional[PricingStrategy]:
        """更新定价策略"""
        strategy_data = strategy.model_dump(exclude_unset=True)
        if 'type' in strategy_data:
            # 不允许修改type
            del strategy_data['type']
        return super().update(session, strategy_id, **strategy_data)

    def delete(self, session: Session, strategy_id: int) -> bool:
        """删除定价策略"""
        return super().delete(session, strategy_id)

    def get_strategies_by_scope(self, session: Session, scope: PricingStrategyScope) -> List[PricingStrategy]:
        """获取指定范围的定价策略列表

        Args:
            session: 数据库会话
            scope: 策略适用范围

        Returns:
            List[PricingStrategy]: 策略列表
        """
        return session.query(PricingStrategy).filter(PricingStrategy.scope == scope).all()

    def get_products_by_strategy(self, session: Session, strategy_id: int, skip: int = 0, limit: int = 100) -> List[
        Product]:
        """获取与特定定价策略绑定的所有产品

        Args:
            session: 数据库会话
            strategy_id: 定价策略ID
            skip: 跳过数量
            limit: 限制数量

        Returns:
            List[Product]: 产品列表
        """
        strategy = session.query(PricingStrategy).filter(PricingStrategy.id == strategy_id).first()
        if not strategy:
            return []

        return strategy.products[skip:skip + limit]

    def add_products(self, session: Session, product_ids: List[int], strategy_id: int) -> dict:
        """批量将产品绑定到定价策略

        Returns:
            dict: 包含成功和失败的产品ID列表
        """
        strategy = session.query(PricingStrategy).filter(PricingStrategy.id == strategy_id).first()
        if not strategy:
            return {"success": [], "failed": product_ids}
        success_ids = []
        failed_ids = []

        for product_id in product_ids:
            # 获取Product实例
            product = session.query(Product).filter(Product.id == product_id).first()
            if not product:
                failed_ids.append(product_id)
                continue

            # 检查是否已经绑定
            existing = session.query(product_pricing_strategy_relation).filter(
                and_(
                    product_pricing_strategy_relation.c.product_id == product_id,
                    product_pricing_strategy_relation.c.pricing_strategy_id == strategy_id
                )
            ).first()

            if not existing:
                strategy.products.append(product)
                success_ids.append(product_id)
            else:
                # 已经绑定过，也视为成功
                success_ids.append(product_id)

        session.commit()
        return {"success": success_ids, "failed": failed_ids}

    def remove_products(self, session: Session, product_ids: List[int], strategy_id: int) -> dict:
        """批量将产品从定价策略解绑

        Returns:
            dict: 包含成功和失败的产品ID列表
        """
        strategy = session.query(PricingStrategy).filter(PricingStrategy.id == strategy_id).first()
        if not strategy:
            return {"success": [], "failed": product_ids}

        success_ids = []
        failed_ids = []

        for product_id in product_ids:
            # 获取Product实例
            product = session.query(Product).filter(Product.id == product_id).first()
            if not product:
                failed_ids.append(product_id)
                continue

            # 检查是否已经绑定
            existing = session.query(product_pricing_strategy_relation).filter(
                and_(
                    product_pricing_strategy_relation.c.product_id == product_id,
                    product_pricing_strategy_relation.c.pricing_strategy_id == strategy_id
                )
            ).first()

            if existing:
                # 移除关联关系
                strategy.products.remove(product)
                success_ids.append(product_id)
            else:
                # 未绑定，也视为成功
                success_ids.append(product_id)

        session.commit()
        return {"success": success_ids, "failed": failed_ids}

    def search(self, session: Session, keyword: str = None, name: str = None, strategy_type: PricingStrategyType = None,
               status=None, skip: int = 0, limit: int = 100) -> dict:
        """
        根据条件搜索产品列表及总数

        Args:
            session: 数据库会话
            keyword: 搜索关键字，可搜索产品名称、产品描述等
            name: 产品名称（模糊匹配）
            strategy_type: 产品类型
            status: 产品状态
            skip: 分页起始位置
            limit: 分页大小

        Returns:
            dict: 包含 'total'（搜索结果总数）和 'list'（分页后的产品列表）的字典
        """
        query = session.query(self.model)

        # 如果提供了关键字，则添加过滤条件
        if keyword:
            search_keyword = f"%{keyword}%"
            query = query.filter(
                or_(
                    self.model.name.like(search_keyword),
                    self.model.description.like(search_keyword)
                )
            )

        # 按产品名称过滤
        if name:
            search_name = f"%{name}%"
            query = query.filter(self.model.name.like(search_name))

        # 按产品类型过滤
        if strategy_type is not None:
            query = query.filter(self.model.type == strategy_type)

        # 按状态过滤
        if status is not None:
            query = query.filter(self.model.status == status)

        # 查询总数
        total = query.count()
        # 查询分页后的产品列表
        items = query.order_by(self.model.id.desc()).offset(skip).limit(limit).all()

        return {
            "total": total,
            "list": items
        }


class DiscountStrategyDAO(DAO):
    """折扣策略DAO类"""

    def __init__(self):
        super().__init__(DiscountStrategy)

    def create(self, session: Session, strategy: DiscountStrategyCreate) -> DiscountStrategy:
        """创建折扣策略"""
        strategy_data = strategy.model_dump()
        return super().create(session, **strategy_data)

    def get(self, session: Session, strategy_id: int) -> Optional[DiscountStrategy]:
        """获取折扣策略"""
        return super().get(session, strategy_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[DiscountStrategy]:
        """获取折扣策略列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, strategy_id: int, strategy: DiscountStrategyUpdate) -> Optional[
        DiscountStrategy]:
        """更新折扣策略"""
        strategy_data = strategy.model_dump(exclude_unset=True)
        # 检查 strategy_data 中是否存在 type 属性
        if 'type' in strategy_data:
            # 如果存在，删除该属性
            del strategy_data['type']
        return super().update(session, strategy_id, **strategy_data)

    def delete(self, session: Session, strategy_id: int) -> bool:
        """删除折扣策略"""
        return super().delete(session, strategy_id)


class FullReductionStrategyDAO(DAO):
    """满减策略DAO类"""

    def __init__(self):
        super().__init__(FullReductionStrategy)

    def create(self, session: Session, strategy: FullReductionStrategyCreate) -> FullReductionStrategy:
        """创建满减策略"""
        strategy_data = strategy.model_dump()
        return super().create(session, **strategy_data)

    def get(self, session: Session, strategy_id: int) -> Optional[FullReductionStrategy]:
        """获取满减策略"""
        return super().get(session, strategy_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[FullReductionStrategy]:
        """获取满减策略列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, strategy_id: int, strategy: FullReductionStrategyUpdate) -> Optional[
        FullReductionStrategy]:
        """更新满减策略"""
        strategy_data = strategy.model_dump(exclude_unset=True)
        # 检查 strategy_data 中是否存在 type 属性
        if 'type' in strategy_data:
            # 如果存在，删除该属性
            del strategy_data['type']
        return super().update(session, strategy_id, **strategy_data)

    def delete(self, session: Session, strategy_id: int) -> bool:
        """删除满减策略"""
        return super().delete(session, strategy_id)


class TimeLimitedStrategyDAO(DAO):
    """限时策略DAO类"""

    def __init__(self):
        super().__init__(TimeLimitedStrategy)

    def create(self, session: Session, strategy: TimeLimitedStrategyCreate) -> TimeLimitedStrategy:
        """创建限时策略"""
        strategy_data = strategy.model_dump()
        return super().create(session, **strategy_data)

    def get(self, session: Session, strategy_id: int) -> Optional[TimeLimitedStrategy]:
        """获取限时策略"""
        return super().get(session, strategy_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[TimeLimitedStrategy]:
        """获取限时策略列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, strategy_id: int, strategy: TimeLimitedStrategyUpdate) -> Optional[
        TimeLimitedStrategy]:
        """更新限时策略"""
        strategy_data = strategy.model_dump(exclude_unset=True)
        # 检查 strategy_data 中是否存在 type 属性
        if 'type' in strategy_data:
            # 如果存在，删除该属性
            del strategy_data['type']
        return super().update(session, strategy_id, **strategy_data)

    def delete(self, session: Session, strategy_id: int) -> bool:
        """删除限时策略"""
        return super().delete(session, strategy_id)


class MemberPriceStrategyDAO(DAO):
    """会员价格策略DAO类"""

    def __init__(self):
        super().__init__(MemberPriceStrategy)

    def create(self, session: Session, strategy: MemberPriceStrategyCreate) -> MemberPriceStrategy:
        """创建会员价格策略"""
        strategy_data = strategy.model_dump()
        return super().create(session, **strategy_data)

    def get(self, session: Session, strategy_id: int) -> Optional[MemberPriceStrategy]:
        """获取会员价格策略"""
        return super().get(session, strategy_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[MemberPriceStrategy]:
        """获取会员价格策略列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, strategy_id: int, strategy: MemberPriceStrategyUpdate) -> Optional[
        MemberPriceStrategy]:
        """更新会员价格策略"""
        strategy_data = strategy.model_dump(exclude_unset=True)
        # 检查 strategy_data 中是否存在 type 属性
        if 'type' in strategy_data:
            # 如果存在，删除该属性
            del strategy_data['type']
        return super().update(session, strategy_id, **strategy_data)

    def delete(self, session: Session, strategy_id: int) -> bool:
        """删除会员价格策略"""
        return super().delete(session, strategy_id)


class BundleStrategyDAO(DAO):
    """捆绑销售策略DAO类"""

    def __init__(self):
        super().__init__(BundleStrategy)

    def create(self, session: Session, strategy: BundleStrategyCreate) -> BundleStrategy:
        """创建捆绑销售策略"""
        strategy_data = strategy.model_dump(exclude={'products'})

        # 创建基础策略
        db_strategy = super().create(session, **strategy_data)
        session.flush()  # 确保获得ID

        # 创建产品关联
        if strategy.products:
            for product_rel in strategy.products:
                rel_data = {
                    'bundle_strategy_id': db_strategy.id,
                    'product_id': product_rel.product_id,
                    'quantity': product_rel.quantity
                }
                db_rel = BundleStrategyProdRel(**rel_data)
                session.add(db_rel)

        session.commit()
        session.refresh(db_strategy)
        return db_strategy

    def get(self, session: Session, strategy_id: int) -> Optional[BundleStrategy]:
        """获取捆绑销售策略"""
        return super().get(session, strategy_id)

    def get_with_bundle_products(self, session: Session, strategy_id: int) -> Optional[BundleStrategy]:
        """获取捆绑销售策略（包含关联的产品信息）"""
        return session.query(BundleStrategy).options(
            joinedload(BundleStrategy.bundle_product_rels)
        ).filter(BundleStrategy.id == strategy_id).first()

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[BundleStrategy]:
        """获取捆绑销售策略列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def get_list_with_bundle_products(self, session: Session, skip: int = 0, limit: int = 100) -> List[BundleStrategy]:
        """获取捆绑销售策略列表（包含关联的产品信息）"""
        return session.query(BundleStrategy).options(
            joinedload(BundleStrategy.bundle_product_rels)
        ).offset(skip).limit(limit).all()

    def update(self, session: Session, strategy_id: int, strategy: BundleStrategyUpdate) -> Optional[BundleStrategy]:
        """更新捆绑销售策略"""
        strategy_data = strategy.model_dump(exclude_unset=True, exclude={'products'})

        # 检查 strategy_data 中是否存在 type 属性
        if 'type' in strategy_data:
            # 如果存在，删除该属性
            del strategy_data['type']

        # 更新基础策略数据
        db_strategy = super().update(session, strategy_id, **strategy_data)
        if not db_strategy:
            return None

        # 更新产品关联
        if strategy.products is not None:
            # 删除现有关联
            session.query(BundleStrategyProdRel).filter(
                BundleStrategyProdRel.bundle_strategy_id == strategy_id
            ).delete()

            # 创建新关联
            for product_rel in strategy.products:
                rel_data = {
                    'bundle_strategy_id': strategy_id,
                    'product_id': product_rel.product_id,
                    'quantity': product_rel.quantity
                }
                db_rel = BundleStrategyProdRel(**rel_data)
                session.add(db_rel)

        session.commit()
        session.refresh(db_strategy)
        return db_strategy

    def delete(self, session: Session, strategy_id: int) -> bool:
        """删除捆绑销售策略"""
        # 先删除关联的产品关系
        session.query(BundleStrategyProdRel).filter(
            BundleStrategyProdRel.bundle_strategy_id == strategy_id
        ).delete()

        # 再删除策略本身
        return super().delete(session, strategy_id)

    def get_bundle_products(self, session: Session, strategy_id: int) -> List[BundleStrategyProdRel]:
        """获取捆绑策略关联的产品列表"""
        return session.query(BundleStrategyProdRel).filter(
            BundleStrategyProdRel.bundle_strategy_id == strategy_id
        ).all()

    def add_bundle_products(self, session: Session, strategy_id: int, products: List[dict]) -> dict:
        """批量添加捆绑产品"""
        strategy = session.query(BundleStrategy).filter(BundleStrategy.id == strategy_id).first()
        if not strategy:
            return {"success": [], "failed": products}

        success_products = []
        failed_products = []

        for product_data in products:
            product_id = product_data.get('product_id')
            quantity = product_data.get('quantity', 1)

            # 检查产品是否存在
            product = session.query(Product).filter(Product.id == product_id).first()
            if not product:
                failed_products.append(product_data)
                continue

            # 检查是否已经存在关联
            existing = session.query(BundleStrategyProdRel).filter(
                and_(
                    BundleStrategyProdRel.bundle_strategy_id == strategy_id,
                    BundleStrategyProdRel.product_id == product_id
                )
            ).first()

            if not existing:
                rel_data = {
                    'bundle_strategy_id': strategy_id,
                    'product_id': product_id,
                    'quantity': quantity
                }
                db_rel = BundleStrategyProdRel(**rel_data)
                session.add(db_rel)
                success_products.append(product_data)
            else:
                # 更新数量
                existing.quantity = quantity
                success_products.append(product_data)

        session.commit()
        return {"success": success_products, "failed": failed_products}

    def remove_bundle_products(self, session: Session, strategy_id: int, product_ids: List[int]) -> dict:
        """批量移除捆绑产品"""
        strategy = session.query(BundleStrategy).filter(BundleStrategy.id == strategy_id).first()
        if not strategy:
            return {"success": [], "failed": product_ids}

        success_ids = []
        failed_ids = []

        for product_id in product_ids:
            existing = session.query(BundleStrategyProdRel).filter(
                and_(
                    BundleStrategyProdRel.bundle_strategy_id == strategy_id,
                    BundleStrategyProdRel.product_id == product_id
                )
            ).first()

            if existing:
                session.delete(existing)
                success_ids.append(product_id)
            else:
                # 不存在也视为成功
                success_ids.append(product_id)

        session.commit()
        return {"success": success_ids, "failed": failed_ids}


# 创建DAO实例
pricing_strategy_dao = PricingStrategyDAO()
discount_strategy_dao = DiscountStrategyDAO()
full_reduction_strategy_dao = FullReductionStrategyDAO()
time_limited_strategy_dao = TimeLimitedStrategyDAO()
member_price_strategy_dao = MemberPriceStrategyDAO()
bundle_strategy_dao = BundleStrategyDAO()
