from typing import List, Optional

from sqlalchemy.orm import Session

from app.dao.base import DAO
from app.models.reservation import (
    ReservationRequest, ReservationStatus, ReservationType, ReservationScope,
    BuffetReservationRequest, BizReservationRequest
)
from app.schemas.reservation import (
    ReservationRequestCreate, ReservationRequestUpdate,
    BuffetReservationRequestCreate, BuffetReservationRequestUpdate,
    BizReservationRequestCreate, BizReservationRequestUpdate
)


class ReservationRequestDAO(DAO):
    """预订请求DAO类"""

    def __init__(self):
        super().__init__(ReservationRequest)

    def create(self, session: Session, reservation: ReservationRequestCreate) -> ReservationRequest:
        """创建预订请求"""
        reservation_data = reservation.model_dump()
        return super().create(session, **reservation_data)

    def get(self, session: Session, reservation_id: int) -> Optional[ReservationRequest]:
        """获取预订请求"""
        return super().get(session, reservation_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[ReservationRequest]:
        """获取预订请求列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, reservation_id: int, reservation: ReservationRequestUpdate) -> Optional[ReservationRequest]:
        """更新预订请求"""
        reservation_data = reservation.model_dump(exclude_unset=True)
        return super().update(session, reservation_id, **reservation_data)

    def delete(self, session: Session, reservation_id: int) -> bool:
        """删除预订请求"""
        return super().delete(session, reservation_id)
        
    def get_by_user(self, session: Session, user_id: int, skip: int = 0, limit: int = 100) -> List[ReservationRequest]:
        """获取用户的所有预订请求"""
        return session.query(self.model).filter(self.model.user_id == user_id).offset(skip).limit(limit).all()
        
    def get_by_product(self, session: Session, product_id: int, skip: int = 0, limit: int = 100) -> List[ReservationRequest]:
        """获取产品的所有预订请求"""
        return session.query(self.model).filter(self.model.product_id == product_id).offset(skip).limit(limit).all()
        
    def get_by_status(self, session: Session, status: ReservationStatus, skip: int = 0, limit: int = 100) -> List[ReservationRequest]:
        """获取指定状态的预订请求"""
        return session.query(self.model).filter(self.model.status == status).offset(skip).limit(limit).all()

    def get_by_order(self, session: Session, orders_id: int, skip: int = 0, limit: int = 100) -> List[ReservationRequest]:
        """获取指定订单的所有预订请求"""
        return session.query(self.model).filter(self.model.orders_id == orders_id).offset(skip).limit(limit).all()

    def get_by_order_first(self, session: Session, orders_id: int) -> Optional[ReservationRequest]:
        """获取指定订单的第一个预订请求"""
        return session.query(self.model).filter(self.model.orders_id == orders_id).first()

    def get_by_type(self, session: Session, reservation_type: ReservationType, skip: int = 0, limit: int = 100) -> List[ReservationRequest]:
        """获取指定类型的预订请求"""
        return session.query(self.model).filter(self.model.type == reservation_type).offset(skip).limit(limit).all()

    def get_by_scope(self, session: Session, scope: ReservationScope, skip: int = 0, limit: int = 100) -> List[ReservationRequest]:
        """获取指定范围的预订请求"""
        return session.query(self.model).filter(self.model.scope == scope).offset(skip).limit(limit).all()

    def get_by_dining_time_range(self, session: Session, start_time, end_time, skip: int = 0, limit: int = 100) -> List[ReservationRequest]:
        """获取指定就餐时间范围内的预订请求"""
        query = session.query(self.model)
        if start_time:
            query = query.filter(self.model.dining_start_time >= start_time)
        if end_time:
            query = query.filter(self.model.dining_end_time <= end_time)
        return query.offset(skip).limit(limit).all()

    def get_by_user_dining_time_range(self, session: Session, user_id: int, start_time, end_time, skip: int = 0, limit: int = 100) -> List[ReservationRequest]:
        """获取指定用户在指定就餐时间范围内的预订请求"""
        query = session.query(self.model).filter(self.model.user_id == user_id)
        if start_time:
            query = query.filter(self.model.dining_start_time >= start_time)
        if end_time:
            query = query.filter(self.model.dining_end_time <= end_time)
        return query.offset(skip).limit(limit).all()

    def get_by_user_dining_time_range_all(self, session: Session, user_id: int, start_time, end_time) -> List[ReservationRequest]:
        """获取指定用户在指定就餐时间范围内的预订请求，不限制数量"""
        query = session.query(self.model).filter(self.model.user_id == user_id)
        if start_time:
            query = query.filter(self.model.dining_start_time >= start_time)
        if end_time:
            query = query.filter(self.model.dining_end_time <= end_time)
        return query.all()

    def pay_deposit(self, session: Session, reservation_id: int) -> Optional[ReservationRequest]:
        """支付定金"""
        reservation = self.get(session, reservation_id)
        if reservation:
            reservation.pay_deposit()
            session.commit()
            session.refresh(reservation)
            return reservation
        return None
        
    def pay_balance(self, session: Session, reservation_id: int) -> Optional[ReservationRequest]:
        """支付尾款"""
        reservation = self.get(session, reservation_id)
        if reservation:
            reservation.pay_balance()
            session.commit()
            session.refresh(reservation)
            return reservation
        return None
        
    def cancel_reservation(self, session: Session, reservation_id: int) -> Optional[ReservationRequest]:
        """取消预订"""
        reservation = self.get(session, reservation_id)
        if reservation:
            reservation.status = ReservationStatus.CANCELLED
            session.commit()
            session.refresh(reservation)
            return reservation
        return None

    def get_by_order_item_id_and_user_id(self, session: Session, order_item_id: int, user_id: int) -> Optional[ReservationRequest]:
        """获取指定预订请求"""
        return session.query(self.model).filter(self.model.order_item_id == order_item_id, self.model.user_id == user_id).first()

    def get_by_verification_code(self, session: Session, verification_code: str) -> Optional[ReservationRequest]:
        """根据验证码获取预订请求"""
        return session.query(self.model).filter(self.model.verification_code == verification_code).first()

    def create_biz_reservation(self, session: Session, base_reservation: ReservationRequestCreate, biz_info: BizReservationRequestCreate) -> BizReservationRequest:
        """创建商务餐预订请求

        Args:
            session: 数据库会话
            base_reservation: 基础预订信息
            biz_info: 商务餐附加信息

        Returns:
            BizReservationRequest: 创建的商务餐预订请求
        """
        # 创建基础预订请求
        reservation_data = base_reservation.model_dump()
        base_reservation = super().create(session, **reservation_data)

        # 创建商务餐预订请求
        biz_data = biz_info.model_dump()
        biz_data['id'] = base_reservation.id  # 使用基础预订请求的ID
        biz_reservation = BizReservationRequest(**biz_data)
        session.add(biz_reservation)
        session.commit()
        session.refresh(biz_reservation)

        return biz_reservation


class BuffetReservationRequestDAO(DAO):
    """自助餐预订请求DAO类"""

    def __init__(self):
        super().__init__(BuffetReservationRequest)

    def create(self, session: Session, reservation: BuffetReservationRequestCreate) -> BuffetReservationRequest:
        """创建自助餐预订请求"""
        reservation_data = reservation.model_dump()
        return super().create(session, **reservation_data)

    def get(self, session: Session, reservation_id: int) -> Optional[BuffetReservationRequest]:
        """获取自助餐预订请求"""
        return super().get(session, reservation_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[BuffetReservationRequest]:
        """获取自助餐预订请求列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, reservation_id: int, reservation: BuffetReservationRequestUpdate) -> Optional[BuffetReservationRequest]:
        """更新自助餐预订请求"""
        reservation_data = reservation.model_dump(exclude_unset=True)
        return super().update(session, reservation_id, **reservation_data)

    def delete(self, session: Session, reservation_id: int) -> bool:
        """删除自助餐预订请求"""
        return super().delete(session, reservation_id)

    def get_by_user(self, session: Session, user_id: int, skip: int = 0, limit: int = 100) -> List[BuffetReservationRequest]:
        """获取用户的所有自助餐预订请求"""
        return session.query(self.model).filter(self.model.user_id == user_id).offset(skip).limit(limit).all()


class BizReservationRequestDAO(DAO):
    """商务餐预订请求DAO类"""

    def __init__(self):
        super().__init__(BizReservationRequest)

    def create(self, session: Session, reservation: BizReservationRequestCreate) -> BizReservationRequest:
        """创建商务餐预订请求"""
        reservation_data = reservation.model_dump()
        return super().create(session, **reservation_data)

    def get(self, session: Session, reservation_id: int) -> Optional[BizReservationRequest]:
        """获取商务餐预订请求"""
        return super().get(session, reservation_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[BizReservationRequest]:
        """获取商务餐预订请求列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, reservation_id: int, reservation: BizReservationRequestUpdate) -> Optional[BizReservationRequest]:
        """更新商务餐预订请求"""
        reservation_data = reservation.model_dump(exclude_unset=True)
        return super().update(session, reservation_id, **reservation_data)

    def delete(self, session: Session, reservation_id: int) -> bool:
        """删除商务餐预订请求"""
        return super().delete(session, reservation_id)

    def get_by_user(self, session: Session, user_id: int, skip: int = 0, limit: int = 100) -> List[BizReservationRequest]:
        """获取用户的所有商务餐预订请求"""
        return session.query(self.model).filter(self.model.user_id == user_id).offset(skip).limit(limit).all()

    def get_by_contact_info(self, session: Session, name: str = None, phone: str = None, skip: int = 0, limit: int = 100) -> List[BizReservationRequest]:
        """根据联系信息获取商务餐预订请求"""
        query = session.query(self.model)
        if name:
            query = query.filter(self.model.name.like(f"%{name}%"))
        if phone:
            query = query.filter(self.model.phone.like(f"%{phone}%"))
        return query.offset(skip).limit(limit).all()

# 创建DAO实例
reservation_request_dao = ReservationRequestDAO()
buffet_reservation_request_dao = BuffetReservationRequestDAO()
biz_reservation_request_dao = BizReservationRequestDAO()