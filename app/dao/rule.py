from operator import and_
from typing import List, Optional, Dict, Any

from sqlalchemy.orm import Session

from app.dao.base import DAO
from app.models.enum import Status
from app.models.product import Product
from app.models.rule import Rule, RuleItem, product_rule_relation, DiningReservationRule, RuleType
from app.schemas.rule import (
    RuleCreate, RuleUpdate, RuleItemCreate, RuleItemUpdate,
    DiningReservationRuleCreate, DiningReservationRuleUpdate
)
from app.models.rule import RuleScope, RuleOrderType


class RuleDAO(DAO):
    """规则 DAO 类"""

    def __init__(self):
        super().__init__(Rule)

    def create(self, session: Session, rule: RuleCreate) -> Rule:
        """创建规则"""
        rule_data = rule.model_dump()
        return super().create(session, **rule_data)

    def get(self, session: Session, rule_id: int) -> Optional[Rule]:
        """获取规则"""
        return super().get(session, rule_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[Rule]:
        """获取规则列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def get_active_rules(self, session: Session, skip: int = 0, limit: int = 100) -> List[Rule]:
        """获取活跃规则列表"""
        return session.query(self.model).filter(
            self.model.status == Status.ACTIVE
        ).offset(skip).limit(limit).all()

    def search(self, session: Session, keyword: Optional[str] = None, 
              name: Optional[str] = None, status: Optional[Status] = None,
              skip: int = 0, limit: int = 100) -> Dict[str, Any]:
        """
        根据条件搜索规则
        
        Args:
            session: 数据库会话
            keyword: 关键词，用于模糊搜索
            name: 规则名称，用于模糊搜索
            status: 规则状态
            skip: 分页偏移量
            limit: 分页大小
            
        Returns:
            Dict: 包含规则列表和总数的字典
        """
        query = session.query(self.model)
        
        # 应用过滤条件
        if keyword:
            query = query.filter(self.model.name.like(f"%{keyword}%"))
        
        if name:
            query = query.filter(self.model.name.like(f"%{name}%"))
            
        if status is not None:
            query = query.filter(self.model.status == status)
            
        # 获取总数
        total = query.count()
        
        # 应用分页
        rules = query.order_by(self.model.id.desc()).offset(skip).limit(limit).all()
        
        return {
            "list": rules,
            "total": total
        }

    def update(self, session: Session, rule_id: int, rule: RuleUpdate) -> Optional[Rule]:
        """更新规则"""
        rule_data = rule.model_dump(exclude_unset=True)
        return super().update(session, rule_id, **rule_data)

    def delete(self, session: Session, rule_id: int) -> bool:
        """删除规则"""
        return super().delete(session, rule_id)

    def get_products_by_rule(self, session: Session, rule_id: int, skip: int = 0, limit: int = 100) -> List[Product]:
        """获取与特定规则绑定的所有产品

        Args:
            session: 数据库会话
            rule_id: 规则ID
            skip: 跳过数量
            limit: 限制数量

        Returns:
            List[Product]: 产品列表
        """
        rule = session.query(Rule).filter(Rule.id == rule_id).first()
        if not rule:
            return []

        return rule.products[skip:skip + limit]

    def get_rules_by_product(self, session: Session, product_id: int, skip: int = 0, limit: int = 100) -> List[Rule]:
        """获取产品关联的规则列表"""
        return session.query(self.model).filter(
            self.model.products.any(Product.id == product_id)
        ).offset(skip).limit(limit).all()

    def add_products(self, session: Session, rule_id: int, product_ids: List[int]) -> dict:
        """批量将规则绑定到产品

        Returns:
            dict: 包含成功和失败的产品ID列表
        """
        rule = session.query(Rule).filter(Rule.id == rule_id).first()
        if not rule:
            return {"success": [], "failed": product_ids}

        success_ids = []
        failed_ids = []

        for product_id in product_ids:
            # 获取Product实例
            product = session.query(Product).filter(Product.id == product_id).first()
            if not product:
                failed_ids.append(product_id)
                continue

            # 检查是否已经绑定
            existing = session.query(product_rule_relation).filter(
                and_(
                    product_rule_relation.c.rule_id == rule_id,
                    product_rule_relation.c.product_id == product_id
                )
            ).first()

            if not existing:
                rule.products.append(product)
                success_ids.append(product_id)
            else:
                # 已经绑定过，也视为成功
                success_ids.append(product_id)

        session.commit()
        return {"success": success_ids, "failed": failed_ids}

    def remove_products(self, session: Session, rule_id: int, product_ids: List[int]) -> dict:
        """批量将规则从产品解绑

        Returns:
            dict: 包含成功和失败的产品ID列表
        """
        rule = session.query(Rule).filter(Rule.id == rule_id).first()
        if not rule:
            return {"success": [], "failed": product_ids}

        success_ids = []
        failed_ids = []

        for product_id in product_ids:
            # 获取Product实例
            product = session.query(Product).filter(Product.id == product_id).first()
            if not product:
                failed_ids.append(product_id)
                continue

            # 检查是否已经绑定
            existing = session.query(product_rule_relation).filter(
                and_(
                    product_rule_relation.c.rule_id == rule_id,
                    product_rule_relation.c.product_id == product_id
                )
            ).first()

            if existing:
                # 移除关联关系
                rule.products.remove(product)
                success_ids.append(product_id)
            else:
                # 未绑定，也视为成功
                success_ids.append(product_id)

        session.commit()
        return {"success": success_ids, "failed": failed_ids}

    def to_dict(self, rule: Rule) -> dict:
        """将规则转换为字典"""
        return {
            "id": rule.id,
            "name": rule.name,
            "status": rule.status,
            "type": rule.type,
            "scope": rule.scope,
            "order_type": rule.order_type,
            "created_at": rule.created_at,
            "updated_at": rule.updated_at,
            "rule_items": [rule_item_dao.to_dict(rule_item) for rule_item in rule.rule_items],
            "products": [product.id for product in rule.products],
            "reservation_requests": [reservation_request.id for reservation_request in rule.reservation_requests]
        }

    def get_dining_reservation_rule(self, session: Session, rule_id: int) -> Optional[DiningReservationRule]:
        """获取餐厅预订规则

        根据规则ID获取餐厅预订规则(DiningReservationRule)对象

        Args:
            session: 数据库会话
            rule_id: 规则ID

        Returns:
            Optional[DiningReservationRule]: 餐厅预订规则对象或None
        """
        return session.query(DiningReservationRule).filter(DiningReservationRule.id == rule_id).first()

    def dining_reservation_rule_to_dict(self, rule: DiningReservationRule) -> dict:
        """将餐厅预订规则转换为字典

        Args:
            rule: 餐厅预订规则对象

        Returns:
            dict: 包含餐厅预订规则所有字段的字典
        """
        if not rule:
            return {}

        # 基本Rule字段
        result = {
            "id": rule.id,
            "name": rule.name,
            "status": rule.status,
            "type": rule.type.value if rule.type else None,
            "scope": rule.scope.value if rule.scope else None,
            "order_type": rule.order_type.value if rule.order_type else None,
            "created_at": rule.created_at,
            "updated_at": rule.updated_at,

            # DiningReservationRule特有字段
            "alias": rule.alias,
            "dining_start_time_cron_str": rule.dining_start_time_cron_str,
            "dining_end_time_cron_str": rule.dining_end_time_cron_str,
            "verify_start_time_cron_str": rule.verify_start_time_cron_str,
            "verify_end_time_cron_str": rule.verify_end_time_cron_str,
            "order_deadline": rule.order_deadline,
            "cancellation_deadline": rule.cancellation_deadline,
            "is_auto_verify": rule.is_auto_verify,
            "generated_count": rule.generated_count,
            "quantity": rule.quantity,
        }

        # 添加关联的规则项
        rule_items = []
        if rule.rule_items:
            for item in rule.rule_items:
                rule_items.append(rule_item_dao.to_dict(item))
        result["rule_items"] = rule_items

        return result

    def create_dining_reservation_rule(self, session: Session, rule: DiningReservationRuleCreate) -> DiningReservationRule:
        """创建餐厅预订规则"""
        rule_data = rule.model_dump()
        # 确保设置正确的类型
        rule_data['type'] = RuleType.DINING_RESERVATION
        db_rule = DiningReservationRule(**rule_data)
        session.add(db_rule)
        session.commit()
        session.refresh(db_rule)
        return db_rule

    def update_dining_reservation_rule(self, session: Session, rule_id: int, rule: DiningReservationRuleUpdate) -> Optional[DiningReservationRule]:
        """更新餐厅预订规则"""
        db_rule = session.query(DiningReservationRule).filter(DiningReservationRule.id == rule_id).first()
        if db_rule:
            rule_data = rule.model_dump(exclude_unset=True)
            for key, value in rule_data.items():
                setattr(db_rule, key, value)
            session.commit()
            session.refresh(db_rule)
        return db_rule

    def get_by_scope_and_order_type(self, session: Session, scope: RuleScope, order_type: RuleOrderType) -> Optional[DiningReservationRule]:
        """根据scope和order_type获取规则"""
        return session.query(DiningReservationRule).filter(
            DiningReservationRule.scope == scope,
            DiningReservationRule.order_type == order_type
        ).first()


class RuleItemDAO(DAO):
    """规则项 DAO 类"""

    def __init__(self):
        super().__init__(RuleItem)

    def create(self, session: Session, rule_item: RuleItemCreate) -> RuleItem:
        """创建规则项"""
        rule_item_data = rule_item.model_dump()
        return super().create(session, **rule_item_data)

    def get(self, session: Session, rule_item_id: int) -> Optional[RuleItem]:
        """获取规则项"""
        return super().get(session, rule_item_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[RuleItem]:
        """获取规则项列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def get_by_rule_id(self, session: Session, rule_id: int) -> Optional[RuleItem]:
        """根据rule_id获取规则项"""
        return session.query(RuleItem).filter(RuleItem.rule_id == rule_id).first()

    def get_by_rule(self, session: Session, rule_id: int, skip: int = 0, limit: int = 100) -> List[RuleItem]:
        """获取特定规则的所有规则项"""
        return session.query(self.model).filter(
            self.model.rule_id == rule_id
        ).offset(skip).limit(limit).all()

    def update(self, session: Session, rule_item_id: int, rule_item: RuleItemUpdate) -> Optional[RuleItem]:
        """更新规则项"""
        rule_item_data = rule_item.model_dump(exclude_unset=True)
        return super().update(session, rule_item_id, **rule_item_data)

    def delete(self, session: Session, rule_item_id: int) -> bool:
        """删除规则项"""
        return super().delete(session, rule_item_id)

    def to_dict(self, rule_item: RuleItem) -> dict:
        """将规则项转换为字典"""
        return {
            "id": rule_item.id,
            "name": rule_item.name,
            "alias": rule_item.alias,
            "rule_id": rule_item.rule_id,
            "start_time": rule_item.start_time,
            "end_time": rule_item.end_time,
            "start_time_cron_str": rule_item.start_time_cron_str,
            "end_time_cron_str": rule_item.end_time_cron_str,
            "order_deadline": rule_item.order_deadline,
            "cancellation_deadline": rule_item.cancellation_deadline,
            "generated_count": rule_item.generated_count,
            "quantity": rule_item.quantity,
            "order": rule_item.order,
            "allowed_operations": rule_item.allowed_operations,
            "forbidden_operations": rule_item.forbidden_operations,
            "created_at": rule_item.created_at,
            "updated_at": rule_item.updated_at,
            "reservation_requests": [reservation_request.id for reservation_request in rule_item.reservation_requests]
        }


# 创建 DAO 实例
rule_dao = RuleDAO()
rule_item_dao = RuleItemDAO()
