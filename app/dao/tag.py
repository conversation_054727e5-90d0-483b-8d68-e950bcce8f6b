from typing import List, Optional
from sqlalchemy.orm import Session

from app.dao.base import DAO
from app.models.tag import Tag
from app.models.product import Product
from app.schemas.tag import TagCreate, TagUpdate


class TagDAO(DAO):
    """标签 DAO 类"""

    def __init__(self):
        super().__init__(Tag)

    def create(self, session: Session, tag: TagCreate) -> Tag:
        """创建标签"""
        tag_data = tag.model_dump()
        return super().create(session, **tag_data)

    def get(self, session: Session, tag_id: int) -> Optional[Tag]:
        """获取标签"""
        return super().get(session, tag_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[Tag]:
        """获取标签列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def get_by_name(self, session: Session, name: str) -> Optional[Tag]:
        """根据名称获取标签"""
        return session.query(self.model).filter(
            self.model.name == name
        ).first()

    def get_by_product(self, session: Session, product_id: int) -> List[Tag]:
        """获取产品相关标签"""
        return session.query(self.model).filter(
            self.model.products.any(Product.id == product_id)
        ).all()

    def update(self, session: Session, tag_id: int, tag: TagUpdate) -> Optional[Tag]:
        """更新标签"""
        tag_data = tag.model_dump(exclude_unset=True)
        return super().update(session, tag_id, **tag_data)

    def delete(self, session: Session, tag_id: int) -> bool:
        """删除标签"""
        return super().delete(session, tag_id)

    def bind_products(self, session: Session, tag_id: int, product_ids: List[int]) -> Optional[Tag]:
        """将标签同时绑定到多个产品"""
        tag = self.get(session, tag_id)
        if not tag:
            return None

        # 获取所有有效的产品
        products = session.query(Product).filter(Product.id.in_(product_ids)).all()
        if not products:
            return None

        # 获取已经绑定的产品ID列表，避免重复添加
        existing_product_ids = {p.id for p in tag.products}

        # 添加尚未绑定的产品
        added = False
        for product in products:
            if product.id not in existing_product_ids:
                tag.products.append(product)
                added = True

        if added:
            session.commit()
            session.refresh(tag)

        return tag

    def unbind_products(self, session: Session, tag_id: int, product_ids: List[int]) -> Optional[Tag]:
        """将标签同时从多个产品解绑"""
        tag = self.get(session, tag_id)
        if not tag:
            return None

        # 获取现有的产品对象
        existing_products = {p.id: p for p in tag.products}

        # 移除指定的产品
        removed = False
        for product_id in product_ids:
            if product_id in existing_products:
                tag.products.remove(existing_products[product_id])
                removed = True

        if removed:
            session.commit()
            session.refresh(tag)

        return tag


# 创建 DAO 实例
tag_dao = TagDAO()
