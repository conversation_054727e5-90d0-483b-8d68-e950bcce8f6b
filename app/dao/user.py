from typing import List, Optional, Dict, Any
import logging
from fastapi import HTT<PERSON>Exception
from sqlalchemy.orm import Session
from sqlalchemy import or_, func

from app.dao.base import DAO
from app.models.user import User, PersonalUser, Enterprise, EnterpriseUserRelation
from app.models.enum import Status
from app.schemas.user import (
    UserCreate, UserUpdate,
    PersonalUserCreate, PersonalUserUpdate,
    EnterpriseCreate, EnterpriseUpdate,
    EnterpriseUserRelationCreate, EnterpriseUserRelationUpdate
)
from app.schemas.user import PersonalUserCreate, PersonalUserUpdate, EnterpriseCreate, EnterpriseUpdate
from app.dao.account import gift_account_dao, regular_account_dao, wechat_account_dao
from app.schemas.account import GiftAccountCreate, RegularAccountCreate, WechatAccountCreate

# 创建日志记录器
logger = logging.getLogger(__name__)


class UserDAO(DAO):
    """用户DAO类"""

    def __init__(self):
        super().__init__(User)

    def create(self, session: Session, user: UserCreate) -> User:
        """创建用户"""
        user_data = user.model_dump()
        return super().create(session, **user_data)

    def get(self, session: Session, user_id: int) -> Optional[User]:
        """获取用户"""
        return super().get(session, user_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[User]:
        """获取用户列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, user_id: int, user: UserUpdate) -> Optional[User]:
        """更新用户"""
        user_data = user.model_dump(exclude_unset=True)
        # 检查 strategy_data 中是否存在 type 属性
        if 'type' in user_data:
            # 如果存在，删除该属性
            del user_data['type']
        return super().update(session, user_id, **user_data)

    def delete(self, session: Session, user_id: int) -> bool:
        """删除用户"""
        return super().delete(session, user_id)


class PersonalUserDAO(DAO):
    """个人用户DAO类"""

    def __init__(self):
        super().__init__(PersonalUser)

    def create(self, session: Session, user: PersonalUserCreate) -> PersonalUser:
        """创建个人用户"""
        try:
            user_data = user.model_dump()

            if "phone" in user_data:
                existing_personal = session.query(PersonalUser).filter(
                    PersonalUser.phone == user_data["phone"]
                ).first()

                if existing_personal:
                    raise HTTPException(status_code=409, detail="已存在同一手机号")

            # 用手机号作为用户名
            if 'phone' in user_data:
                user_data['username'] = user_data['phone']
            instance = super().create(session, **user_data)

            # 2. 创建普通账户
            regular_account_data = RegularAccountCreate(user_id=instance.id)
            regular_account = regular_account_dao.create(session, regular_account_data)

            # 3. 创建赠送账户
            gift_account_data = GiftAccountCreate(user_id=instance.id)
            gift_account = gift_account_dao.create(session, gift_account_data)

            # 4. 创建微信账户
            wechat_account_data = WechatAccountCreate(user_id=instance.id)
            wechat_account = wechat_account_dao.create(session, wechat_account_data)

            if regular_account and gift_account and wechat_account:
                # 提交事务
                session.commit()
                session.refresh(instance)
                logger.info(f"个人用户创建成功: id={instance.id}, phone={user_data.get('phone')}")
                return instance
            else:
                # 如果任何一个账户创建失败，回滚事务
                session.rollback()
                logger.error(f"个人用户创建失败: 账户创建失败, phone={user_data.get('phone')}")
                raise Exception("创建用户失败：账户创建异常")

        except Exception as e:
            # 回滚事务
            session.rollback()
            # 记录异常信息
            logger.error(f"个人用户创建失败: {str(e)}, phone={user.phone if hasattr(user, 'phone') else '未知'}")
            # 直接抛出原始异常
            raise

    def get(self, session: Session, user_id: int) -> Optional[PersonalUser]:
        """获取个人用户"""
        return super().get(session, user_id)

    def get_with_enterprises(self, session: Session, user_id: int) -> Optional[PersonalUser]:
        """获取个人用户及其关联的企业信息"""
        personal_user = session.query(PersonalUser).filter(PersonalUser.id == user_id).first()
        if not personal_user:
            return None
        
        # 获取企业列表但不直接赋值给personal_user.enterprises
        enterprises = self.get_enterprises(session, user_id)
        
        # 构建符合响应结构的数据
        result = {
            "id": personal_user.id,
            "username": personal_user.username,
            "phone": personal_user.phone,
            "email": personal_user.email,
            "address": personal_user.address,
            "real_name": personal_user.real_name,
            "id_card": personal_user.id_card,
            "status": personal_user.status.value if hasattr(personal_user.status, 'value') else personal_user.status,
            "register_time": personal_user.register_time,
            "created_at": personal_user.created_at,
            "updated_at": personal_user.updated_at,
            "enterprises": [
                {
                    "id": enterprise.id,
                    "username": enterprise.username,
                    "company_name": enterprise.company_name,
                    "business_license": enterprise.business_license,
                    "phone": enterprise.phone,
                    "email": enterprise.email,
                    "address": enterprise.address,
                    "status": enterprise.status.value if hasattr(enterprise.status, 'value') else enterprise.status,
                    "register_time": enterprise.register_time,
                    "created_at": enterprise.created_at,
                    "updated_at": enterprise.updated_at
                }
                for enterprise in enterprises
            ]
        }
        
        return result

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> dict:
        """
        获取个人用户列表及总数
        
        Args:
            session: 数据库会话
            skip: 跳过条数
            limit: 返回条数
            
        Returns:
            dict: 包含 'total'（用户总数）和 'list'（分页后的用户列表）的字典
        """
        # 查询用户总数
        total = session.query(PersonalUser).count()
        # 查询分页后的用户列表
        items = session.query(PersonalUser).order_by(PersonalUser.id.desc()).offset(skip).limit(limit).all()
        return {
            "total": total,
            "list": items
        }

    def search(self, session: Session, keyword: str = None, name: str = None, phone: str = None, status=None,
               skip: int = 0, limit: int = 100) -> dict:
        """
        根据条件搜索个人用户列表及总数
        
        Args:
            session: 数据库会话
            keyword: 搜索关键字，可搜索用户名、手机号、邮箱、真实姓名等
            name: 用户名（精确匹配或模糊匹配）
            phone: 手机号（精确匹配或模糊匹配）
            status: 用户状态
            skip: 分页起始位置
            limit: 分页大小
            
        Returns:
            dict: 包含 'total'（搜索结果总数）和 'list'（分页后的用户列表）的字典
        """
        query = session.query(PersonalUser)

        # 如果提供了关键字，则添加过滤条件
        if keyword:
            search_keyword = f"%{keyword}%"
            query = query.filter(
                or_(
                    PersonalUser.phone.like(search_keyword),
                    PersonalUser.email.like(search_keyword),
                    PersonalUser.real_name.like(search_keyword),
                    PersonalUser.address.like(search_keyword),
                    PersonalUser.username.like(search_keyword)
                )
            )

        # 按用户名过滤
        if name:
            search_name = f"%{name}%"
            query = query.filter(
                or_(
                    PersonalUser.username.like(search_name),
                    PersonalUser.real_name.like(search_name)
                )
            )

        # 按手机号过滤
        if phone:
            # 如果手机号看起来是完整的11位手机号，则进行精确匹配
            if len(phone) == 11 and phone.isdigit():
                query = query.filter(PersonalUser.phone == phone)
            else:
                # 否则进行模糊匹配
                query = query.filter(PersonalUser.phone.like(f"%{phone}%"))

        # 按状态过滤
        if status is not None:
            query = query.filter(PersonalUser.status == status)

        # 查询总数
        total = query.count()
        # 查询分页后的用户列表
        items = query.order_by(PersonalUser.id.desc()).offset(skip).limit(limit).all()

        return {
            "total": total,
            "list": items
        }

    def update(self, session: Session, user_id: int, user: PersonalUserUpdate) -> Optional[PersonalUser]:
        """更新个人用户"""
        user_data = user.model_dump(exclude_unset=True)
        # 更新手机号需要同步更新用户名
        if "phone" in user_data:
            existing_personal = session.query(PersonalUser).filter(
                PersonalUser.phone == user_data["phone"]  # 排除当前正在更新的手机号
            ).first()

            if existing_personal:
                raise HTTPException(status_code=409, detail="已存在同一手机号")

        # 用手机号作为用户名
        if 'phone' in user_data:
            user_data['username'] = user_data['phone']

        # 检查 strategy_data 中是否存在 type 属性
        if 'type' in user_data:
            # 如果存在，删除该属性
            del user_data['type']
        return super().update(session, user_id, **user_data)

    def delete(self, session: Session, user_id: int) -> bool:
        """删除个人用户"""
        # 先获取个人用户
        personal_user = self.get(session, user_id)
        if personal_user:
            # 先删除个人用户
            session.delete(personal_user)
            # 再删除基础用户
            base_user = session.query(User).filter(User.id == user_id).first()
            if base_user:
                session.delete(base_user)
            session.commit()
            return True
        return False

    def batch_set_status(self, session: Session, user_ids: List[int], status) -> int:
        """
        批量设置用户状态
        
        Args:
            session: 数据库会话
            user_ids: 要更新的用户ID列表
            status: 要设置的状态
            
        Returns:
            int: 更新的记录数
        """
        result = session.query(User).filter(User.id.in_(user_ids)).update(
            {User.status: status},
            synchronize_session='fetch'
        )
        session.commit()
        return result

    def get_enterprises(self, session: Session, user_id: int, status=Status.ACTIVE) -> List[Enterprise]:
        """获取个人用户关联的所有企业"""
        personal_user = self.get(session, user_id)
        if personal_user:
            query = session.query(EnterpriseUserRelation).filter(
                EnterpriseUserRelation.personal_user_id == user_id
            )
            if status is not None:
                query = query.filter(EnterpriseUserRelation.relation_status == status)
            relations = query.all()
            enterprise_ids = [relation.enterprise_id for relation in relations]
            return session.query(Enterprise).filter(Enterprise.id.in_(enterprise_ids)).all()
        return []

    def get_by_openid(self, session: Session, wechat_id: str) -> Optional[PersonalUser]:
        """通过 openid 获取个人用户"""
        return session.query(self.model).filter(self.model.wechat_id == wechat_id).first()

    def get_by_token(self, session: Session, token: str) -> Optional[PersonalUser]:
        """通过 token 获取个人用户"""
        return session.query(self.model).filter(self.model.token == token).first()

    def update_by_openid(self, session: Session, wechat_id: str, user_data: dict) -> Optional[PersonalUser]:
        """通过 openid 更新个人用户"""
        return session.query(self.model).filter(self.model.wechat_id == wechat_id).update(user_data)

    def get_by_phone(self, session: Session, phone: str) -> Optional[PersonalUser]:
        """通过手机号获取个人用户"""
        return session.query(self.model).filter(self.model.phone == phone).first()

    def update_wechat_id(self, session: Session, user_id: int, wechat_id: str) -> Optional[PersonalUser]:
        """更新个人用户微信ID"""
        try:
            result = session.query(self.model).filter(self.model.id == user_id).update({"wechat_id": wechat_id})
            session.commit()
            return result
        except Exception as e:
            session.rollback()
            raise e

    def update_unionid(self, session: Session, user_id: int, unionid: str) -> Optional[PersonalUser]:
        """更新个人用户公众号UnionID"""
        try:
            result = session.query(self.model).filter(self.model.id == user_id).update({"gzh_unionid": unionid})
            session.commit()
            return result
        except Exception as e:
            session.rollback()
            raise e

    def get_by_unionid(self, session: Session, unionid: str) -> Optional[PersonalUser]:
        """通过公众号UnionID获取个人用户"""
        return session.query(self.model).filter(self.model.gzh_unionid == unionid).first()

    def search_by_name(self, session: Session, username: str) -> List[Dict[str, Any]]:
        """
        根据用户名进行模糊搜索

        Args:
            session: 数据库会话
            username: 用户名关键词

        Returns:
            List[Dict[str, Any]]: 包含用户名和ID的列表
        """
        search_username = f"%{username}%"
        users = session.query(self.model).filter(
            or_(
                self.model.username.like(search_username),
                self.model.real_name.like(search_username)
            ),
            self.model.status == Status.ACTIVE
        ).all()

        return [{"username": user.username, "id": user.id, "real_name": user.real_name} for user in users]


class EnterpriseDAO(DAO):
    """企业用户DAO类"""

    def __init__(self):
        super().__init__(Enterprise)

    def create(self, session: Session, enterprise: EnterpriseCreate) -> Enterprise:
        """创建企业用户"""
        try:
            enterprise_data = enterprise.model_dump()

            if "company_name" in enterprise_data:
                existing_enterprise = session.query(Enterprise).filter(
                    Enterprise.company_name == enterprise_data["company_name"]
                ).first()

                if existing_enterprise:
                    raise HTTPException(status_code=409, detail="已存在同名企业")

            # 用公司名作为用户名
            if 'company_name' in enterprise_data:
                enterprise_data['username'] = enterprise_data['company_name']

            instance = super().create(session, **enterprise_data)

            # 2. 创建普通账户
            regular_account_data = RegularAccountCreate(user_id=instance.id)
            regular_account = regular_account_dao.create(session, regular_account_data)

            # 3. 创建赠送账户
            gift_account_data = GiftAccountCreate(user_id=instance.id)
            gift_account = gift_account_dao.create(session, gift_account_data)

            if regular_account and gift_account:
                # 提交事务
                session.commit()
                session.refresh(instance)
                logger.info(f"企业用户创建成功: id={instance.id}, company_name={enterprise_data.get('company_name')}")
                return instance
            else:
                # 如果任何一个账户创建失败，回滚事务
                session.rollback()
                logger.error(f"企业用户创建失败: 账户创建失败, company_name={enterprise_data.get('company_name')}")
                raise Exception("创建企业用户失败：账户创建异常")

        except Exception as e:
            # 回滚事务
            session.rollback()
            # 记录异常信息
            logger.error(f"企业用户创建失败: {str(e)}, company_name={enterprise.company_name if hasattr(enterprise, 'company_name') else '未知'}")
            # 直接抛出原始异常
            raise

    def get(self, session: Session, enterprise_id: int) -> Optional[Enterprise]:
        """获取企业用户"""
        return super().get(session, enterprise_id)

    def get_with_users(self, session: Session, enterprise_id: int) -> Optional[dict]:
        """获取企业用户及其关联的个人用户信息"""
        enterprise = session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
        if not enterprise:
            return None
        
        # 获取关联的用户列表但不直接赋值给enterprise.users
        users = self.get_users(session, enterprise_id)
        
        # 构建符合响应结构的数据
        result = {
            "id": enterprise.id,
            "username": enterprise.username,
            "company_name": enterprise.company_name,
            "business_license": enterprise.business_license,
            "phone": enterprise.phone,
            "email": enterprise.email,
            "address": enterprise.address,
            "status": enterprise.status.value if hasattr(enterprise.status, 'value') else enterprise.status,
            "register_time": enterprise.register_time,
            "created_at": enterprise.created_at,
            "updated_at": enterprise.updated_at,
            "users": [
                {
                    "id": user.id,
                    "username": user.username,
                    "phone": user.phone,
                    "email": user.email,
                    "address": user.address,
                    "real_name": user.real_name,
                    "id_card": user.id_card,
                    "status": user.status.value if hasattr(user.status, 'value') else user.status,
                    "register_time": user.register_time,
                    "created_at": user.created_at,
                    "updated_at": user.updated_at
                }
                for user in users
            ]
        }
        
        return result

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> dict:
        """
        获取企业用户列表及总数
        
        Args:
            session: 数据库会话
            skip: 跳过条数
            limit: 返回条数
            
        Returns:
            dict: 包含 'total'（企业总数）和 'list'（分页后的企业列表）的字典
        """
        # 查询企业总数
        total = session.query(Enterprise).count()
        # 查询分页后的企业列表
        items = session.query(Enterprise).order_by(Enterprise.id.desc()).offset(skip).limit(limit).all()
        return {
            "total": total,
            "list": items
        }

    def search(self, session: Session, keyword: str = None,
               name: str = None,
               company_name: str = None,
               phone: str = None,
               status=None,
               skip: int = 0, limit: int = 100) -> dict:
        """
        根据条件搜索企业用户列表及总数
        
        Args:
            session: 数据库会话
            keyword: 搜索关键字，可搜索企业名称、手机号、邮箱、地址等
            name: 企业名称（精确匹配或模糊匹配）
            company_name: 企业名称（精确匹配或模糊匹配）
            phone: 手机号（精确匹配或模糊匹配）
            status: 企业状态
            skip: 分页起始位置
            limit: 分页大小
            
        Returns:
            dict: 包含 'total'（搜索结果总数）和 'list'（分页后的企业列表）的字典
        """
        query = session.query(self.model)

        # 如果提供了关键字，则添加过滤条件
        if keyword:
            search_keyword = f"%{keyword}%"
            query = query.filter(
                or_(
                    self.model.company_name.like(search_keyword),
                    self.model.phone.like(search_keyword),
                    self.model.email.like(search_keyword),
                    self.model.address.like(search_keyword),
                    self.model.username.like(search_keyword)
                )
            )

        if name:
            company_name = name
        # 按企业名称过滤
        if company_name:
            search_name = f"%{company_name}%"
            query = query.filter(self.model.company_name.like(search_name))

        # 按手机号过滤
        if phone:
            # 如果手机号看起来是完整的11位手机号，则进行精确匹配
            if len(phone) == 11 and phone.isdigit():
                query = query.filter(self.model.phone == phone)
            else:
                # 否则进行模糊匹配
                query = query.filter(self.model.phone.like(f"%{phone}%"))

        # 按状态过滤
        if status is not None:
            query = query.filter(self.model.status == status)

        # 查询总数
        total = query.count()
        # 查询分页后的企业列表
        items = query.order_by(self.model.id.desc()).offset(skip).limit(limit).all()

        return {
            "total": total,
            "list": items
        }

    def update(self, session: Session, enterprise_id: int, enterprise: EnterpriseUpdate) -> Optional[Enterprise]:
        """更新企业用户"""
        enterprise_data = enterprise.model_dump(exclude_unset=True)

        if "company_name" in enterprise_data:
            existing_enterprise = session.query(Enterprise).filter(
                Enterprise.company_name == enterprise_data["company_name"],
                Enterprise.id != enterprise_id  # 排除当前正在更新的企业
            ).first()

            if existing_enterprise:
                raise HTTPException(status_code=409, detail="已存在相同公司名的企业用户")

        # 用公司名作为用户名
        if 'company_name' in enterprise_data:
            enterprise_data['username'] = enterprise_data['company_name']
        # 检查 strategy_data 中是否存在 type 属性
        if 'type' in enterprise_data:
            # 如果存在，删除该属性
            del enterprise_data['type']
        return super().update(session, enterprise_id, **enterprise_data)

    def delete(self, session: Session, enterprise_id: int) -> bool:
        """删除企业用户"""
        # 先获取企业用户
        enterprise = self.get(session, enterprise_id)
        if enterprise:
            # 先删除企业用户
            session.delete(enterprise)
            # 再删除基础用户
            base_user = session.query(User).filter(User.id == enterprise_id).first()
            if base_user:
                session.delete(base_user)
            session.commit()
            return True
        return False

    def batch_set_status(self, session: Session, enterprise_ids: List[int], status) -> int:
        """
        批量设置企业状态
        
        Args:
            session: 数据库会话
            enterprise_ids: 要更新的企业ID列表
            status: 要设置的状态
            
        Returns:
            int: 更新的记录数
        """
        result = session.query(User).filter(User.id.in_(enterprise_ids)).update(
            {User.status: status},
            synchronize_session='fetch'
        )
        session.commit()
        return result

    def get_users(self, session: Session, enterprise_id: int, status=Status.ACTIVE) -> List[PersonalUser]:
        """获取企业用户关联的所有个人用户"""
        enterprise = self.get(session, enterprise_id)
        if enterprise:
            query = session.query(EnterpriseUserRelation).filter(
                EnterpriseUserRelation.enterprise_id == enterprise_id
            )
            if status is not None:
                query = query.filter(EnterpriseUserRelation.relation_status == status)
            relations = query.all()
            user_ids = [relation.personal_user_id for relation in relations]
            return session.query(PersonalUser).filter(PersonalUser.id.in_(user_ids)).all()
        return []

    def get_by_phone(self, session: Session, phone: str) -> Optional[Enterprise]:
        """通过手机号获取企业用户"""
        return session.query(self.model).filter(self.model.phone == phone).first()

    def get_name_list_by_keyword(self, session: Session, keyword: str) -> List[dict]:
        """
        通过关键字模糊搜索企业名称，返回包含ID和公司名称的列表

        Args:
            session: 数据库会话
            keyword: 搜索关键字，用于模糊匹配企业名称

        Returns:
            List[dict]: 包含企业ID和公司名称的字典列表
                格式: [{"id": 1, "company_name": "微软"}, {"id": 2, "company_name": "谷歌"}]
        """
        if not keyword:
            return []

        search_keyword = f"%{keyword}%"
        results = session.query(
            self.model.id,
            self.model.company_name
        ).filter(
            self.model.company_name.like(search_keyword)
        ).order_by(self.model.id.desc()).all()

        return [
            {"id": result.id, "company_name": result.company_name}
            for result in results
        ]


class EnterpriseUserRelationDAO(DAO):
    """企业用户关联DAO类"""

    def __init__(self):
        super().__init__(EnterpriseUserRelation)

    def create(self, session: Session, relation: EnterpriseUserRelationCreate) -> EnterpriseUserRelation:
        """创建企业用户关联"""
        relation_data = relation.model_dump()
        return super().create(session, **relation_data)

    def get(self, session: Session, relation_id: int) -> Optional[EnterpriseUserRelation]:
        """获取企业用户关联"""
        return super().get(session, relation_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[EnterpriseUserRelation]:
        """获取企业用户关联列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, relation_id: int, relation: EnterpriseUserRelationUpdate) -> Optional[
        EnterpriseUserRelation]:
        """更新企业用户关联"""
        relation_data = relation.model_dump(exclude_unset=True)
        return super().update(session, relation_id, **relation_data)

    def delete(self, session: Session, relation_id: int) -> bool:
        """删除企业用户关联"""
        return super().delete(session, relation_id)

    def get_list_by_personal_user_id(self, session: Session, personal_user_id: int, status=Status.ACTIVE) -> List[EnterpriseUserRelation]:
        """通过企业ID和用户ID获取关联"""
        query = session.query(self.model).filter(
            EnterpriseUserRelation.personal_user_id == personal_user_id
        )
        if status is not None:
            query = query.filter(EnterpriseUserRelation.relation_status == status)
        return query.all()

    def get_by_enterprise_and_user(self, session: Session, enterprise_id: int, user_id: int, status=Status.ACTIVE) -> Optional[
        EnterpriseUserRelation]:
        """通过企业ID和用户ID获取关联"""
        query = session.query(self.model).filter(
            EnterpriseUserRelation.enterprise_id == enterprise_id,
            EnterpriseUserRelation.personal_user_id == user_id
        )
        if status is not None:
            query = query.filter(EnterpriseUserRelation.relation_status == status)
        return query.first()

    def get_by_personal_user_id(self, session: Session, personal_user_id: int, status=Status.ACTIVE) -> List[Enterprise]:
        """通过个人用户ID获取企业用户列表"""
        query = session.query(self.model).filter(
            self.model.personal_user_id == personal_user_id
        )
        if status is not None:
            query = query.filter(self.model.relation_status == status)
        return query.all()

    def get_enterprises_by_user(self, session: Session, user_id: int, skip: int = 0, limit: int = 10, status=Status.ACTIVE) -> List[Enterprise]:
        """获取个人用户关联的企业列表（带分页）
        
        Args:
            session: 数据库会话
            user_id: 个人用户ID
            skip: 跳过的记录数
            limit: 返回的最大记录数
            
        Returns:
            List[Enterprise]: 企业列表
        """
        # 通过关联表找到所有企业ID
        query = session.query(self.model).filter(
            self.model.personal_user_id == user_id
        )
        if status is not None:
            query = query.filter(self.model.relation_status == status)
        relations = query.all()
        
        enterprise_ids = [relation.enterprise_id for relation in relations]
        
        # 如果没有相关企业，返回空列表
        if not enterprise_ids:
            return []
            
        # 查询企业数据并应用分页
        return session.query(Enterprise).filter(
            Enterprise.id.in_(enterprise_ids)
        ).offset(skip).limit(limit).all()
    
    def count_enterprises_by_user(self, session: Session, user_id: int, status=Status.ACTIVE) -> int:
        """获取个人用户关联的企业总数
        
        Args:
            session: 数据库会话
            user_id: 个人用户ID
            
        Returns:
            int: 企业总数
        """
        # 通过关联表找到所有企业ID
        query = session.query(self.model).filter(
            self.model.personal_user_id == user_id
        )
        if status is not None:
            query = query.filter(self.model.relation_status == status)
        relations = query.all()
        
        enterprise_ids = [relation.enterprise_id for relation in relations]
        
        # 如果没有相关企业，返回0
        if not enterprise_ids:
            return 0
            
        # 查询企业总数
        return session.query(Enterprise).filter(
            Enterprise.id.in_(enterprise_ids)
        ).count()
    
    def get_users_by_enterprise(self, session: Session, enterprise_id: int, skip: int = 0, limit: int = 10, status=Status.ACTIVE) -> List[PersonalUser]:
        """获取企业关联的个人用户列表（带分页）
        
        Args:
            session: 数据库会话
            enterprise_id: 企业ID
            skip: 跳过的记录数
            limit: 返回的最大记录数
            
        Returns:
            List[PersonalUser]: 个人用户列表
        """
        # 通过关联表找到所有用户ID
        query = session.query(self.model).filter(
            self.model.enterprise_id == enterprise_id
        )
        if status is not None:
            query = query.filter(self.model.relation_status == status)
        relations = query.all()
        
        user_ids = [relation.personal_user_id for relation in relations]
        
        # 如果没有相关用户，返回空列表
        if not user_ids:
            return []
            
        # 查询个人用户数据并应用分页
        return session.query(PersonalUser).filter(
            PersonalUser.id.in_(user_ids)
        ).offset(skip).limit(limit).all()
    
    def count_users_by_enterprise(self, session: Session, enterprise_id: int, status=Status.ACTIVE) -> int:
        """获取企业关联的个人用户总数
        
        Args:
            session: 数据库会话
            enterprise_id: 企业ID
            
        Returns:
            int: 用户总数
        """
        # 通过关联表找到所有用户ID
        query = session.query(self.model).filter(
            self.model.enterprise_id == enterprise_id
        )
        if status is not None:
            query = query.filter(self.model.relation_status == status)
        relations = query.all()
        
        user_ids = [relation.personal_user_id for relation in relations]
        
        # 如果没有相关用户，返回0
        if not user_ids:
            return 0
            
        # 查询用户总数
        return session.query(PersonalUser).filter(
            PersonalUser.id.in_(user_ids)
        ).count()

    def search_users_by_enterprise(self, session: Session, enterprise_id: int, keyword: str = None, phone: str = None, username: str = None, skip: int = 0, limit: int = 20, status=Status.ACTIVE) -> dict:
        """根据条件搜索企业下的用户列表

        Args:
            session: 数据库会话
            enterprise_id: 企业ID
            keyword: 搜索关键字，可搜索用户名、手机号、真实姓名等
            phone: 手机号（模糊匹配）
            username: 用户名（模糊匹配）
            skip: 分页起始位置
            limit: 分页大小

        Returns:
            dict: 包含 'total'（搜索结果总数）和 'list'（用户详细信息列表）的字典
        """
        # 先获取企业下所有的用户关系
        relations_query = session.query(self.model).filter(
            self.model.enterprise_id == enterprise_id
        )
        if status is not None:
            relations_query = relations_query.filter(self.model.relation_status == status)

        # 获取所有用户ID
        user_ids = [relation.personal_user_id for relation in relations_query.all()]

        if not user_ids:
            return {"total": 0, "list": []}

        # 构建用户查询
        users_query = session.query(PersonalUser).filter(PersonalUser.id.in_(user_ids))

        # 添加搜索条件
        if keyword:
            search_keyword = f"%{keyword}%"
            users_query = users_query.filter(
                or_(
                    PersonalUser.username.like(search_keyword),
                    PersonalUser.phone.like(search_keyword),
                    PersonalUser.real_name.like(search_keyword),
                    PersonalUser.email.like(search_keyword)
                )
            )

        if phone:
            phone_keyword = f"%{phone}%"
            users_query = users_query.filter(PersonalUser.phone.like(phone_keyword))

        if username:
            username_keyword = f"%{username}%"
            users_query = users_query.filter(PersonalUser.username.like(username_keyword))

        # 获取总数
        total = users_query.count()

        # 获取分页数据
        users = users_query.order_by(PersonalUser.id.desc()).offset(skip).limit(limit).all()

        # 构建返回数据，包含用户详细信息和企业关系信息
        user_list = []
        for user in users:
            # 获取该用户在企业中的关系信息
            relation_query = session.query(self.model).filter(
                self.model.enterprise_id == enterprise_id,
                self.model.personal_user_id == user.id
            )
            if status is not None:
                relation_query = relation_query.filter(self.model.relation_status == status)
            relation = relation_query.first()

            user_info = {
                "id": user.id,
                "username": user.username,
                "phone": user.phone,
                "real_name": user.real_name,
                "status": relation.relation_status.value if relation and hasattr(relation.relation_status, 'value') else relation.relation_status if relation else None,
                "is_admin": relation.is_admin if relation else False
            }
            user_list.append(user_info)

        return {
            "total": total,
            "list": user_list
        }


# 创建DAO实例
user_dao = UserDAO()
personal_user_dao = PersonalUserDAO()
enterprise_dao = EnterpriseDAO()
enterprise_user_relation_dao = EnterpriseUserRelationDAO()
