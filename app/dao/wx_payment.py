from typing import Optional, Dict, Any, List
from datetime import datetime
from sqlalchemy.orm import Session
from app.models.order import WxPaymentRecord, WxPaymentStatus, WxRefundRecord, WxRefundStatus

class WxPaymentDAO():
    """微信支付记录数据访问对象"""
    model = WxPaymentRecord

    @staticmethod
    def create_payment_record(
        session: Session,
        order_id: int,
        order_no: str,
        openid: str,
        total_amount: float,
        description: str,
        prepay_id: Optional[str] = None,
        payment_request_params: Optional[Dict[str, Any]] = None
    ) -> Optional[WxPaymentRecord]:
        """创建微信支付记录
        
        Args:
            session: 数据库会话
            order_id: 订单ID
            order_no: 商户订单号
            openid: 用户openid
            total_amount: 订单总金额(元)
            description: 商品描述
            prepay_id: 预支付交易会话标识
            payment_request_params: 支付请求参数
            
        Returns:
            Optional[WxPaymentRecord]: 创建成功返回支付记录对象，失败返回None
        """
        try:
            wx_payment = WxPaymentRecord(
                order_id=order_id,
                order_no=order_no,
                openid=openid,
                total_amount=total_amount,
                description=description,
                prepay_id=prepay_id,
                payment_request_params=payment_request_params,
                status=WxPaymentStatus.UNPAID
            )
            session.add(wx_payment)
            session.commit()
            session.refresh(wx_payment)
            return wx_payment
        except Exception as e:
            session.rollback()
            return None
    
    @staticmethod
    def update_payment_status(
        session: Session,
        order_no: str,
        transaction_id: str,
        trade_state: str,
        trade_state_desc: str,
        payer_total: float,
        notify_data: Dict[str, Any],
        success_time: Optional[datetime] = None,
        bank_type: Optional[str] = None
    ) -> Optional[WxPaymentRecord]:
        """更新微信支付记录状态
        
        Args:
            session: 数据库会话
            order_no: 商户订单号
            transaction_id: 微信支付订单号
            trade_state: 交易状态
            trade_state_desc: 交易状态描述
            payer_total: 用户实际支付金额(元)
            notify_data: 支付回调数据
            success_time: 支付完成时间
            bank_type: 付款银行类型
            
        Returns:
            Optional[WxPaymentRecord]: 更新成功返回支付记录对象，失败返回None
        """
        try:
            payment_record = session.query(WxPaymentRecord).filter(
                WxPaymentRecord.order_no == order_no
            ).first()
            
            if not payment_record:
                return None
            
            # 交易状态映射
            status_map = {
                "SUCCESS": WxPaymentStatus.SUCCESS,
                "REFUND": WxPaymentStatus.REFUND,
                "NOTPAY": WxPaymentStatus.NOTPAY,
                "CLOSED": WxPaymentStatus.CLOSED,
                "USERPAYING": WxPaymentStatus.USERPAYING,
                "PAYERROR": WxPaymentStatus.PAYERROR
            }
            
            payment_record.transaction_id = transaction_id
            payment_record.trade_state = trade_state
            payment_record.trade_state_desc = trade_state_desc
            payment_record.payer_total = payer_total
            payment_record.notify_data = notify_data
            payment_record.status = status_map.get(trade_state, WxPaymentStatus.UNPAID)
            
            if success_time:
                payment_record.success_time = success_time
            
            if bank_type:
                payment_record.bank_type = bank_type
            
            session.commit()
            session.refresh(payment_record)
            return payment_record
        except Exception as e:
            session.rollback()
            return None

    @staticmethod
    def get_payment_by_order_no(session: Session, order_no: str) -> Optional[WxPaymentRecord]:
        """根据商户订单号获取支付记录"""
        try:
            return session.query(WxPaymentRecord).filter(
                WxPaymentRecord.order_no == order_no
            ).first()
        except Exception:
            return None
    
    @staticmethod
    def get_payment_by_transaction_id(session: Session, transaction_id: str) -> Optional[WxPaymentRecord]:
        """根据微信支付订单号获取支付记录"""
        try:
            return session.query(WxPaymentRecord).filter(
                WxPaymentRecord.transaction_id == transaction_id
            ).first()
        except Exception:
            return None

    @staticmethod
    def get_payments_by_order_id(session: Session, order_id: int) -> List[WxPaymentRecord]:
        """根据订单ID获取所有支付记录"""
        try:
            return session.query(WxPaymentRecord).filter(
                WxPaymentRecord.order_id == order_id,
                WxPaymentRecord.status == WxPaymentStatus.SUCCESS
            ).all()
        except Exception:
            return []

    def get_personal_payment_by_order_no(self, session: Session, order_no: str) -> Optional[WxPaymentRecord]:
        """获取个人支付的微信支付记录（带_personal后缀的订单号）"""
        personal_order_no = f"{order_no}_personal"
        return self.get_payment_by_order_no(session, personal_order_no)

# 实例化DAO
wx_payment_dao = WxPaymentDAO()

class WxPaymentService:
    """微信支付服务"""
    
    @staticmethod
    def create_payment_record(
        db: Session,
        order_id: int,
        order_no: str,
        openid: str,
        total_amount: float,
        description: str,
        prepay_id: Optional[str] = None,
        payment_request_params: Optional[Dict[str, Any]] = None
    ) -> Optional[WxPaymentRecord]:
        """创建微信支付记录"""
        return wx_payment_dao.create_payment_record(
            db, order_id, order_no, openid, total_amount, 
            description, prepay_id, payment_request_params
        )
    
    @staticmethod
    def update_payment_status(
        db: Session,
        order_no: str,
        transaction_id: str,
        trade_state: str,
        trade_state_desc: str,
        payer_total: float,
        notify_data: Dict[str, Any],
        success_time: Optional[datetime] = None,
        bank_type: Optional[str] = None
    ) -> Optional[WxPaymentRecord]:
        """更新微信支付记录状态"""
        return wx_payment_dao.update_payment_status(
            db, order_no, transaction_id, trade_state, trade_state_desc,
            payer_total, notify_data, success_time, bank_type
        )
    
    @staticmethod
    def get_payment_by_order_no(db: Session, order_no: str) -> Optional[WxPaymentRecord]:
        """根据商户订单号获取支付记录"""
        return wx_payment_dao.get_payment_by_order_no(db, order_no)
    
    @staticmethod
    def get_payment_by_transaction_id(db: Session, transaction_id: str) -> Optional[WxPaymentRecord]:
        """根据微信支付订单号获取支付记录"""
        return wx_payment_dao.get_payment_by_transaction_id(db, transaction_id)

    @staticmethod
    def get_payments_by_order_id(db: Session, order_id: int) -> List[WxPaymentRecord]:
        """根据订单ID获取所有支付记录"""
        return wx_payment_dao.get_payments_by_order_id(db, order_id)

# 实例化服务
wx_payment_service = WxPaymentService()
