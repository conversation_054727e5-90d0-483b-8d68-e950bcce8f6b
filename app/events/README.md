# 事件系统文档

本文档为应用程序中实现的事件驱动通信系统提供全面的文档说明。

## 目录

- [概述](#概述)
- [核心组件](#核心组件)
- [快速开始](#快速开始)
- [事件处理器模块导入](#事件处理器模块导入)
  - [导入机制](#导入机制)
  - [导入位置](#导入位置)
  - [导入时机](#导入时机)
  - [添加新的事件处理器](#添加新的事件处理器)
- [配置](#配置)
- [最佳实践](#最佳实践)
- [监控和调试](#监控和调试)
- [故障排除](#故障排除)
- [示例](#示例)

## 概述

事件系统为不同模块之间提供了一种健壮的、异步的通信方式，避免了紧耦合。它使用 asyncio 进行高效的事件处理，使用 Pydantic 提供类型安全的事件数据模型。

## 核心组件

### 1. EventBus (`app/events/bus.py`)
中央事件调度器，管理事件订阅并将事件分发给已注册的处理器。

### 2. BaseEvent (`app/events/base.py`)
所有事件数据模型的基类，提供通用的元数据字段，如 ID、时间戳和来源。

### 3. 事件模型 (`app/events/models.py`)
不同类型事件的 Pydantic 模型：
- `UserEvent` - 用户相关事件（创建、更新、登录等）
- `OrderEvent` - 订单相关事件（创建、支付、取消等）
- `ReservationEvent` - 预订相关事件
- `PaymentEvent` - 支付交易事件
- `NotificationEvent` - 各种渠道的通知事件
- `SystemEvent` - 系统级事件（启动、错误等）

### 4. 事件处理器 (`app/events/handlers.py`)
用于管理事件处理器的注册表和装饰器。

### 5. FastAPI 依赖 (`app/events/deps.py`)
与 FastAPI 路由的依赖注入集成。

## 快速开始

### 1. 发布事件

```python
from app.events import get_event_bus, UserEvent, UserEventAction

# 获取事件总线
event_bus = get_event_bus()

# 创建并发布事件
event = UserEvent(
    action=UserEventAction.CREATED,
    user_id=123,
    username="john_doe",
    source="user_service"
)

await event_bus.publish(event)
```

### 2. 创建事件处理器

```python
from app.events.handlers import event_handler
from app.events.models import UserEvent

@event_handler(UserEvent)
async def handle_user_events(event: UserEvent):
    print(f"用户 {event.user_id} 执行了操作: {event.action}")
```

### 3. 在 FastAPI 路由中使用

```python
from fastapi import APIRouter, Depends
from app.events.deps import EventBusDep

router = APIRouter()

@router.post("/users")
async def create_user(user_data: dict, event_bus: EventBusDep):
    # 在这里编写创建用户的逻辑...

    # 发布事件
    event = UserEvent(
        action=UserEventAction.CREATED,
        user_id=user_id,
        username=user_data["username"],
        source="api"
    )
    await event_bus.publish(event)

    return {"message": "用户已创建"}
```

## 事件类型和操作

### UserEvent 操作
- `CREATED` - 用户账户已创建
- `UPDATED` - 用户信息已更新
- `DELETED` - 用户账户已删除
- `LOGIN` - 用户已登录
- `LOGOUT` - 用户已登出
- `PASSWORD_CHANGED` - 用户已更改密码

### OrderEvent 操作
- `CREATED` - 订单已创建
- `UPDATED` - 订单信息已更新
- `CANCELLED` - 订单已取消
- `PAID` - 订单支付已完成
- `REFUNDED` - 订单已退款
- `COMPLETED` - 订单已完成

### PaymentEvent 操作
- `INITIATED` - 支付流程已开始
- `COMPLETED` - 支付成功
- `FAILED` - 支付失败
- `REFUNDED` - 支付已退款
- `CANCELLED` - 支付已取消

### NotificationEvent 操作
- `SEND_EMAIL` - 发送邮件通知
- `SEND_SMS` - 发送短信通知
- `SEND_WECHAT` - 发送微信通知
- `SEND_PUSH` - 发送推送通知

## 高级用法

### 条件事件处理器

```python
from app.events.handlers import event_handler

# 只处理高级用户的事件
@event_handler(UserEvent, filter_func=lambda e: e.additional_data.get("is_premium"))
async def handle_premium_user_events(event: UserEvent):
    print(f"高级用户事件: {event.action}")
```

### 事件处理器中的错误处理

```python
@event_handler(OrderEvent)
async def handle_order_events(event: OrderEvent):
    try:
        # 处理事件
        await process_order(event)
    except Exception as e:
        logger.error(f"处理订单事件 {event.id} 时出错: {e}")
        # 可选择发布错误事件
```

### 服务集成

```python
class UserService:
    def __init__(self):
        self.event_bus = get_event_bus()

    async def create_user(self, user_data: dict):
        # 在数据库中创建用户
        user = await self.db.create_user(user_data)

        # 发布事件
        event = UserEvent(
            action=UserEventAction.CREATED,
            user_id=user.id,
            username=user.username,
            source="user_service"
        )
        await self.event_bus.publish(event)

        return user
```

## 配置

事件系统在应用程序启动时自动初始化。您可以通过修改 `app/core/events.py` 中的初始化来配置它。

### 事件总线配置

```python
# 创建具有自定义队列大小的事件总线
event_bus = EventBus(max_queue_size=2000)
```

## 事件处理器模块导入

### 导入机制

事件处理器通过 `@event_handler` 装饰器自动注册。为了确保处理器被正确注册，包含处理器的模块必须在应用启动时被导入。

### 导入位置

事件处理器模块应该在 `app/core/events.py` 的 `_register_default_handlers()` 函数中导入：

```python
async def _register_default_handlers() -> None:
    """Register default event handlers for system events."""

    # 导入所有事件处理器模块
    logger.info("开始注册事件处理器...")

    try:
        # 业务通知处理器
        import app.tasks.notifications.biz_dinner
        logger.info("✅ 成功注册 biz_dinner 事件处理器")

        # 添加更多事件处理器模块
        # import app.tasks.notifications.user_notifications
        # import app.tasks.notifications.order_notifications
        # import app.tasks.notifications.payment_notifications

        logger.info("✅ 所有事件处理器注册完成")

    except ImportError as e:
        logger.error(f"❌ 导入事件处理器模块失败: {e}")
        raise
    except Exception as e:
        logger.error(f"❌ 注册事件处理器时发生错误: {e}")
        raise
```

### 导入时机

1. **应用启动时**：在 FastAPI 应用的 `lifespan` 事件中
2. **事件系统初始化时**：调用 `initialize_event_system()` 时
3. **事件总线启动前**：确保处理器在事件总线开始处理事件前注册

### 事件处理器生命周期

```
应用启动
    ↓
FastAPI lifespan 开始
    ↓
调用 event_system_lifespan()
    ↓
调用 initialize_event_system()
    ↓
调用 _register_default_handlers()
    ↓
导入事件处理器模块 (import app.tasks.notifications.biz_dinner)
    ↓
@event_handler 装饰器执行
    ↓
处理器注册到 EventHandlerRegistry
    ↓
处理器订阅到 EventBus
    ↓
事件总线启动 (event_bus.start())
    ↓
应用就绪，可以处理事件
    ↓
... 应用运行期间 ...
    ↓
应用关闭
    ↓
事件总线停止 (event_bus.stop())
    ↓
FastAPI lifespan 结束
```

### 推荐的模块组织结构

```
app/
├── tasks/
│   └── notifications/
│       ├── __init__.py
│       ├── biz_dinner.py          # 订单相关事件处理器
│       ├── user_notifications.py  # 用户相关事件处理器
│       ├── payment_notifications.py # 支付相关事件处理器
│       └── system_notifications.py # 系统相关事件处理器
├── events/
│   └── ...
└── core/
    └── events.py                 # 事件系统初始化和模块导入
```

## 最佳实践

### 1. 事件命名
- 使用描述性的操作名称
- 遵循现有的操作枚举
- 在相似的事件类型中保持一致性

### 2. 事件数据
- 在事件中包含所有相关信息
- 使用 `additional_data` 字段存储额外信息
- 创建后保持事件不可变

### 3. 处理器设计
- 保持处理器专注于单一职责
- 使用过滤器处理特定事件条件
- 在处理器内优雅地处理错误
- 避免在处理器中进行长时间运行的操作

### 4. 错误处理
- 始终在 try-catch 块中包装处理器逻辑
- 适当地记录错误
- 考虑为关键故障发布错误事件

### 5. 测试
- 在您的服务中测试事件发布
- 独立测试事件处理器
- 使用模拟事件总线进行单元测试

## 监控和调试

### 事件总线统计

```python
from app.events import get_event_bus

event_bus = get_event_bus()
stats = event_bus.get_stats()
print(stats)
```

### 日志记录
事件系统包含全面的日志记录：
- 事件发布在 DEBUG 级别记录
- 处理器注册在 INFO 级别记录
- 错误在 ERROR 级别记录

## 示例

查看 `app/events/examples/` 目录中的完整示例：
- `handlers.py` - 事件处理器示例
- `usage.py` - FastAPI 集成示例
- `service_integration.py` - 服务集成模式

### 验证导入是否成功

可以使用以下代码验证事件处理器是否正确注册：

```python
from app.events.bus import get_event_bus
from app.events.handlers import get_event_handler_registry

# 获取注册信息
event_bus = get_event_bus()
registry = get_event_handler_registry()

# 查看已注册的处理器
all_handlers = registry.get_all_handlers()
for event_type, handlers in all_handlers.items():
    print(f"{event_type.__name__}: {len(handlers)} 个处理器")
    for handler in handlers:
        print(f"  - {handler.name}")

# 查看事件总线状态
print(f"事件总线状态: {event_bus.get_stats()}")
```

### 添加新的事件处理器

要添加新的事件处理器，请按照以下步骤：

#### 1. 创建事件处理器模块

在 `app/tasks/notifications/` 目录下创建新的处理器模块：

```python
# app/tasks/notifications/user_notifications.py
from app.events.handlers import event_handler, logger
from app.events.models import UserEvent, UserEventAction

@event_handler(UserEvent)
async def handle_user_events(event: UserEvent):
    """处理用户相关事件"""
    if event.action == UserEventAction.CREATED:
        logger.info(f"新用户注册: {event.username} (ID: {event.user_id})")
        # 发送欢迎邮件等逻辑

    elif event.action == UserEventAction.LOGIN:
        logger.info(f"用户登录: {event.username}")
        # 记录登录日志等逻辑
```

#### 2. 在事件系统中注册模块

在 `app/core/events.py` 的 `_register_default_handlers()` 函数中添加导入：

```python
async def _register_default_handlers() -> None:
    try:
        # 现有的处理器
        import app.tasks.notifications.biz_dinner

        # 添加新的处理器模块
        import app.tasks.notifications.user_notifications
        logger.info("✅ 成功注册 user_notifications 事件处理器")

    except Exception as e:
        logger.error(f"❌ 注册事件处理器时发生错误: {e}")
        raise
```

#### 3. 验证注册成功

重启应用后，检查日志确认处理器已成功注册：

```
INFO:app.core.events:开始注册事件处理器...
INFO:app.events.handlers:Registered event handler 'handle_user_events' for UserEvent
INFO:app.core.events:✅ 成功注册 user_notifications 事件处理器
```

### 注意事项

1. **导入顺序很重要**：必须在事件总线启动前导入处理器模块
2. **避免循环导入**：确保事件处理器模块不会导致循环导入
3. **错误处理**：导入失败会阻止应用启动，需要适当的错误处理
4. **性能考虑**：大量处理器可能影响启动时间
5. **模块命名**：使用描述性的模块名称，便于维护和理解

## 故障排除

### 常见问题

1. **事件未被处理**
   - 检查事件总线是否已启动（`event_bus.get_stats()['running']` 应为 `True`）
   - 验证处理器是否正确注册（检查 `registry.get_all_handlers()`）
   - 检查处理器代码中的错误
   - 确认事件处理器模块已在 `app/core/events.py` 中导入

2. **处理器未接收到事件**
   - 确保处理器为正确的事件类型注册
   - 如果使用了过滤器函数，请检查它们
   - 验证事件总线是否正在运行
   - 检查事件类型是否匹配

3. **处理器注册失败**
   - 检查模块导入是否成功（查看启动日志）
   - 确认 `@event_handler` 装饰器语法正确
   - 验证处理器函数是否为 `async` 函数
   - 检查是否有导入错误或语法错误

4. **性能问题**
   - 监控事件队列大小
   - 检查慢速处理器
   - 如果需要，考虑增加队列大小
   - 避免在处理器中执行长时间运行的同步操作

### 调试模式

启用调试日志记录以查看详细的事件处理信息：

```python
import logging
logging.getLogger("app.events").setLevel(logging.DEBUG)
```

### 测试事件系统

可以使用项目根目录下的 `test_event_registration.py` 脚本来测试事件系统：

```bash
python test_event_registration.py
```

该脚本会：
- 初始化事件系统
- 检查处理器注册状态
- 发布测试事件
- 验证事件处理是否正常
