"""
Event System Package

This package provides a comprehensive event-driven communication system
for cross-module communication using asyncio and Pydantic.

Key Components:
- EventBus: Central event dispatcher using asyncio
- BaseEvent: Pydantic-based event data models
- EventHandler: Type-safe event handlers
- Dependency injection integration with FastAPI
"""

# Import base components first
from app.events.base import BaseEvent, EventHandler
from app.events.bus import EventBus, get_event_bus

# Import models
from app.events.models import (
    UserEvent, OrderEvent, ReservationEvent, PaymentEvent,
    NotificationEvent, SystemEvent
)

# Import handlers and dependencies (these may depend on the above)
from app.events.handlers import EventHandlerRegistry, get_event_handler_registry
from app.events.deps import get_event_bus_dep

__all__ = [
    # Core components
    "BaseEvent",
    "EventHandler", 
    "EventBus",
    "EventHandlerRegistry",
    
    # Event models
    "UserEvent",
    "OrderEvent", 
    "ReservationEvent",
    "PaymentEvent",
    "NotificationEvent",
    "SystemEvent",
    
    # Dependencies
    "get_event_bus",
    "get_event_bus_dep",
    "get_event_handler_registry",
]
