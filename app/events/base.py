"""
Base Event System Classes

This module defines the core interfaces and base classes for the event system.
"""

from datetime import datetime
from typing import Dict, Any, Type, TypeVar, Generic, Callable, Awaitable, Optional, List
from uuid import uuid4
from pydantic import BaseModel, Field

# Type variable for event types
T = TypeVar('T', bound='BaseEvent')


class BaseEvent(BaseModel):
    """
    Base class for all event data models.

    All events in the system should inherit from this class to ensure
    they have the required metadata fields.
    """
    id: str = Field(default_factory=lambda: str(uuid4()))
    timestamp: datetime = Field(default_factory=datetime.now)
    source: str = ""

    # Allow extra fields for forward compatibility
    class Config:
        extra = "allow"
    
    @classmethod
    def create(cls, **kwargs) -> 'BaseEvent':
        """
        Factory method to create an event with default metadata.
        
        Args:
            **kwargs: Event data fields
            
        Returns:
            BaseEvent: A new event instance
        """
        return cls(**kwargs)


class EventHandler(Generic[T]):
    """
    Base class for event handlers.
    
    Event handlers process events of a specific type. They can be registered
    with the event bus to receive events of their target type.
    """
    
    def __init__(
        self, 
        event_type: Type[T],
        handler_func: Callable[[T], Awaitable[None]],
        name: Optional[str] = None,
        filter_func: Optional[Callable[[T], bool]] = None
    ):
        """
        Initialize an event handler.
        
        Args:
            event_type: The type of event this handler processes
            handler_func: Async function that processes events
            name: Optional name for the handler (defaults to function name)
            filter_func: Optional function to filter events before processing
        """
        self.event_type = event_type
        self.handler_func = handler_func
        self.name = name or handler_func.__name__
        self.filter_func = filter_func
    
    async def handle(self, event: T) -> None:
        """
        Process an event if it passes the filter.
        
        Args:
            event: The event to process
        """
        if self.filter_func is None or self.filter_func(event):
            await self.handler_func(event)
    
    def can_handle(self, event: BaseEvent) -> bool:
        """
        Check if this handler can process the given event.
        
        Args:
            event: The event to check
            
        Returns:
            bool: True if this handler can process the event
        """
        return isinstance(event, self.event_type)
