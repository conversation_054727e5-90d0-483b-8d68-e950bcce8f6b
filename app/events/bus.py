"""
Event Bus Implementation

This module provides the core event bus functionality using asyncio
for efficient asynchronous event processing.
"""

import asyncio
import logging
from collections import defaultdict
from contextlib import asynccontextmanager
from typing import Dict, List, Type, Optional, Any

from app.events.base import BaseEvent, EventHandler

# Use standard logging instead of custom logger to avoid dependencies
logger = logging.getLogger(__name__)


class EventBus:
    """
    Asynchronous event bus for cross-module communication.
    
    The event bus manages event subscriptions and dispatches events
    to registered handlers using asyncio for efficient processing.
    """
    
    def __init__(self, max_queue_size: int = 1000):
        """
        Initialize the event bus.
        
        Args:
            max_queue_size: Maximum number of events in the processing queue
        """
        self._handlers: Dict[Type[BaseEvent], List[EventHandler]] = defaultdict(list)
        self._event_queue: asyncio.Queue = asyncio.Queue(maxsize=max_queue_size)
        self._processing_task: Optional[asyncio.Task] = None
        self._running = False
        self._stats = {
            "events_published": 0,
            "events_processed": 0,
            "events_failed": 0,
            "handlers_registered": 0
        }
    
    async def start(self) -> None:
        """Start the event bus processing loop."""
        if self._running:
            logger.warning("Event bus is already running")
            return
        
        self._running = True
        self._processing_task = asyncio.create_task(self._process_events())
        logger.info("Event bus started")
    
    async def stop(self) -> None:
        """Stop the event bus and wait for pending events to be processed."""
        if not self._running:
            return
        
        self._running = False
        
        # Wait for the processing task to complete
        if self._processing_task:
            try:
                await asyncio.wait_for(self._processing_task, timeout=5.0)
            except asyncio.TimeoutError:
                logger.warning("Event bus processing task did not complete within timeout")
                self._processing_task.cancel()
        
        logger.info("Event bus stopped")
    
    def subscribe(self, handler: EventHandler) -> None:
        """
        Subscribe an event handler to receive events.
        
        Args:
            handler: The event handler to register
        """
        event_type = handler.event_type
        self._handlers[event_type].append(handler)
        self._stats["handlers_registered"] += 1
        
        logger.info(f"Registered handler '{handler.name}' for event type '{event_type.__name__}'")
    
    def unsubscribe(self, handler: EventHandler) -> bool:
        """
        Unsubscribe an event handler.
        
        Args:
            handler: The event handler to unregister
            
        Returns:
            bool: True if the handler was found and removed
        """
        event_type = handler.event_type
        if handler in self._handlers[event_type]:
            self._handlers[event_type].remove(handler)
            logger.info(f"Unregistered handler '{handler.name}' for event type '{event_type.__name__}'")
            return True
        return False
    
    async def publish(self, event: BaseEvent) -> None:
        """
        Publish an event to the bus.
        
        Args:
            event: The event to publish
        """
        if not self._running:
            logger.warning("Event bus is not running, event will be queued")
        
        try:
            await self._event_queue.put(event)
            self._stats["events_published"] += 1
            logger.debug(f"Published event: {event.__class__.__name__} (ID: {event.id})")
        except asyncio.QueueFull:
            logger.error(f"Event queue is full, dropping event: {event.__class__.__name__}")
    
    async def _process_events(self) -> None:
        """Internal method to process events from the queue."""
        logger.info("Event processing loop started")
        
        while self._running:
            try:
                # Wait for an event with a timeout to allow checking _running flag
                event = await asyncio.wait_for(self._event_queue.get(), timeout=1.0)
                await self._dispatch_event(event)
                self._event_queue.task_done()
                
            except asyncio.TimeoutError:
                # Timeout is expected, continue the loop
                continue
            except Exception as e:
                logger.error(f"Error in event processing loop: {e}")
        
        # Process remaining events in the queue
        while not self._event_queue.empty():
            try:
                event = self._event_queue.get_nowait()
                await self._dispatch_event(event)
                self._event_queue.task_done()
            except asyncio.QueueEmpty:
                break
            except Exception as e:
                logger.error(f"Error processing remaining events: {e}")
        
        logger.info("Event processing loop stopped")
    
    async def _dispatch_event(self, event: BaseEvent) -> None:
        """
        Dispatch an event to all registered handlers.
        
        Args:
            event: The event to dispatch
        """
        event_type = type(event)
        handlers = self._handlers.get(event_type, [])
        
        if not handlers:
            logger.debug(f"No handlers registered for event type: {event_type.__name__}")
            return
        
        # Create tasks for all handlers to process the event concurrently
        tasks = []
        for handler in handlers:
            if handler.can_handle(event):
                task = asyncio.create_task(self._handle_event_safely(handler, event))
                tasks.append(task)
        
        if tasks:
            # Wait for all handlers to complete
            await asyncio.gather(*tasks, return_exceptions=True)
            self._stats["events_processed"] += 1
    
    async def _handle_event_safely(self, handler: EventHandler, event: BaseEvent) -> None:
        """
        Safely handle an event with error handling.
        
        Args:
            handler: The event handler
            event: The event to handle
        """
        try:
            await handler.handle(event)
            logger.debug(f"Handler '{handler.name}' processed event {event.id}")
        except Exception as e:
            self._stats["events_failed"] += 1
            logger.error(f"Handler '{handler.name}' failed to process event {event.id}: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get event bus statistics."""
        return {
            **self._stats,
            "queue_size": self._event_queue.qsize(),
            "running": self._running,
            "registered_event_types": list(self._handlers.keys())
        }


# Global event bus instance
_event_bus: Optional[EventBus] = None


def get_event_bus() -> EventBus:
    """
    Get the global event bus instance.
    
    Returns:
        EventBus: The global event bus instance
    """
    global _event_bus
    if _event_bus is None:
        _event_bus = EventBus()
    return _event_bus


@asynccontextmanager
async def event_bus_lifespan():
    """
    Context manager for event bus lifecycle management.
    
    Use this in FastAPI lifespan events to properly start and stop the event bus.
    """
    bus = get_event_bus()
    await bus.start()
    try:
        yield bus
    finally:
        await bus.stop()
