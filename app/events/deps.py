"""
FastAPI Dependencies for Event System

This module provides FastAPI dependency injection functions
for the event system components.
"""

try:
    from typing import Annotated
except ImportError:
    # For Python < 3.9, use typing_extensions
    try:
        from typing_extensions import Annotated
    except ImportError:
        # Fallback for older versions
        Annotated = None

from fastapi import Depends

from app.events.bus import EventBus, get_event_bus
from app.events.handlers import EventHandlerRegistry, get_event_handler_registry


def get_event_bus_dep() -> EventBus:
    """
    FastAPI dependency to get the event bus instance.

    Returns:
        EventBus: The global event bus instance
    """
    return get_event_bus()


def get_event_handler_registry_dep() -> EventHandlerRegistry:
    """
    FastAPI dependency to get the event handler registry.

    Returns:
        EventHandlerRegistry: The global event handler registry
    """
    return get_event_handler_registry()


# Type aliases for dependency injection (only if Annotated is available)
if Annotated is not None:
    EventBusDep = Annotated[EventBus, Depends(get_event_bus_dep)]
    EventHandlerRegistryDep = Annotated[EventHandlerRegistry, Depends(get_event_handler_registry_dep)]
else:
    # Fallback for older Python versions
    EventBusDep = EventBus
    EventHandlerRegistryDep = EventHandlerRegistry
