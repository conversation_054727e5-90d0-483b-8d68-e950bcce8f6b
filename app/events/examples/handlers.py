"""
Example Event Handlers

This module demonstrates how to create and register event handlers
for different types of events in the system.
"""

import asyncio

from app.events.handlers import event_handler
from app.events.models import (
    UserEvent, OrderEvent, ReservationEvent, PaymentEvent,
    NotificationEvent, SystemEvent, UserEventAction, OrderEventAction
)
from app.utils.logger import logger


# Example 1: Simple user event handler
@event_handler(UserEvent)
async def log_user_events(event: UserEvent):
    """Log all user events for audit purposes."""
    logger.info(f"User Event: {event.action} for user {event.user_id} at {event.timestamp}")
    
    # You can add additional logic here, such as:
    # - Sending notifications
    # - Updating analytics
    # - Triggering other business processes


# Example 2: Conditional event handler - only handle user creation events
@event_handler(UserEvent, filter_func=lambda e: e.action == UserEventAction.CREATED)
async def handle_new_user_registration(event: UserEvent):
    """Handle new user registration events."""
    logger.info(f"New user registered: {event.user_id}")
    
    # Example actions for new user registration:
    # - Send welcome email
    # - Create default user settings
    # - Initialize user account
    
    # Simulate some async work
    await asyncio.sleep(0.1)
    logger.info(f"Welcome process completed for user {event.user_id}")


# Example 3: Order event handler with business logic
@event_handler(OrderEvent)
async def handle_order_events(event: OrderEvent):
    """Handle order-related events."""
    if event.action == OrderEventAction.CREATED:
        logger.info(f"New order created: {event.order_no} for user {event.user_id}")
        # Could trigger inventory checks, payment processing, etc.
        
    elif event.action == OrderEventAction.PAID:
        logger.info(f"Order paid: {event.order_no}, amount: {event.amount}")
        # Could trigger fulfillment process, send confirmation, etc.
        
    elif event.action == OrderEventAction.CANCELLED:
        logger.info(f"Order cancelled: {event.order_no}")
        # Could trigger refund process, inventory restoration, etc.


# Example 4: Notification event handler
@event_handler(NotificationEvent)
async def handle_notification_events(event: NotificationEvent):
    """Handle notification events by routing to appropriate channels."""
    logger.info(f"Sending {event.channel} notification to {event.recipient_type} {event.recipient_id}")
    
    # Route to different notification services based on channel
    if event.channel == "email":
        await send_email_notification(event)
    elif event.channel == "sms":
        await send_sms_notification(event)
    elif event.channel == "wechat":
        await send_wechat_notification(event)
    elif event.channel == "push":
        await send_push_notification(event)


# Example 5: High-priority notification handler
@event_handler(NotificationEvent, filter_func=lambda e: e.priority >= 3)
async def handle_urgent_notifications(event: NotificationEvent):
    """Handle urgent notifications with special processing."""
    logger.warning(f"URGENT notification: {event.content}")
    
    # Could implement special handling for urgent notifications:
    # - Send to multiple channels
    # - Escalate to administrators
    # - Log to special monitoring systems


# Example 6: Reservation event handler
@event_handler(ReservationEvent)
async def handle_reservation_events(event: ReservationEvent):
    """Handle reservation-related events."""
    logger.info(f"Reservation event: {event.action} for reservation {event.reservation_id}")
    
    # Example reservation handling logic
    if event.action.value == "confirmed":
        # Send confirmation message
        logger.info(f"Sending confirmation for reservation {event.reservation_id}")
    elif event.action.value == "cancelled":
        # Handle cancellation
        logger.info(f"Processing cancellation for reservation {event.reservation_id}")


# Example 7: Payment event handler with error handling
@event_handler(PaymentEvent)
async def handle_payment_events(event: PaymentEvent):
    """Handle payment events with comprehensive error handling."""
    try:
        logger.info(f"Processing payment event: {event.action} for order {event.order_id}")
        
        if event.action.value == "completed":
            # Update order status, send receipt, etc.
            await process_successful_payment(event)
        elif event.action.value == "failed":
            # Handle payment failure
            await process_failed_payment(event)
        elif event.action.value == "refunded":
            # Process refund
            await process_refund(event)
            
    except Exception as e:
        logger.error(f"Error processing payment event {event.id}: {e}")
        # Could publish an error event or send alert


# Example 8: System event handler for monitoring
@event_handler(SystemEvent, filter_func=lambda e: e.level in ["error", "critical"])
async def handle_system_errors(event: SystemEvent):
    """Handle system errors and critical events."""
    logger.error(f"System {event.level}: {event.message} in {event.component}")
    
    # Could implement:
    # - Send alerts to administrators
    # - Create support tickets
    # - Trigger automated recovery procedures


# Helper functions (these would be implemented based on your specific needs)

async def send_email_notification(event: NotificationEvent):
    """Send email notification."""
    logger.info(f"Sending email: {event.subject} to recipient {event.recipient_id}")
    # Implement email sending logic
    await asyncio.sleep(0.1)  # Simulate async work


async def send_sms_notification(event: NotificationEvent):
    """Send SMS notification."""
    logger.info(f"Sending SMS to recipient {event.recipient_id}")
    # Implement SMS sending logic
    await asyncio.sleep(0.1)  # Simulate async work


async def send_wechat_notification(event: NotificationEvent):
    """Send WeChat notification."""
    logger.info(f"Sending WeChat message to recipient {event.recipient_id}")
    # Implement WeChat API integration
    await asyncio.sleep(0.1)  # Simulate async work


async def send_push_notification(event: NotificationEvent):
    """Send push notification."""
    logger.info(f"Sending push notification to recipient {event.recipient_id}")
    # Implement push notification service
    await asyncio.sleep(0.1)  # Simulate async work


async def process_successful_payment(event: PaymentEvent):
    """Process successful payment."""
    logger.info(f"Processing successful payment: {event.transaction_id}")
    # Implement payment success logic
    await asyncio.sleep(0.1)  # Simulate async work


async def process_failed_payment(event: PaymentEvent):
    """Process failed payment."""
    logger.info(f"Processing failed payment for order: {event.order_id}")
    # Implement payment failure logic
    await asyncio.sleep(0.1)  # Simulate async work


async def process_refund(event: PaymentEvent):
    """Process refund."""
    logger.info(f"Processing refund: {event.transaction_id}")
    # Implement refund logic
    await asyncio.sleep(0.1)  # Simulate async work
