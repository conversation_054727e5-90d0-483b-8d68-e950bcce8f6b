"""
Service Integration Examples

This module demonstrates how to integrate the event system with existing
services in the application, showing practical usage patterns.
"""

from typing import Optional, Dict, Any
from sqlalchemy.orm import Session

from app.events import get_event_bus
from app.events.models import (
    UserEvent, OrderEvent, ReservationEvent, PaymentEvent,
    NotificationEvent, UserEventAction, OrderEventAction,
    ReservationEventAction, PaymentEventAction, NotificationEventAction
)
from app.utils.logger import logger


class EventAwareUserService:
    """
    Example of integrating events into a user service.
    
    This demonstrates how to modify existing services to publish
    events when important actions occur.
    """
    
    def __init__(self):
        self.event_bus = get_event_bus()
    
    async def create_user(self, db: Session, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a user and publish relevant events."""
        try:
            # Simulate user creation logic
            user_id = user_data.get("id", 1)
            username = user_data.get("username", "unknown")
            user_type = user_data.get("type", "personal")
            
            logger.info(f"Creating user: {username}")
            
            # Here you would normally create the user in the database
            # user = create_user_in_db(db, user_data)
            
            # Publish user created event
            user_event = UserEvent(
                action=UserEventAction.CREATED,
                user_id=user_id,
                username=username,
                user_type=user_type,
                source="user_service",
                additional_data={
                    "registration_ip": user_data.get("ip_address"),
                    "user_agent": user_data.get("user_agent")
                }
            )
            await self.event_bus.publish(user_event)
            
            # Publish welcome notification event
            welcome_event = NotificationEvent(
                action=NotificationEventAction.SEND_EMAIL,
                recipient_id=user_id,
                recipient_type="user",
                channel="email",
                subject="Welcome to our platform!",
                content=f"Welcome {username}! Your account has been created successfully.",
                priority=2,
                source="user_service"
            )
            await self.event_bus.publish(welcome_event)
            
            return {
                "success": True,
                "user_id": user_id,
                "username": username,
                "events_published": ["user_created", "welcome_notification"]
            }
            
        except Exception as e:
            logger.error(f"Error creating user: {e}")
            raise
    
    async def update_user(self, db: Session, user_id: int, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update a user and publish update event."""
        try:
            logger.info(f"Updating user: {user_id}")
            
            # Here you would normally update the user in the database
            # updated_user = update_user_in_db(db, user_id, update_data)
            
            # Publish user updated event
            user_event = UserEvent(
                action=UserEventAction.UPDATED,
                user_id=user_id,
                source="user_service",
                additional_data={
                    "updated_fields": list(update_data.keys()),
                    "update_timestamp": update_data.get("updated_at")
                }
            )
            await self.event_bus.publish(user_event)
            
            return {
                "success": True,
                "user_id": user_id,
                "updated_fields": list(update_data.keys())
            }
            
        except Exception as e:
            logger.error(f"Error updating user {user_id}: {e}")
            raise


class EventAwareOrderService:
    """
    Example of integrating events into an order service.
    """
    
    def __init__(self):
        self.event_bus = get_event_bus()
    
    async def create_order(self, db: Session, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create an order and publish creation event."""
        try:
            order_id = order_data.get("id", 1)
            user_id = order_data.get("user_id")
            amount = order_data.get("amount", 0.0)
            order_no = f"ORDER_{order_id}"
            
            logger.info(f"Creating order: {order_no}")
            
            # Here you would normally create the order in the database
            # order = create_order_in_db(db, order_data)
            
            # Publish order created event
            order_event = OrderEvent(
                action=OrderEventAction.CREATED,
                order_id=order_id,
                order_no=order_no,
                user_id=user_id,
                amount=amount,
                status="pending",
                payment_status="unpaid",
                source="order_service",
                additional_data={
                    "order_type": order_data.get("type"),
                    "items_count": len(order_data.get("items", []))
                }
            )
            await self.event_bus.publish(order_event)
            
            return {
                "success": True,
                "order_id": order_id,
                "order_no": order_no,
                "status": "pending"
            }
            
        except Exception as e:
            logger.error(f"Error creating order: {e}")
            raise
    
    async def update_order_status(self, db: Session, order_id: int, new_status: str) -> Dict[str, Any]:
        """Update order status and publish appropriate events."""
        try:
            logger.info(f"Updating order {order_id} status to: {new_status}")
            
            # Here you would normally update the order in the database
            # order = update_order_status_in_db(db, order_id, new_status)
            
            # Map status to event action
            action_mapping = {
                "paid": OrderEventAction.PAID,
                "cancelled": OrderEventAction.CANCELLED,
                "completed": OrderEventAction.COMPLETED
            }
            
            if new_status in action_mapping:
                order_event = OrderEvent(
                    action=action_mapping[new_status],
                    order_id=order_id,
                    order_no=f"ORDER_{order_id}",
                    status=new_status,
                    source="order_service"
                )
                await self.event_bus.publish(order_event)
            
            return {
                "success": True,
                "order_id": order_id,
                "new_status": new_status
            }
            
        except Exception as e:
            logger.error(f"Error updating order {order_id} status: {e}")
            raise


class EventAwarePaymentService:
    """
    Example of integrating events into a payment service.
    """
    
    def __init__(self):
        self.event_bus = get_event_bus()
    
    async def process_payment(self, db: Session, payment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process payment and publish payment events."""
        try:
            order_id = payment_data.get("order_id")
            user_id = payment_data.get("user_id")
            amount = payment_data.get("amount")
            payment_method = payment_data.get("payment_method", "wechat_pay")
            
            logger.info(f"Processing payment for order {order_id}")
            
            # Publish payment initiated event
            payment_initiated_event = PaymentEvent(
                action=PaymentEventAction.INITIATED,
                order_id=order_id,
                user_id=user_id,
                amount=amount,
                payment_method=payment_method,
                source="payment_service"
            )
            await self.event_bus.publish(payment_initiated_event)
            
            # Simulate payment processing
            # In real implementation, this would call external payment API
            success = True  # Simulate successful payment
            
            if success:
                transaction_id = f"TXN_{order_id}_{payment_data.get('timestamp', '123456')}"
                
                # Publish payment completed event
                payment_completed_event = PaymentEvent(
                    action=PaymentEventAction.COMPLETED,
                    order_id=order_id,
                    user_id=user_id,
                    amount=amount,
                    payment_method=payment_method,
                    transaction_id=transaction_id,
                    source="payment_service"
                )
                await self.event_bus.publish(payment_completed_event)
                
                return {
                    "success": True,
                    "transaction_id": transaction_id,
                    "status": "completed"
                }
            else:
                # Publish payment failed event
                payment_failed_event = PaymentEvent(
                    action=PaymentEventAction.FAILED,
                    order_id=order_id,
                    user_id=user_id,
                    amount=amount,
                    payment_method=payment_method,
                    source="payment_service",
                    additional_data={"error_reason": "Payment gateway error"}
                )
                await self.event_bus.publish(payment_failed_event)
                
                return {
                    "success": False,
                    "status": "failed",
                    "error": "Payment processing failed"
                }
                
        except Exception as e:
            logger.error(f"Error processing payment: {e}")
            raise


class EventAwareReservationService:
    """
    Example of integrating events into a reservation service.
    """
    
    def __init__(self):
        self.event_bus = get_event_bus()
    
    async def create_reservation(self, db: Session, reservation_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a reservation and publish creation event."""
        try:
            reservation_id = reservation_data.get("id", 1)
            user_id = reservation_data.get("user_id")
            dining_date = reservation_data.get("dining_date")
            
            logger.info(f"Creating reservation: {reservation_id}")
            
            # Here you would normally create the reservation in the database
            # reservation = create_reservation_in_db(db, reservation_data)
            
            # Publish reservation created event
            reservation_event = ReservationEvent(
                action=ReservationEventAction.CREATED,
                reservation_id=reservation_id,
                user_id=user_id,
                dining_date=dining_date,
                status="pending",
                source="reservation_service",
                additional_data={
                    "party_size": reservation_data.get("party_size"),
                    "special_requests": reservation_data.get("special_requests")
                }
            )
            await self.event_bus.publish(reservation_event)
            
            return {
                "success": True,
                "reservation_id": reservation_id,
                "status": "pending"
            }
            
        except Exception as e:
            logger.error(f"Error creating reservation: {e}")
            raise
    
    async def confirm_reservation(self, db: Session, reservation_id: int) -> Dict[str, Any]:
        """Confirm a reservation and publish confirmation event."""
        try:
            logger.info(f"Confirming reservation: {reservation_id}")
            
            # Here you would normally update the reservation in the database
            # reservation = confirm_reservation_in_db(db, reservation_id)
            
            # Publish reservation confirmed event
            reservation_event = ReservationEvent(
                action=ReservationEventAction.CONFIRMED,
                reservation_id=reservation_id,
                status="confirmed",
                source="reservation_service"
            )
            await self.event_bus.publish(reservation_event)
            
            return {
                "success": True,
                "reservation_id": reservation_id,
                "status": "confirmed"
            }
            
        except Exception as e:
            logger.error(f"Error confirming reservation {reservation_id}: {e}")
            raise


# Example of how to use these services
async def example_business_workflow():
    """
    Example business workflow that demonstrates cross-service event communication.
    """
    user_service = EventAwareUserService()
    order_service = EventAwareOrderService()
    payment_service = EventAwarePaymentService()
    reservation_service = EventAwareReservationService()
    
    # This would normally use a real database session
    db = None
    
    try:
        # Step 1: Create user
        user_result = await user_service.create_user(db, {
            "id": 123,
            "username": "john_doe",
            "type": "personal",
            "ip_address": "***********"
        })
        
        # Step 2: Create order
        order_result = await order_service.create_order(db, {
            "id": 456,
            "user_id": 123,
            "amount": 99.99,
            "type": "reservation",
            "items": [{"product_id": 1, "quantity": 2}]
        })
        
        # Step 3: Process payment
        payment_result = await payment_service.process_payment(db, {
            "order_id": 456,
            "user_id": 123,
            "amount": 99.99,
            "payment_method": "wechat_pay",
            "timestamp": "1234567890"
        })
        
        # Step 4: Create reservation (if payment successful)
        if payment_result["success"]:
            reservation_result = await reservation_service.create_reservation(db, {
                "id": 789,
                "user_id": 123,
                "dining_date": "2024-01-15",
                "party_size": 2
            })
            
            # Step 5: Confirm reservation
            await reservation_service.confirm_reservation(db, 789)
        
        logger.info("Business workflow completed successfully")
        
    except Exception as e:
        logger.error(f"Error in business workflow: {e}")
        raise
