"""
Event System Usage Examples

This module demonstrates how to use the event system in different scenarios,
including publishing events, using dependency injection, and integrating
with FastAPI routes.
"""

import asyncio
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.events import (
    EventBus, get_event_bus, UserEvent, OrderEvent, ReservationEvent,
    PaymentEvent, NotificationEvent, SystemEvent
)
from app.events.deps import EventBusDep, EventHandlerRegistryDep
from app.events.models import (
    UserEventAction, OrderEventAction, PaymentEventAction,
    NotificationEventAction, SystemEventAction
)
from app.utils.logger import logger

# Create a router for event system examples
router = APIRouter(prefix="/events", tags=["Event System Examples"])


@router.post("/publish/user-event")
async def publish_user_event_example(
    user_id: int,
    action: str,
    event_bus: EventBusDep
):
    """
    Example endpoint to publish a user event.
    
    This demonstrates how to use the event bus in FastAPI routes
    with dependency injection.
    """
    try:
        # Create and publish a user event
        event = UserEvent(
            action=UserEventAction(action),
            user_id=user_id,
            username=f"user_{user_id}",
            source="api_endpoint",
            additional_data={"endpoint": "/events/publish/user-event"}
        )
        
        await event_bus.publish(event)
        
        return {
            "message": "User event published successfully",
            "event_id": event.id,
            "timestamp": event.timestamp
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid action: {e}")
    except Exception as e:
        logger.error(f"Error publishing user event: {e}")
        raise HTTPException(status_code=500, detail="Failed to publish event")


@router.post("/publish/order-event")
async def publish_order_event_example(
    order_id: int,
    action: str,
    event_bus: EventBusDep,
    amount: float = None
):
    """Example endpoint to publish an order event."""
    try:
        event = OrderEvent(
            action=OrderEventAction(action),
            order_id=order_id,
            order_no=f"ORDER_{order_id}",
            amount=amount,
            source="order_service"
        )
        
        await event_bus.publish(event)
        
        return {
            "message": "Order event published successfully",
            "event_id": event.id,
            "order_id": order_id,
            "action": action
        }
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid action: {e}")
    except Exception as e:
        logger.error(f"Error publishing order event: {e}")
        raise HTTPException(status_code=500, detail="Failed to publish event")


@router.post("/publish/notification")
async def publish_notification_example(
    recipient_id: int,
    channel: str,
    content: str,
    event_bus: EventBusDep,
    priority: int = 1
):
    """Example endpoint to publish a notification event."""
    try:
        event = NotificationEvent(
            action=NotificationEventAction.SEND_WECHAT if channel == "wechat" else NotificationEventAction.SEND_EMAIL,
            recipient_id=recipient_id,
            recipient_type="user",
            channel=channel,
            content=content,
            priority=priority,
            source="notification_api"
        )
        
        await event_bus.publish(event)
        
        return {
            "message": "Notification event published successfully",
            "event_id": event.id,
            "recipient_id": recipient_id,
            "channel": channel
        }
        
    except Exception as e:
        logger.error(f"Error publishing notification event: {e}")
        raise HTTPException(status_code=500, detail="Failed to publish event")


@router.get("/stats")
async def get_event_bus_stats(event_bus: EventBusDep):
    """Get event bus statistics."""
    stats = event_bus.get_stats()
    return {
        "message": "Event bus statistics",
        "stats": stats
    }


@router.get("/handlers")
async def get_registered_handlers(registry: EventHandlerRegistryDep):
    """Get information about registered event handlers."""
    handlers = registry.get_all_handlers()
    
    handler_info = {}
    for event_type, handler_list in handlers.items():
        handler_info[event_type.__name__] = [
            {
                "name": handler.name,
                "has_filter": handler.filter_func is not None
            }
            for handler in handler_list
        ]
    
    return {
        "message": "Registered event handlers",
        "handlers": handler_info
    }


# Example service functions that demonstrate event publishing

async def simulate_user_registration(user_id: int, username: str) -> Dict[str, Any]:
    """
    Simulate user registration process with event publishing.
    
    This demonstrates how to integrate event publishing into
    business logic functions.
    """
    event_bus = get_event_bus()
    
    try:
        # Simulate user creation logic
        logger.info(f"Creating user: {username}")
        await asyncio.sleep(0.1)  # Simulate database operation
        
        # Publish user created event
        user_event = UserEvent(
            action=UserEventAction.CREATED,
            user_id=user_id,
            username=username,
            source="user_service",
            additional_data={"registration_method": "api"}
        )
        await event_bus.publish(user_event)
        
        # Publish notification event for welcome message
        notification_event = NotificationEvent(
            action=NotificationEventAction.SEND_EMAIL,
            recipient_id=user_id,
            recipient_type="user",
            channel="email",
            subject="Welcome to our platform!",
            content=f"Welcome {username}! Your account has been created successfully.",
            priority=2,
            source="user_service"
        )
        await event_bus.publish(notification_event)
        
        return {
            "success": True,
            "user_id": user_id,
            "username": username,
            "events_published": 2
        }
        
    except Exception as e:
        logger.error(f"Error in user registration: {e}")
        
        # Publish system error event
        error_event = SystemEvent(
            action=SystemEventAction.ERROR,
            component="user_service",
            message=f"User registration failed for {username}: {e}",
            level="error",
            source="user_service"
        )
        await event_bus.publish(error_event)
        
        return {
            "success": False,
            "error": str(e)
        }


async def simulate_order_processing(order_id: int, user_id: int, amount: float) -> Dict[str, Any]:
    """
    Simulate order processing with multiple event publications.
    
    This demonstrates a complex business process that publishes
    multiple events at different stages.
    """
    event_bus = get_event_bus()
    
    try:
        # Step 1: Order created
        logger.info(f"Processing order {order_id}")
        
        order_created_event = OrderEvent(
            action=OrderEventAction.CREATED,
            order_id=order_id,
            order_no=f"ORDER_{order_id}",
            user_id=user_id,
            amount=amount,
            status="pending",
            source="order_service"
        )
        await event_bus.publish(order_created_event)
        
        # Step 2: Simulate payment processing
        await asyncio.sleep(0.2)  # Simulate payment processing time
        
        payment_event = PaymentEvent(
            action=PaymentEventAction.COMPLETED,
            order_id=order_id,
            user_id=user_id,
            amount=amount,
            payment_method="wechat_pay",
            transaction_id=f"TXN_{order_id}_{int(datetime.now().timestamp())}",
            source="payment_service"
        )
        await event_bus.publish(payment_event)
        
        # Step 3: Order paid
        order_paid_event = OrderEvent(
            action=OrderEventAction.PAID,
            order_id=order_id,
            order_no=f"ORDER_{order_id}",
            user_id=user_id,
            amount=amount,
            status="paid",
            payment_status="paid",
            source="order_service"
        )
        await event_bus.publish(order_paid_event)
        
        # Step 4: Send confirmation notification
        confirmation_event = NotificationEvent(
            action=NotificationEventAction.SEND_WECHAT,
            recipient_id=user_id,
            recipient_type="user",
            channel="wechat",
            content=f"Your order {order_id} has been confirmed and paid. Amount: ¥{amount}",
            priority=2,
            source="order_service"
        )
        await event_bus.publish(confirmation_event)
        
        return {
            "success": True,
            "order_id": order_id,
            "status": "completed",
            "events_published": 4
        }
        
    except Exception as e:
        logger.error(f"Error in order processing: {e}")
        
        # Publish error event
        error_event = SystemEvent(
            action=SystemEventAction.ERROR,
            component="order_service",
            message=f"Order processing failed for order {order_id}: {e}",
            level="error",
            source="order_service"
        )
        await event_bus.publish(error_event)
        
        return {
            "success": False,
            "error": str(e)
        }


# Test endpoints for the simulation functions
@router.post("/simulate/user-registration")
async def simulate_user_registration_endpoint(user_id: int, username: str):
    """Test endpoint for user registration simulation."""
    result = await simulate_user_registration(user_id, username)
    return result


@router.post("/simulate/order-processing")
async def simulate_order_processing_endpoint(order_id: int, user_id: int, amount: float):
    """Test endpoint for order processing simulation."""
    result = await simulate_order_processing(order_id, user_id, amount)
    return result
