# 事件系统使用示例

本文档提供了如何在实际项目中使用事件系统的详细示例。

## 基础使用

### 1. 发布用户事件

```python
from app.events import get_event_bus, UserEvent, UserEventAction

async def create_user_example():
    """创建用户并发布事件的示例"""
    event_bus = get_event_bus()
    
    # 创建用户事件
    user_event = UserEvent(
        action=UserEventAction.CREATED,
        user_id=123,
        username="张三",
        user_type="personal",
        source="user_service",
        additional_data={
            "registration_method": "手机号",
            "referrer": "朋友推荐"
        }
    )
    
    # 发布事件
    await event_bus.publish(user_event)
    print("用户创建事件已发布")
```

### 2. 创建事件处理器

```python
from app.events.handlers import event_handler
from app.events.models import UserEvent, OrderEvent

@event_handler(UserEvent)
async def handle_user_events(event: UserEvent):
    """处理用户事件"""
    if event.action == UserEventAction.CREATED:
        print(f"新用户注册: {event.username} (ID: {event.user_id})")
        # 这里可以添加欢迎邮件、初始化用户设置等逻辑
        
    elif event.action == UserEventAction.LOGIN:
        print(f"用户登录: {event.username}")
        # 这里可以添加登录统计、安全检查等逻辑

@event_handler(OrderEvent)
async def handle_order_events(event: OrderEvent):
    """处理订单事件"""
    if event.action == OrderEventAction.CREATED:
        print(f"新订单创建: {event.order_no}")
        # 发送订单确认通知
        
    elif event.action == OrderEventAction.PAID:
        print(f"订单支付完成: {event.order_no}, 金额: ¥{event.amount}")
        # 触发发货流程
```

## 在 FastAPI 路由中使用

### 用户管理接口

```python
from fastapi import APIRouter, Depends, HTTPException
from app.events.deps import EventBusDep
from app.events.models import UserEvent, UserEventAction

router = APIRouter(prefix="/users", tags=["用户管理"])

@router.post("/register")
async def register_user(
    user_data: dict,
    event_bus: EventBusDep
):
    """用户注册接口"""
    try:
        # 创建用户逻辑（省略具体实现）
        user_id = 123  # 假设创建成功后返回的用户ID
        
        # 发布用户创建事件
        event = UserEvent(
            action=UserEventAction.CREATED,
            user_id=user_id,
            username=user_data["username"],
            user_type="personal",
            source="api_register",
            additional_data={
                "registration_ip": "***********",
                "user_agent": "Mozilla/5.0..."
            }
        )
        await event_bus.publish(event)
        
        return {
            "success": True,
            "message": "用户注册成功",
            "user_id": user_id
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"注册失败: {str(e)}")

@router.post("/login")
async def login_user(
    login_data: dict,
    event_bus: EventBusDep
):
    """用户登录接口"""
    # 验证用户凭据（省略具体实现）
    user_id = 123
    username = "张三"
    
    # 发布登录事件
    event = UserEvent(
        action=UserEventAction.LOGIN,
        user_id=user_id,
        username=username,
        source="api_login"
    )
    await event_bus.publish(event)
    
    return {
        "success": True,
        "message": "登录成功",
        "token": "jwt_token_here"
    }
```

### 订单管理接口

```python
from app.events.models import OrderEvent, OrderEventAction, PaymentEvent, PaymentEventAction

@router.post("/orders")
async def create_order(
    order_data: dict,
    event_bus: EventBusDep
):
    """创建订单接口"""
    # 创建订单逻辑（省略具体实现）
    order_id = 456
    order_no = f"ORDER_{order_id}"
    
    # 发布订单创建事件
    event = OrderEvent(
        action=OrderEventAction.CREATED,
        order_id=order_id,
        order_no=order_no,
        user_id=order_data["user_id"],
        amount=order_data["amount"],
        status="pending",
        source="api_order",
        additional_data={
            "items": order_data["items"],
            "delivery_address": order_data.get("address")
        }
    )
    await event_bus.publish(event)
    
    return {
        "success": True,
        "order_id": order_id,
        "order_no": order_no,
        "status": "pending"
    }

@router.post("/orders/{order_id}/pay")
async def pay_order(
    order_id: int,
    payment_data: dict,
    event_bus: EventBusDep
):
    """订单支付接口"""
    # 处理支付逻辑（省略具体实现）
    transaction_id = f"TXN_{order_id}_{int(time.time())}"
    
    # 发布支付完成事件
    payment_event = PaymentEvent(
        action=PaymentEventAction.COMPLETED,
        order_id=order_id,
        user_id=payment_data["user_id"],
        amount=payment_data["amount"],
        payment_method="微信支付",
        transaction_id=transaction_id,
        source="api_payment"
    )
    await event_bus.publish(payment_event)
    
    # 发布订单支付事件
    order_event = OrderEvent(
        action=OrderEventAction.PAID,
        order_id=order_id,
        order_no=f"ORDER_{order_id}",
        amount=payment_data["amount"],
        status="paid",
        payment_status="paid",
        source="api_payment"
    )
    await event_bus.publish(order_event)
    
    return {
        "success": True,
        "transaction_id": transaction_id,
        "status": "paid"
    }
```

## 服务层集成

### 用户服务

```python
from app.events import get_event_bus
from app.events.models import UserEvent, UserEventAction, NotificationEvent, NotificationEventAction

class UserService:
    """用户服务类，集成事件发布"""
    
    def __init__(self):
        self.event_bus = get_event_bus()
    
    async def create_user(self, user_data: dict) -> dict:
        """创建用户"""
        try:
            # 数据库操作（省略具体实现）
            user_id = await self._save_user_to_db(user_data)
            
            # 发布用户创建事件
            user_event = UserEvent(
                action=UserEventAction.CREATED,
                user_id=user_id,
                username=user_data["username"],
                user_type=user_data.get("type", "personal"),
                source="user_service"
            )
            await self.event_bus.publish(user_event)
            
            # 发布欢迎通知事件
            notification_event = NotificationEvent(
                action=NotificationEventAction.SEND_EMAIL,
                recipient_id=user_id,
                recipient_type="user",
                channel="email",
                subject="欢迎加入我们的平台！",
                content=f"亲爱的 {user_data['username']}，欢迎您！",
                priority=2,
                source="user_service"
            )
            await self.event_bus.publish(notification_event)
            
            return {
                "success": True,
                "user_id": user_id,
                "username": user_data["username"]
            }
            
        except Exception as e:
            # 发布错误事件
            error_event = SystemEvent(
                action=SystemEventAction.ERROR,
                component="user_service",
                message=f"创建用户失败: {str(e)}",
                level="error",
                source="user_service"
            )
            await self.event_bus.publish(error_event)
            raise
    
    async def _save_user_to_db(self, user_data: dict) -> int:
        """保存用户到数据库（模拟实现）"""
        # 这里应该是实际的数据库操作
        return 123  # 返回用户ID
```

### 通知服务

```python
from app.events.handlers import event_handler
from app.events.models import NotificationEvent, UserEvent, OrderEvent

class NotificationService:
    """通知服务，响应各种事件并发送通知"""
    
    @event_handler(UserEvent)
    async def handle_user_events(self, event: UserEvent):
        """处理用户事件并发送相应通知"""
        if event.action == UserEventAction.CREATED:
            await self.send_welcome_notification(event.user_id, event.username)
        elif event.action == UserEventAction.LOGIN:
            await self.send_login_notification(event.user_id)
    
    @event_handler(OrderEvent)
    async def handle_order_events(self, event: OrderEvent):
        """处理订单事件并发送相应通知"""
        if event.action == OrderEventAction.CREATED:
            await self.send_order_confirmation(event.user_id, event.order_no)
        elif event.action == OrderEventAction.PAID:
            await self.send_payment_confirmation(event.user_id, event.order_no, event.amount)
    
    async def send_welcome_notification(self, user_id: int, username: str):
        """发送欢迎通知"""
        print(f"发送欢迎邮件给用户 {username} (ID: {user_id})")
        # 实际的邮件发送逻辑
    
    async def send_order_confirmation(self, user_id: int, order_no: str):
        """发送订单确认通知"""
        print(f"发送订单确认通知: {order_no} 给用户 {user_id}")
        # 实际的通知发送逻辑
    
    async def send_payment_confirmation(self, user_id: int, order_no: str, amount: float):
        """发送支付确认通知"""
        print(f"发送支付确认通知: 订单 {order_no}, 金额 ¥{amount} 给用户 {user_id}")
        # 实际的通知发送逻辑
```

## 条件事件处理

### 高级用户特殊处理

```python
@event_handler(UserEvent, filter_func=lambda e: e.additional_data and e.additional_data.get("is_vip"))
async def handle_vip_user_events(event: UserEvent):
    """只处理VIP用户的事件"""
    print(f"VIP用户事件: {event.action} - {event.username}")
    # VIP用户的特殊处理逻辑

@event_handler(OrderEvent, filter_func=lambda e: e.amount and e.amount > 1000)
async def handle_large_orders(event: OrderEvent):
    """处理大额订单"""
    print(f"大额订单: {event.order_no}, 金额: ¥{event.amount}")
    # 大额订单的特殊处理逻辑，如风控检查、人工审核等
```

## 错误处理和监控

### 错误处理示例

```python
@event_handler(OrderEvent)
async def handle_order_with_error_handling(event: OrderEvent):
    """带错误处理的订单事件处理器"""
    try:
        if event.action == OrderEventAction.PAID:
            # 处理支付成功逻辑
            await process_paid_order(event)
            
    except Exception as e:
        logger.error(f"处理订单事件失败: {event.order_id}, 错误: {str(e)}")
        
        # 发布错误事件
        error_event = SystemEvent(
            action=SystemEventAction.ERROR,
            component="order_handler",
            message=f"订单处理失败: {event.order_id} - {str(e)}",
            level="error",
            source="order_handler",
            additional_data={
                "original_event_id": event.id,
                "order_id": event.order_id
            }
        )
        
        event_bus = get_event_bus()
        await event_bus.publish(error_event)

async def process_paid_order(event: OrderEvent):
    """处理已支付订单的具体逻辑"""
    # 更新库存
    # 生成发货单
    # 发送确认邮件
    pass
```

这些示例展示了如何在实际项目中有效地使用事件系统来实现模块间的解耦通信。
