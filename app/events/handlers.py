"""
Event Handler Registry

This module provides utilities for registering and managing event handlers,
including decorators for easy handler registration.
"""

import inspect
import logging
from functools import wraps
from typing import Dict, List, Type, Callable, Awaitable, Optional

from app.events.base import BaseEvent, EventHandler
from app.events.bus import get_event_bus

# Use standard logging instead of custom logger to avoid dependencies
logger = logging.getLogger(__name__)


class EventHandlerRegistry:
    """
    Registry for managing event handlers.
    
    This class provides utilities for registering handlers and
    automatically subscribing them to the event bus.
    """
    
    def __init__(self):
        self._handlers: Dict[Type[BaseEvent], List[EventHandler]] = {}
        self._registered_functions: List[Callable] = []
    
    def register_handler(
        self,
        event_type: Type[BaseEvent],
        handler_func: Callable[[BaseEvent], Awaitable[None]],
        name: Optional[str] = None,
        filter_func: Optional[Callable[[BaseEvent], bool]] = None
    ) -> EventHandler:
        """
        Register an event handler.
        
        Args:
            event_type: The type of event to handle
            handler_func: The async function that handles the event
            name: Optional name for the handler
            filter_func: Optional filter function
            
        Returns:
            EventHandler: The created event handler
        """
        handler = EventHandler(
            event_type=event_type,
            handler_func=handler_func,
            name=name,
            filter_func=filter_func
        )
        
        if event_type not in self._handlers:
            self._handlers[event_type] = []
        
        self._handlers[event_type].append(handler)
        self._registered_functions.append(handler_func)
        
        # Subscribe to the event bus
        event_bus = get_event_bus()
        event_bus.subscribe(handler)
        
        logger.info(f"Registered event handler '{handler.name}' for {event_type.__name__}")
        return handler
    
    def get_handlers(self, event_type: Type[BaseEvent]) -> List[EventHandler]:
        """
        Get all handlers for a specific event type.
        
        Args:
            event_type: The event type to get handlers for
            
        Returns:
            List[EventHandler]: List of handlers for the event type
        """
        return self._handlers.get(event_type, [])
    
    def get_all_handlers(self) -> Dict[Type[BaseEvent], List[EventHandler]]:
        """Get all registered handlers."""
        return self._handlers.copy()
    
    def unregister_handler(self, handler: EventHandler) -> bool:
        """
        Unregister an event handler.
        
        Args:
            handler: The handler to unregister
            
        Returns:
            bool: True if the handler was found and removed
        """
        event_type = handler.event_type
        if event_type in self._handlers and handler in self._handlers[event_type]:
            self._handlers[event_type].remove(handler)
            
            # Unsubscribe from the event bus
            event_bus = get_event_bus()
            event_bus.unsubscribe(handler)
            
            logger.info(f"Unregistered event handler '{handler.name}' for {event_type.__name__}")
            return True
        return False


# Global registry instance
_registry: Optional[EventHandlerRegistry] = None


def get_event_handler_registry() -> EventHandlerRegistry:
    """
    Get the global event handler registry.
    
    Returns:
        EventHandlerRegistry: The global registry instance
    """
    global _registry
    if _registry is None:
        _registry = EventHandlerRegistry()
    return _registry


def event_handler(
    event_type: Type[BaseEvent],
    name: Optional[str] = None,
    filter_func: Optional[Callable[[BaseEvent], bool]] = None
):
    """
    Decorator for registering event handlers.
    
    Args:
        event_type: The type of event to handle
        name: Optional name for the handler
        filter_func: Optional filter function
        
    Example:
        @event_handler(UserEvent)
        async def handle_user_event(event: UserEvent):
            print(f"User {event.user_id} performed action: {event.action}")
    """
    def decorator(func: Callable[[BaseEvent], Awaitable[None]]):
        # Validate that the function is async
        if not inspect.iscoroutinefunction(func):
            raise ValueError(f"Event handler {func.__name__} must be an async function")
        
        # Register the handler
        registry = get_event_handler_registry()
        handler = registry.register_handler(
            event_type=event_type,
            handler_func=func,
            name=name or func.__name__,
            filter_func=filter_func
        )
        
        # Store the handler reference on the function for potential unregistration
        func._event_handler = handler
        
        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await func(*args, **kwargs)
        
        return wrapper
    
    return decorator


def conditional_handler(condition: Callable[[BaseEvent], bool]):
    """
    Decorator for creating conditional event handlers.
    
    Args:
        condition: Function that returns True if the event should be handled
        
    Example:
        @conditional_handler(lambda event: event.user_id > 100)
        @event_handler(UserEvent)
        async def handle_premium_user_event(event: UserEvent):
            print(f"Premium user {event.user_id} event")
    """
    def decorator(func):
        if hasattr(func, '_event_handler'):
            # Update the filter function of an existing handler
            func._event_handler.filter_func = condition
        else:
            # Store the condition for later use
            func._filter_condition = condition
        return func
    
    return decorator
