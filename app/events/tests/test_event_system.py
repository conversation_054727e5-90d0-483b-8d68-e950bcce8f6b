#!/usr/bin/env python3
"""
Event System Test Script

This script tests the event system functionality to ensure it works correctly.
Run this script to verify the event system is properly implemented.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import event system components directly to avoid config dependencies
from app.events.base import BaseEvent, EventHandler
from app.events.bus import EventBus
from app.events.models import (
    UserEvent, OrderEvent, NotificationEvent, SystemEvent,
    UserEventAction, OrderEventAction, NotificationEventAction,
    SystemEventAction
)

# Simple logger replacement to avoid config dependencies
class SimpleLogger:
    def info(self, msg): print(f"INFO: {msg}")
    def error(self, msg): print(f"ERROR: {msg}")
    def debug(self, msg): print(f"DEBUG: {msg}")
    def warning(self, msg): print(f"WARNING: {msg}")

logger = SimpleLogger()


# Test event handlers
received_events = []

async def test_user_handler(event: UserEvent):
    """Test handler for user events."""
    received_events.append(("user", event))
    print(f"✅ Received user event: {event.action} for user {event.user_id}")

async def test_order_handler(event: OrderEvent):
    """Test handler for order events."""
    received_events.append(("order", event))
    print(f"✅ Received order event: {event.action} for order {event.order_id}")

async def test_notification_handler(event: NotificationEvent):
    """Test handler for notification events."""
    received_events.append(("notification", event))
    print(f"✅ Received notification event: {event.action} via {event.channel}")

async def test_system_handler(event: SystemEvent):
    """Test handler for system events."""
    received_events.append(("system", event))
    print(f"✅ Received system event: {event.action} - {event.message}")


async def test_basic_event_publishing():
    """Test basic event publishing and handling."""
    print("\n🧪 Testing basic event publishing...")

    # Create a new event bus instance for testing
    event_bus = EventBus()

    # Manually register handlers
    user_handler = EventHandler(UserEvent, test_user_handler)
    order_handler = EventHandler(OrderEvent, test_order_handler)
    notification_handler = EventHandler(NotificationEvent, test_notification_handler)
    system_handler = EventHandler(SystemEvent, test_system_handler)

    event_bus.subscribe(user_handler)
    event_bus.subscribe(order_handler)
    event_bus.subscribe(notification_handler)
    event_bus.subscribe(system_handler)

    await event_bus.start()

    try:
        # Test user event
        user_event = UserEvent(
            action=UserEventAction.CREATED,
            user_id=123,
            username="test_user",
            source="test_script"
        )
        await event_bus.publish(user_event)
        
        # Test order event
        order_event = OrderEvent(
            action=OrderEventAction.CREATED,
            order_id=456,
            order_no="TEST_ORDER_456",
            user_id=123,
            amount=99.99,
            source="test_script"
        )
        await event_bus.publish(order_event)
        
        # Test notification event
        notification_event = NotificationEvent(
            action=NotificationEventAction.SEND_EMAIL,
            recipient_id=123,
            recipient_type="user",
            channel="email",
            content="Test notification message",
            source="test_script"
        )
        await event_bus.publish(notification_event)
        
        # Test system event
        system_event = SystemEvent(
            action=SystemEventAction.INFO,
            component="test_script",
            message="Event system test completed",
            source="test_script"
        )
        await event_bus.publish(system_event)
        
        # Wait for events to be processed
        await asyncio.sleep(0.5)
        
        print(f"✅ Published 4 events, received {len(received_events)} events")
        
        # Verify all events were received
        assert len(received_events) == 4, f"Expected 4 events, got {len(received_events)}"
        
        event_types = [event_type for event_type, _ in received_events]
        expected_types = ["user", "order", "notification", "system"]
        
        for expected_type in expected_types:
            assert expected_type in event_types, f"Missing event type: {expected_type}"
        
        print("✅ All events were processed correctly!")
        
    finally:
        await event_bus.stop()


async def test_event_filtering():
    """Test event filtering functionality."""
    print("\n🧪 Testing event filtering...")

    filtered_events = []

    async def filtered_user_handler(event: UserEvent):
        """Handler that only processes events for users with ID > 100."""
        filtered_events.append(event)
        print(f"✅ Filtered handler received event for user {event.user_id}")

    event_bus = EventBus()

    # Register handler with filter
    handler = EventHandler(UserEvent, filtered_user_handler, filter_func=lambda e: e.user_id > 100)
    event_bus.subscribe(handler)

    await event_bus.start()
    
    try:
        # This event should be filtered out (user_id <= 100)
        event1 = UserEvent(
            action=UserEventAction.CREATED,
            user_id=50,
            username="user_50",
            source="test_script"
        )
        await event_bus.publish(event1)
        
        # This event should pass the filter (user_id > 100)
        event2 = UserEvent(
            action=UserEventAction.CREATED,
            user_id=150,
            username="user_150",
            source="test_script"
        )
        await event_bus.publish(event2)
        
        # Wait for events to be processed
        await asyncio.sleep(0.5)
        
        # Only one event should have been processed by the filtered handler
        assert len(filtered_events) == 1, f"Expected 1 filtered event, got {len(filtered_events)}"
        assert filtered_events[0].user_id == 150, "Wrong event was processed by filter"
        
        print("✅ Event filtering works correctly!")
        
    finally:
        await event_bus.stop()


async def test_event_bus_stats():
    """Test event bus statistics."""
    print("\n🧪 Testing event bus statistics...")

    event_bus = EventBus()

    # Register a simple handler
    handler = EventHandler(UserEvent, test_user_handler)
    event_bus.subscribe(handler)

    await event_bus.start()
    
    try:
        # Get initial stats
        initial_stats = event_bus.get_stats()
        print(f"Initial stats: {initial_stats}")
        
        # Publish some events
        for i in range(3):
            event = UserEvent(
                action=UserEventAction.CREATED,
                user_id=i,
                username=f"user_{i}",
                source="test_script"
            )
            await event_bus.publish(event)
        
        # Wait for processing
        await asyncio.sleep(0.5)
        
        # Get final stats
        final_stats = event_bus.get_stats()
        print(f"Final stats: {final_stats}")
        
        # Verify stats were updated
        assert final_stats["events_published"] >= initial_stats["events_published"] + 3
        assert final_stats["events_processed"] >= initial_stats["events_processed"] + 3
        
        print("✅ Event bus statistics work correctly!")
        
    finally:
        await event_bus.stop()


# Removed handler registry test since we're not using the global registry


async def test_error_handling():
    """Test error handling in event handlers."""
    print("\n🧪 Testing error handling...")

    async def error_handler(event: SystemEvent):
        """Handler that raises an error."""
        if event.message == "trigger_error":
            raise ValueError("Test error")
        print(f"✅ System event processed: {event.message}")

    event_bus = EventBus()

    # Register the error handler
    handler = EventHandler(SystemEvent, error_handler)
    event_bus.subscribe(handler)

    await event_bus.start()
    
    try:
        # Publish an event that will cause an error
        error_event = SystemEvent(
            action=SystemEventAction.ERROR,
            component="test",
            message="trigger_error",
            source="test_script"
        )
        await event_bus.publish(error_event)
        
        # Publish a normal event
        normal_event = SystemEvent(
            action=SystemEventAction.INFO,
            component="test",
            message="normal_event",
            source="test_script"
        )
        await event_bus.publish(normal_event)
        
        # Wait for processing
        await asyncio.sleep(0.5)
        
        # Check that the event bus is still running despite the error
        stats = event_bus.get_stats()
        assert stats["running"] == True, "Event bus stopped due to handler error"
        assert stats["events_failed"] > 0, "Error count not updated"
        
        print("✅ Error handling works correctly!")
        
    finally:
        await event_bus.stop()


async def main():
    """Run all tests."""
    print("🚀 Starting Event System Tests")
    print("=" * 50)

    try:
        await test_basic_event_publishing()
        await test_event_filtering()
        await test_event_bus_stats()
        # Handler registry test removed
        await test_error_handling()

        print("\n" + "=" * 50)
        print("🎉 All tests passed! Event system is working correctly.")

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
