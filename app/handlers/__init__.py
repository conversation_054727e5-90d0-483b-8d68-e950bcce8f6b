"""
事件处理器自动导入模块

这个模块自动发现并导入handlers包中的所有处理器模块，确保它们被注册到事件系统中。

使用方法:
    只需导入这个包，所有以_handler.py结尾的处理器模块都会自动导入并注册。

    示例:
        import app.handlers  # 自动导入所有处理器

约定:
    - 处理器文件应该以_handler.py结尾
    - 每个处理器文件应该使用@event_handler装饰器
    - 多个处理器可以定义在单个文件中
"""

import os
import importlib
from pathlib import Path
from app.utils.logger import logger


def auto_import_handlers():
    """
    自动导入handlers目录下的所有处理器模块
    
    遍历handlers目录，导入所有以_handler.py结尾的文件，
    确保其中的事件处理器被注册到事件系统中。
    
    Returns:
        list: 成功导入的模块名称列表
    """
    handlers_dir = Path(__file__).parent
    handler_modules = []
    
    logger.info("开始自动导入事件处理器...")
    
    # 遍历handlers目录
    for file_path in handlers_dir.glob("*_handler.py"):
        if file_path.is_file() and file_path.name != "__init__.py":
            module_name = f"app.handlers.{file_path.stem}"
            try:
                module = importlib.import_module(module_name)
                handler_modules.append(module_name)
                logger.info(f"成功导入事件处理器: {module_name}")
            except ImportError as e:
                logger.warning(f"导入事件处理器失败: {module_name}: {e}")
            except Exception as e:
                logger.error(f"导入事件处理器失败: {module_name}: {e}")
    
    if handler_modules:
        logger.info(f"自动导入 {len(handler_modules)} 个事件处理器: {handler_modules}")
    else:
        logger.warning("没有找到以_handler.py结尾的处理器模块")
    
    return handler_modules


# 在模块导入时自动执行
auto_import_handlers()
