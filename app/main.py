import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from starlette import status
from starlette.requests import Request
from starlette.responses import JSONResponse
from contextlib import asynccontextmanager

from app.api.v1 import api_router
from app.callback import callback_router
from app.core.config import settings
from app.utils.logger import logger
from app.core.scheduler import setup_scheduler, scheduler  # 导入调度器
from app.core.events import event_system_lifespan  # 导入事件系统生命周期管理

tags_metadata = [
    {"name": "管理模块", "description": "管理员管理接口"},
    {"name": "用户模块", "description": "用户管理接口"},
    {"name": "商品模块", "description": "商品管理接口"},
    {"name": "价格模块", "description": "价格策略管理接口"}
    # {"name": "订单模块", "description": "订单相关接口"},
]

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    应用生命周期事件处理
    """
    # 启动事件
    logger.info(f"=== {settings.PROJECT_NAME} 服务启动 ===")
    logger.info(f"数据库日志记录状态: {'开启' if settings.DB_LOGGING_ENABLED else '关闭'}")

    # 初始化事件系统
    async with event_system_lifespan():
        # 设置并启动调度器
        setup_scheduler()
        scheduler.start()
        logger.info("定时任务调度器已启动")

        yield

        # 关闭事件
        scheduler.shutdown()
        logger.info("定时任务调度器已关闭")

    logger.info(f"=== {settings.PROJECT_NAME} 服务关闭 ===")

app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    description="乙禾素食管理系统 API",
    version="1.0.0",
    openapi_tags=tags_metadata,
    swagger_ui_parameters={"docExpansion": "none"},
    lifespan=lifespan
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 根据配置决定是否挂载静态文件
if settings.ENABLE_STATIC_FILES:
    app.mount("/static", StaticFiles(directory="static"), name="static")
    logger.info("静态文件服务已启用")
else:
    logger.info("静态文件服务已禁用")

# 注册 API 路由
app.include_router(api_router, prefix=settings.API_V1_STR)
# 注册 回调 路由
app.include_router(callback_router, prefix=settings.CALLBACK_STR)


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    error_messages = []
    for error in exc.errors():
        # loc = " -> ".join(map(str, error["loc"]))
        loc = error["loc"][-1]
        msg = error["msg"]
        error_messages.append(f"{loc}: {msg}")

    logger.error(f"请求验证错误: {error_messages}")
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={"message": "请求参数错误", "error": error_messages}
    )


@app.exception_handler(status.HTTP_401_UNAUTHORIZED)
async def unauthorized_exception_handler(request: Request, exc: HTTPException):
    """处理未授权异常"""
    logger.warning(f"未授权访问: {request.url.path}")
    return JSONResponse(
        status_code=status.HTTP_401_UNAUTHORIZED,
        content={"message": "未授权访问，请先登录", "error": str(exc.detail)}
    )


@app.exception_handler(status.HTTP_403_FORBIDDEN)
async def forbidden_exception_handler(request: Request, exc: HTTPException):
    """处理禁止访问异常"""
    logger.warning(f"权限不足: {request.url.path}")
    return JSONResponse(
        status_code=status.HTTP_403_FORBIDDEN,
        content={"message": "权限不足，无法访问此资源", "error": str(exc.detail)}
    )


# @app.get("/")
# async def root():
#     """根路径"""
#     return {"message": "欢迎使用乙禾素食管理系统 API"}

if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8000)
