# 统一消息发送模块

这是一个统一的消息发送抽象模块，支持多种不同的发送渠道，包括邮件、短信、钉钉消息、微信小程序消息和系统消息。

## 特性

- **统一接口**: 所有消息类型使用相同的API接口
- **多渠道支持**: 支持邮件、短信、钉钉、微信小程序、系统消息
- **异步发送**: 支持异步批量发送
- **配置管理**: 统一的配置管理系统
- **错误处理**: 完善的错误处理和重试机制
- **扩展性**: 易于扩展新的消息发送渠道

## 架构设计

```
app/messager/
├── core/                    # 核心抽象层
│   ├── base.py             # 基础抽象类
│   ├── message.py          # 消息数据结构
│   └── manager.py          # 消息管理器
├── senders/                # 具体实现层
│   ├── email.py            # 邮件发送器
│   ├── sms.py              # 短信发送器
│   ├── dingtalk.py         # 钉钉消息发送器
│   ├── wechat_miniapp.py   # 微信小程序消息发送器
│   └── system.py           # 系统消息发送器
├── config/                 # 配置管理
│   └── settings.py         # 配置定义
├── factory.py              # 工厂类
└── examples.py             # 使用示例
```

## 快速开始

### 1. 基本使用

```python
import asyncio
from app.messager import (
    get_global_message_manager, 
    Message, 
    MessageType, 
    MessageRecipient, 
    MessageContent
)

async def send_notification():
    # 创建消息
    message = Message(
        type=MessageType.EMAIL,
        recipients=[
            MessageRecipient(
                id="user_001",
                name="张三",
                contact="<EMAIL>"
            )
        ],
        content=MessageContent(
            title="系统通知",
            body="这是一条测试消息"
        )
    )
    
    # 获取消息管理器并发送
    manager = get_global_message_manager()
    result = await manager.send_message(message)
    
    print(f"发送结果: 成功={result.success_count}, 失败={result.failed_count}")

# 运行
asyncio.run(send_notification())
```

### 2. 发送不同类型的消息

#### 发送邮件

```python
message = Message(
    type=MessageType.EMAIL,
    recipients=[MessageRecipient(id="user1", contact="<EMAIL>")],
    content=MessageContent(
        title="邮件标题",
        body="邮件内容",
        attachments=["/path/to/file.pdf"]  # 可选附件
    )
)
```

#### 发送短信

```python
message = Message(
    type=MessageType.SMS,
    recipients=[MessageRecipient(id="user1", contact="13800138001")],
    content=MessageContent(
        body="您的验证码是123456",
        template_id="123456",
        template_data={"code": "123456"}
    )
)
```

#### 发送钉钉消息

```python
message = Message(
    type=MessageType.DINGTALK,
    recipients=[MessageRecipient(id="group1", contact="dingtalk_group")],
    content=MessageContent(
        title="告警通知",
        body="服务器异常，请及时处理",
        template_data={
            "at_mobiles": ["13800138001"],
            "is_at_all": False
        }
    )
)
```

#### 发送微信小程序消息

```python
message = Message(
    type=MessageType.WECHAT_MINIAPP,
    recipients=[MessageRecipient(id="user1", contact="wx_openid_123")],
    content=MessageContent(
        title="订单通知",
        body="您的订单已确认",
        template_id="template_id_123",
        template_data={
            "time51": {"value": "2025-01-15 18:00"},
            "thing5": {"value": "自助餐午餐"},
            "number61": {"value": 10}
        }
    )
)
```

#### 发送系统消息

```python
message = Message(
    type=MessageType.SYSTEM,
    recipients=[MessageRecipient(id="user1", contact="user1")],
    content=MessageContent(
        title="系统维护通知",
        body="系统将于今晚进行维护"
    )
)
```

### 3. 批量发送

```python
from app.messager.core.message import MessageBatch

# 创建消息批次
batch = MessageBatch(
    name="通知批次",
    messages=[message1, message2, message3]
)

# 批量发送
results = await manager.send_batch_messages(batch)
```

### 4. 自定义配置

```python
from app.messager import MessageSenderFactory

# 自定义邮件配置
custom_config = {
    "smtp_server": "smtp.qq.com",
    "username": "<EMAIL>",
    "password": "your_password"
}

# 创建自定义发送器
email_sender = MessageSenderFactory.create_email_sender(custom_config)
```

## 配置说明

### 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# 邮件配置
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_password
EMAIL_USE_TLS=true
EMAIL_SENDER_NAME=System

# 短信配置（腾讯云）
SMS_SECRET_ID=your_secret_id
SMS_SECRET_KEY=your_secret_key
SMS_SMSSDK_APPID=your_app_id
SMS_SIGN_NAME=your_sign_name
SMS_TEMPLATE_ID=your_template_id

# 钉钉配置
DINGTALK_WEBHOOK_URL=https://oapi.dingtalk.com/robot/send?access_token=xxx
DINGTALK_SECRET=your_secret
DINGTALK_AT_MOBILES=13800138001,13800138002
DINGTALK_IS_AT_ALL=false

# 微信小程序配置
WECHAT_TEMPLATE_ID=your_template_id
WECHAT_MINIAPP_APPID=your_miniapp_appid
WECHAT_MINIAPP_PAGEPATH=pages/notify/notify

# 系统消息配置
SYSTEM_MESSAGE_USE_DATABASE=true
```

## 扩展新的发送渠道

要添加新的消息发送渠道，只需要：

1. 继承 `BaseMessageSender` 类
2. 实现必要的抽象方法
3. 在工厂类中注册新的发送器

```python
from app.messager.core.base import BaseMessageSender
from app.messager.core.message import MessageType

class CustomSender(BaseMessageSender):
    @property
    def supported_message_type(self) -> MessageType:
        return MessageType.CUSTOM  # 需要先在MessageType中添加
    
    def _initialize(self) -> None:
        # 初始化逻辑
        pass
    
    def _validate_message_specific(self, message: Message) -> bool:
        # 验证逻辑
        return True
    
    async def send_single(self, message: Message, recipient_index: int = 0) -> SendResult:
        # 发送逻辑
        pass
```

## 与现有代码的兼容

现有的 `app/messager/miniapp/` 目录下的代码保持不变，新的统一抽象与现有代码完全兼容。你可以：

1. 继续使用现有的miniapp发送功能
2. 逐步迁移到新的统一接口
3. 两者并存使用

## 注意事项

1. 确保相关的依赖包已安装（如 `tencentcloud-sdk-python` 用于短信）
2. 配置文件中的敏感信息要妥善保管
3. 在生产环境中建议使用配置中心管理配置
4. 系统消息需要数据库支持，确保相关表已创建
