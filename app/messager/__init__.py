#!/usr/bin/env python3
"""
统一消息发送模块

提供统一的消息发送抽象，支持多种发送渠道：
- 邮件发送
- 短信发送
- 钉钉消息
- 微信小程序消息
- 系统消息
"""

from .core.manager import MessageManager
from .core.message import Message, MessageType, MessagePriority, MessageRecipient, MessageContent, MessageBatch
from .core.base import BaseMessageSender, SendResult, SendStatus, BatchSendResult

# 导入具体的发送器实现
from .senders.email import EmailSender
from .senders.sms import SMSSender
from .senders.dingtalk import DingTalkSender
from .senders.wechat_miniapp import WechatMiniAppSender
from .senders.system import SystemMessageSender

# 导入工厂类和便捷函数
from .factory import MessageSenderFactory, get_global_message_manager, reload_global_message_manager

__all__ = [
    'MessageManager',
    'Message',
    'MessageType',
    'MessagePriority',
    'MessageRecipient',
    'MessageContent',
    'MessageBatch',
    'BaseMessageSender',
    'SendResult',
    'SendStatus',
    'BatchSendResult',
    'EmailSender',
    'SMSSender',
    'DingTalkSender',
    'WechatMiniAppSender',
    'SystemMessageSender',
    'MessageSenderFactory',
    'get_global_message_manager',
    'reload_global_message_manager'
]