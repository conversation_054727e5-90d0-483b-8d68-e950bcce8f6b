#!/usr/bin/env python3
"""
消息发送配置管理
"""
import os
from typing import Dict, Optional

from pydantic import BaseModel, Field

from app.core.config import settings


class EmailConfig(BaseModel):
    """邮件配置"""
    smtp_server: str = Field(default="smtp.gmail.com", description="SMTP服务器")
    smtp_port: int = Field(default=587, description="SMTP端口")
    username: str = Field(default="", description="邮箱用户名")
    password: str = Field(default="", description="邮箱密码")
    use_tls: bool = Field(default=True, description="是否使用TLS")
    sender_name: str = Field(default="System", description="发送者名称")
    sender_email: str = Field(default="", description="发送者邮箱")


class SMSConfig(BaseModel):
    """短信配置"""
    secret_id: str = Field(default="", description="腾讯云SecretId")
    secret_key: str = Field(default="", description="腾讯云SecretKey")
    sms_sdk_app_id: str = Field(default="", description="短信应用ID")
    sign_name: str = Field(default="", description="短信签名")
    template_id: str = Field(default="", description="短信模板ID")
    region: str = Field(default="ap-guangzhou", description="地域")


class DingTalkConfig(BaseModel):
    """钉钉配置"""
    webhook_url: str = Field(default="", description="钉钉机器人Webhook URL")
    secret: str = Field(default="", description="钉钉机器人密钥")
    at_mobiles: list = Field(default_factory=list, description="@的手机号列表")
    at_user_ids: list = Field(default_factory=list, description="@的用户ID列表")
    is_at_all: bool = Field(default=False, description="是否@所有人")


class WechatMiniAppConfig(BaseModel):
    """微信小程序配置"""
    template_id: str = Field(default="", description="模板消息ID")
    miniprogram: Dict[str, str] = Field(
        default_factory=lambda: {
            "appid": "",
            "pagepath": ""
        },
        description="小程序配置"
    )
    default_url: Optional[str] = Field(default=None, description="默认跳转URL")


class SystemMessageConfig(BaseModel):
    """系统消息配置"""
    use_database: bool = Field(default=True, description="是否使用数据库存储")
    default_message_type: str = Field(default="system", description="默认消息类型")


class MessageConfig(BaseModel):
    """消息发送总配置"""
    email: EmailConfig = Field(default_factory=EmailConfig)
    sms: SMSConfig = Field(default_factory=SMSConfig)
    dingtalk: DingTalkConfig = Field(default_factory=DingTalkConfig)
    wechat_miniapp: WechatMiniAppConfig = Field(default_factory=WechatMiniAppConfig)
    system: SystemMessageConfig = Field(default_factory=SystemMessageConfig)


def get_message_config() -> MessageConfig:
    """
    获取消息配置，从环境变量和配置文件中读取

    Returns:
        MessageConfig: 消息配置对象
    """
    # 尝试从全局 settings 获取配置
    try:  # 邮件配置
        email_config = EmailConfig(
            smtp_server=settings.EMAIL_SMTP_SERVER,
            smtp_port=settings.EMAIL_SMTP_PORT,
            username=settings.EMAIL_USERNAME,
            password=settings.EMAIL_PASSWORD,
            use_tls=settings.EMAIL_USE_TLS,
            sender_name=settings.EMAIL_SENDER_NAME,
            sender_email=settings.EMAIL_SENDER_EMAIL
        )
    except Exception:
        # 如果无法获取 settings，回退到环境变量
        email_config = EmailConfig(
            smtp_server=os.getenv('EMAIL_SMTP_SERVER', 'smtp.163.com'),
            smtp_port=int(os.getenv('EMAIL_SMTP_PORT', '587')),
            username=os.getenv('EMAIL_USERNAME', ''),
            password=os.getenv('EMAIL_PASSWORD', ''),
            use_tls=os.getenv('EMAIL_USE_TLS', 'true').lower() == 'true',
            sender_name=os.getenv('EMAIL_SENDER_NAME', 'System'),
            sender_email=os.getenv('EMAIL_SENDER_EMAIL', '')
        )

    # 短信配置
    try:
        sms_config = SMSConfig(
            secret_id=settings.SMS_SECRET_ID,
            secret_key=settings.SMS_SECRET_KEY,
            sms_sdk_app_id=settings.SMS_SMSSDK_APPID,
            sign_name=settings.SMS_SIGN_NAME,
            template_id=settings.SMS_TEMPLATE_ID,
            region=os.getenv('SMS_REGION', 'ap-guangzhou')
        )
    except Exception:
        sms_config = SMSConfig(
            secret_id=os.getenv('SMS_SECRET_ID', ''),
            secret_key=os.getenv('SMS_SECRET_KEY', ''),
            sms_sdk_app_id=os.getenv('SMS_SMSSDK_APPID', ''),
            sign_name=os.getenv('SMS_SIGN_NAME', ''),
            template_id=os.getenv('SMS_TEMPLATE_ID', ''),
            region=os.getenv('SMS_REGION', 'ap-guangzhou')
        )

    # 钉钉配置
    try:
        dingtalk_config = DingTalkConfig(
            webhook_url=getattr(settings, 'DINGTALK_WEBHOOK_URL', ''),
            secret=getattr(settings, 'DINGTALK_SECRET', ''),
            at_mobiles=getattr(settings, 'DINGTALK_AT_MOBILES', '').split(',') if getattr(settings, 'DINGTALK_AT_MOBILES', '') else [],
            at_user_ids=getattr(settings, 'DINGTALK_AT_USER_IDS', '').split(',') if getattr(settings, 'DINGTALK_AT_USER_IDS', '') else [],
            is_at_all=getattr(settings, 'DINGTALK_IS_AT_ALL', False)
        )
    except Exception:
        dingtalk_config = DingTalkConfig(
            webhook_url=os.getenv('DINGTALK_WEBHOOK_URL', ''),
            secret=os.getenv('DINGTALK_SECRET', ''),
            at_mobiles=os.getenv('DINGTALK_AT_MOBILES', '').split(',') if os.getenv('DINGTALK_AT_MOBILES') else [],
            at_user_ids=os.getenv('DINGTALK_AT_USER_IDS', '').split(',') if os.getenv('DINGTALK_AT_USER_IDS') else [],
            is_at_all=os.getenv('DINGTALK_IS_AT_ALL', 'false').lower() == 'true'
        )

    # 微信小程序配置
    try:
        wechat_miniapp_config = WechatMiniAppConfig(
            template_id=getattr(settings, 'WECHAT_TEMPLATE_ID', ''),
            miniprogram={
                "appid": getattr(settings, 'WECHAT_APPID', ''),
                "pagepath": getattr(settings, 'WECHAT_MINIAPP_PAGEPATH', '')
            },
            default_url=getattr(settings, 'WECHAT_DEFAULT_URL', None)
        )
    except Exception:
        wechat_miniapp_config = WechatMiniAppConfig(
            template_id=os.getenv('WECHAT_TEMPLATE_ID', ''),
            miniprogram={
                "appid": os.getenv('WECHAT_MINIAPP_APPID', ''),
                "pagepath": os.getenv('WECHAT_MINIAPP_PAGEPATH', '')
            },
            default_url=os.getenv('WECHAT_DEFAULT_URL')
        )

    # 系统消息配置
    try:
        system_config = SystemMessageConfig(
            use_database=getattr(settings, 'SYSTEM_MESSAGE_USE_DATABASE', True),
            default_message_type=getattr(settings, 'SYSTEM_MESSAGE_DEFAULT_TYPE', 'system')
        )
    except Exception:
        system_config = SystemMessageConfig(
            use_database=os.getenv('SYSTEM_MESSAGE_USE_DATABASE', 'true').lower() == 'true',
            default_message_type=os.getenv('SYSTEM_MESSAGE_DEFAULT_TYPE', 'system')
        )

    return MessageConfig(
        email=email_config,
        sms=sms_config,
        dingtalk=dingtalk_config,
        wechat_miniapp=wechat_miniapp_config,
        system=system_config
    )


# 全局配置实例
_message_config: Optional[MessageConfig] = None


def get_global_message_config() -> MessageConfig:
    """获取全局消息配置实例"""
    global _message_config
    if _message_config is None:
        _message_config = get_message_config()
    return _message_config


def reload_message_config() -> MessageConfig:
    """重新加载消息配置"""
    global _message_config
    _message_config = get_message_config()
    return _message_config
