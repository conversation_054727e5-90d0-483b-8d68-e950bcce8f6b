#!/usr/bin/env python3
"""
消息发送器基础抽象类
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from datetime import datetime
from enum import Enum
from pydantic import BaseModel

from .message import Message, MessageType


class SendStatus(str, Enum):
    """发送状态枚举"""
    SUCCESS = "success"
    FAILED = "failed"
    PENDING = "pending"
    RETRY = "retry"
    CANCELLED = "cancelled"


class SendResult(BaseModel):
    """发送结果"""
    status: SendStatus
    message_id: Optional[str] = None
    recipient_id: str
    recipient_contact: str
    error_message: Optional[str] = None
    error_code: Optional[str] = None
    sent_at: Optional[datetime] = None
    extra_data: Optional[Dict[str, Any]] = None

    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class BatchSendResult(BaseModel):
    """批量发送结果"""
    total_count: int
    success_count: int
    failed_count: int
    pending_count: int
    results: List[SendResult]
    started_at: datetime
    completed_at: Optional[datetime] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class BaseMessageSender(ABC):
    """消息发送器基础抽象类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化发送器
        
        Args:
            config: 发送器配置
        """
        self.config = config or {}
        self._initialize()
    
    @abstractmethod
    def _initialize(self) -> None:
        """初始化发送器，子类实现具体的初始化逻辑"""
        pass
    
    @property
    @abstractmethod
    def supported_message_type(self) -> MessageType:
        """返回支持的消息类型"""
        pass
    
    @abstractmethod
    async def send_single(self, message: Message, recipient_index: int = 0) -> SendResult:
        """
        发送单条消息给单个接收者
        
        Args:
            message: 消息对象
            recipient_index: 接收者索引
            
        Returns:
            SendResult: 发送结果
        """
        pass
    
    async def send_batch(self, message: Message) -> BatchSendResult:
        """
        批量发送消息给多个接收者
        
        Args:
            message: 消息对象
            
        Returns:
            BatchSendResult: 批量发送结果
        """
        started_at = datetime.now()
        results = []
        
        for i, recipient in enumerate(message.recipients):
            try:
                result = await self.send_single(message, i)
                results.append(result)
            except Exception as e:
                # 发送异常时创建失败结果
                result = SendResult(
                    status=SendStatus.FAILED,
                    recipient_id=recipient.id,
                    recipient_contact=recipient.contact,
                    error_message=str(e),
                    sent_at=datetime.now()
                )
                results.append(result)
        
        # 统计结果
        success_count = sum(1 for r in results if r.status == SendStatus.SUCCESS)
        failed_count = sum(1 for r in results if r.status == SendStatus.FAILED)
        pending_count = sum(1 for r in results if r.status == SendStatus.PENDING)
        
        return BatchSendResult(
            total_count=len(results),
            success_count=success_count,
            failed_count=failed_count,
            pending_count=pending_count,
            results=results,
            started_at=started_at,
            completed_at=datetime.now()
        )
    
    def validate_message(self, message: Message) -> bool:
        """
        验证消息是否符合当前发送器的要求
        
        Args:
            message: 消息对象
            
        Returns:
            bool: 是否有效
        """
        if message.type != self.supported_message_type:
            return False
        
        if not message.recipients:
            return False
        
        if not message.content.body:
            return False
        
        return self._validate_message_specific(message)
    
    @abstractmethod
    def _validate_message_specific(self, message: Message) -> bool:
        """
        子类实现特定的消息验证逻辑
        
        Args:
            message: 消息对象
            
        Returns:
            bool: 是否有效
        """
        pass
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """
        获取配置项
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        return self.config.get(key, default)
    
    def set_config(self, key: str, value: Any) -> None:
        """
        设置配置项
        
        Args:
            key: 配置键
            value: 配置值
        """
        self.config[key] = value
