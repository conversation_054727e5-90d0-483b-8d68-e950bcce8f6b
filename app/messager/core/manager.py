#!/usr/bin/env python3
"""
消息发送管理器
"""

from typing import Dict, List, Optional, Type
from datetime import datetime
import asyncio
import logging

from .base import BaseMessageSender, BatchSendResult, SendResult, SendStatus
from .message import Message, MessageType, MessageBatch


logger = logging.getLogger(__name__)


class MessageManager:
    """消息发送管理器"""
    
    def __init__(self):
        """初始化消息管理器"""
        self._senders: Dict[MessageType, BaseMessageSender] = {}
        self._default_configs: Dict[MessageType, Dict] = {}
    
    def register_sender(
        self, 
        message_type: MessageType, 
        sender: BaseMessageSender,
        set_as_default: bool = True
    ) -> None:
        """
        注册消息发送器
        
        Args:
            message_type: 消息类型
            sender: 发送器实例
            set_as_default: 是否设为默认发送器
        """
        if set_as_default or message_type not in self._senders:
            self._senders[message_type] = sender
            logger.info(f"注册消息发送器: {message_type.value} -> {sender.__class__.__name__}")
    
    def register_sender_class(
        self,
        message_type: MessageType,
        sender_class: Type[BaseMessageSender],
        config: Optional[Dict] = None,
        set_as_default: bool = True
    ) -> None:
        """
        注册消息发送器类
        
        Args:
            message_type: 消息类型
            sender_class: 发送器类
            config: 发送器配置
            set_as_default: 是否设为默认发送器
        """
        sender = sender_class(config)
        self.register_sender(message_type, sender, set_as_default)
    
    def get_sender(self, message_type: MessageType) -> Optional[BaseMessageSender]:
        """
        获取消息发送器
        
        Args:
            message_type: 消息类型
            
        Returns:
            发送器实例或None
        """
        return self._senders.get(message_type)
    
    def list_senders(self) -> Dict[MessageType, str]:
        """
        列出所有已注册的发送器
        
        Returns:
            消息类型到发送器类名的映射
        """
        return {
            msg_type: sender.__class__.__name__ 
            for msg_type, sender in self._senders.items()
        }
    
    async def send_message(self, message: Message) -> BatchSendResult:
        """
        发送消息

        Args:
            message: 消息对象

        Returns:
            批量发送结果
        """
        # 检查是否有接收者
        if not message.recipients:
            return BatchSendResult(
                total_count=0,
                success_count=0,
                failed_count=1,
                pending_count=0,
                results=[
                    SendResult(
                        status=SendStatus.FAILED,
                        recipient_id="",
                        recipient_contact="",
                        error_message="消息没有接收者",
                        error_code="NO_RECIPIENTS",
                        sent_at=datetime.now()
                    )
                ],
                started_at=datetime.now(),
                completed_at=datetime.now()
            )

        sender = self.get_sender(message.type)
        if not sender:
            # 创建失败结果
            results = [
                SendResult(
                    status=SendStatus.FAILED,
                    recipient_id=recipient.id,
                    recipient_contact=recipient.contact,
                    error_message=f"未找到消息类型 {message.type.value} 的发送器",
                    error_code="SENDER_NOT_FOUND",
                    sent_at=datetime.now()
                )
                for recipient in message.recipients
            ]

            return BatchSendResult(
                total_count=len(results),
                success_count=0,
                failed_count=len(results),
                pending_count=0,
                results=results,
                started_at=datetime.now(),
                completed_at=datetime.now()
            )
        
        # 验证消息
        if not sender.validate_message(message):
            results = [
                SendResult(
                    status=SendStatus.FAILED,
                    recipient_id=recipient.id,
                    recipient_contact=recipient.contact,
                    error_message="消息验证失败",
                    error_code="MESSAGE_VALIDATION_FAILED",
                    sent_at=datetime.now()
                )
                for recipient in message.recipients
            ]
            
            return BatchSendResult(
                total_count=len(results),
                success_count=0,
                failed_count=len(results),
                pending_count=0,
                results=results,
                started_at=datetime.now(),
                completed_at=datetime.now()
            )
        
        # 发送消息
        try:
            if message.send_immediately:
                return await sender.send_batch(message)
            else:
                # TODO: 实现定时发送逻辑
                logger.warning("定时发送功能尚未实现，将立即发送")
                return await sender.send_batch(message)
        except Exception as e:
            logger.error(f"发送消息时发生异常: {str(e)}")
            results = [
                SendResult(
                    status=SendStatus.FAILED,
                    recipient_id=recipient.id,
                    recipient_contact=recipient.contact,
                    error_message=f"发送异常: {str(e)}",
                    error_code="SEND_EXCEPTION",
                    sent_at=datetime.now()
                )
                for recipient in message.recipients
            ]
            
            return BatchSendResult(
                total_count=len(results),
                success_count=0,
                failed_count=len(results),
                pending_count=0,
                results=results,
                started_at=datetime.now(),
                completed_at=datetime.now()
            )
    
    async def send_batch_messages(self, batch: MessageBatch) -> List[BatchSendResult]:
        """
        批量发送多条消息
        
        Args:
            batch: 消息批次
            
        Returns:
            批量发送结果列表
        """
        results = []
        
        # 并发发送所有消息
        tasks = [self.send_message(message) for message in batch.messages]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # 创建异常结果
                message = batch.messages[i]
                error_results = [
                    SendResult(
                        status=SendStatus.FAILED,
                        recipient_id=recipient.id,
                        recipient_contact=recipient.contact,
                        error_message=f"批量发送异常: {str(result)}",
                        error_code="BATCH_SEND_EXCEPTION",
                        sent_at=datetime.now()
                    )
                    for recipient in message.recipients
                ]
                
                batch_result = BatchSendResult(
                    total_count=len(error_results),
                    success_count=0,
                    failed_count=len(error_results),
                    pending_count=0,
                    results=error_results,
                    started_at=datetime.now(),
                    completed_at=datetime.now()
                )
                processed_results.append(batch_result)
            else:
                processed_results.append(result)
        
        return processed_results
