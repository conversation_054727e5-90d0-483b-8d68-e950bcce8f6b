#!/usr/bin/env python3
"""
消息数据结构定义
"""

from enum import Enum
from typing import Dict, Any, Optional, List
from datetime import datetime
from pydantic import BaseModel, Field


class MessageType(str, Enum):
    """消息类型枚举"""
    EMAIL = "email"
    SMS = "sms"
    DINGTALK = "dingtalk"
    WECHAT_MINIAPP = "wechat_miniapp"
    SYSTEM = "system"


class MessagePriority(str, Enum):
    """消息优先级枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class MessageRecipient(BaseModel):
    """消息接收者"""
    id: str = Field(..., description="接收者ID")
    name: Optional[str] = Field(None, description="接收者姓名")
    contact: str = Field(..., description="联系方式（邮箱/手机号/openid等）")
    extra_data: Optional[Dict[str, Any]] = Field(default_factory=dict, description="额外数据")


class MessageContent(BaseModel):
    """消息内容"""
    title: Optional[str] = Field(None, description="消息标题")
    body: str = Field(..., description="消息正文")
    template_id: Optional[str] = Field(None, description="模板ID")
    template_data: Optional[Dict[str, Any]] = Field(default_factory=dict, description="模板数据")
    attachments: Optional[List[str]] = Field(default_factory=list, description="附件列表")
    extra_data: Optional[Dict[str, Any]] = Field(default_factory=dict, description="额外数据")


class Message(BaseModel):
    """统一消息数据结构"""
    id: Optional[str] = Field(None, description="消息ID")
    type: MessageType = Field(..., description="消息类型")
    priority: MessagePriority = Field(default=MessagePriority.NORMAL, description="消息优先级")
    
    # 发送者信息
    sender_id: Optional[str] = Field(None, description="发送者ID")
    sender_name: Optional[str] = Field(None, description="发送者姓名")
    
    # 接收者信息
    recipients: List[MessageRecipient] = Field(..., description="接收者列表")
    
    # 消息内容
    content: MessageContent = Field(..., description="消息内容")
    
    # 发送配置
    send_immediately: bool = Field(default=True, description="是否立即发送")
    scheduled_time: Optional[datetime] = Field(None, description="定时发送时间")
    retry_count: int = Field(default=3, description="重试次数")
    timeout: int = Field(default=30, description="超时时间（秒）")
    
    # 元数据
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    extra_data: Optional[Dict[str, Any]] = Field(default_factory=dict, description="额外数据")

    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class MessageBatch(BaseModel):
    """批量消息"""
    id: Optional[str] = Field(None, description="批次ID")
    name: str = Field(..., description="批次名称")
    messages: List[Message] = Field(..., description="消息列表")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
