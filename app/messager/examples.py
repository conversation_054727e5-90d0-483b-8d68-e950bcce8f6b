#!/usr/bin/env python3
"""
消息发送使用示例
"""

import asyncio
import os
import sys
from datetime import datetime

from app.messager.core.message import MessageBatch

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.messager.core.message import Message, MessageType, MessagePriority, MessageRecipient, MessageContent
from app.messager.factory import get_global_message_manager, MessageSenderFactory


async def example_send_email():
    """发送邮件示例"""
    print("=== 发送邮件示例 ===")

    # 创建消息
    message = Message(
        type=MessageType.EMAIL,
        priority=MessagePriority.NORMAL,
        recipients=[
            MessageRecipient(
                id="user_001",
                name="潘潘",
                contact="<EMAIL>"
            ),
            MessageRecipient(
                id="user_002",
                name="泗均",
                contact="<EMAIL>"
            )
        ],
        content=MessageContent(
            title="你好",
            body="来信已经收到，请按时到达",
            attachments=[]
        )
    )

    # 获取消息管理器并发送
    manager = get_global_message_manager()
    result = await manager.send_message(message)

    print(f"发送结果: 总数={result.total_count}, 成功={result.success_count}, 失败={result.failed_count}")
    for r in result.results:
        print(f"  {r.recipient_contact}: {r.status} - {r.error_message or '成功'}")


async def example_send_sms():
    """发送短信示例"""
    print("\n=== 发送短信示例 ===")

    message = Message(
        type=MessageType.SMS,
        priority=MessagePriority.HIGH,
        recipients=[
            MessageRecipient(
                id="user_001",
                name="张三",
                contact="13800138001"
            )
        ],
        content=MessageContent(
            body="您的验证码是123456，请在5分钟内使用。",
            template_id="123456",
            template_data={
                "code": "123456",
                "time": "5分钟"
            }
        )
    )

    manager = get_global_message_manager()
    result = await manager.send_message(message)

    print(f"发送结果: 总数={result.total_count}, 成功={result.success_count}, 失败={result.failed_count}")
    for r in result.results:
        print(f"  {r.recipient_contact}: {r.status} - {r.error_message or '成功'}")


async def example_send_dingtalk():
    """发送钉钉消息示例"""
    print("\n=== 发送钉钉消息示例 ===")

    message = Message(
        type=MessageType.DINGTALK,
        priority=MessagePriority.NORMAL,
        recipients=[
            MessageRecipient(
                id="dingtalk_group",
                name="开发群",
                contact="dingtalk_group"
            )
        ],
        content=MessageContent(
            title="点餐消息通知",
            body="商务餐预订通知:您有一张新的订单请及时处理！",
            template_data={
                "at_mobiles": [""],
                "is_at_all": False
            }
        )
    )

    manager = get_global_message_manager()
    result = await manager.send_message(message)

    print(f"发送结果: 总数={result.total_count}, 成功={result.success_count}, 失败={result.failed_count}")
    for r in result.results:
        print(f"  {r.recipient_contact}: {r.status} - {r.error_message or '成功'}")


async def example_send_wechat_miniapp():
    """发送微信小程序消息示例"""
    print("\n=== 发送微信小程序消息示例 ===")

    message = Message(
        type=MessageType.WECHAT_MINIAPP,
        priority=MessagePriority.NORMAL,
        recipients=[
            MessageRecipient(
                id="user_001",
                name="潘",
                contact="oOGCK6jT5FP45r07gFioSDUQS0hU"  # 微信openid
            ),
            MessageRecipient(
                id="user_002",
                name="泗均",
                contact="oOGCK6p-sJJvRpDBoL8dlhOg6-ks"  # 微信openid
            )
        ],
        content=MessageContent(
            title="订单通知",
            body="您的订单已确认",
            template_id="WYycb9FEm8qgpPHpo1XTYejQp_2H7Wilsx1IGrB4GDo",
            template_data={
                "time11": {"value": datetime.now().strftime("%Y-%m-%d %H:%M")},
                "thing12": {"value": "自助餐午餐"},
                "character_string14": {"value": 10}
            }
        )
    )

    manager = get_global_message_manager()
    result = await manager.send_message(message)

    print(f"发送结果: 总数={result.total_count}, 成功={result.success_count}, 失败={result.failed_count}")
    for r in result.results:
        print(f"  {r.recipient_contact}: {r.status} - {r.error_message or '成功'}")


async def example_send_system_message():
    """发送系统消息示例"""
    print("\n=== 发送系统消息示例 ===")

    message = Message(
        type=MessageType.SYSTEM,
        priority=MessagePriority.NORMAL,
        recipients=[
            MessageRecipient(
                id="user_001",
                name="张三",
                contact="user_001"
            ),
            MessageRecipient(
                id="user_002",
                name="李四",
                contact="user_002"
            )
        ],
        content=MessageContent(
            title="系统维护通知",
            body="系统将于今晚22:00-24:00进行维护，期间可能无法正常使用，请提前做好准备。",
            extra_data={
                "maintenance_start": "22:00",
                "maintenance_end": "24:00"
            }
        )
    )

    manager = get_global_message_manager()
    result = await manager.send_message(message)

    print(f"发送结果: 总数={result.total_count}, 成功={result.success_count}, 失败={result.failed_count}")
    for r in result.results:
        print(f"  {r.recipient_contact}: {r.status} - {r.error_message or '成功'}")


async def example_batch_send():
    """批量发送不同类型消息示例"""
    print("\n=== 批量发送不同类型消息示例 ===")

    # 创建多条不同类型的消息
    messages = [
        Message(
            type=MessageType.SYSTEM,
            recipients=[MessageRecipient(id="user_001", name="张三", contact="user_001")],
            content=MessageContent(title="系统通知", body="这是系统消息")
        ),
        Message(
            type=MessageType.EMAIL,
            recipients=[MessageRecipient(id="user_002", name="李四", contact="<EMAIL>")],
            content=MessageContent(title="邮件通知", body="这是邮件消息")
        )
    ]

    # 创建消息批次
    batch = MessageBatch(
        name="混合消息批次",
        messages=messages
    )

    manager = get_global_message_manager()
    results = await manager.send_batch_messages(batch)

    print(f"批次发送完成，共 {len(results)} 条消息")
    for i, result in enumerate(results):
        print(f"  消息 {i + 1}: 总数={result.total_count}, 成功={result.success_count}, 失败={result.failed_count}")


async def example_custom_sender():
    """使用自定义配置创建发送器示例"""
    print("\n=== 使用自定义配置创建发送器示例 ===")

    # # 自定义邮件配置
    # custom_email_config = {
    #     "smtp_server": "smtp.163.com",
    #     "smtp_port": 25,
    #     "username": "<EMAIL>",
    #     "password": "LEcu2iTzNT97hMAE",
    #     "sender_name": "乙禾素"
    # }
    #
    # # 创建自定义邮件发送器
    # email_sender = MessageSenderFactory.create_email_sender(custom_email_config)
    #
    # # 创建消息
    # message = Message(
    #     type=MessageType.EMAIL,
    #     recipients=[MessageRecipient(id="user_001", name="潘潘", contact="<EMAIL>")],
    #     content=MessageContent(title="明日订餐统计", body="自助餐共49人次")
    # )
    #
    # # 直接使用发送器发送
    # result = await email_sender.send_batch(message)

    # 自定义钉钉
    # custom_dingtalk_config = {
    #     "webhook_url": "https://oapi.dingtalk.com/robot/send?access_token=a6a9414ff79a1396a5890388c9aadf6411cc88343f70b4d9151002d585bc1506",
    #     "secret": "",
    #     "at_mobiles": [],
    #     "at_user_ids": [],
    #     "is_at_all": True
    # }

    # dingtalk_sender = MessageSenderFactory.create_dingtalk_sender(custom_dingtalk_config)
    # message = Message(
    #     type=MessageType.DINGTALK,
    #     priority=MessagePriority.NORMAL,
    #     recipients=[
    #         MessageRecipient(
    #             id="dingtalk_group",
    #             name="开发群",
    #             contact="dingtalk_group"
    #         )
    #     ],
    #     content=MessageContent(
    #         title="订餐消息通知",
    #         body="自助餐点餐统计：午餐共49人次",
    #         template_data={
    #             "at_mobiles": [""],
    #             "is_at_all": False
    #         }
    #     )
    # )
    # result = await dingtalk_sender.send_batch(message)

    # 自定义微信小程序
    custom_miniapp_config = {
        "template_id": "WYycb9FEm8qgpPHpo1XTYejQp_2H7Wilsx1IGrB4GDo	",
        "miniprogram": {
            "appid": "wxc58a034f610866c5",
            "pagepath": "pages/admin/statistic/statistic"
        }
    }

    wechat_miniapp_sender = MessageSenderFactory.create_wechat_miniapp_sender(custom_miniapp_config)
    message = Message(
        type=MessageType.WECHAT_MINIAPP,
        priority=MessagePriority.NORMAL,
        recipients=[
            MessageRecipient(
                id="user_001",
                name="潘",
                contact="oOGCK6jT5FP45r07gFioSDUQS0hU"  # 微信openid
            ),
            MessageRecipient(
                id="user_002",
                name="泗均",
                contact="oOGCK6p-sJJvRpDBoL8dlhOg6-ks"  # 微信openid
            )
        ],
        content=MessageContent(
            title="订单通知",
            body="您的订单已确认",
            template_id="WYycb9FEm8qgpPHpo1XTYejQp_2H7Wilsx1IGrB4GDo",
            template_data={
                "time11": {"value": datetime.now().strftime("%Y-%m-%d")},
                "thing12": {"value": "自助餐午餐"},
                "character_string14": {"value": 49}
            }
        )
    )
    result = await wechat_miniapp_sender.send_batch(message)

    print(f"自定义发送器结果: 总数={result.total_count}, 成功={result.success_count}, 失败={result.failed_count}")


async def main():
    """运行所有示例"""
    print("消息发送器使用示例")
    print("=" * 50)

    # 注意：以下示例需要相应的配置才能正常工作
    # 在实际使用时，请确保环境变量或配置文件中有正确的配置

    # await example_send_system_message()  # 系统消息不需要外部配置

    # 其他示例需要相应的配置，这里注释掉避免报错
    # await example_send_email()
    # await example_send_sms()
    # await example_send_dingtalk()
    # await example_send_wechat_miniapp()
    # await example_batch_send()
    # await example_custom_sender()

    print("\n示例运行完成！")


if __name__ == "__main__":
    asyncio.run(main())
