#!/usr/bin/env python3
"""
消息发送器工厂类
"""

from typing import Dict, Any, Optional
import logging

from .core.manager import MessageManager
from .core.message import MessageType
from .senders.email import EmailSender
from .senders.sms import SMSSender
from .senders.dingtalk import DingTalkSender
from .senders.wechat_miniapp import WechatMiniAppSender
from .senders.system import SystemMessageSender
from .config.settings import get_global_message_config


logger = logging.getLogger(__name__)


class MessageSenderFactory:
    """消息发送器工厂类"""
    
    @staticmethod
    def create_manager(config: Optional[Dict[str, Any]] = None) -> MessageManager:
        """
        创建消息管理器并注册所有发送器
        
        Args:
            config: 自定义配置，如果为None则使用全局配置
            
        Returns:
            MessageManager: 配置好的消息管理器
        """
        if config is None:
            message_config = get_global_message_config()
        else:
            # 如果提供了自定义配置，需要转换为配置对象
            from .config.settings import MessageConfig
            message_config = MessageConfig(**config)
        
        manager = MessageManager()
        
        # 注册邮件发送器
        try:
            email_sender = EmailSender(message_config.email.dict())
            manager.register_sender(MessageType.EMAIL, email_sender)
            logger.info("邮件发送器注册成功")
        except Exception as e:
            logger.warning(f"邮件发送器注册失败: {str(e)}")
        
        # 注册短信发送器
        try:
            sms_sender = SMSSender(message_config.sms.dict())
            manager.register_sender(MessageType.SMS, sms_sender)
            logger.info("短信发送器注册成功")
        except Exception as e:
            logger.warning(f"短信发送器注册失败: {str(e)}")
        
        # 注册钉钉发送器
        try:
            dingtalk_sender = DingTalkSender(message_config.dingtalk.dict())
            manager.register_sender(MessageType.DINGTALK, dingtalk_sender)
            logger.info("钉钉发送器注册成功")
        except Exception as e:
            logger.warning(f"钉钉发送器注册失败: {str(e)}")
        
        # 注册微信小程序发送器
        try:
            wechat_sender = WechatMiniAppSender(message_config.wechat_miniapp.dict())
            manager.register_sender(MessageType.WECHAT_MINIAPP, wechat_sender)
            logger.info("微信小程序发送器注册成功")
        except Exception as e:
            logger.warning(f"微信小程序发送器注册失败: {str(e)}")
        
        # 注册系统消息发送器
        try:
            system_sender = SystemMessageSender(message_config.system.dict())
            manager.register_sender(MessageType.SYSTEM, system_sender)
            logger.info("系统消息发送器注册成功")
        except Exception as e:
            logger.warning(f"系统消息发送器注册失败: {str(e)}")
        
        logger.info(f"消息管理器创建完成，已注册发送器: {list(manager.list_senders().keys())}")
        return manager
    
    @staticmethod
    def create_email_sender(config: Optional[Dict[str, Any]] = None) -> EmailSender:
        """创建邮件发送器"""
        if config is None:
            config = get_global_message_config().email.dict()
        return EmailSender(config)
    
    @staticmethod
    def create_sms_sender(config: Optional[Dict[str, Any]] = None) -> SMSSender:
        """创建短信发送器"""
        if config is None:
            config = get_global_message_config().sms.dict()
        return SMSSender(config)
    
    @staticmethod
    def create_dingtalk_sender(config: Optional[Dict[str, Any]] = None) -> DingTalkSender:
        """创建钉钉发送器"""
        if config is None:
            config = get_global_message_config().dingtalk.dict()
        return DingTalkSender(config)
    
    @staticmethod
    def create_wechat_miniapp_sender(config: Optional[Dict[str, Any]] = None) -> WechatMiniAppSender:
        """创建微信小程序发送器"""
        if config is None:
            config = get_global_message_config().wechat_miniapp.dict()
        return WechatMiniAppSender(config)
    
    @staticmethod
    def create_system_sender(config: Optional[Dict[str, Any]] = None) -> SystemMessageSender:
        """创建系统消息发送器"""
        if config is None:
            config = get_global_message_config().system.dict()
        return SystemMessageSender(config)


# 全局消息管理器实例
_global_manager: Optional[MessageManager] = None


def get_global_message_manager() -> MessageManager:
    """获取全局消息管理器实例"""
    global _global_manager
    if _global_manager is None:
        _global_manager = MessageSenderFactory.create_manager()
    return _global_manager


def reload_global_message_manager() -> MessageManager:
    """重新加载全局消息管理器"""
    global _global_manager
    _global_manager = MessageSenderFactory.create_manager()
    return _global_manager
