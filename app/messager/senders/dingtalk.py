#!/usr/bin/env python3
"""
钉钉消息发送器实现
"""

import json
import hmac
import hashlib
import base64
import urllib.parse
from typing import Optional, Dict, Any
from datetime import datetime
import logging
import time
import requests

from ..core.base import BaseMessageSender, SendResult, SendStatus
from ..core.message import Message, MessageType


logger = logging.getLogger(__name__)


class DingTalkSender(BaseMessageSender):
    """钉钉消息发送器"""
    
    def _initialize(self) -> None:
        """初始化钉钉发送器"""
        # 钉钉机器人配置
        self.webhook_url = self.get_config('webhook_url', '')
        self.secret = self.get_config('secret', '')
        self.at_mobiles = self.get_config('at_mobiles', [])
        self.at_user_ids = self.get_config('at_user_ids', [])
        self.is_at_all = self.get_config('is_at_all', False)
        
        logger.info(f"钉钉发送器初始化完成: webhook配置{'已设置' if self.webhook_url else '未设置'}")
    
    @property
    def supported_message_type(self) -> MessageType:
        """返回支持的消息类型"""
        return MessageType.DINGTALK
    
    def _validate_message_specific(self, message: Message) -> bool:
        """验证钉钉消息的特定要求"""
        # 检查webhook URL
        if not self.webhook_url:
            logger.error("钉钉发送器缺少webhook_url配置")
            return False
        
        # 钉钉机器人消息不需要特定的接收者格式验证
        # 因为接收者通过@功能或群组来指定
        
        return True
    
    def _generate_sign(self, timestamp: int) -> str:
        """生成钉钉签名"""
        if not self.secret:
            return ""
        
        string_to_sign = f'{timestamp}\n{self.secret}'
        hmac_code = hmac.new(
            self.secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
        return sign
    
    def _build_webhook_url(self) -> str:
        """构建带签名的webhook URL"""
        if not self.secret:
            return self.webhook_url
        
        timestamp = int(round(time.time() * 1000))
        sign = self._generate_sign(timestamp)
        
        return f"{self.webhook_url}&timestamp={timestamp}&sign={sign}"
    
    def _build_message_payload(self, message: Message) -> Dict[str, Any]:
        """构建钉钉消息载荷"""
        # 基础消息结构
        payload = {
            "msgtype": "text",
            "text": {
                "content": message.content.body
            },
            "at": {
                "atMobiles": self.at_mobiles,
                "atUserIds": self.at_user_ids,
                "isAtAll": self.is_at_all
            }
        }
        
        # 如果有标题，使用markdown格式
        if message.content.title:
            payload["msgtype"] = "markdown"
            payload["markdown"] = {
                "title": message.content.title,
                "text": f"## {message.content.title}\n\n{message.content.body}"
            }
            del payload["text"]
        
        # 处理模板数据中的@信息
        if message.content.template_data:
            template_data = message.content.template_data
            
            # 从模板数据中提取@信息
            if "at_mobiles" in template_data:
                payload["at"]["atMobiles"].extend(template_data["at_mobiles"])
            
            if "at_user_ids" in template_data:
                payload["at"]["atUserIds"].extend(template_data["at_user_ids"])
            
            if "is_at_all" in template_data:
                payload["at"]["isAtAll"] = template_data["is_at_all"]
        
        return payload
    
    async def send_single(self, message: Message, recipient_index: int = 0) -> SendResult:
        """发送单条钉钉消息"""
        # 钉钉机器人消息是发送到群组的，不区分具体接收者
        # 这里使用第一个接收者作为标识
        if not message.recipients:
            return SendResult(
                status=SendStatus.FAILED,
                recipient_id="",
                recipient_contact="",
                error_message="没有接收者信息",
                error_code="NO_RECIPIENTS",
                sent_at=datetime.now()
            )
        
        recipient = message.recipients[0] if message.recipients else None
        recipient_id = recipient.id if recipient else "dingtalk_group"
        recipient_contact = recipient.contact if recipient else "钉钉群组"
        
        try:
            # 构建webhook URL
            webhook_url = self._build_webhook_url()
            
            # 构建消息载荷
            payload = self._build_message_payload(message)
            
            # 发送HTTP请求
            headers = {
                'Content-Type': 'application/json'
            }

            logger.info(f'payload:{payload}')

            response = requests.post(
                webhook_url,
                headers=headers,
                data=json.dumps(payload),
                timeout=30
            )
            
            # 解析响应
            if response.status_code == 200:
                result = response.json()
                
                if result.get('errcode') == 0:
                    logger.info(f"钉钉消息发送成功: {result}")
                    
                    return SendResult(
                        status=SendStatus.SUCCESS,
                        message_id=message.id,
                        recipient_id=recipient_id,
                        recipient_contact=recipient_contact,
                        sent_at=datetime.now(),
                        extra_data=result
                    )
                else:
                    logger.error(f"钉钉消息发送失败: {result}")
                    
                    return SendResult(
                        status=SendStatus.FAILED,
                        recipient_id=recipient_id,
                        recipient_contact=recipient_contact,
                        error_message=result.get('errmsg', '未知错误'),
                        error_code=str(result.get('errcode', 'UNKNOWN')),
                        sent_at=datetime.now()
                    )
            else:
                logger.error(f"钉钉消息发送HTTP错误: {response.status_code}, {response.text}")
                
                return SendResult(
                    status=SendStatus.FAILED,
                    recipient_id=recipient_id,
                    recipient_contact=recipient_contact,
                    error_message=f"HTTP错误: {response.status_code}",
                    error_code="HTTP_ERROR",
                    sent_at=datetime.now()
                )
                
        except requests.exceptions.Timeout:
            logger.error("钉钉消息发送超时")
            return SendResult(
                status=SendStatus.FAILED,
                recipient_id=recipient_id,
                recipient_contact=recipient_contact,
                error_message="请求超时",
                error_code="REQUEST_TIMEOUT",
                sent_at=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"钉钉消息发送异常: {str(e)}")
            return SendResult(
                status=SendStatus.FAILED,
                recipient_id=recipient_id,
                recipient_contact=recipient_contact,
                error_message=f"钉钉消息发送异常: {str(e)}",
                error_code="DINGTALK_SEND_ERROR",
                sent_at=datetime.now()
            )
