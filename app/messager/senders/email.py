#!/usr/bin/env python3
"""
邮件发送器实现
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import Optional
from datetime import datetime
import logging
import os

from ..core.base import BaseMessageSender, SendResult, SendStatus
from ..core.message import Message, MessageType


logger = logging.getLogger(__name__)


class EmailSender(BaseMessageSender):
    """邮件发送器"""
    
    def _initialize(self) -> None:
        """初始化邮件发送器"""
        # 默认配置
        self.smtp_server = self.get_config('smtp_server', '')
        self.smtp_port = self.get_config('smtp_port', 25)
        self.username = self.get_config('username', '')
        self.password = self.get_config('password', '')
        self.use_tls = self.get_config('use_tls', True)
        self.sender_name = self.get_config('sender_name', 'System')
        self.sender_email = self.get_config('sender_email', self.username)
        
        logger.info(f"邮件发送器初始化完成: {self.smtp_server}:{self.smtp_port}")
    
    @property
    def supported_message_type(self) -> MessageType:
        """返回支持的消息类型"""
        return MessageType.EMAIL
    
    def _validate_message_specific(self, message: Message) -> bool:
        """验证邮件消息的特定要求"""
        # 检查接收者邮箱格式
        for recipient in message.recipients:
            if '@' not in recipient.contact:
                logger.error(f"无效的邮箱地址: {recipient.contact}")
                return False
        
        # 检查必要的配置
        if not self.username or not self.password:
            logger.error("邮件发送器缺少用户名或密码配置")
            return False
        
        return True
    
    async def send_single(self, message: Message, recipient_index: int = 0) -> SendResult:
        """发送单封邮件"""
        if recipient_index >= len(message.recipients):
            return SendResult(
                status=SendStatus.FAILED,
                recipient_id="",
                recipient_contact="",
                error_message="接收者索引超出范围",
                error_code="RECIPIENT_INDEX_OUT_OF_RANGE",
                sent_at=datetime.now()
            )
        
        recipient = message.recipients[recipient_index]
        
        try:
            # 创建邮件消息
            msg = MIMEMultipart()
            msg['From'] = f"{self.sender_name} <{self.sender_email}>"
            msg['To'] = recipient.contact
            msg['Subject'] = message.content.title or "通知消息"
            
            # 添加邮件正文
            body = MIMEText(message.content.body, 'html' if '<' in message.content.body else 'plain', 'utf-8')
            msg.attach(body)
            
            # 添加附件
            if message.content.attachments:
                for attachment_path in message.content.attachments:
                    if os.path.exists(attachment_path):
                        with open(attachment_path, "rb") as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())
                        
                        encoders.encode_base64(part)
                        part.add_header(
                            'Content-Disposition',
                            f'attachment; filename= {os.path.basename(attachment_path)}'
                        )
                        msg.attach(part)
            
            # 发送邮件
            context = ssl.create_default_context()
            
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                if self.use_tls:
                    server.starttls(context=context)
                server.login(self.username, self.password)
                
                text = msg.as_string()
                server.sendmail(self.sender_email, recipient.contact, text)
            
            logger.info(f"邮件发送成功: {recipient.contact}")
            
            return SendResult(
                status=SendStatus.SUCCESS,
                message_id=message.id,
                recipient_id=recipient.id,
                recipient_contact=recipient.contact,
                sent_at=datetime.now()
            )
            
        except smtplib.SMTPAuthenticationError as e:
            logger.error(f"邮件认证失败: {str(e)}")
            return SendResult(
                status=SendStatus.FAILED,
                recipient_id=recipient.id,
                recipient_contact=recipient.contact,
                error_message="邮件认证失败",
                error_code="SMTP_AUTH_ERROR",
                sent_at=datetime.now()
            )
            
        except smtplib.SMTPRecipientsRefused as e:
            logger.error(f"收件人被拒绝: {str(e)}")
            return SendResult(
                status=SendStatus.FAILED,
                recipient_id=recipient.id,
                recipient_contact=recipient.contact,
                error_message="收件人被拒绝",
                error_code="SMTP_RECIPIENTS_REFUSED",
                sent_at=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"邮件发送异常: {str(e)}")
            return SendResult(
                status=SendStatus.FAILED,
                recipient_id=recipient.id,
                recipient_contact=recipient.contact,
                error_message=f"邮件发送异常: {str(e)}",
                error_code="EMAIL_SEND_ERROR",
                sent_at=datetime.now()
            )
