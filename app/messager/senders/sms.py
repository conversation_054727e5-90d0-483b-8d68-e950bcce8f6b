#!/usr/bin/env python3
"""
短信发送器实现
"""

import re
from typing import Optional
from datetime import datetime
import logging

from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.sms.v20210111 import sms_client, models

from ..core.base import BaseMessageSender, SendResult, SendStatus
from ..core.message import Message, MessageType


logger = logging.getLogger(__name__)


class SMSSender(BaseMessageSender):
    """短信发送器"""
    
    def _initialize(self) -> None:
        """初始化短信发送器"""
        # 腾讯云短信配置
        self.secret_id = self.get_config('secret_id', '')
        self.secret_key = self.get_config('secret_key', '')
        self.sms_sdk_app_id = self.get_config('sms_sdk_app_id', '')
        self.sign_name = self.get_config('sign_name', '')
        self.template_id = self.get_config('template_id', '')
        self.region = self.get_config('region', 'ap-guangzhou')
        
        logger.info(f"短信发送器初始化完成: SDK App ID = {self.sms_sdk_app_id}")
    
    @property
    def supported_message_type(self) -> MessageType:
        """返回支持的消息类型"""
        return MessageType.SMS
    
    def _validate_message_specific(self, message: Message) -> bool:
        """验证短信消息的特定要求"""
        # 检查手机号格式
        phone_pattern = re.compile(r'^(\+86)?1[3-9]\d{9}$')
        
        for recipient in message.recipients:
            phone = recipient.contact.replace('+86', '').replace(' ', '').replace('-', '')
            if not phone_pattern.match(phone):
                logger.error(f"无效的手机号码: {recipient.contact}")
                return False
        
        # 检查必要的配置
        if not all([self.secret_id, self.secret_key, self.sms_sdk_app_id, self.sign_name]):
            logger.error("短信发送器缺少必要的配置信息")
            return False
        
        # 检查短信内容长度
        if len(message.content.body) > 500:
            logger.error("短信内容过长，超过500字符限制")
            return False
        
        return True
    
    def _normalize_phone_number(self, phone: str) -> str:
        """标准化手机号码格式"""
        # 移除空格和连字符
        phone = phone.replace(' ', '').replace('-', '')
        
        # 如果没有国家代码，添加+86
        if not phone.startswith('+'):
            if phone.startswith('86'):
                phone = '+' + phone
            else:
                phone = '+86' + phone
        
        return phone
    
    async def send_single(self, message: Message, recipient_index: int = 0) -> SendResult:
        """发送单条短信"""
        if recipient_index >= len(message.recipients):
            return SendResult(
                status=SendStatus.FAILED,
                recipient_id="",
                recipient_contact="",
                error_message="接收者索引超出范围",
                error_code="RECIPIENT_INDEX_OUT_OF_RANGE",
                sent_at=datetime.now()
            )
        
        recipient = message.recipients[recipient_index]
        
        try:
            # 标准化手机号码
            phone_number = self._normalize_phone_number(recipient.contact)
            
            # 创建腾讯云认证对象
            cred = credential.Credential(self.secret_id, self.secret_key)
            
            # 实例化HTTP选项
            http_profile = HttpProfile()
            http_profile.endpoint = "sms.tencentcloudapi.com"
            
            # 实例化客户端选项
            client_profile = ClientProfile()
            client_profile.httpProfile = http_profile
            
            # 实例化SMS客户端
            client = sms_client.SmsClient(cred, self.region, client_profile)
            
            # 实例化请求对象
            req = models.SendSmsRequest()
            
            # 准备模板参数
            template_params = []
            if message.content.template_data:
                # 如果有模板数据，使用模板数据
                template_params = list(message.content.template_data.values())
            else:
                # 否则使用消息正文作为模板参数
                template_params = [message.content.body]
            
            # 设置请求参数
            params = {
                "PhoneNumberSet": [phone_number],
                "SmsSdkAppId": self.sms_sdk_app_id,
                "SignName": self.sign_name,
                "TemplateId": message.content.template_id or self.template_id,
                "TemplateParamSet": [str(param) for param in template_params],
            }
            req.from_json_string(str(params).replace("'", '"'))
            
            # 发送短信
            resp = client.SendSms(req)
            
            # 解析响应
            if resp.SendStatusSet and len(resp.SendStatusSet) > 0:
                send_status = resp.SendStatusSet[0]
                
                if send_status.Code == "Ok":
                    logger.info(f"短信发送成功: {phone_number}, SerialNo: {send_status.SerialNo}")
                    
                    return SendResult(
                        status=SendStatus.SUCCESS,
                        message_id=send_status.SerialNo,
                        recipient_id=recipient.id,
                        recipient_contact=recipient.contact,
                        sent_at=datetime.now(),
                        extra_data={
                            "serial_no": send_status.SerialNo,
                            "fee": send_status.Fee
                        }
                    )
                else:
                    logger.error(f"短信发送失败: {phone_number}, Code: {send_status.Code}, Message: {send_status.Message}")
                    
                    return SendResult(
                        status=SendStatus.FAILED,
                        recipient_id=recipient.id,
                        recipient_contact=recipient.contact,
                        error_message=send_status.Message,
                        error_code=send_status.Code,
                        sent_at=datetime.now()
                    )
            else:
                logger.error(f"短信发送响应异常: {phone_number}")
                return SendResult(
                    status=SendStatus.FAILED,
                    recipient_id=recipient.id,
                    recipient_contact=recipient.contact,
                    error_message="短信发送响应异常",
                    error_code="SMS_RESPONSE_ERROR",
                    sent_at=datetime.now()
                )
                
        except Exception as e:
            logger.error(f"短信发送异常: {str(e)}")
            return SendResult(
                status=SendStatus.FAILED,
                recipient_id=recipient.id,
                recipient_contact=recipient.contact,
                error_message=f"短信发送异常: {str(e)}",
                error_code="SMS_SEND_ERROR",
                sent_at=datetime.now()
            )
