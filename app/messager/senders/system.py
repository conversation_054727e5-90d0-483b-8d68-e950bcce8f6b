#!/usr/bin/env python3
"""
系统消息发送器实现
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
import logging
import json

from ..core.base import BaseMessageSender, SendResult, SendStatus
from ..core.message import Message, MessageType

# 导入数据库相关模块
try:
    from app.db.session import SessionLocal
    from sqlalchemy.orm import Session
    # 使用已存在的SystemMessage模型
    from app.models.message import SystemMessage, MessageStatus
except ImportError:
    logger = logging.getLogger(__name__)
    logger.warning("无法导入数据库模块，系统消息发送器将使用内存存储")
    SessionLocal = None
    Session = None
    SystemMessage = None
    MessageStatus = None


logger = logging.getLogger(__name__)


class SystemMessageSender(BaseMessageSender):
    """系统消息发送器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化系统消息发送器"""
        super().__init__(config)
        # 如果数据库不可用，使用内存存储
        self._memory_storage: List[Dict[str, Any]] = []
    
    def _initialize(self) -> None:
        """初始化系统消息发送器"""
        self.use_database = self.get_config('use_database', True) and SessionLocal is not None
        self.default_sender_type = self.get_config('default_sender_type', 'system')

        if self.use_database:
            logger.info("系统消息发送器初始化完成: 使用数据库存储")
        else:
            logger.info("系统消息发送器初始化完成: 使用内存存储")
    
    @property
    def supported_message_type(self) -> MessageType:
        """返回支持的消息类型"""
        return MessageType.SYSTEM
    
    def _validate_message_specific(self, message: Message) -> bool:
        """验证系统消息的特定要求"""
        # 检查接收者ID
        for recipient in message.recipients:
            if not recipient.id:
                logger.error(f"系统消息接收者缺少ID: {recipient}")
                return False
        
        return True
    
    def _save_to_database(self, message: Message, recipient_index: int) -> bool:
        """保存消息到数据库"""
        if not self.use_database or SystemMessage is None:
            return False

        try:
            db = SessionLocal()
            recipient = message.recipients[recipient_index]

            # 导入枚举类型
            from app.models.message import MessageType as ModelMessageType, MessagePriority as ModelMessagePriority

            # 映射优先级
            priority_mapping = {
                'low': ModelMessagePriority.LOW,
                'normal': ModelMessagePriority.NORMAL,
                'high': ModelMessagePriority.HIGH,
                'urgent': ModelMessagePriority.URGENT
            }

            priority_value = message.priority.value if hasattr(message.priority, 'value') else str(message.priority)
            model_priority = priority_mapping.get(priority_value, ModelMessagePriority.NORMAL)

            # 创建系统消息记录
            system_message = SystemMessage(
                message_id=message.id,
                recipient_id=recipient.id,
                recipient_name=recipient.name,
                title=message.content.title,
                content=message.content.body,
                message_type=ModelMessageType.SYSTEM,
                priority=model_priority,
                sender_id=message.sender_id,
                sender_name=message.sender_name,
                sender_type=self.default_sender_type,
                extra_data=message.content.extra_data if message.content.extra_data else None,
                sent_at=datetime.now()
            )

            db.add(system_message)
            db.commit()
            db.refresh(system_message)

            logger.info(f"系统消息已保存到数据库: ID={system_message.id}")
            return True

        except Exception as e:
            logger.warning(f"保存系统消息到数据库失败，将使用内存存储: {str(e)}")
            if 'db' in locals():
                db.rollback()
            # 数据库失败时，自动回退到内存存储
            self.use_database = False
            return self._save_to_memory(message, recipient_index)
        finally:
            if 'db' in locals():
                db.close()
    
    def _save_to_memory(self, message: Message, recipient_index: int) -> bool:
        """保存消息到内存"""
        try:
            recipient = message.recipients[recipient_index]
            priority_value = message.priority.value if hasattr(message.priority, 'value') else str(message.priority)

            message_record = {
                "id": len(self._memory_storage) + 1,
                "message_id": message.id,
                "recipient_id": recipient.id,
                "recipient_name": recipient.name,
                "title": message.content.title,
                "content": message.content.body,
                "message_type": "SYSTEM",
                "priority": priority_value,
                "is_read": False,
                "read_at": None,
                "created_at": datetime.now(),
                "extra_data": message.content.extra_data
            }
            
            self._memory_storage.append(message_record)
            
            logger.info(f"系统消息已保存到内存: ID={message_record['id']}")
            return True
            
        except Exception as e:
            logger.error(f"保存系统消息到内存失败: {str(e)}")
            return False
    
    async def send_single(self, message: Message, recipient_index: int = 0) -> SendResult:
        """发送单条系统消息"""
        if recipient_index >= len(message.recipients):
            return SendResult(
                status=SendStatus.FAILED,
                recipient_id="",
                recipient_contact="",
                error_message="接收者索引超出范围",
                error_code="RECIPIENT_INDEX_OUT_OF_RANGE",
                sent_at=datetime.now()
            )
        
        recipient = message.recipients[recipient_index]
        
        try:
            # 尝试保存到数据库
            if self.use_database:
                success = self._save_to_database(message, recipient_index)
            else:
                success = self._save_to_memory(message, recipient_index)
            
            if success:
                logger.info(f"系统消息发送成功: {recipient.id}")
                
                return SendResult(
                    status=SendStatus.SUCCESS,
                    message_id=message.id,
                    recipient_id=recipient.id,
                    recipient_contact=recipient.contact or recipient.id,
                    sent_at=datetime.now(),
                    extra_data={
                        "storage_type": "database" if self.use_database else "memory"
                    }
                )
            else:
                logger.error(f"系统消息保存失败: {recipient.id}")
                
                return SendResult(
                    status=SendStatus.FAILED,
                    recipient_id=recipient.id,
                    recipient_contact=recipient.contact or recipient.id,
                    error_message="系统消息保存失败",
                    error_code="SYSTEM_MESSAGE_SAVE_FAILED",
                    sent_at=datetime.now()
                )
                
        except Exception as e:
            logger.error(f"系统消息发送异常: {str(e)}")
            return SendResult(
                status=SendStatus.FAILED,
                recipient_id=recipient.id,
                recipient_contact=recipient.contact or recipient.id,
                error_message=f"系统消息发送异常: {str(e)}",
                error_code="SYSTEM_MESSAGE_SEND_ERROR",
                sent_at=datetime.now()
            )
    
    def get_messages_for_user(self, user_id: str, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """获取用户的系统消息"""
        if self.use_database and SystemMessage is not None:
            return self._get_messages_from_database(user_id, limit, offset)
        else:
            return self._get_messages_from_memory(user_id, limit, offset)
    
    def _get_messages_from_database(self, user_id: str, limit: int, offset: int) -> List[Dict[str, Any]]:
        """从数据库获取消息"""
        try:
            db = SessionLocal()
            messages = db.query(SystemMessage).filter(
                SystemMessage.recipient_id == user_id
            ).order_by(
                SystemMessage.created_at.desc()
            ).offset(offset).limit(limit).all()
            
            return [
                {
                    "id": msg.id,
                    "message_id": msg.message_id,
                    "title": msg.title,
                    "content": msg.content,
                    "priority": msg.priority.value if hasattr(msg.priority, 'value') else str(msg.priority),
                    "message_type": msg.message_type.value if hasattr(msg.message_type, 'value') else str(msg.message_type),
                    "status": msg.status.value if hasattr(msg.status, 'value') else str(msg.status),
                    "is_read": msg.is_read,
                    "read_at": msg.read_at,
                    "created_at": msg.created_at,
                    "sent_at": msg.sent_at,
                    "sender_id": msg.sender_id,
                    "sender_name": msg.sender_name,
                    "extra_data": msg.extra_data
                }
                for msg in messages
            ]
        except Exception as e:
            logger.error(f"从数据库获取系统消息失败: {str(e)}")
            return []
        finally:
            if 'db' in locals():
                db.close()
    
    def _get_messages_from_memory(self, user_id: str, limit: int, offset: int) -> List[Dict[str, Any]]:
        """从内存获取消息"""
        user_messages = [
            msg for msg in self._memory_storage 
            if msg["recipient_id"] == user_id
        ]
        
        # 按创建时间倒序排序
        user_messages.sort(key=lambda x: x["created_at"], reverse=True)
        
        # 分页
        return user_messages[offset:offset + limit]
