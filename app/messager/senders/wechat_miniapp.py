#!/usr/bin/env python3
"""
微信小程序消息发送器实现
"""

from typing import Optional, Dict, Any
from datetime import datetime
import logging

from ..core.base import BaseMessageSender, SendResult, SendStatus
from ..core.message import Message, MessageType

logger = logging.getLogger(__name__)


def _get_send_template_message():
    """延迟导入微信模板消息发送函数"""
    try:
        from app.callback.wechat_mp import send_template_message
        return send_template_message
    except ImportError:
        logger.warning("无法导入微信模板消息发送函数，微信小程序消息发送器将无法正常工作")
        return None


class WechatMiniAppSender(BaseMessageSender):
    """微信小程序消息发送器"""

    def _initialize(self) -> None:
        """初始化微信小程序发送器"""
        # 微信模板消息配置
        self.template_id = self.get_config('template_id', '')
        self.miniprogram = self.get_config('miniprogram', {})
        self.default_url = self.get_config('default_url', None)

        # 默认小程序配置
        if not self.miniprogram:
            self.miniprogram = {
                "appid": "",
                "pagepath": ""
            }

        logger.info(f"微信小程序发送器初始化完成: template_id = {self.template_id}")

    @property
    def supported_message_type(self) -> MessageType:
        """返回支持的消息类型"""
        return MessageType.WECHAT_MINIAPP

    def _validate_message_specific(self, message: Message) -> bool:
        """验证微信小程序消息的特定要求"""
        # 检查是否有发送函数
        send_template_message = _get_send_template_message()
        if send_template_message is None:
            logger.error("微信模板消息发送函数未导入")
            return False

        # 检查接收者openid格式
        for recipient in message.recipients:
            if not recipient.contact or len(recipient.contact) < 20:
                logger.error(f"无效的微信openid: {recipient.contact}")
                return False

        # 检查模板ID
        template_id = message.content.template_id or self.template_id
        if not template_id:
            logger.error("缺少微信模板ID")
            return False

        # 检查模板数据格式
        if message.content.template_data:
            # 微信模板数据应该是 {"key": {"value": "xxx"}} 格式
            for key, value in message.content.template_data.items():
                if not isinstance(value, dict) or 'value' not in value:
                    logger.error(f"微信模板数据格式错误: {key} = {value}")
                    return False

        return True

    def _build_template_data(self, message: Message) -> Dict[str, Dict[str, str]]:
        """构建微信模板数据"""
        if message.content.template_data:
            # 如果已经是正确格式，直接返回
            return message.content.template_data

        # 否则从消息内容构建默认模板数据
        template_data = {
            "first": {"value": message.content.title or "通知消息"},
            "keyword1": {"value": datetime.now().strftime("%Y-%m-%d %H:%M")},
            "keyword2": {"value": message.content.body},
            "remark": {"value": "请及时查看"}
        }

        return template_data

    async def send_single(self, message: Message, recipient_index: int = 0) -> SendResult:
        """发送单条微信小程序消息"""
        if recipient_index >= len(message.recipients):
            return SendResult(
                status=SendStatus.FAILED,
                recipient_id="",
                recipient_contact="",
                error_message="接收者索引超出范围",
                error_code="RECIPIENT_INDEX_OUT_OF_RANGE",
                sent_at=datetime.now()
            )

        recipient = message.recipients[recipient_index]

        try:
            # 构建模板数据
            template_data = self._build_template_data(message)

            # 获取模板ID
            template_id = message.content.template_id or self.template_id

            # 获取跳转URL
            url = message.content.extra_data.get('url') if message.content.extra_data else None
            url = url or self.default_url

            # 获取小程序配置
            miniprogram = message.content.extra_data.get('miniprogram') if message.content.extra_data else None
            miniprogram = miniprogram or self.miniprogram

            logger.info(f"发送微信模板消息: openid={recipient.contact}, template_id={template_id}")
            logger.debug(f"模板数据: {template_data}")

            # 获取并调用微信模板消息发送函数
            send_template_message = _get_send_template_message()
            if send_template_message is None:
                logger.error("微信模板消息发送函数未导入")
                return SendResult(
                    status=SendStatus.FAILED,
                    recipient_id=recipient.id,
                    recipient_contact=recipient.contact,
                    error_message="微信模板消息发送函数未导入",
                    error_code="WECHAT_TEMPLATE_FUNCTION_NOT_AVAILABLE",
                    sent_at=datetime.now()
                )

            success = send_template_message(
                openid=recipient.contact,
                template_id=template_id,
                data=template_data,
                url=url,
                miniprogram=miniprogram
            )

            if success:
                logger.info(f"微信模板消息发送成功: {recipient.contact}")

                return SendResult(
                    status=SendStatus.SUCCESS,
                    message_id=message.id,
                    recipient_id=recipient.id,
                    recipient_contact=recipient.contact,
                    sent_at=datetime.now(),
                    extra_data={
                        "template_id": template_id,
                        "template_data": template_data
                    }
                )
            else:
                logger.error(f"微信模板消息发送失败: {recipient.contact}")

                return SendResult(
                    status=SendStatus.FAILED,
                    recipient_id=recipient.id,
                    recipient_contact=recipient.contact,
                    error_message="微信模板消息发送失败",
                    error_code="WECHAT_TEMPLATE_SEND_FAILED",
                    sent_at=datetime.now()
                )

        except Exception as e:
            logger.error(f"微信小程序消息发送异常: {str(e)}")
            return SendResult(
                status=SendStatus.FAILED,
                recipient_id=recipient.id,
                recipient_contact=recipient.contact,
                error_message=f"微信小程序消息发送异常: {str(e)}",
                error_code="WECHAT_MINIAPP_SEND_ERROR",
                sent_at=datetime.now()
            )
