#!/usr/bin/env python3
"""
消息发送器测试脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.messager import (
    get_global_message_manager,
    MessageSenderFactory,
    Message,
    MessageType,
    MessagePriority,
    MessageRecipient,
    MessageContent
)


async def test_system_message():
    """测试系统消息发送"""
    print("=== 测试系统消息发送 ===")
    
    try:
        # 创建系统消息
        message = Message(
            type=MessageType.SYSTEM,
            priority=MessagePriority.NORMAL,
            recipients=[
                MessageRecipient(
                    id="test_user_001",
                    name="测试用户1",
                    contact="test_user_001"
                ),
                MessageRecipient(
                    id="test_user_002",
                    name="测试用户2",
                    contact="test_user_002"
                )
            ],
            content=MessageContent(
                title="系统测试通知",
                body="这是一条系统消息测试，用于验证消息发送器是否正常工作。",
                extra_data={
                    "test_type": "system_message",
                    "timestamp": "2025-01-15 18:00:00"
                }
            )
        )
        
        # 获取消息管理器
        manager = get_global_message_manager()
        
        # 发送消息
        result = await manager.send_message(message)
        
        # 输出结果
        print(f"发送结果:")
        print(f"  总数: {result.total_count}")
        print(f"  成功: {result.success_count}")
        print(f"  失败: {result.failed_count}")
        print(f"  待处理: {result.pending_count}")
        
        print(f"\n详细结果:")
        for r in result.results:
            status_icon = "✅" if r.status.value == "success" else "❌"
            print(f"  {status_icon} {r.recipient_id} ({r.recipient_contact}): {r.status.value}")
            if r.error_message:
                print(f"    错误: {r.error_message}")
        
        return result.success_count > 0
        
    except Exception as e:
        print(f"系统消息测试失败: {str(e)}")
        return False


async def test_message_validation():
    """测试消息验证功能"""
    print("\n=== 测试消息验证功能 ===")
    
    try:
        # 测试无效消息（没有接收者）
        invalid_message = Message(
            type=MessageType.EMAIL,
            recipients=[],  # 空接收者列表
            content=MessageContent(
                title="测试邮件",
                body="这是测试内容"
            )
        )
        
        manager = get_global_message_manager()
        result = await manager.send_message(invalid_message)
        
        print(f"无效消息测试结果:")
        print(f"  成功: {result.success_count}")
        print(f"  失败: {result.failed_count}")
        
        # 应该全部失败
        if result.failed_count > 0 and result.success_count == 0:
            print("  ✅ 消息验证功能正常")
            return True
        else:
            print("  ❌ 消息验证功能异常")
            return False
            
    except Exception as e:
        print(f"消息验证测试失败: {str(e)}")
        return False


async def test_sender_registration():
    """测试发送器注册功能"""
    print("\n=== 测试发送器注册功能 ===")
    
    try:
        manager = get_global_message_manager()
        senders = manager.list_senders()
        
        print(f"已注册的发送器:")
        for msg_type, sender_class in senders.items():
            print(f"  {msg_type.value}: {sender_class}")
        
        # 检查是否注册了系统消息发送器
        if MessageType.SYSTEM in senders:
            print("  ✅ 系统消息发送器注册成功")
            return True
        else:
            print("  ❌ 系统消息发送器未注册")
            return False
            
    except Exception as e:
        print(f"发送器注册测试失败: {str(e)}")
        return False


async def test_factory_creation():
    """测试工厂类创建功能"""
    print("\n=== 测试工厂类创建功能 ===")
    
    try:
        # 测试创建系统消息发送器
        system_sender = MessageSenderFactory.create_system_sender()
        
        if system_sender and system_sender.supported_message_type == MessageType.SYSTEM:
            print("  ✅ 工厂类创建系统发送器成功")
            
            # 测试直接使用发送器
            message = Message(
                type=MessageType.SYSTEM,
                recipients=[
                    MessageRecipient(
                        id="factory_test_user",
                        name="工厂测试用户",
                        contact="factory_test_user"
                    )
                ],
                content=MessageContent(
                    title="工厂测试消息",
                    body="这是通过工厂类创建的发送器发送的消息"
                )
            )
            
            result = await system_sender.send_batch(message)
            
            if result.success_count > 0:
                print("  ✅ 工厂创建的发送器工作正常")
                return True
            else:
                print("  ❌ 工厂创建的发送器发送失败")
                return False
        else:
            print("  ❌ 工厂类创建发送器失败")
            return False
            
    except Exception as e:
        print(f"工厂类测试失败: {str(e)}")
        return False


async def test_message_priority():
    """测试消息优先级功能"""
    print("\n=== 测试消息优先级功能 ===")
    
    try:
        # 创建不同优先级的消息
        priorities = [MessagePriority.LOW, MessagePriority.NORMAL, MessagePriority.HIGH, MessagePriority.URGENT]
        
        for priority in priorities:
            message = Message(
                type=MessageType.SYSTEM,
                priority=priority,
                recipients=[
                    MessageRecipient(
                        id=f"priority_test_{priority.value}",
                        name=f"优先级测试用户_{priority.value}",
                        contact=f"priority_test_{priority.value}"
                    )
                ],
                content=MessageContent(
                    title=f"优先级测试 - {priority.value}",
                    body=f"这是一条{priority.value}优先级的测试消息"
                )
            )
            
            manager = get_global_message_manager()
            result = await manager.send_message(message)
            
            print(f"  {priority.value}优先级消息: {'✅' if result.success_count > 0 else '❌'}")
        
        print("  ✅ 消息优先级功能测试完成")
        return True
        
    except Exception as e:
        print(f"消息优先级测试失败: {str(e)}")
        return False


async def main():
    """运行所有测试"""
    print("消息发送器统一抽象测试")
    print("=" * 50)
    
    tests = [
        ("系统消息发送", test_system_message),
        ("消息验证功能", test_message_validation),
        ("发送器注册功能", test_sender_registration),
        ("工厂类创建功能", test_factory_creation),
        ("消息优先级功能", test_message_priority)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            if result:
                passed += 1
        except Exception as e:
            print(f"{test_name}测试异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！消息发送器统一抽象实现成功！")
    else:
        print("⚠️  部分测试失败，请检查实现")
    
    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
