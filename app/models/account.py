import enum
from datetime import datetime, UTC

from sqlalchemy import Column, Integer, String, Float, ForeignKey, DateTime, Enum, UniqueConstraint
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.models.enum import Status
from app.utils.common import get_current_time


class AccountType(enum.Enum):
    """账户类型枚举"""
    ACCOUNT = "account"  # 账户：用于账户管理
    REGULAR = "regular"  # 普通账户：用于日常消费和充值
    GIFT = "gift"  # 赠送账户：用于赠送金额的管理
    POINTS = "points"  # 积分账户：用于积分累计和兑换
    MEMBER = "member"  # 会员账户：用于会员等级和特权管理
    WECHAT = "wechat"  # 微信账户：用于微信支付和退款


class TransactionType(enum.Enum):
    """交易类型枚举"""
    NONE = "none"  # 无交易
    PAYMENT = "payment"  # 支付：支付订单或交易
    DEPOSIT = "deposit"  # 充值：向账户存入资金
    WITHDRAW = "withdraw"  # 提现：从账户提取资金
    TRANSFER = "transfer"  # 转账：账户间资金转移
    CONSUME = "consume"  # 消费：使用账户资金进行支付
    REFUND = "refund"  # 退款：退回已支付的资金


class Account(Base):
    """基础账户类"""
    __tablename__ = "accounts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    balance = Column(Float, default=0.0)
    status = Column(Enum(Status), nullable=False, default=Status.ACTIVE, comment="账户状态")
    type = Column(Enum(AccountType), nullable=False, default=AccountType.ACCOUNT)
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    __table_args__ = (
        UniqueConstraint('user_id', 'type', name='uq_user_id_type'),
    )

    # 关联
    user = relationship("User", back_populates="accounts")
    transactions = relationship("AccountTransaction", back_populates="account")
    __mapper_args__ = {
        'polymorphic_on': type,
        'polymorphic_identity': AccountType.ACCOUNT
    }


class RegularAccount(Account):
    """普通账户类
    用于日常消费和充值的标准账户类型
    继承自基础账户类，具有基本的资金管理功能
    """
    __tablename__ = "regular_accounts"

    id = Column(Integer, ForeignKey("accounts.id"), primary_key=True)

    __mapper_args__ = {
        "polymorphic_identity": AccountType.REGULAR
    }

class WechatAccount(Account):
    """微信账户类
    用于微信支付和退款的账户类型
    继承自基础账户类，具有基本的资金管理功能
    """
    __tablename__ = "wechat_accounts"

    id = Column(Integer, ForeignKey("accounts.id"), primary_key=True)

    __mapper_args__ = {
        "polymorphic_identity": AccountType.WECHAT
    }

class GiftAccount(Account):
    """赠送账户类
    用于管理赠送金额的特殊账户类型
    属性：
        gift_amount: 赠送金额总额
    """
    __tablename__ = "gift_accounts"

    id = Column(Integer, ForeignKey("accounts.id"), primary_key=True)
    gift_amount = Column(Float, default=0.0)

    __mapper_args__ = {
        "polymorphic_identity": AccountType.GIFT
    }


class PointsAccount(Account):
    """积分账户类
    用于管理用户积分的特殊账户类型
    属性：
        points_total: 积分总额
    """
    __tablename__ = "points_accounts"

    id = Column(Integer, ForeignKey("accounts.id"), primary_key=True)
    points_total = Column(Integer, default=0)

    __mapper_args__ = {
        "polymorphic_identity": AccountType.POINTS
    }


class MemberAccount(Account):
    """会员账户类
    用于管理会员等级和特权的特殊账户类型
    属性：
        member_level: 会员等级
        member_points: 会员积分
        valid_until: 会员有效期
    """
    __tablename__ = "member_accounts"

    id = Column(Integer, ForeignKey("accounts.id"), primary_key=True)
    member_level = Column(String(20))
    member_points = Column(Integer, default=0)
    valid_until = Column(DateTime)

    __mapper_args__ = {
        "polymorphic_identity": AccountType.MEMBER
    }


class AccountTransaction(Base):
    """账户流水类"""
    __tablename__ = "account_transactions"

    id = Column(Integer, primary_key=True, index=True)
    account_id = Column(Integer, ForeignKey("accounts.id"), nullable=False)
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=True)
    transaction_type = Column(Enum(TransactionType), default=TransactionType.NONE, nullable=False, comment="交易类型")
    amount = Column(Float, nullable=False)
    transaction_time = Column(DateTime, default=datetime.now, nullable=False)
    description = Column(String(200))

    # 关联
    order = relationship("Order", back_populates="transactions")
    account = relationship("Account", back_populates="transactions")
