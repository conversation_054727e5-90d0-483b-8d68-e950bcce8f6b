from datetime import datetime

from sqlalchemy import Column, DateTime, Enum, Integer, String, Date, ForeignKey, UniqueConstraint, Table
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.models.enum import Status
from app.utils.common import get_current_time

# 创建 Admin 与 Role 的关联表
admin_role_relations = Table(
    'admin_role_relations',
    Base.metadata,
    Column('admin_id', Integer, ForeignKey('admins.id')),
    Column('role_id', Integer, ForeignKey('roles.id')),
    UniqueConstraint('admin_id', 'role_id', name='uq_admin_role')
)

# 创建 Role 与 Permission 的关联表
role_permission_relations = Table(
    'role_permission_relations',
    Base.metadata,
    Column('role_id', Integer, ForeignKey('roles.id')),
    Column('permission_id', Integer, ForeignKey('permissions.id')),
    UniqueConstraint('role_id', 'permission_id', name='uq_role_permission')
)


class Admin(Base):
    """管理员"""
    __tablename__ = "admins"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False, comment="管理员名称")
    password = Column(String(100), nullable=False, comment="管理员密码")
    phone = Column(String(20), nullable=True, unique=True, comment="手机号码")
    email = Column(String(100), nullable=True, comment="电子邮箱")
    username = Column(String(50), nullable=True, comment="用户名")
    status = Column(Enum(Status), default=Status.ACTIVE, comment="管理员状态")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    last_login_time = Column(DateTime)

    # 关联
    roles = relationship("Role", secondary=admin_role_relations, back_populates="admins")


class Role(Base):
    """角色"""
    __tablename__ = "roles"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, nullable=False, comment="角色名称")
    description = Column(String(200), nullable=True, comment="角色描述")
    status = Column(Enum(Status), nullable=False, default=Status.ACTIVE, comment="角色状态")
    created_at = Column(Date, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 关联
    admins = relationship("Admin", secondary=admin_role_relations, back_populates="roles")
    permissions = relationship("Permission", secondary=role_permission_relations, back_populates="roles")


class Permission(Base):
    """权限"""
    __tablename__ = "permissions"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, nullable=False, comment="权限名称")
    code = Column(String(50), unique=True, nullable=False, comment="权限代码")
    permission_type = Column(String(20), nullable=False, comment="权限类型")
    description = Column(String(200), nullable=True, comment="权限描述")
    status = Column(Enum(Status), nullable=False, default=Status.ACTIVE, comment="权限状态")
    created_at = Column(Date, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 关联
    roles = relationship("Role", secondary=role_permission_relations, back_populates="permissions")
