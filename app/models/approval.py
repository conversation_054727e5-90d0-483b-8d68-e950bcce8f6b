import enum
from datetime import datetime

from sqlalchemy import Column, Integer, ForeignKey, Text, Enum, DateTime
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.utils.common import get_current_time


class ApprovalStatus(enum.Enum):
    """审批状态枚举
    定义了审批请求的状态
    """
    PENDING = "pending"  # 待审批：审批请求已提交但未处理
    APPROVED = "approved"  # 已通过：审批请求已通过
    REJECTED = "rejected"  # 已拒绝：审批请求已拒绝


class Approval(Base):
    __tablename__ = "approvals"

    id = Column(Integer, primary_key=True, index=True, comment="审批请求唯一标识")
    reservation_request_id = Column(Integer, ForeignKey("reservation_requests.id"), nullable=False,
                                    comment="预订请求ID")
    applicant_id = Column(Integer, ForeignKey("enterprise_user_relations.id"), nullable=False,
                          comment="申请人ID")
    enterprise_id = Column(Integer, ForeignKey("enterprises.id"), nullable=False, comment="企业ID")
    status = Column(Enum(ApprovalStatus), nullable=False, default=ApprovalStatus.PENDING, comment="审批状态")
    comment = Column(Text, comment="审批意见")
    approver_id = Column(Integer, ForeignKey("enterprise_user_relations.id"), nullable=True,
                         comment="审批人ID")
    approval_time = Column(DateTime, comment="审批时间")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 关联
    reservation_request = relationship("ReservationRequest", back_populates="approval")
    applicant = relationship("EnterpriseUserRelation", foreign_keys=[applicant_id], back_populates="applicant_approvals")
    approver = relationship("EnterpriseUserRelation", foreign_keys=[approver_id], back_populates="approver_approvals")
    enterprise = relationship("Enterprise", back_populates="approvals")
