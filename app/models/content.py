import enum
from datetime import datetime

from sqlalchemy import Column, Integer, String, Foreign<PERSON>ey, Text, Enum, Table, DateTime, UniqueConstraint
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.models.enum import Status
from app.models.file import content_file_relation
from app.models.menu import menu_content_relation
from app.utils.common import get_current_time


class ContentType(enum.Enum):
    CONTENT = "content"
    ARTICLE = "article"
    DISH = "dish"


# 产品内容关联表
product_content_relation = Table(
    'product_content_relations',
    Base.metadata,
    Column('product_id', Integer, ForeignKey('products.id')),
    Column('content_id', Integer, ForeignKey('contents.id')),
    UniqueConstraint('product_id', 'content_id', name='uq_product_content')
)

# 内容标签关联表
content_tag_relation = Table(
    'content_tag_relations',
    Base.metadata,
    Column('content_id', Integer, ForeignKey('contents.id')),
    Column('tag_id', Integer, Foreign<PERSON>ey('tags.id')),
    UniqueConstraint('content_id', 'tag_id', name='uq_content_tag')
)


class Content(Base):
    """内容"""
    __tablename__ = "contents"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(200))
    content = Column(Text)
    image = Column(String(200))
    thumbnail = Column(String(200))
    sort_order = Column(Integer)
    status = Column(Enum(Status), default=Status.ACTIVE)
    type = Column(Enum(ContentType), default=ContentType.CONTENT)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # 关联
    products = relationship("Product", secondary=product_content_relation, back_populates="contents")
    files = relationship("File", secondary=content_file_relation, back_populates="contents")
    tags = relationship("Tag", secondary=content_tag_relation, back_populates="contents")
    menus = relationship("Menu", secondary=menu_content_relation, back_populates="contents")

    __mapper_args__ = {
        'polymorphic_on': type,
        'polymorphic_identity': ContentType.CONTENT
    }


class Article(Content):
    """文章"""
    __tablename__ = "articles"
    id = Column(Integer, ForeignKey('contents.id', ondelete='CASCADE'), primary_key=True)
    summary = Column(Text)
    ad_image = Column(String(200), default="", comment="广告图片")
    ad_link = Column(String(200), default="", comment="广告链接")
    __mapper_args__ = {
        'polymorphic_identity': ContentType.ARTICLE
    }


class Dish(Content):
    """菜品"""
    __tablename__ = "dishes"
    id = Column(Integer, ForeignKey('contents.id', ondelete='CASCADE'), primary_key=True)
    __mapper_args__ = {
        'polymorphic_identity': ContentType.DISH
    }
