import enum
from datetime import datetime

from sqlalchemy import Column, Integer, String, ForeignKey, Enum, Table, DateTime, UniqueConstraint
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.utils.common import get_current_time


class FileType(enum.Enum):
    IMAGE = "image"
    VIDEO = "video"
    AUDIO = "audio"
    TEXT = "text"
    PDF = "pdf"
    DOC = "doc"
    DOCX = "docx"
    XLS = "xls"
    XLSX = "xlsx"
    PPT = "ppt"
    PPTX = "pptx"
    ZIP = "zip"
    RAR = "rar"
    JPEG = "jpeg"
    JPG = "jpg"
    PNG = "png"
    BMP = "bmp"
    GIF = "gif"
    SVG = "svg"
    MP4 = "mp4"
    AVI = "avi"
    MOV = "mov"
    WAV = "wav"
    MP3 = "mp3"
    WMA = "wma"
    M4A = "m4a"


# 内容文件关联表
content_file_relation = Table(
    'content_file_relations',
    Base.metadata,
    Column('content_id', Integer, ForeignKey('contents.id')),
    Column('file_id', Integer, ForeignKey('files.id')),
    UniqueConstraint('content_id', 'file_id', name='uq_content_file')
)


class File(Base):
    """文件"""
    __tablename__ = "files"

    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String(100))
    file_type = Column(Enum(FileType))
    file_path = Column(String(200))
    file_size = Column(Integer)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # 关联
    contents = relationship("Content", secondary=content_file_relation, back_populates="files")
