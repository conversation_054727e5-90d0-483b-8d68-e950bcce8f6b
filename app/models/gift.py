import enum
from datetime import datetime

from sqlalchemy import Column, Integer, String, Text, Enum, DateTime, Boolean, Float, ForeignKey
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.models.enum import Status
from app.models.order import OrderType


class GiftRuleType(enum.Enum):
    """赠送规则类型枚举
    定义了系统中所有支持的赠送规则类型
    """
    GIFT = "gift"  #
    ORDER_GIFT = "order_gift"  # 订单赠送规则


class OrdGiftRuleOrdProdRel(Base):
    __tablename__ = "order_gift_rule_order_product_rel"
    id = Column(Integer, primary_key=True, index=True, comment="条件ID")
    gift_rule_condition_id = Column(Integer, ForeignKey("order_gift_rules.id"), nullable=False, comment="赠送规则ID")
    order_product_id = Column(Integer, ForeignKey("products.id"), nullable=False, comment="商品ID")
    quantity = Column(Integer, nullable=False, default=1, comment="数量")

    gift_rule_condition = relationship("OrderGiftRule", back_populates="order_product_rels")
    order_product = relationship("Product", back_populates="gift_rule_condition_rels")


class OrdGiftRuleGiftProdRel(Base):
    __tablename__ = "order_gift_rule_gift_product_rel"
    id = Column(Integer, primary_key=True, index=True, comment="条件ID")
    gift_rule_result_id = Column(Integer, ForeignKey("order_gift_rules.id"), nullable=False, comment="赠送规则ID")
    gift_product_id = Column(Integer, ForeignKey("products.id"), nullable=False, comment="商品ID")
    quantity = Column(Integer, nullable=False, comment="数量")

    gift_rule_result = relationship("OrderGiftRule", back_populates="gift_product_rels")
    gift_product = relationship("Product", back_populates="gift_rule_result_rels")


class GiftRule(Base):
    """
    基础赠送策略类
    所有具体的赠送策略都继承自这个类
    属性：
        id: 赠送ID
        name: 赠送名称
        description: 赠送描述
        start_time: 赠送生效时间
        end_time: 赠送结束时间
        scope: 赠送作用范围
        is_mutual_exclusive: 是否互斥
        status: 赠送状态
        type: 赠送类型
        created_at: 创建时间
        updated_at: 更新时间
    注意：
        该类是一个抽象类，不能直接实例化
        具体的赠送赠送类需要继承自这个类，并实现自己的逻辑
    """
    __tablename__ = "gift_rules"

    id = Column(Integer, primary_key=True, index=True, comment="赠送ID")
    name = Column(String(100), nullable=False, comment="赠送名称")
    description = Column(Text, comment="赠送描述")
    start_time = Column(DateTime, nullable=True, default=datetime.now, comment="赠送生效时间")
    end_time = Column(DateTime, nullable=True, default=datetime.now, comment="赠送结束时间")
    # 作用范围

    is_mutual_exclusive = Column(Boolean, nullable=False, default=False, comment="是否互斥")
    status = Column(Enum(Status), nullable=False,
                    default=Status.ACTIVE,
                    comment="赠送状态")
    type = Column(Enum(GiftRuleType), nullable=False, default=GiftRuleType.GIFT,
                  comment="赠送规则类型")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 关联
    # orders = relationship("Product", secondary=product_pricing_strategy_relation,
    #                         back_populates="pricing_strategies")
    __mapper_args__ = {
        "polymorphic_on": type,
        "polymorphic_identity": GiftRuleType.GIFT
    }


class OrderGiftRule(GiftRule):
    __tablename__ = "order_gift_rules"
    id = Column(Integer, ForeignKey("gift_rules.id"), primary_key=True)
    order_type = Column(Enum(OrderType), nullable=False, default=OrderType.RESERVATION,
                        comment="订单类型")
    order_start_time = Column(DateTime, nullable=True, default=datetime.now, comment="订单生效时间")
    order_end_time = Column(DateTime, nullable=True, default=datetime.now, comment="订单结束时间")
    order_amount = Column(Float, nullable=False, default=0.0, comment="订单金额")
    gift_amount = Column(Float, nullable=False, default=0.0, comment="赠送金额")

    order_product_rels = relationship("OrdGiftRuleOrdProdRel", back_populates="gift_rule_condition", cascade="all, delete-orphan")
    gift_product_rels = relationship("OrdGiftRuleGiftProdRel", back_populates="gift_rule_result", cascade="all, delete-orphan")

    __mapper_args__ = {
        "polymorphic_identity": GiftRuleType.ORDER_GIFT
    }
