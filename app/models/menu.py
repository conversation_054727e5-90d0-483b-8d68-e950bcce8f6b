from datetime import datetime

from sqlalchemy import Integer, Column, ForeignKey, String, DateTime, Enum, Table, UniqueConstraint
from sqlalchemy.orm import relationship

from app.db import Base
from app.models.enum import Status
from app.utils.common import get_current_time

menu_content_relation = Table(
    'menu_content_relations',
    Base.metadata,
    Column('menu_id', Integer, ForeignKey('menus.id')),
    Column('content_id', Integer, ForeignKey('contents.id')),
    UniqueConstraint('menu_id', 'content_id', name='uq_menu_content')
)


class Menu(Base):
    __tablename__ = "menus"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False, comment="菜单名称")
    description = Column(String(200), nullable=True, comment="菜单描述")
    status = Column(Enum(Status), nullable=False, default="active", comment="菜单状态")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, comment="产品ID")
    rule_id = Column(Integer, ForeignKey("rules.id"), nullable=False, comment="规则ID")
    available_start_time_cron_str = Column(String(50), nullable=True, comment="生效开始时间cron表达式")
    available_end_time_cron_str = Column(String(50), nullable=True, comment="生效结束时间cron表达式")
    available_date = Column(DateTime, nullable=True, comment="生效日")
    # 关联
    product = relationship("Product", back_populates="menus")
    rule = relationship("Rule", back_populates="menus")
    contents = relationship("Content", secondary=menu_content_relation, back_populates="menus")
