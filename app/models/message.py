#!/usr/bin/env python3
"""
消息相关数据模型
"""

import enum
from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Enum, Index
from sqlalchemy.dialects.mysql import JSON

from app.db.base_class import Base


class MessageType(enum.Enum):
    """消息类型枚举"""
    SYSTEM = "system"
    NOTIFICATION = "notification"
    ALERT = "alert"
    REMINDER = "reminder"
    ANNOUNCEMENT = "announcement"


class MessagePriority(enum.Enum):
    """消息优先级枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class MessageStatus(enum.Enum):
    """消息状态枚举"""
    UNREAD = "unread"
    READ = "read"
    ARCHIVED = "archived"
    DELETED = "deleted"


class SystemMessage(Base):
    """系统消息模型"""
    __tablename__ = "system_messages"

    id = Column(Integer, primary_key=True, index=True, comment="消息ID")
    message_id = Column(String(100), index=True, comment="消息唯一标识")
    
    # 接收者信息
    recipient_id = Column(String(100), index=True, nullable=False, comment="接收者ID")
    recipient_name = Column(String(100), comment="接收者姓名")
    recipient_type = Column(String(50), default="user", comment="接收者类型")
    
    # 消息内容
    title = Column(String(200), comment="消息标题")
    content = Column(Text, nullable=False, comment="消息内容")
    message_type = Column(Enum(MessageType), default=MessageType.SYSTEM, comment="消息类型")
    priority = Column(Enum(MessagePriority), default=MessagePriority.NORMAL, comment="消息优先级")
    
    # 消息状态
    status = Column(Enum(MessageStatus), default=MessageStatus.UNREAD, comment="消息状态")
    is_read = Column(Boolean, default=False, comment="是否已读")
    read_at = Column(DateTime, comment="阅读时间")
    
    # 发送者信息
    sender_id = Column(String(100), comment="发送者ID")
    sender_name = Column(String(100), comment="发送者姓名")
    sender_type = Column(String(50), default="system", comment="发送者类型")
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    sent_at = Column(DateTime, comment="发送时间")
    expires_at = Column(DateTime, comment="过期时间")
    
    # 扩展信息
    extra_data = Column(JSON, comment="额外数据JSON")
    tags = Column(String(500), comment="消息标签，逗号分隔")
    category = Column(String(100), comment="消息分类")
    
    # 创建索引
    __table_args__ = (
        Index('idx_recipient_status', 'recipient_id', 'status'),
        Index('idx_recipient_created', 'recipient_id', 'created_at'),
        Index('idx_message_type_priority', 'message_type', 'priority'),
        Index('idx_created_at', 'created_at'),
        Index('idx_sender_id', 'sender_id'),
        {'comment': '系统消息表'}
    )


class MessageTemplate(Base):
    """消息模板模型"""
    __tablename__ = "message_templates"

    id = Column(Integer, primary_key=True, index=True, comment="模板ID")
    template_code = Column(String(100), unique=True, index=True, nullable=False, comment="模板代码")
    template_name = Column(String(200), nullable=False, comment="模板名称")
    
    # 模板内容
    title_template = Column(String(500), comment="标题模板")
    content_template = Column(Text, nullable=False, comment="内容模板")
    message_type = Column(Enum(MessageType), default=MessageType.SYSTEM, comment="消息类型")
    priority = Column(Enum(MessagePriority), default=MessagePriority.NORMAL, comment="默认优先级")
    
    # 模板配置
    variables = Column(JSON, comment="模板变量配置JSON")
    is_active = Column(Boolean, default=True, comment="是否启用")
    description = Column(Text, comment="模板描述")
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    created_by = Column(String(100), comment="创建者")
    
    # 创建索引
    __table_args__ = (
        Index('idx_template_code', 'template_code'),
        Index('idx_message_type', 'message_type'),
        Index('idx_is_active', 'is_active'),
        {'comment': '消息模板表'}
    )


class MessageLog(Base):
    """消息发送日志模型"""
    __tablename__ = "message_logs"

    id = Column(Integer, primary_key=True, index=True, comment="日志ID")
    message_id = Column(String(100), index=True, comment="消息ID")
    batch_id = Column(String(100), index=True, comment="批次ID")
    
    # 发送信息
    sender_type = Column(String(50), nullable=False, comment="发送器类型")
    recipient_id = Column(String(100), index=True, comment="接收者ID")
    recipient_contact = Column(String(200), comment="接收者联系方式")
    
    # 发送结果
    send_status = Column(String(20), nullable=False, comment="发送状态")
    error_code = Column(String(50), comment="错误代码")
    error_message = Column(Text, comment="错误信息")
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    sent_at = Column(DateTime, comment="发送时间")
    
    # 扩展信息
    extra_data = Column(JSON, comment="额外数据JSON")
    
    # 创建索引
    __table_args__ = (
        Index('idx_message_id', 'message_id'),
        Index('idx_batch_id', 'batch_id'),
        Index('idx_recipient_status', 'recipient_id', 'send_status'),
        Index('idx_sender_type', 'sender_type'),
        Index('idx_created_at', 'created_at'),
        {'comment': '消息发送日志表'}
    )


class MessageSubscription(Base):
    """消息订阅模型"""
    __tablename__ = "message_subscriptions"

    id = Column(Integer, primary_key=True, index=True, comment="订阅ID")
    user_id = Column(String(100), index=True, nullable=False, comment="用户ID")
    
    # 订阅配置
    message_type = Column(Enum(MessageType), nullable=False, comment="消息类型")
    channel = Column(String(50), nullable=False, comment="接收渠道")
    is_enabled = Column(Boolean, default=True, comment="是否启用")
    
    # 过滤条件
    priority_filter = Column(String(100), comment="优先级过滤")
    category_filter = Column(String(200), comment="分类过滤")
    tag_filter = Column(String(500), comment="标签过滤")
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    
    # 创建索引
    __table_args__ = (
        Index('idx_user_type', 'user_id', 'message_type'),
        Index('idx_user_channel', 'user_id', 'channel'),
        Index('idx_is_enabled', 'is_enabled'),
        {'comment': '消息订阅表'}
    )
