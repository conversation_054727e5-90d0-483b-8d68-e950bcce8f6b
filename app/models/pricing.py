import enum
from datetime import datetime

from sqlalchemy import Column, Integer, String, Text, Enum, DateTime, ForeignKey, Float, Boolean, Table, \
    UniqueConstraint
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.models.enum import Status
from app.utils.common import get_current_time, get_50_years_after_current_time


class PricingStrategyType(enum.Enum):
    """定价策略类型枚举
    定义了系统中所有支持的定价策略类型
    """
    PRICING = "pricing"  # 普通价格：商品的标准售价
    DISCOUNT = "discount"  # 折扣价格：按比例打折
    FULL_REDUCTION = "full_reduction"  # 满减价格：满额减价
    MEMBER_PRICE = "member_price"  # 会员价格：针对不同会员等级的价格
    TIME_LIMITED = "time_limited"  # 限时特价：特定时间内的优惠价格
    BUNDLE = "bundle"  # 捆绑销售：捆绑销售的商品价格


class PricingStrategyScope(enum.Enum):
    """定价策略作用范围枚举
    定义了定价策略的作用范围
    """
    ORDER = "order"  # 订单范围
    PRODUCT = "product"  # 订单范围


class MemberLevel(enum.Enum):
    """会员等级枚举
    定义了系统中所有支持的会员等级
    """
    NONE = "none"  # 普通用户
    BASIC = "basic"  # 基础会员
    PREMIUM = "premium"  # 高级会员
    VIP = "vip"  # VIP会员


class UsageCycle(enum.Enum):
    """优惠券使用周期枚举
    定义了优惠券的使用周期
    """
    PER_ORDER = "per_order"  # 每次订单
    PER_DAY = "per_day"  # 每天
    PER_WEEK = "per_week"  # 每周
    PER_MONTH = "per_month"  # 每月
    PER_YEAR = "per_year"  # 每年


product_pricing_strategy_relation = Table(
    'product_pricing_strategy_relations',
    Base.metadata,
    Column('product_id', Integer, ForeignKey('products.id')),
    Column('pricing_strategy_id', Integer, ForeignKey('pricing_strategies.id')),
    UniqueConstraint('product_id', 'pricing_strategy_id', name='uq_product_pricing_strategy')
)


class PricingStrategy(Base):
    """
    基础定价策略类
    所有具体的定价策略都继承自这个类
    属性：
        id: 策略ID
        name: 策略名称
        description: 策略描述
        start_time: 策略生效时间
        end_time: 策略结束时间
        scope: 策略作用范围
        is_mutual_exclusive: 是否互斥
        status: 策略状态
        type: 策略类型
        created_at: 创建时间
        updated_at: 更新时间
    注意：
        该类是一个抽象类，不能直接实例化
        具体的定价策略类需要继承自这个类，并实现自己的逻辑
    """
    __tablename__ = "pricing_strategies"

    id = Column(Integer, primary_key=True, index=True, comment="策略ID")
    name = Column(String(100), nullable=False, comment="策略名称")
    description = Column(Text, comment="策略描述")
    start_time = Column(DateTime, nullable=True, default=datetime.now, comment="策略生效时间")
    end_time = Column(DateTime, nullable=True, default=datetime.now, comment="策略结束时间")
    # 作用范围
    scope = Column(Enum(PricingStrategyScope), nullable=False, default=PricingStrategyScope.PRODUCT,
                   comment="策略作用范围")
    is_mutual_exclusive = Column(Boolean, nullable=False, default=False, comment="是否互斥")
    status = Column(Enum(Status), nullable=False,
                    default=Status.ACTIVE,
                    comment="策略状态")
    type = Column(Enum(PricingStrategyType), nullable=False, default=PricingStrategyType.PRICING,
                  comment="策略类型")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 关联
    products = relationship("Product", secondary=product_pricing_strategy_relation,
                            back_populates="pricing_strategies")
    __mapper_args__ = {
        "polymorphic_on": type,
        "polymorphic_identity": PricingStrategyType.PRICING
    }


class DiscountStrategy(PricingStrategy):
    """折扣策略类
    用于管理商品折扣定价的特殊策略类型
    属性：
        id: 继承自基础策略的ID
        discount_rate: 折扣比例（0-1之间的小数）
        min_amount: 最低消费金额
        max_discount: 最大优惠金额
    """
    __tablename__ = "discount_strategies"

    id = Column(Integer, ForeignKey("pricing_strategies.id"), primary_key=True, comment="关联的基础策略ID")
    discount_rate = Column(Float, nullable=False, default=1, comment="折扣比例（0-1之间的小数）")
    min_amount = Column(Float, nullable=False, default=-1, comment="最低消费金额")
    max_discount = Column(Float, nullable=False, default=-1, comment="最大优惠金额")

    __mapper_args__ = {
        "polymorphic_identity": PricingStrategyType.DISCOUNT
    }

    def apply(self, *args, **kwargs):
        price = kwargs.get('price', 0)
        quantity = kwargs.get('quantity', 0)
        amount = kwargs.get('amount', 0)
        member_level = kwargs.get('member_level', MemberLevel.NONE)

        discount_rate = max(0, min(1, self.discount_rate))
        if discount_rate <= 0:
            discount_rate = 1

        # 确保折扣率在有效范围内
        if amount <= 0:
            return amount

        # 只有满足最低消费条件才应用折扣
        if self.min_amount > 0 and amount < self.min_amount:
            return amount

        # 应用折扣
        discounted_amount = amount * discount_rate

        # 应用最大折扣限制
        if self.max_discount > 0:
            # 计算折扣的金额
            discount_amount = amount - discounted_amount
            if discount_amount > self.max_discount:
                # 如果折扣超过最大限制，则使用最大折扣
                discounted_amount = amount - self.max_discount

        return discounted_amount


class FullReductionStrategy(PricingStrategy):
    """满减策略类
    用于管理满额减价的特殊策略类型
    属性：
        id: 继承自基础策略的ID
        full_amount: 满足金额
        reduction_amount: 减免金额
    """
    __tablename__ = "full_reduction_strategies"

    id = Column(Integer, ForeignKey("pricing_strategies.id"), primary_key=True, comment="关联的基础策略ID")
    full_amount = Column(Float, nullable=False, default=-1, comment="满足金额")
    reduction_amount = Column(Float, nullable=False, default=-1, comment="减免金额")

    __mapper_args__ = {
        "polymorphic_identity": PricingStrategyType.FULL_REDUCTION
    }

    def apply(self, *args, **kwargs):
        price = kwargs.get('price', 0)
        quantity = kwargs.get('quantity', 0)
        amount = kwargs.get('amount', 0)
        member_level = kwargs.get('member_level', MemberLevel.NONE)

        # 确保参数有效
        if amount <= 0 or self.full_amount <= 0 or self.reduction_amount <= 0:
            return amount

        # 只有满足满额条件才减免
        if amount >= self.full_amount:
            return amount - self.reduction_amount
        return amount


class MemberPriceStrategy(PricingStrategy):
    """会员价格策略类
    用于管理会员专属价格的特殊策略类型
    属性：
        id: 继承自基础策略的ID
        member_level: 会员等级
        price: 会员价格
    """
    __tablename__ = "member_price_strategies"

    id = Column(Integer, ForeignKey("pricing_strategies.id"), primary_key=True, comment="关联的基础策略ID")
    member_level = Column(Enum(MemberLevel), nullable=False, default=MemberLevel.BASIC, comment="会员等级")
    price = Column(Float, nullable=False, default=-1, comment="会员价格")

    __mapper_args__ = {
        "polymorphic_identity": PricingStrategyType.MEMBER_PRICE
    }

    def apply(self, *args, **kwargs):
        price = kwargs.get('price', 0)
        quantity = kwargs.get('quantity', 0)
        amount = kwargs.get('amount', 0)
        member_level = kwargs.get('member_level', MemberLevel.NONE)

        if member_level == self.member_level:
            return self.price
        return price


class TimeLimitedStrategy(PricingStrategy):
    """限时策略类
    用于管理限时特价的特殊策略类型
    属性：
        id: 继承自基础策略的ID
        price: 特价金额
        stock_limit: 库存限制
    """
    __tablename__ = "time_limited_strategies"

    id = Column(Integer, ForeignKey("pricing_strategies.id"), primary_key=True, comment="关联的基础策略ID")
    special_price = Column(Float, nullable=False, default=-1, comment="特价金额")
    stock_limit = Column(Integer, nullable=False, default=0, comment="库存限制")

    __mapper_args__ = {
        "polymorphic_identity": PricingStrategyType.TIME_LIMITED
    }

    def apply(self, *args, **kwargs):
        price = kwargs.get('price', 0)
        quantity = kwargs.get('quantity', 0)
        amount = kwargs.get('amount', 0)
        member_level = kwargs.get('member_level', MemberLevel.NONE)

        return min(self.special_price, price)

class BundleStrategy(PricingStrategy):
    __tablename__ = "bundle_strategies"
    id = Column(Integer, ForeignKey("pricing_strategies.id"), primary_key=True, comment="关联的基础策略ID")
    deduction = Column(Float, nullable=False, default=-1, comment="扣除金额")
    usage_cycle = Column(Enum(UsageCycle), nullable=False, default=UsageCycle.PER_ORDER, comment="使用周期")
    usage_limit = Column(Integer, nullable=False, default=1, comment="使用限制")
    need_rematch = Column(Boolean, nullable=False, default=False,
                          comment="是否要重新匹配，即命中一次后下一次需要所有条件都符合才能命中")

    bundle_product_rels = relationship("BundleStrategyProdRel", back_populates="bundle_strategy", cascade="all, delete-orphan")
    __mapper_args__ = {
        "polymorphic_identity": PricingStrategyType.BUNDLE
    }

class BundleStrategyProdRel(Base):
    __tablename__ = "bundle_strategy_product_rel"
    id = Column(Integer, primary_key=True, index=True, comment="ID")
    bundle_strategy_id = Column(Integer, ForeignKey("bundle_strategies.id"), nullable=False, comment="捆绑价格策略规则ID")
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, comment="商品ID")
    quantity = Column(Integer, nullable=False, default=1, comment="数量")

    bundle_strategy = relationship("BundleStrategy", back_populates="bundle_product_rels")
    bundle_product = relationship("Product", back_populates="bundle_strategy_rels")

