import enum
from datetime import datetime, UTC

from sqlalchemy import Column, ForeignKey, Integer, String, Enum, DateTime, Boolean
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.utils.common import get_current_time

class ReservationType(enum.Enum):
    """预订类型"""
    RESERVATION = "reservation"  # 预订类型
    BUFFET_RESERVATION = "buffet_reservation"  # 自助餐预订类型
    BIZ_DINING_RESERVATION = "biz_dining_reservation"  # 商务餐预订类型

class ReservationScope(enum.Enum):
    ORDER = "order"  # 订单范围
    PRODUCT = "product"  # 订单范围

# 假设这里定义了预订状态的枚举类型
class ReservationStatus(enum.Enum):
    PENDING = 'pending' # 待支付
    PAID_DEPOSIT = 'paid_deposit' # 已支付定金
    PAID_FULL = 'paid_full' # 已支付全款
    CANCELLED = 'cancelled' # 已取消
    VERIFIED = 'verified' # 已核销
    AUTO_VERIFIED = 'auto_verified'  # 自动核销

class ReservationRequest(Base):
    """预订请求
    预订请求可与订单或者订单项绑定
    绑定订单时，订单项不做绑定，反之亦然
    """
    __tablename__ = "reservation_requests"
    id = Column(Integer, primary_key=True, index=True)
    orders_id = Column(Integer, ForeignKey("orders.id"), nullable=True)
    order_item_id = Column(Integer, ForeignKey("order_items.id"), nullable=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=True)
    rule_id = Column(Integer, ForeignKey("rules.id"), nullable=False)
    rule_item_id = Column(Integer, ForeignKey("rule_items.id"), nullable=False)
    status = Column(Enum(ReservationStatus), default=ReservationStatus.PENDING, nullable=False)
    reservation_period = Column(String(50), nullable=True, comment="预订时段")
    dining_start_time = Column(DateTime, nullable=True, comment="就餐开始时间")
    dining_end_time = Column(DateTime, nullable=True, comment="就餐结束时间")
    reservation_time = Column(DateTime, default=datetime.now)
    verification_code = Column(String(128))
    type = Column(Enum(ReservationType), nullable=False, default=ReservationType.RESERVATION,
                  comment="预订类型")
    scope = Column(Enum(ReservationScope), nullable=False, default=ReservationScope.PRODUCT,
                   comment="预订作用范围")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 定义关联关系
    order = relationship("Order", back_populates="reservation_requests")
    order_item = relationship("OrderItem", back_populates="reservation_requests")
    user = relationship("User", back_populates="reservation_requests")
    product = relationship("Product", back_populates="reservation_requests")
    rule = relationship("Rule", back_populates="reservation_requests")
    rule_item = relationship("RuleItem", back_populates="reservation_requests")
    # 添加与Approval的一对一关系
    approval = relationship("Approval", back_populates="reservation_request", uselist=False)

    __mapper_args__ = {
        'polymorphic_on': type,
        'polymorphic_identity': ReservationType.RESERVATION
    }

    def pay_deposit(self):
        # 实现支付定金的逻辑
        self.status = ReservationStatus.PAID_DEPOSIT
        # 这里可以添加更多支付定金的逻辑，例如更新数据库等

    def pay_balance(self):
        # 实现支付尾款的逻辑
        self.status = ReservationStatus.PAID_FULL
        # 这里可以添加更多支付尾款的逻辑，例如更新数据库等

class BuffetReservationRequest(ReservationRequest):
    """自助餐预订请求"""
    __tablename__ = "buffet_reservation_requests"
    id = Column(Integer, ForeignKey("reservation_requests.id"), primary_key=True)
    __mapper_args__ = {
        'polymorphic_identity': ReservationType.BUFFET_RESERVATION
    }

class BizReservationRequest(ReservationRequest):
    """商务餐预订请求"""
    __tablename__ = "biz_reservation_requests"
    id = Column(Integer, ForeignKey("reservation_requests.id"), primary_key=True)
    name = Column(String(100), nullable=True, default="", comment="联系人姓名")
    phone = Column(String(20), nullable=True, default="", comment="联系人电话")
    persons = Column(Integer, nullable=True, default=0, comment="预订人数")
    remark = Column(String(200), nullable=True, default="", comment="备注")

    __mapper_args__ = {
        'polymorphic_identity': ReservationType.BIZ_DINING_RESERVATION
    }
