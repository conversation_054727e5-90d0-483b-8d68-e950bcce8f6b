import enum
from datetime import datetime

from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, Table, UniqueConstraint, Enum, Boolean
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.models.enum import Status
from app.utils.common import get_current_time

product_rule_relation = Table(
    'product_rule_relations',
    Base.metadata,
    Column('product_id', Integer, ForeignKey('products.id')),
    Column('rule_id', Integer, ForeignKey('rules.id')),
    UniqueConstraint('product_id', 'rule_id', name='uq_product_rule')
)


class RuleType(enum.Enum):
    """规则类型"""
    RESERVATION = "reservation"  # 预订规则
    DINING_RESERVATION = "dining_reservation"  # 餐厅预订规则

class RuleScope(enum.Enum):
    ORDER = "order"  # 订单范围
    PRODUCT = "product"  # 产品范围

class RuleOrderType(enum.Enum):
    NONE = "none"  # 无类型
    RESERVATION = "reservation"  # 预订订单
    BUFFET = "buffet"  # 自助餐订单
    BIZ_DINING = "biz_dining"  # 商务餐订单
    ACTIVITY = "activity"    # 活动订单

class MealType(enum.Enum):
    """餐食类型"""
    BREAKFAST = "breakfast"  # 早餐
    LUNCH = "lunch"  # 午餐
    AFTERNOON_TEA = "afternoon_tea"  # 下午茶
    DINNER = "dinner"  # 晚餐
    NIGHT_SNACK = "night_snack"  # 宵夜

class Rule(Base):
    __tablename__ = "rules"

    id = Column(Integer, primary_key=True, index=True, comment="规则ID")
    name = Column(String(100), unique=True, index=True, comment="规则名称")
    status = Column(String(20), default=Status.ACTIVE, comment="状态")
    type = Column(Enum(RuleType), default=RuleType.RESERVATION, comment="规则类型")
    scope = Column(Enum(RuleScope), default=RuleScope.PRODUCT, comment="规则作用范围")
    order_type = Column(Enum(RuleOrderType), default=RuleOrderType.NONE, comment="订单类型")
    created_at = Column(DateTime, default=get_current_time())
    updated_at = Column(DateTime, default=get_current_time(), onupdate=get_current_time())

    __mapper_args__ = {
        'polymorphic_on': type,
        'polymorphic_identity': RuleType.RESERVATION
    }

    # 关联
    rule_items = relationship("RuleItem", back_populates="rule", cascade="all, delete-orphan")
    products = relationship("Product", secondary=product_rule_relation, back_populates="rules")
    reservation_requests = relationship("ReservationRequest", back_populates="rule")
    menus = relationship("Menu", back_populates="rule")

class DiningReservationRule(Rule):
    #添加别名
    __tablename__ = "dining_reservation_rules"
    id = Column(Integer, ForeignKey('rules.id'), primary_key=True, comment="规则ID")
    alias = Column(String(50), comment="别名")
    dining_start_time_cron_str = Column(String(50), comment="服务开始时间cron表达式")
    dining_end_time_cron_str = Column(String(50), comment="服务结束时间cron表达式")
    verify_start_time_cron_str = Column(String(50), comment="核销开始时间cron表达式")
    verify_end_time_cron_str = Column(String(50), comment="核销结束时间cron表达式")
    is_auto_verify = Column(Boolean, default=False, nullable=False, comment="是否自动核销")
    order_deadline = Column(Integer, default=0, comment="订购截止提前量，单位：分钟")
    cancellation_deadline = Column(Integer, default=0, comment="取消截止提前量，单位：分钟")
    generated_count = Column(Integer, default=1, comment="生成的数量")
    quantity = Column(Integer, comment="数量")
    __mapper_args__ = {
        'polymorphic_identity': RuleType.DINING_RESERVATION
    }

class RuleItem(Base):
    """规则项"""
    __tablename__ = "rule_items"

    id = Column(Integer, primary_key=True, index=True, comment="规则项ID")
    rule_id = Column(Integer, ForeignKey("rules.id"), comment="规则ID")
    name = Column(String(100), comment="规则项名称")
    alias = Column(String(50), comment="别名")
    start_time = Column(DateTime, nullable=True, default=None, comment="开始时间")
    end_time = Column(DateTime, nullable=True, default=None, comment="结束时间")
    start_time_cron_str = Column(String(50), comment="cron表达式")
    end_time_cron_str = Column(String(50), comment="cron表达式")
    order_deadline = Column(Integer, default=0, comment="订购截止时间，单位：分钟")
    cancellation_deadline = Column(Integer, default=0, comment="取消截止时间，单位：分钟")
    verified_start_time = Column(Integer, default=0, comment="核销开始时间，单位：分钟")
    verified_end_time = Column(Integer, default=0, comment="核销截止时间，单位：分钟")
    generated_count = Column(Integer, default=1, comment="生成的数量")
    quantity = Column(Integer, comment="数量")
    order = Column(Integer, nullable=False, default=1, comment="排序")
    allowed_operations = Column(String(100), nullable=True, default="", comment="允许的操作")
    forbidden_operations = Column(String(100), nullable=True, default="", comment="禁止的操作")
    meal_type = Column(Enum(MealType), default=MealType.LUNCH, nullable=True, comment="餐食类型")
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # 关联
    rule = relationship("Rule", back_populates="rule_items")
    reservation_requests = relationship("ReservationRequest", back_populates="rule_item")
