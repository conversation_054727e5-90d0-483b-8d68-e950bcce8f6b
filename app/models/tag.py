from sqlalchemy import Column, Integer, String, Table, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.models.content import content_tag_relation

# 产品标签关联表
product_tag_relation = Table(
    'product_tag_relations',
    Base.metadata,
    Column('product_id', Integer, ForeignKey('products.id')),
    Column('tag_id', Integer, ForeignKey('tags.id')),
    UniqueConstraint('product_id', 'tag_id', name='uq_product_tag')
)


class Tag(Base):
    """标签"""
    __tablename__ = "tags"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True)

    # 关联
    products = relationship("Product", secondary=product_tag_relation, back_populates="tags")
    contents = relationship("Content", secondary=content_tag_relation, back_populates="tags")
