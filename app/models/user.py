import enum
from datetime import datetime, UTC

from sqlalchemy import <PERSON>umn, Integer, String, Date, <PERSON>olean, ForeignKey, DateTime, Enum, UniqueConstraint
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.models.enum import Status
from app.utils.common import get_current_time


class UserType(enum.Enum):
    USER = "user"
    PERSONAL = "personal"  # 个人用户：普通消费者
    ENTERPRISE = "enterprise"  # 企业用户：企业客户


class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True, comment="用户唯一标识")
    username = Column(String(50), unique=True, index=True, comment="用户名")
    register_time = Column(DateTime, default=datetime.now, comment="注册日期")
    status = Column(Enum(Status), default=Status.ACTIVE, comment="状态,用户管理员对用户对控制")
    type = Column(Enum(UserType), nullable=False, default=UserType.USER, comment="用户类型")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 关联
    accounts = relationship("Account", back_populates="user")
    orders = relationship("Order", back_populates="user")
    reservation_requests = relationship("ReservationRequest", back_populates="user")
    coupon_usage_records = relationship("CouponUsageRecord", back_populates="user")

    __mapper_args__ = {
        "polymorphic_on": type,
        "polymorphic_identity": UserType.USER
    }


class PersonalUser(User):
    __tablename__ = "personal_users"

    id = Column(Integer, ForeignKey("users.id"), primary_key=True, comment="关联的基础用户ID")
    phone = Column(String(20), unique=True, nullable=False, comment="手机号")
    wechat_id = Column(String(50), unique=True, nullable=True, comment="微信ID")
    password = Column(String(100), nullable=True, comment="密码")
    email = Column(String(100), nullable=True, comment="电子邮箱")
    address = Column(String(200), nullable=True, comment="地址")
    real_name = Column(String(50), nullable=True, comment="真实姓名")
    id_card = Column(String(18), unique=True, nullable=True, comment="身份证号")

    enterprises = relationship("EnterpriseUserRelation", back_populates="personal_user",
                               foreign_keys="EnterpriseUserRelation.personal_user_id")

    # 微信小程序，用户个人信息
    nickname = Column(String(128), nullable=True, comment="用户昵称")
    avatar_url = Column(String(256), nullable=True, comment="头像URL")
    gender = Column(Integer, nullable=True, comment="性别")
    country = Column(String(64), nullable=True, comment="国家")
    province = Column(String(64), nullable=True, comment="省份")
    city = Column(String(64), nullable=True, comment="城市")
    token = Column(String(128), nullable=True, comment="用户令牌")
    token_expiry = Column(DateTime, nullable=True, comment="令牌过期时间")
    msg_status = Column(Integer, default=0, comment="消息状态")
    gzh_openid = Column(String(64), nullable=True, comment="公众号（服务号）用户ID")
    gzh_unionid = Column(String(64), nullable=True, comment="公众号（服务号）用户UnionID")

    __mapper_args__ = {
        "polymorphic_identity": UserType.PERSONAL
    }


class Enterprise(User):
    __tablename__ = "enterprises"

    id = Column(Integer, ForeignKey("users.id"), primary_key=True, comment="关联的基础用户ID")
    company_name = Column(String(100), unique=True, nullable=False, comment="企业名称")
    business_license = Column(String(100), nullable=True, comment="营业执照号")
    phone = Column(String(20), nullable=True, comment="联系电话")
    email = Column(String(100), nullable=True, comment="企业邮箱")
    address = Column(String(200), nullable=True, comment="企业地址")

    # 关联
    personal_users = relationship("EnterpriseUserRelation", back_populates="enterprise",
                                  foreign_keys="EnterpriseUserRelation.enterprise_id")
    approvals = relationship("Approval", back_populates="enterprise")

    __mapper_args__ = {
        "polymorphic_identity": UserType.ENTERPRISE
    }


class EnterpriseUserRelation(Base):
    __tablename__ = "enterprise_user_relations"

    id = Column(Integer, primary_key=True, index=True, comment="关联关系唯一标识")
    enterprise_id = Column(Integer, ForeignKey("enterprises.id"), nullable=False, comment="企业用户ID")
    personal_user_id = Column(Integer, ForeignKey("personal_users.id"), nullable=False, comment="个人用户ID")
    password = Column(String(100), nullable=True, comment="密码")
    is_admin = Column(Boolean, default=False, nullable=True, comment="是否为企业管理员")
    remark = Column(String(200), comment="备注信息")
    relation_status = Column(Enum(Status), default=Status.ACTIVE, comment="状态,用企业对用户对控制")
    created_at = Column(Date, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    __table_args__ = (
        UniqueConstraint("enterprise_id", "personal_user_id", name="uq_enterprise_personal"),
    )

    # 关联
    enterprise = relationship("Enterprise", back_populates="personal_users", foreign_keys=[enterprise_id])
    personal_user = relationship("PersonalUser", back_populates="enterprises", foreign_keys=[personal_user_id])

    applicant_approvals = relationship("Approval", back_populates="applicant", foreign_keys="Approval.applicant_id")
    approver_approvals = relationship("Approval", back_populates="approver", foreign_keys="Approval.approver_id")
