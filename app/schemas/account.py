from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, ConfigDict, field_validator, field_serializer

from app.models.account import AccountType, TransactionType
from app.models.enum import Status


# Account基础模型
class AccountBase(BaseModel):
    balance: float = 0.0
    status: Status = Status.ACTIVE


class AccountCreate(AccountBase):
    user_id: int


class AccountUpdate(BaseModel):
    balance: Optional[float] = None
    status: Optional[Status] = None


class AccountResponse(AccountBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    @field_serializer("created_at", "updated_at")
    def serialize_datetime(self, value: datetime) -> str:
        return value.strftime("%Y-%m-%d %H:%M:%S")


class AccountInDB(AccountResponse):
    model_config = ConfigDict(from_attributes=True)


class Account(AccountInDB):
    pass


# RegularAccount模型
class RegularAccountBase(AccountBase):
    pass


class RegularAccountCreate(AccountCreate):
    pass


class RegularAccountUpdate(AccountUpdate):
    pass


class RegularAccountResponse(RegularAccountBase, AccountResponse):
    """用于API响应的普通账户模型"""
    pass


class RegularAccountInDB(RegularAccountBase, AccountInDB):
    pass


class RegularAccount(RegularAccountInDB):
    pass


# GiftAccount模型
class GiftAccountBase(AccountBase):
    gift_amount: float = 0.0


class GiftAccountCreate(AccountCreate, GiftAccountBase):
    gift_amount: float = 0.0


class GiftAccountUpdate(AccountUpdate):
    gift_amount: Optional[float] = None


class GiftAccountResponse(GiftAccountBase, AccountResponse):
    """用于API响应的赠送账户模型"""
    pass


class GiftAccountInDB(GiftAccountBase, AccountInDB):
    pass


class GiftAccount(GiftAccountInDB):
    pass


# PointsAccount模型
class PointsAccountBase(AccountBase):
    points_total: int = 0


class PointsAccountCreate(AccountCreate, PointsAccountBase):
    pass


class PointsAccountUpdate(AccountUpdate):
    points_total: Optional[int] = None


class PointsAccountResponse(PointsAccountBase, AccountResponse):
    pass


class PointsAccountInDB(PointsAccountBase, AccountInDB):
    pass


class PointsAccount(PointsAccountInDB):
    pass


# WechatAccount 模型
class WechatAccountBase(AccountBase):
    pass


class WechatAccountCreate(AccountCreate):
    pass


class WechatAccountUpdate(AccountUpdate):
    pass


class WechatAccountResponse(WechatAccountBase, AccountResponse):
    pass


class WechatAccountInDB(WechatAccountBase, AccountInDB):
    pass


class WechatAccount(WechatAccountInDB):
    pass

# MemberAccount模型
class MemberAccountBase(AccountBase):
    member_level: str
    member_points: int = 0
    valid_until: Optional[datetime] = None


class MemberAccountCreate(AccountCreate, MemberAccountBase):
    pass


class MemberAccountUpdate(AccountUpdate):
    member_level: Optional[str] = None
    member_points: Optional[int] = None
    valid_until: Optional[datetime] = None


class MemberAccountResponse(MemberAccountBase, AccountResponse):
    @field_serializer("created_at", "updated_at", "valid_until")
    def serialize_datetime(self, value: datetime) -> str:
        return value.strftime("%Y-%m-%d %H:%M:%S")


class MemberAccountInDB(MemberAccountBase, AccountInDB):
    pass


class MemberAccount(MemberAccountInDB):
    pass


# AccountTransaction模型
class AccountTransactionBase(BaseModel):
    account_id: int
    order_id: Optional[int] = None
    transaction_type: TransactionType = TransactionType.NONE
    amount: float
    description: Optional[str] = None
    transaction_time: datetime


class AccountTransactionCreate(AccountTransactionBase):
    pass


class AccountTransactionUpdate(BaseModel):
    description: Optional[str] = None


class AccountTransactionResponse(AccountTransactionBase):
    id: int
    model_config = ConfigDict(from_attributes=True)

    @field_serializer("transaction_time")
    def serialize_datetime(self, value: datetime) -> str:
        return value.strftime("%Y-%m-%d %H:%M:%S")


class AccountTransactionInDB(AccountTransactionResponse):
    type: TransactionType
    model_config = ConfigDict(from_attributes=True)


class AccountTransaction(AccountTransactionInDB):
    pass


# 关联关系Schema
class AccountWithTransactions(Account):
    transactions: List[AccountTransaction] = []


class AccountTransactionWithAccount(AccountTransaction):
    account: Account


# 详细响应模型
class AccountDetailResponse(AccountResponse):
    """包含交易信息的账户详细响应"""
    transactions: List[AccountTransactionResponse] = []


class RegularAccountDetailResponse(RegularAccountResponse):
    """包含交易信息的普通账户详细响应"""
    transactions: List[AccountTransactionResponse] = []


class GiftAccountDetailResponse(GiftAccountResponse):
    """包含交易信息的赠送账户详细响应"""
    transactions: List[AccountTransactionResponse] = []


class PointsAccountDetailResponse(PointsAccountResponse):
    """包含交易信息的积分账户详细响应"""
    transactions: List[AccountTransactionResponse] = []


class MemberAccountDetailResponse(MemberAccountResponse):
    """包含交易信息的会员账户详细响应"""
    transactions: List[AccountTransactionResponse] = []


# 用户交易记录模型
class UserTransactionItem(BaseModel):
    """用户交易记录项"""
    id: int
    username: str
    account_id: int
    account_type: str
    order_id: Optional[int] = None
    order_no: Optional[str] = None
    payment_method: Optional[str] = None
    transaction_type: str
    amount: float
    transaction_time: datetime
    description: Optional[str] = None
    
    model_config = ConfigDict(from_attributes=True)
    
    @field_serializer("transaction_time")
    def serialize_datetime(self, value: datetime) -> str:
        return value.strftime("%Y-%m-%d %H:%M:%S")


class UserTransactionListData(BaseModel):
    """用户交易记录列表数据"""
    total: int
    list: List[UserTransactionItem]


class UserTransactionListResponse(BaseModel):
    """用户交易记录列表响应"""
    code: int = 200
    message: str = "获取用户交易记录成功"
    data: UserTransactionListData
