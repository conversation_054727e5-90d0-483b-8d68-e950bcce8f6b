from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, ConfigDict

from app.models.enum import Status
from app.schemas.common import CommonResponse


# Admin schemas
class AdminBase(BaseModel):
    name: str
    phone: Optional[str] = None
    email: Optional[str] = None
    username: Optional[str] = None
    status: Optional[Status] = Status.ACTIVE


class AdminCreate(AdminBase):
    password: str


class AdminUpdate(BaseModel):
    name: Optional[str] = None
    password: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    username: Optional[str] = None
    status: Optional[Status] = None


class AdminUpdateWithId(AdminUpdate):
    """包含ID的管理员更新模型，用于根据请求体中的ID更新管理员"""
    id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    last_login_time: Optional[datetime] = None
    roles: Optional[List] = []
    
    # 重写status字段以支持整数输入
    status: Optional[int] = None


class AdminResponse(AdminBase):
    id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    last_login_time: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class AdminInDB(AdminResponse):
    id: int
    model_config = ConfigDict(from_attributes=True)


class Admin(AdminInDB):
    pass


# Role schemas
class RoleBase(BaseModel):
    name: str
    description: Optional[str] = None
    status: Status = Status.ACTIVE


class RoleCreate(RoleBase):
    pass


class RoleUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[Status] = None


class RoleUpdateWithId(RoleUpdate):
    """包含ID的角色更新模型，用于根据请求体中的ID更新角色"""
    id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    permissions: Optional[List] = []
    
    # 重写status字段以支持整数输入
    status: Optional[int] = None


class RoleResponse(RoleBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class RoleInDB(RoleBase):
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class Role(RoleInDB):
    pass


# Permission schemas
class PermissionBase(BaseModel):
    name: str
    code: str
    permission_type: Optional[str] = None
    description: Optional[str] = None
    status: Optional[Status] = Status.ACTIVE


class PermissionCreate(PermissionBase):
    pass


class PermissionUpdate(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    permission_type: Optional[str] = None
    description: Optional[str] = None
    status: Optional[Status] = None


class PermissionUpdateWithId(PermissionUpdate):
    """包含ID的权限更新模型，用于根据请求体中的ID更新权限"""
    id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    roles: Optional[List] = []
    
    # 重写status字段以支持整数输入
    status: Optional[int] = None


class PermissionResponse(PermissionBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class PermissionInDB(PermissionResponse):
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class Permission(PermissionInDB):
    pass


# 关联关系Schema
class AdminWithRoles(Admin):
    roles: List[Role] = []


class RoleWithAdmins(Role):
    admins: List[Admin] = []


class RoleWithPermissions(Role):
    permissions: List[Permission] = []


class PermissionWithRoles(Permission):
    roles: List[Role] = []


# 详细响应模型
class AdminDetailResponse(AdminResponse):
    """包含角色信息的管理员详细响应"""
    roles: List[RoleResponse] = []


class RoleDetailResponse(RoleResponse):
    """包含权限信息的角色详细响应"""
    permissions: List[PermissionResponse] = []


class PermissionDetailResponse(PermissionResponse):
    """包含角色信息的权限详细响应"""
    roles: List[RoleResponse] = []


# 统一格式的响应模型
class AdminListResponse(CommonResponse):
    """管理员列表响应"""
    data: List[AdminResponse]


class AdminCreateResponse(CommonResponse):
    """创建管理员响应"""
    data: Admin


class AdminDetailAPIResponse(CommonResponse):
    """管理员详情响应"""
    data: AdminDetailResponse


class AdminUpdateResponse(CommonResponse):
    """更新管理员响应"""
    data: AdminResponse


class AdminDeleteResponse(CommonResponse):
    """删除管理员响应"""
    data: Optional[dict] = None


class RoleListResponse(CommonResponse):
    """角色列表响应"""
    data: List[RoleResponse]


class RoleCreateResponse(CommonResponse):
    """创建角色响应"""
    data: Role


class RoleDetailAPIResponse(CommonResponse):
    """角色详情响应"""
    data: RoleDetailResponse


class RoleUpdateResponse(CommonResponse):
    """更新角色响应"""
    data: RoleResponse


class RoleDeleteResponse(CommonResponse):
    """删除角色响应"""
    data: Optional[dict] = None


class RolePermissionsResponse(CommonResponse):
    """角色权限列表响应"""
    data: List[PermissionResponse]


class PermissionListResponse(CommonResponse):
    """权限列表响应"""
    data: List[PermissionResponse]


class PermissionCreateResponse(CommonResponse):
    """创建权限响应"""
    data: Permission


class PermissionDetailAPIResponse(CommonResponse):
    """权限详情响应"""
    data: PermissionResponse


class PermissionUpdateResponse(CommonResponse):
    """更新权限响应"""
    data: PermissionResponse


class PermissionDeleteResponse(CommonResponse):
    """删除权限响应"""
    data: Optional[dict] = None


# 搜索相关Schema
class AdminSearchRequest(BaseModel):
    """管理员搜索请求"""
    keyword: Optional[str] = None
    page: int = 1
    pageSize: int = 10


class AdminListData(BaseModel):
    """管理员列表数据"""
    total: int
    list: List[AdminResponse]


class AdminSearchResponse(CommonResponse):
    """管理员搜索响应"""
    data: AdminListData


class RoleSearchRequest(BaseModel):
    """角色搜索请求"""
    keyword: Optional[str] = None
    page: int = 1
    pageSize: int = 10


class RoleListData(BaseModel):
    """角色列表数据"""
    total: int
    list: List[RoleResponse]


class RoleSearchResponse(CommonResponse):
    """角色搜索响应"""
    data: RoleListData


class PermissionSearchRequest(BaseModel):
    """权限搜索请求"""
    keyword: Optional[str] = None
    page: int = 1
    pageSize: int = 10


class PermissionListData(BaseModel):
    """权限列表数据"""
    total: int
    list: List[PermissionResponse]


class PermissionSearchResponse(CommonResponse):
    """权限搜索响应"""
    data: PermissionListData


# 批量操作Schema
class AdminBatchStatusRequest(BaseModel):
    """批量更新管理员状态请求"""
    admin_ids: List[int]
    status: int  # 0为禁用，1为启用


class AdminBatchDeleteRequest(BaseModel):
    """批量删除管理员请求"""
    ids: List[int]


class AdminBatchOperationResponse(CommonResponse):
    """批量操作响应"""
    data: Optional[dict] = None


# Role批量操作Schema
class RoleBatchStatusRequest(BaseModel):
    """批量更新角色状态请求"""
    role_ids: List[int]
    status: int  # 0为禁用，1为启用


class RoleBatchDeleteRequest(BaseModel):
    """批量删除角色请求"""
    ids: List[int]


class RoleBatchOperationResponse(CommonResponse):
    """角色批量操作响应"""
    data: Optional[dict] = None


# Permission批量操作Schema
class PermissionBatchStatusRequest(BaseModel):
    """批量更新权限状态请求"""
    permission_ids: List[int]
    status: int  # 0为禁用，1为启用


class PermissionBatchDeleteRequest(BaseModel):
    """批量删除权限请求"""
    ids: List[int]


class PermissionBatchOperationResponse(CommonResponse):
    """权限批量操作响应"""
    data: Optional[dict] = None
