from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict, field_serializer

from app.models.approval import ApprovalStatus


# 基础模型
class ApprovalBase(BaseModel):
    """审批基础模型"""
    reservation_request_id: int
    applicant_id: int
    enterprise_id: int
    status: ApprovalStatus
    comment: Optional[str] = None


# 创建模型
class ApprovalCreate(ApprovalBase):
    """审批创建模型"""
    pass


# 更新模型
class ApprovalUpdate(BaseModel):
    """审批更新模型"""
    status: Optional[ApprovalStatus] = None
    comment: Optional[str] = None
    approver_id: Optional[int] = None
    approval_time: Optional[datetime] = None


# 数据库响应模型
class ApprovalInDB(ApprovalBase):
    """审批数据库模型"""
    id: int
    approver_id: Optional[int] = None
    approval_time: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    @field_serializer("created_at", "updated_at", "approval_time")
    def serialize_datetime(self, value: datetime) -> Optional[str]:
        if value is None:
            return None
        return value.strftime("%Y-%m-%d %H:%M:%S")

    model_config = ConfigDict(from_attributes=True)


# API响应模型
class Approval(ApprovalInDB):
    """用于API响应的审批模型"""
    pass


# 详细响应模型 - 包含关联信息
class ApplicantInfo(BaseModel):
    """申请人信息"""
    id: int
    personal_user_id: int
    is_admin: Optional[bool] = None

    model_config = ConfigDict(from_attributes=True)


class ApproverInfo(BaseModel):
    """审批人信息"""
    id: int
    personal_user_id: int
    is_admin: Optional[bool] = None

    model_config = ConfigDict(from_attributes=True)


class EnterpriseInfo(BaseModel):
    """企业信息"""
    id: int
    company_name: str
    business_license: str

    model_config = ConfigDict(from_attributes=True)


class ReservationRequestInfo(BaseModel):
    """预订请求信息"""
    id: int
    status: str
    reservation_period: str

    model_config = ConfigDict(from_attributes=True)


class ApprovalDetail(Approval):
    """包含关联信息的审批详细响应"""
    applicant: Optional[ApplicantInfo] = None
    approver: Optional[ApproverInfo] = None
    enterprise: Optional[EnterpriseInfo] = None
    reservation_request: Optional[ReservationRequestInfo] = None


# 操作响应模型
class ApprovalActionResponse(BaseModel):
    """审批操作响应"""
    success: bool
    message: str
    approval: Optional[Approval] = None 