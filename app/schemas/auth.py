from typing import Optional
from fastapi import Form
from fastapi.security import OAuth2PasswordRequestForm


class EnhancedOAuth2PasswordRequestForm(OAuth2PasswordRequestForm):
    """
    扩展的 OAuth2 表单，支持额外的公司类型和公司ID字段
    """
    def __init__(
        self,
        username: str = Form(...),
        password: str = Form(...),
        grant_type: Optional[str] = Form(default=None, regex="password"),
        scope: str = Form(default=""),
        client_id: Optional[str] = Form(default=None),
        client_secret: Optional[str] = Form(default=None),
        company_type: Optional[int] = Form(default=2),  # 默认为乙禾公司(2)
        company_id: Optional[int] = Form(default=0),    # 默认为乙禾公司ID(0)
    ):
        super().__init__(
            username=username,
            password=password,
            grant_type=grant_type,
            scope=scope,
            client_id=client_id,
            client_secret=client_secret
        )
        self.company_type = company_type
        self.company_id = company_id 