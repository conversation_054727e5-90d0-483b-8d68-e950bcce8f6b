from pydantic import BaseModel, Field
from enum import IntEnum
from typing import Literal


class AudioEnum(IntEnum):
    """音频枚举"""
    DI = 0  # 滴
    SUCCESS = 40  # 出餐成功
    MEITUAN_SUCCESS = 41  # 美团出餐成功
    ELEME_SUCCESS = 42  # 饿了么出餐成功
    FAILURE = 45  # 出餐失败
    MEITUAN_FAILURE = 46  # 美团出餐失败
    ELEME_FAILURE = 47  # 饿了么出餐失败
    REPORT_SUCCESS = 53  # 上报成功
    REPORT_FAILURE = 54  # 上报失败
    WRITE_OFF_START = 55  # 开始核销
    WRITE_OFF_SUCCESS = 62  # 核销成功
    WRITE_OFF_FAILURE = 72  # 核销失败


class FeieyunCallbackRequest(BaseModel):
    """飞鹅云回调请求模型"""
    result: str = Field(..., description="加密后的回调结果")
    sn: str = Field(..., description="序列号")


class FeieyunCallbackResponse(BaseModel):
    """飞鹅云回调响应模型"""
    res: Literal["SUCCESS", "FAILURE"] = Field(..., description="响应结果")
    audio: int = Field(..., description="音频代码", ge=0, le=72)
