from datetime import datetime
from typing import Optional, List, Any

from fastapi import UploadFile
from pydantic import BaseModel, ConfigDict

from app.models.content import ContentType
from app.models.enum import Status
from app.schemas.file import File


class ContentBase(BaseModel):
    name: str
    content: Optional[str] = None
    image: Optional[str] = None
    thumbnail: Optional[str] = None
    sort_order: Optional[int] = 0
    status: Optional[Status] = Status.ACTIVE


class ContentCreate(ContentBase):
    pass


class ContentUpdate(BaseModel):
    name: Optional[str] = None
    content: Optional[str] = None
    image: Optional[str] = None
    thumbnail: Optional[str] = None
    sort_order: Optional[int] = 0
    status: Optional[Status] = None


class ContentResponse(ContentBase):
    status: Status
    id: int
    created_at: datetime
    updated_at: datetime


class ContentInDB(ContentResponse):
    type: ContentType
    model_config = ConfigDict(from_attributes=True)


class Content(ContentInDB):
    pass


class ContentWithFiles(Content):
    files: List[File] = []


# 内容添加与去除文件Schema
class ContentAndFiles(BaseModel):
    content_id: int
    file_ids: List[int]


# 产品添加与去除内容Schema
class ProductAndContent(BaseModel):
    product_id: int
    content_ids: List[int]


# 文章相关 Schema
class ArticleBase(ContentBase):
    summary: Optional[str] = None
    ad_image: Optional[str] = None
    ad_link: Optional[str] = None


class ArticleCreate(BaseModel):
    name: str
    content: Optional[str] = None
    summary: Optional[str] = None
    image: Optional[str] = None
    thumbnail: Optional[str] = None
    sort_order: Optional[int] = 0
    status: Optional[Status] = Status.ACTIVE
    ad_image: Optional[str] = None
    ad_link: Optional[str] = None


class ArticleUpdate(ContentUpdate):
    summary: Optional[str] = None
    ad_image: Optional[str] = None
    ad_link: Optional[str] = None


class ArticleResponse(ContentResponse):
    summary: Optional[str] = None
    ad_image: Optional[str] = None
    ad_link: Optional[str] = None


class ArticleInDB(ArticleResponse):
    type: ContentType = ContentType.ARTICLE
    model_config = ConfigDict(from_attributes=True)


class Article(ArticleInDB):
    pass


class ArticleWithFiles(Article):
    files: List[File] = []


class ArticleListData(BaseModel):
    total: int
    list: List[ArticleResponse]


class ArticleListResponse(BaseModel):
    code: int
    message: str
    data: ArticleListData


class ArticleCreateResponse(BaseModel):
    code: int
    message: str
    data: ArticleResponse


class ArticleUpdateResponse(BaseModel):
    code: int
    message: str
    data: ArticleUpdate


class ArticleWithFilesResponse(BaseModel):
    code: int
    message: str
    data: ArticleWithFiles


class ArticleDeleteRequest(BaseModel):
    ids: list[int] = []


# 文章搜索Schema
class ArticleSearchRequest(BaseModel):
    keyword: Optional[str] = None
    page: int = 1
    pageSize: int = 10


class ArticleSearchResponse(BaseModel):
    code: int
    message: str
    data: ArticleListData


# 菜品相关 Schema
class DishBase(ContentBase):
    pass


class DishCreate(BaseModel):
    name: str
    content: Optional[str] = None
    image: Optional[str] = None
    thumbnail: Optional[str] = None
    sort_order: Optional[int] = 0
    status: Optional[Status] = Status.ACTIVE


class DishUpdate(ContentUpdate):
    pass


class DishResponse(ContentResponse):
    pass


class DishInDB(DishResponse):
    type: ContentType = ContentType.DISH
    model_config = ConfigDict(from_attributes=True)


class Dish(DishInDB):
    pass


class DishWithFiles(Dish):
    files: List[File] = []


class DishListData(BaseModel):
    total: int
    list: List[DishResponse]


class DishListResponse(BaseModel):
    code: int
    message: str
    data: DishListData


class DishCreateResponse(BaseModel):
    code: int
    message: str
    data: DishResponse


class DishUpdateResponse(BaseModel):
    code: int
    message: str
    data: DishUpdate


class DishWithFilesReponse(BaseModel):
    code: int
    message: str
    data: DishWithFiles


class DishDeleteRequest(BaseModel):
    ids: list[int] = []


# 菜品搜索Schema
class DishSearchRequest(BaseModel):
    keyword: Optional[str] = None
    page: int = 1
    pageSize: int = 10


class DishSearchResponse(BaseModel):
    code: int
    message: str
    data: DishListData


# 批量更新内容状态Schema
class ContentBatchStatusUpdate(BaseModel):
    content_ids: List[int]
    status: Status
