from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict

from app.models.file import FileType


# File schemas
class FileBase(BaseModel):
    filename: str
    file_type: FileType
    file_path: str
    file_size: int


class FileCreate(FileBase):
    pass


class FileUpdate(BaseModel):
    filename: Optional[str] = None
    file_type: Optional[FileType] = None
    file_path: Optional[str] = None
    file_size: Optional[int] = None


class FileResponse(FileBase):
    id: int
    created_at: datetime
    updated_at: datetime


class FileInDB(FileResponse):
    model_config = ConfigDict(from_attributes=True)


class File(FileInDB):
    pass
