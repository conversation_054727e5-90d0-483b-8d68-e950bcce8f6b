from typing import List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field

from app.models.enum import Status
from app.models.gift import GiftRuleType
from app.models.order import OrderType


# 基础赠送规则相关 Schema
class GiftRuleBase(BaseModel):
    """赠送规则基础模型"""
    name: str = Field(..., description="赠送规则名称", max_length=100)
    description: Optional[str] = Field(None, description="赠送规则描述")
    start_time: Optional[datetime] = Field(None, description="赠送生效时间")
    end_time: Optional[datetime] = Field(None, description="赠送结束时间")
    is_mutual_exclusive: bool = Field(False, description="是否互斥")
    status: Status = Field(Status.ACTIVE, description="赠送规则状态")
    type: GiftRuleType = Field(GiftRuleType.GIFT, description="赠送规则类型")


class GiftRuleCreate(GiftRuleBase):
    """创建赠送规则"""
    pass


class GiftRuleUpdate(BaseModel):
    """更新赠送规则"""
    name: Optional[str] = Field(None, description="赠送规则名称", max_length=100)
    description: Optional[str] = Field(None, description="赠送规则描述")
    start_time: Optional[datetime] = Field(None, description="赠送生效时间")
    end_time: Optional[datetime] = Field(None, description="赠送结束时间")
    is_mutual_exclusive: Optional[bool] = Field(None, description="是否互斥")
    status: Optional[Status] = Field(None, description="赠送规则状态")


class GiftRuleResponse(GiftRuleBase):
    """赠送规则响应"""
    id: int = Field(..., description="赠送规则ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


# 订单赠送规则相关 Schema
class OrderGiftRuleBase(GiftRuleBase):
    """订单赠送规则基础模型"""
    order_type: OrderType = Field(OrderType.RESERVATION, description="订单类型")
    order_start_time: Optional[datetime] = Field(None, description="订单生效时间")
    order_end_time: Optional[datetime] = Field(None, description="订单结束时间")
    order_amount: float = Field(0.0, description="订单金额", ge=0)
    gift_amount: float = Field(0.0, description="赠送金额", ge=0)


class OrderGiftRuleCreate(OrderGiftRuleBase):
    """创建订单赠送规则"""
    type: GiftRuleType = Field(GiftRuleType.ORDER_GIFT, description="赠送规则类型")
    order_products: List[int] = Field(default=[], description="订单商品ID列表")
    gift_products: List[int] = Field(default=[], description="赠送商品ID列表")


class OrderGiftRuleUpdate(BaseModel):
    """更新订单赠送规则"""
    name: Optional[str] = Field(None, description="赠送规则名称", max_length=100)
    description: Optional[str] = Field(None, description="赠送规则描述")
    start_time: Optional[datetime] = Field(None, description="赠送生效时间")
    end_time: Optional[datetime] = Field(None, description="赠送结束时间")
    is_mutual_exclusive: Optional[bool] = Field(None, description="是否互斥")
    status: Optional[Status] = Field(None, description="赠送规则状态")
    order_type: Optional[OrderType] = Field(None, description="订单类型")
    order_start_time: Optional[datetime] = Field(None, description="订单生效时间")
    order_end_time: Optional[datetime] = Field(None, description="订单结束时间")
    order_amount: Optional[float] = Field(None, description="订单金额", ge=0)
    gift_amount: Optional[float] = Field(None, description="赠送金额", ge=0)
    order_products: Optional[List[int]] = Field(None, description="订单商品ID列表")
    gift_products: Optional[List[int]] = Field(None, description="赠送商品ID列表")


class OrderGiftRuleResponse(OrderGiftRuleBase):
    """订单赠送规则响应"""
    id: int = Field(..., description="赠送规则ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


# 订单商品关联相关 Schema
class OrdGiftRuleOrdProdRelBase(BaseModel):
    """订单赠送规则与订单商品关联基础模型"""
    gift_rule_condition_id: int = Field(..., description="赠送规则ID")
    order_product_id: int = Field(..., description="商品ID")
    quantity: int = Field(1, description="数量", ge=1)


class OrdGiftRuleOrdProdRelCreate(OrdGiftRuleOrdProdRelBase):
    """创建订单赠送规则与订单商品关联"""
    pass


class OrdGiftRuleOrdProdRelUpdate(BaseModel):
    """更新订单赠送规则与订单商品关联"""
    gift_rule_condition_id: Optional[int] = Field(None, description="赠送规则ID")
    order_product_id: Optional[int] = Field(None, description="商品ID")
    quantity: Optional[int] = Field(None, description="数量", ge=1)


class OrdGiftRuleOrdProdRelResponse(OrdGiftRuleOrdProdRelBase):
    """订单赠送规则与订单商品关联响应"""
    id: int = Field(..., description="关联ID")
    name: Optional[str] = Field(None, description="订单商品名称")

    class Config:
        from_attributes = True


# 赠送商品关联相关 Schema
class OrdGiftRuleGiftProdRelBase(BaseModel):
    """订单赠送规则与赠送商品关联基础模型"""
    gift_rule_result_id: int = Field(..., description="赠送规则ID")
    gift_product_id: int = Field(..., description="商品ID")
    quantity: int = Field(1, description="数量", ge=1)


class OrdGiftRuleGiftProdRelCreate(OrdGiftRuleGiftProdRelBase):
    """创建订单赠送规则与赠送商品关联"""
    pass


class OrdGiftRuleGiftProdRelUpdate(BaseModel):
    """更新订单赠送规则与赠送商品关联"""
    gift_rule_result_id: Optional[int] = Field(None, description="赠送规则ID")
    gift_product_id: Optional[int] = Field(None, description="商品ID")
    quantity: Optional[int] = Field(None, description="数量", ge=1)


class OrdGiftRuleGiftProdRelResponse(OrdGiftRuleGiftProdRelBase):
    """订单赠送规则与赠送商品关联响应"""
    id: int = Field(..., description="关联ID")
    name: Optional[str] = Field(None, description="订单商品名称")

    class Config:
        from_attributes = True


# 带关联信息的详细响应
class OrderGiftRuleWithDetails(OrderGiftRuleResponse):
    """带关联信息的订单赠送规则响应"""
    order_product_rels: List[OrdGiftRuleOrdProdRelResponse] = Field([], description="订单商品关联列表")
    gift_product_rels: List[OrdGiftRuleGiftProdRelResponse] = Field([], description="赠送商品关联列表")


class OrderGiftRuleWithDetailsResponse(BaseModel):
    """带关联信息的订单赠送规则响应"""
    code: int = Field(200, description="响应状态码")
    message: str = Field("成功", description="响应消息")
    data: Optional[OrderGiftRuleWithDetails] = Field(None, description="响应数据")

# 搜索相关 Schema
class GiftRuleSearchRequest(BaseModel):
    """赠送规则搜索请求"""
    keyword: Optional[str] = Field(None, description="关键词搜索")
    name: Optional[str] = Field(None, description="规则名称搜索")
    status: Optional[Status] = Field(None, description="规则状态筛选")
    type: Optional[GiftRuleType] = Field(None, description="规则类型筛选")
    page: int = Field(1, description="页码", ge=1)
    pageSize: int = Field(20, description="每页数量", ge=1, le=100)


class OrderGiftRuleSearchRequest(BaseModel):
    """订单赠送规则搜索请求"""
    keyword: Optional[str] = Field(None, description="关键词搜索")
    name: Optional[str] = Field(None, description="规则名称搜索")
    status: Optional[Status] = Field(None, description="规则状态筛选")
    order_type: Optional[OrderType] = Field(None, description="订单类型筛选")
    page: int = Field(1, description="页码", ge=1)
    pageSize: int = Field(20, description="每页数量", ge=1, le=100)


class GiftRuleListFormattedResponse(BaseModel):
    """赠送规则列表格式化响应"""
    code: int = Field(200, description="响应状态码")
    message: str = Field("成功", description="响应消息")
    data: dict[str, Any] = Field(..., description="响应数据")


# 状态更新相关 Schema
class GiftRuleStatusUpdateRequest(BaseModel):
    """赠送规则状态更新请求"""
    status: Status = Field(..., description="新状态")


# 商品绑定相关 Schema
class ProductAndGiftRule(BaseModel):
    """商品与赠送规则绑定"""
    gift_rule_id: int = Field(..., description="赠送规则ID")
    product_ids: List[int] = Field(..., description="商品ID列表")


# 批量操作响应
class BatchOperationResponse(BaseModel):
    """批量操作响应"""
    code: int = Field(200, description="响应状态码")
    message: str = Field("操作完成", description="响应消息")
    success_count: int = Field(0, description="成功数量")
    failed_count: int = Field(0, description="失败数量")
    success_ids: List[int] = Field([], description="成功的ID列表")
    failed_ids: List[int] = Field([], description="失败的ID列表") 