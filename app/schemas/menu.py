from datetime import datetime
from typing import Optional, List, Any, Dict, Union

from pydantic import BaseModel, ConfigDict, field_validator

from app.models.enum import Status
from app.schemas.rule import RuleType
from app.schemas.content import ContentWithFiles


class MenuBase(BaseModel):
    """菜单基础schema"""
    name: str
    description: Optional[str] = None
    status: Optional[Status] = Status.ACTIVE
    product_id: int
    rule_id: int
    available_start_time_cron_str: Optional[str] = None
    available_end_time_cron_str: Optional[str] = None
    available_date: Optional[datetime] = None


class MenuCreate(MenuBase):
    """创建菜单schema"""
    content_ids: Optional[List[int]] = []


class MenuStatusUpdate(BaseModel):
    """更新菜单状态schema"""
    status: Status


class MenuUpdate(BaseModel):
    """更新菜单schema"""
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[Status] = None
    product_id: Optional[int] = None
    rule_id: Optional[int] = None
    available_start_time_cron_str: Optional[str] = None
    available_end_time_cron_str: Optional[str] = None
    available_date: Optional[datetime] = None
    content_ids: Optional[List[int]] = None


class MenuResponse(MenuBase):
    """菜单响应schema"""
    id: int
    created_at: datetime
    updated_at: datetime
    product_name: Optional[str] = None
    rule_name: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class MenuResponseWithDiningRule(MenuResponse):
    """带餐厅预订规则属性的菜单响应schema"""
    # 基本规则属性
    rule_id: Optional[int] = None
    rule_name: Optional[str] = None
    rule_status: Optional[Status] = None
    rule_type: Optional[str] = None
    
    # 餐厅预订规则特有属性
    rule_alias: Optional[str] = None
    dining_start_time_cron_str: Optional[str] = None
    dining_end_time_cron_str: Optional[str] = None
    verify_start_time_cron_str: Optional[str] = None
    verify_end_time_cron_str: Optional[str] = None
    order_deadline: Optional[int] = None
    cancellation_deadline: Optional[int] = None
    is_auto_verify: Optional[bool] = None
    
    model_config = ConfigDict(from_attributes=True)


class MenuWithContentsResponse(MenuResponseWithDiningRule):
    """带内容列表的菜单响应schema"""
    contents: List[ContentWithFiles] = []
    
    model_config = ConfigDict(from_attributes=True)


class MenuSearchRequest(BaseModel):
    """搜索菜单请求schema"""
    keyword: Optional[str] = None
    name: Optional[str] = None
    product_id: Optional[int] = None
    rule_id: Optional[int] = None
    status: Optional[Status] = None
    skip: int = 0
    limit: int = 10


class MenuListData(BaseModel):
    """菜单列表数据schema"""
    total: int
    list: List[MenuResponse]


class MenuSearchListData(BaseModel):
    """搜索菜单列表数据schema，支持带餐厅预订规则的菜单"""
    total: int
    list: List[MenuResponseWithDiningRule]


class MenuListResponse(BaseModel):
    """菜单列表响应schema"""
    code: int
    message: str
    data: MenuListData


class MenuListWithDiningRuleResponse(BaseModel):
    """包含餐厅预订规则的菜单列表响应schema"""
    code: int
    message: str
    data: MenuSearchListData


class MenuSearchResponse(BaseModel):
    """搜索菜单响应schema"""
    code: int
    message: str
    data: MenuSearchListData


class MenuCreateResponse(BaseModel):
    """创建菜单响应schema"""
    code: int
    message: str
    data: MenuResponse


class MenuDetailResponse(BaseModel):
    """菜单详情响应schema"""
    code: int
    message: str
    data: MenuResponseWithDiningRule


class MenuUpdateResponse(BaseModel):
    """更新菜单响应schema"""
    code: int
    message: str
    data: MenuResponse


class MenuStatusUpdateResponse(BaseModel):
    """更新菜单状态响应schema"""
    code: int
    message: str
    data: MenuResponse


class MenuDeleteRequest(BaseModel):
    """删除菜单请求schema"""
    ids: List[int]


class MenuDeleteResponse(BaseModel):
    """删除菜单响应schema"""
    code: int
    message: str
    data: Dict[str, Any]


class MenuWithContentsDetailResponse(BaseModel):
    """菜单详情及内容响应schema"""
    code: int
    message: str
    data: MenuWithContentsResponse 