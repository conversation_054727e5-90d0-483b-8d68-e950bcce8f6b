from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, field_serializer, ConfigDict

from app.models.enum import Status
from app.models.pricing import PricingStrategyType, MemberLevel, PricingStrategyScope, UsageCycle
from app.schemas.common import CommonResponse


# PricingStrategy schemas
class PricingStrategyBase(BaseModel):
    name: str
    description: Optional[str] = None
    start_time: datetime
    end_time: datetime
    scope: PricingStrategyScope = PricingStrategyScope.PRODUCT
    is_mutual_exclusive: Optional[bool] = False
    status: Optional[Status] = Status.ACTIVE


class PricingStrategyCreate(PricingStrategyBase):
    pass


class PricingStrategyUpdate(PricingStrategyBase):
    name: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    scope: Optional[PricingStrategyScope] = PricingStrategyScope.PRODUCT
    status: Optional[Status] = None


class PricingStrategyResponse(PricingStrategyBase):
    id: int
    type: PricingStrategyType
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

    @field_serializer("created_at", "updated_at", "start_time", "end_time")
    def serialize_datetime(self, value: datetime) -> str:
        return value.strftime("%Y-%m-%d %H:%M:%S")


class PricingStrategyInDB(PricingStrategyBase):
    pass


class PricingStrategy(PricingStrategyInDB):
    pass


# DiscountStrategy schemas
class DiscountStrategyBase(PricingStrategyBase):
    discount_rate: float  # 折扣率，例如0.8表示8折
    min_amount: Optional[float] = None  # 最低消费金额
    max_discount: Optional[float] = None  # 最大优惠金额


class DiscountStrategyCreate(DiscountStrategyBase):
    pass


class DiscountStrategyUpdate(PricingStrategyUpdate):
    discount_rate: Optional[float] = None  # 折扣率，例如0.8表示8折
    min_amount: Optional[float] = None  # 最低消费金额
    max_discount: Optional[float] = None  # 最大优惠金额


class DiscountStrategyResponse(DiscountStrategyBase, PricingStrategyResponse):
    pass


class DiscountStrategyInDB(DiscountStrategyResponse):
    pass


class DiscountStrategy(DiscountStrategyInDB):
    pass


# FullReductionStrategy schemas
class FullReductionStrategyBase(PricingStrategyBase):
    full_amount: float  # 满减金额
    reduction_amount: float  # 优惠金额


class FullReductionStrategyCreate(FullReductionStrategyBase):
    pass


class FullReductionStrategyUpdate(PricingStrategyUpdate):
    full_amount: Optional[float] = None
    reduction_amount: Optional[float] = None


class FullReductionStrategyResponse(FullReductionStrategyBase, PricingStrategyResponse):
    pass


class FullReductionStrategyInDB(FullReductionStrategyResponse):
    pass


class FullReductionStrategy(FullReductionStrategyInDB):
    pass


# TimeLimitedStrategy schemas
class TimeLimitedStrategyBase(PricingStrategyBase):
    special_price: float  # 特价金额
    stock_limit: Optional[int] = None  # 库存限制


class TimeLimitedStrategyCreate(TimeLimitedStrategyBase):
    pass


class TimeLimitedStrategyUpdate(PricingStrategyUpdate):
    special_price: Optional[float] = None
    stock_limit: Optional[int] = None


class TimeLimitedStrategyResponse(TimeLimitedStrategyBase, PricingStrategyResponse):
    pass


class TimeLimitedStrategyInDB(TimeLimitedStrategyResponse):
    pass


class TimeLimitedStrategy(TimeLimitedStrategyInDB):
    pass


# MemberPriceStrategy schemas
class MemberPriceStrategyBase(PricingStrategyBase):
    member_level: str  # 会员等级
    price: float  # 会员价格


class MemberPriceStrategyCreate(MemberPriceStrategyBase):
    pass


class MemberPriceStrategyUpdate(PricingStrategyUpdate):
    member_level: MemberLevel = MemberLevel.BASIC
    price: Optional[float] = None


class MemberPriceStrategyResponse(MemberPriceStrategyBase, PricingStrategyResponse):
    pass


class MemberPriceStrategyInDB(MemberPriceStrategyResponse):
    pass


class MemberPriceStrategy(MemberPriceStrategyInDB):
    pass


# 定价策略和产品关联的Schema
class PricingStrategyAndProduct(BaseModel):
    """定价策略和产品的关联信息"""
    pricing_strategy_id: int
    product_ids: List[int]


# 添加获取绑定关系的Schema
class ProductByStrategyQuery(BaseModel):
    """查询与定价策略绑定的产品"""
    skip: int = 0
    limit: int = 100


class PricingSearchRequest(BaseModel):
    """定价策略搜索请求参数"""
    keyword: Optional[str] = None
    name: Optional[str] = None
    type: Optional[PricingStrategyType] = None
    status: Optional[Status] = None
    page: int = 1
    pageSize: int = 10


class PricingStatusUpdateRequest(BaseModel):
    """定价策略状态更新请求参数"""
    status: Status


class PricingStrategyListData(BaseModel):
    """产品列表响应模式"""
    total: int
    list: List[PricingStrategyResponse]

    model_config = ConfigDict(from_attributes=True)


class PricingStrategyListFormattedResponse(BaseModel):
    """格式化后的产品响应模式"""
    code: int
    message: str
    data: PricingStrategyListData

    model_config = ConfigDict(from_attributes=True)


# BundleStrategy schemas
class BundleStrategyProductRel(BaseModel):
    """捆绑策略商品关联信息"""
    product_id: int
    quantity: int

    model_config = ConfigDict(from_attributes=True)


class BundleStrategyBase(PricingStrategyBase):
    deduction: float  # 扣除金额
    usage_cycle: UsageCycle = UsageCycle.PER_ORDER  # 使用周期
    usage_limit: int = 1  # 使用限制
    need_rematch: bool = False  # 是否要重新匹配


class BundleStrategyCreate(BundleStrategyBase):
    products: List[BundleStrategyProductRel] = []  # 关联的商品列表


class BundleStrategyUpdate(PricingStrategyUpdate):
    deduction: Optional[float] = None
    usage_cycle: Optional[UsageCycle] = None
    usage_limit: Optional[int] = None
    need_rematch: Optional[bool] = None
    products: Optional[List[BundleStrategyProductRel]] = None


class BundleStrategyProductRelResponse(BundleStrategyProductRel):
    """捆绑策略商品关联响应信息"""
    id: int
    bundle_strategy_id: int

    model_config = ConfigDict(from_attributes=True)


class BundleStrategyResponse(BundleStrategyBase, PricingStrategyResponse):
    bundle_product_rels: List[BundleStrategyProductRelResponse] = []

    model_config = ConfigDict(from_attributes=True)


class BundleStrategyFormattedResponse(BaseModel):
    """格式化的捆绑策略响应模型"""
    name: str
    type: str
    description: Optional[str] = None
    start_time: str
    end_time: str
    scope: str
    status: int
    deduction: float
    usage_cycle: str
    usage_limit: int
    is_mutual_exclusive: bool
    need_rematch: bool
    products: List[BundleStrategyProductRel] = []

    model_config = ConfigDict(from_attributes=True)

    @classmethod
    def from_bundle_strategy(cls, strategy: BundleStrategyResponse) -> "BundleStrategyFormattedResponse":
        """从BundleStrategyResponse转换为格式化响应"""
        # 将bundle_product_rels转换为products格式
        products = [
            BundleStrategyProductRel(
                product_id=rel.product_id,
                quantity=rel.quantity
            )
            for rel in strategy.bundle_product_rels
        ]
        
        # 将datetime对象转换为字符串
        start_time_str = strategy.start_time
        end_time_str = strategy.end_time
        
        # 如果是datetime对象，转换为字符串
        if hasattr(strategy.start_time, 'strftime'):
            start_time_str = strategy.start_time.strftime("%Y-%m-%d %H:%M:%S")
        if hasattr(strategy.end_time, 'strftime'):
            end_time_str = strategy.end_time.strftime("%Y-%m-%d %H:%M:%S")
        
        return cls(
            name=strategy.name,
            type=strategy.type.value,
            description=strategy.description or "",
            start_time=start_time_str,
            end_time=end_time_str,
            scope=strategy.scope.value,
            status=strategy.status.value,
            deduction=strategy.deduction,
            usage_cycle=strategy.usage_cycle.value,
            usage_limit=strategy.usage_limit,
            is_mutual_exclusive=strategy.is_mutual_exclusive,
            need_rematch=strategy.need_rematch,
            products=products
        )


class BundleStrategyInDB(BundleStrategyResponse):
    pass


class BundleStrategy(BundleStrategyInDB):
    pass
