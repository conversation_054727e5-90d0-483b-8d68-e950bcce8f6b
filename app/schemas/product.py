from datetime import datetime
from typing import Optional, List, Any, Dict

from pydantic import BaseModel, ConfigDict, field_validator, field_serializer

from app.models.enum import Status
from app.models.product import ProductType, MealType
from app.schemas.tag import Tag
from app.schemas.content import Content, ContentResponse
from app.schemas.rule import RuleResponse
from app.schemas.pricing import PricingStrategyResponse


# Product schemas
class ProductBase(BaseModel):
    name: str
    price: float
    description: Optional[str] = None
    image: Optional[str] = None
    stock: int = 0
    status: Optional[Status] = Status.ACTIVE
    meal_type: Optional[MealType] = MealType.BUFFET
    listed_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class ProductCreate(ProductBase):
    pass


class ProductUpdate(BaseModel):
    name: Optional[str] = None
    price: Optional[float] = None
    description: Optional[str] = None
    image: Optional[str] = None
    stock: Optional[int] = None
    status: Optional[Status] = None
    meal_type: Optional[MealType] = None
    listed_at: Optional[datetime] = None


class ProductResponse(ProductBase):
    type: ProductType
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

    @field_serializer("created_at", "updated_at", "listed_at")
    def serialize_datetime(self, value: datetime) -> str:
        return value.strftime("%Y-%m-%d %H:%M:%S")


class ProductInDB(ProductResponse):
    pass


class Product(ProductInDB):
    pass


class ProductWithRelations(Product):
    contents: List[Content] = []
    tags: List[Tag] = []


# 产品搜索请求和响应模式
class ProductSearchRequest(BaseModel):
    """产品搜索请求模式"""
    keyword: Optional[str] = None
    name: Optional[str] = None
    product_type: Optional[ProductType] = None
    status: Optional[Status] = None
    page: int = 1
    pageSize: int = 10


class ProductStatusUpdateRequest(BaseModel):
    status: Status


class ProductListData(BaseModel):
    """产品列表响应模式"""
    total: int
    list: List[ProductResponse]

    model_config = ConfigDict(from_attributes=True)


class ProductListFormattedResponse(BaseModel):
    """格式化后的产品响应模式"""
    code: int
    message: str
    data: ProductListData
    model_config = ConfigDict(from_attributes=True)


# DirectSaleProduct schemas
class DirectSaleProductBase(ProductBase):
    shipping_fee: Optional[float] = 0


class DirectSaleProductCreate(DirectSaleProductBase):
    pass


class DirectSaleProductUpdate(DirectSaleProductBase):
    pass


class DirectSaleProductResponse(ProductResponse, DirectSaleProductBase):
    pass


class DirectSaleProductInDB(DirectSaleProductResponse):
    pass


class DirectSaleProduct(DirectSaleProductInDB):
    pass


class DirectSaleProductWithRelations(DirectSaleProduct):
    contents: List[Content] = []
    tags: List[Tag] = []


# ReservationProduct schemas
class ReservationProductBase(ProductBase):
    reservation_fee: Optional[float] = None
    max_reservations: Optional[int] = None
    reservation_deadline: Optional[datetime] = None
    cancellation_deadline: Optional[datetime] = None
    is_approval_required: Optional[bool] = False


class ReservationProductCreate(ReservationProductBase):
    pass


class ReservationProductUpdate(ReservationProductBase):
    pass


class ReservationProductResponse(ProductResponse, ReservationProductBase):
    pass


class ReservationProductInDB(ProductInDB, ReservationProductResponse):
    pass


class ReservationProduct(ReservationProductInDB):
    pass


class ReservationProductWithRelations(ReservationProduct):
    contents: List[Content] = []
    tags: List[Tag] = []


# 产品和定价策略关联的Schema
class ProductAndPricingStrategy(BaseModel):
    """产品和定价策略的关联信息"""
    product_id: int
    pricing_strategy_ids: List[int]


# 产品和分类关联的Schema
class ProductAndCategory(BaseModel):
    """产品和分类的关联信息"""
    product_id: int
    category_ids: List[int]


class StrategyByProductQuery(BaseModel):
    """查询与产品绑定的定价策略"""
    skip: int = 0
    limit: int = 100


class ProdContentsSearchReq(BaseModel):
    """产品搜索请求模式"""
    product_id: int
    keyword: Optional[str] = None
    name: Optional[str] = None
    status: Optional[Status] = None
    page: int = 1
    pageSize: int = 10


class ProdContentsSearchData(BaseModel):
    """产品搜索响应模式"""
    total: int
    list: List[ContentResponse]


class ProdContentsSearchResp(BaseModel):
    """产品搜索响应模式"""
    code: int
    message: str
    data: ProdContentsSearchData
    model_config = ConfigDict(from_attributes=True)


class ProdPricingStrategiesSearchReq(BaseModel):
    """产品搜索请求模式"""
    product_id: int
    keyword: Optional[str] = None
    name: Optional[str] = None
    status: Optional[Status] = None
    page: int = 1
    pageSize: int = 10


class ProdPricingStrategiesSearchData(BaseModel):
    """产品搜索响应模式"""
    total: int
    list: List[PricingStrategyResponse]


class ProdPricingStrategiesSearchResp(BaseModel):
    """产品搜索响应模式"""
    code: int
    message: str
    data: ProdPricingStrategiesSearchData
    model_config = ConfigDict(from_attributes=True)


class ProdRulesSearchReq(BaseModel):
    """产品搜索请求模式"""
    product_id: int
    keyword: Optional[str] = None
    name: Optional[str] = None
    status: Optional[Status] = None
    page: int = 1
    pageSize: int = 10


class ProdRulesSearchData(BaseModel):
    """产品搜索响应模式"""
    total: int
    list: List[RuleResponse]


class ProdRulesSearchResp(BaseModel):
    """产品搜索响应模式"""
    code: int
    message: str
    data: ProdRulesSearchData
    model_config = ConfigDict(from_attributes=True)


class DiningRuleInfo(BaseModel):
    """餐厅预订规则基本信息"""
    rule_id: int
    rule_name: str
    alias: Optional[str] = None
    dining_start_time_cron_str: Optional[str] = None
    dining_end_time_cron_str: Optional[str] = None
    verify_start_time_cron_str: Optional[str] = None
    verify_end_time_cron_str: Optional[str] = None
    order_deadline: Optional[int] = None
    cancellation_deadline: Optional[int] = None
    is_auto_verify: bool = False
    status: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    model_config = ConfigDict(from_attributes=True)
    
    @field_serializer("created_at", "updated_at")
    def serialize_datetime(self, value: datetime) -> Optional[str]:
        if value:
            return value.strftime("%Y-%m-%d %H:%M:%S")
        return None


class ProductWithDiningRules(BaseModel):
    """包含产品及其关联的餐厅预订规则的响应模型"""
    product_id: int
    product_name: str
    product_price: float
    product_description: Optional[str] = None
    product_status: Optional[str] = None
    dining_rules: List[DiningRuleInfo] = []
    
    model_config = ConfigDict(from_attributes=True)


class ProductWithDiningRulesData(BaseModel):
    """包含多个产品及其餐厅预订规则的数据模型"""
    total: int
    products: List[ProductWithDiningRules] = []
    
    model_config = ConfigDict(from_attributes=True)


class ProductWithDiningRulesResponse(BaseModel):
    """产品及餐厅预订规则的API响应"""
    code: int
    message: str
    data: Optional[ProductWithDiningRulesData] = None
    
    model_config = ConfigDict(from_attributes=True)


class ProductNameSearchItem(BaseModel):
    """产品名称搜索项"""
    name: str
    id: int

class ProductCategoryBase(BaseModel):
    name: str
    description: Optional[str] = None
    image: Optional[str] = None
    sort_order: Optional[int] = 0
    parent_id: Optional[int] = None


class ProductNameSearchResponse(BaseModel):
    """产品名称搜索响应"""
    code: int
    message: str
    data: List[ProductNameSearchItem]


class ProductCategoryCreate(ProductCategoryBase):
    status: Status = Status.ACTIVE


class ProductCategoryUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    image: Optional[str] = None
    sort_order: Optional[int] = None
    parent_id: Optional[int] = None
    status: Optional[Status] = None


class ProductCategoryInDB(ProductCategoryBase):
    id: int
    status: Status
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class ProductCategoryResponse(BaseModel):
    id: int
    name: str
    description: Optional[str] = None
    image: Optional[str] = None
    sort_order: int
    parent_id: Optional[int] = None
    status: Status
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True
    }

    @field_serializer("created_at", "updated_at")
    def serialize_datetime(self, value: datetime) -> str:
        return value.strftime("%Y-%m-%d %H:%M:%S")


class ProductCategorySearchRequest(BaseModel):
    """产品分类搜索请求模式"""
    keyword: Optional[str] = None
    name: Optional[str] = None
    status: Optional[Status] = None
    parent_id: Optional[int] = None
    page: int = 1
    pageSize: int = 10


class ProductCategoryListData(BaseModel):
    """产品分类列表响应模式"""
    total: int
    list: List[ProductCategoryResponse]

    model_config = ConfigDict(from_attributes=True)


class ProductCategoryListResponse(BaseModel):
    """格式化后的产品分类列表响应模式"""
    code: int
    message: str
    data: ProductCategoryListData
    
    model_config = ConfigDict(from_attributes=True)


class ProductCategoryTreeNode(BaseModel):
    """产品分类树节点"""
    id: int
    name: str
    description: Optional[str] = None
    image: Optional[str] = None
    sort_order: int
    parent_id: Optional[int] = None
    status: Status
    created_at: datetime
    updated_at: datetime
    children: List['ProductCategoryTreeNode'] = []

    model_config = ConfigDict(from_attributes=True)

    @field_serializer("created_at", "updated_at")
    def serialize_datetime(self, value: datetime) -> str:
        return value.strftime("%Y-%m-%d %H:%M:%S")


class ProductCategoryTreeResponse(BaseModel):
    """产品分类树形结构响应"""
    code: int
    message: str
    data: List[ProductCategoryTreeNode]
    
    model_config = ConfigDict(from_attributes=True)


class ProductCategoryStatusUpdateRequest(BaseModel):
    """产品分类状态更新请求"""
    status: Status
