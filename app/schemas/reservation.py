from datetime import datetime
from typing import Optional, List, Dict, Any

from pydantic import BaseModel, ConfigDict, field_serializer

from app.models.reservation import ReservationStatus, ReservationType, ReservationScope


# ReservationRequest schemas
class ReservationRequestBase(BaseModel):
    orders_id: Optional[int] = None
    order_item_id: Optional[int] = None
    user_id: int
    product_id: Optional[int] = None
    rule_id: int
    rule_item_id: int
    status: Optional[ReservationStatus] = ReservationStatus.PENDING
    reservation_period: Optional[str] = None
    dining_start_time: Optional[datetime] = None
    dining_end_time: Optional[datetime] = None
    reservation_time: Optional[datetime] = None
    verification_code: Optional[str] = None
    type: Optional[ReservationType] = ReservationType.RESERVATION
    scope: Optional[ReservationScope] = ReservationScope.PRODUCT


class ReservationRequestCreate(ReservationRequestBase):
    pass


class ReservationRequestUpdate(BaseModel):
    orders_id: Optional[int] = None
    order_item_id: Optional[int] = None
    product_id: Optional[int] = None
    status: Optional[ReservationStatus] = None
    reservation_period: Optional[str] = None
    dining_start_time: Optional[datetime] = None
    dining_end_time: Optional[datetime] = None
    reservation_time: Optional[datetime] = None
    verification_code: Optional[str] = None
    type: Optional[ReservationType] = None
    scope: Optional[ReservationScope] = None


class ReservationRequestResponse(ReservationRequestBase):
    id: int
    created_at: datetime
    updated_at: datetime

    @field_serializer("created_at", "updated_at", "reservation_time", "dining_start_time", "dining_end_time")
    def serialize_datetime(self, value: datetime) -> str:
        if value is None:
            return None
        return value.strftime("%Y-%m-%d %H:%M:%S")


class ReservationRequestInDB(ReservationRequestResponse):
    model_config = ConfigDict(from_attributes=True)


class ReservationRequest(ReservationRequestInDB):
    """用于API响应的预订请求模型"""
    pass


# BuffetReservationRequest schemas
class BuffetReservationRequestBase(ReservationRequestBase):
    """自助餐预订请求基础模型"""
    type: ReservationType = ReservationType.BUFFET_RESERVATION


class BuffetReservationRequestCreate(BuffetReservationRequestBase):
    """创建自助餐预订请求"""
    pass


class BuffetReservationRequestUpdate(ReservationRequestUpdate):
    """更新自助餐预订请求"""
    pass


class BuffetReservationRequestResponse(BuffetReservationRequestBase):
    """自助餐预订请求响应"""
    id: int
    created_at: datetime
    updated_at: datetime

    @field_serializer("created_at", "updated_at", "reservation_time", "dining_start_time", "dining_end_time")
    def serialize_datetime(self, value: datetime) -> str:
        if value is None:
            return None
        return value.strftime("%Y-%m-%d %H:%M:%S")


class BuffetReservationRequestInDB(BuffetReservationRequestResponse):
    model_config = ConfigDict(from_attributes=True)


class BuffetReservationRequest(BuffetReservationRequestInDB):
    """用于API响应的自助餐预订请求模型"""
    pass


# BizReservationRequest schemas
class BizReservationRequestBase(ReservationRequestBase):
    """商务餐预订请求基础模型"""
    type: ReservationType = ReservationType.BIZ_DINING_RESERVATION
    name: Optional[str] = ""
    phone: Optional[str] = ""
    persons: Optional[int] = 0
    remark: Optional[str] = ""


class BizReservationRequestCreate(BizReservationRequestBase):
    """创建商务餐预订请求"""
    pass


class BizReservationRequestUpdate(ReservationRequestUpdate):
    """更新商务餐预订请求"""
    name: Optional[str] = None
    phone: Optional[str] = None
    persons: Optional[int] = None
    remark: Optional[str] = None


class BizReservationRequestResponse(BizReservationRequestBase):
    """商务餐预订请求响应"""
    id: int
    created_at: datetime
    updated_at: datetime

    @field_serializer("created_at", "updated_at", "reservation_time", "dining_start_time", "dining_end_time")
    def serialize_datetime(self, value: datetime) -> str:
        if value is None:
            return None
        return value.strftime("%Y-%m-%d %H:%M:%S")


class BizReservationRequestInDB(BizReservationRequestResponse):
    model_config = ConfigDict(from_attributes=True)


class BizReservationRequest(BizReservationRequestInDB):
    """用于API响应的商务餐预订请求模型"""
    pass


# 详细响应模型
class UserInfo(BaseModel):
    id: int
    username: str

    model_config = ConfigDict(from_attributes=True)


class ProductInfo(BaseModel):
    id: int
    name: str
    price: float

    model_config = ConfigDict(from_attributes=True)


class RuleInfo(BaseModel):
    id: int
    name: str
    status: str

    model_config = ConfigDict(from_attributes=True)


class OrderItemInfo(BaseModel):
    id: int
    quantity: int
    price: float

    model_config = ConfigDict(from_attributes=True)


class ReservationRequestDetail(ReservationRequest):
    """包含关联信息的预订请求详细响应"""
    user: Optional[UserInfo] = None
    product: Optional[ProductInfo] = None
    rule: Optional[RuleInfo] = None
    order_item: Optional[OrderItemInfo] = None


# 操作响应模型
class ReservationActionResponse(BaseModel):
    """预订操作响应"""
    success: bool
    message: str
    reservation: Optional[ReservationRequest] = None


# 预订报表相关 Schema
class ReservationReportItem(BaseModel):
    """预订报表项"""
    id: int
    reservation_period: str
    reservation_time: str
    created_at: str
    updated_at: str
    status: str

    user_id: int
    username: str
    real_name: str
    nick_name: str
    phone: str

    product_id: int
    product_name: str
    product_price: float

    order_item_id: int
    quantity: int
    price: float
    subtotal: float
    final_price: float
    payable_amount: float
    payment_enterprise: str


class ReservationReportData(BaseModel):
    """预订报表响应"""
    list: List[ReservationReportItem]
    total: int
    model_config = ConfigDict(from_attributes=True)


class ReservationReportResponse(BaseModel):
    """预订报表响应"""
    code: int
    message: str
    data: ReservationReportData
    model_config = ConfigDict(from_attributes=True)


class ReservationReportRequest(BaseModel):
    """预订报表请求参数"""
    dining_start_time: Optional[datetime] = None
    dining_end_time: Optional[datetime] = None
    reservation_start_time: Optional[datetime] = None
    reservation_end_time: Optional[datetime] = None
    reservation_period: Optional[str] = None
    phone: Optional[str] = None
    real_name: Optional[str] = None
    nick_name: Optional[str] = None
    status: Optional[List[str]] = None
    payment_enterprise: Optional[str] = None
    page: Optional[int] = None
    page_size: Optional[int] = None


# 预订订单报表相关 Schema
class NewOrderItemInfo(BaseModel):
    """订单项信息"""
    order_item_id: int
    quantity: int
    price: float
    subtotal: float
    final_price: float
    payable_amount: float
    pricing_remark: str
    product_name: str
    product_price: float
    payment_enterprise: str


class ReservationOrderReportItem(BaseModel):
    """预订订单报表项"""
    # reservation_requests 信息
    id: int
    reservation_period: str
    reservation_time: str
    status: str
    created_at: str
    updated_at: str
    user_id: int
    product_id: int
    orders_id: int
    order_item_id: int
    dining_start_time: str
    dining_end_time: str

    # biz_reservation_requests 信息
    contact_name: str
    contact_phone: str
    persons: int
    remark: str

    # orders 信息
    order_no: str
    total_amount: float
    payable_amount: float
    actual_amount_paid: float
    payment_method: str
    payment_status: str
    payment_time: str
    order_created_at: str
    order_updated_at: str
    calculate_amount: float

    # 用户信息
    username: str
    real_name: str
    nick_name: str
    phone: str

    # 订单项信息
    order_items: List[NewOrderItemInfo]


class ReservationOrderReportData(BaseModel):
    """预订订单报表响应"""
    list: List[ReservationOrderReportItem]
    total: int
    model_config = ConfigDict(from_attributes=True)


class ReservationOrderReportResponse(BaseModel):
    """预订订单报表响应"""
    code: int
    message: str
    data: ReservationOrderReportData
    model_config = ConfigDict(from_attributes=True)


class ReservationOrderReportRequest(BaseModel):
    """预订订单报表请求参数"""
    dining_start_time: Optional[datetime] = None
    dining_end_time: Optional[datetime] = None
    reservation_start_time: Optional[datetime] = None
    reservation_end_time: Optional[datetime] = None
    reservation_period: Optional[str] = None
    phone: Optional[str] = None
    real_name: Optional[str] = None
    nick_name: Optional[str] = None
    status: Optional[List[str]] = None
    payment_enterprise: Optional[str] = None
    page: Optional[int] = None
    page_size: Optional[int] = None


class OrderActionPermission(BaseModel):
    """订单操作权限"""
    can_show_order_detail: bool = False  # 是否显示订单按钮
    can_edit: bool = False  # 是否显示加菜/详情按钮
    can_show_qr: bool = False  # 是否显示二维码按钮
    can_cancel: bool = False  # 是否显示取消按钮
    can_checkout: bool = False  # 是否显示结账按钮


class OrderDisplayInfo(BaseModel):
    """订单显示信息"""
    status_text: str  # 状态显示文本
    status_class: str  # 状态样式类名
    cancel_deadline_text: str  # 取消截止时间提示
    action_permissions: OrderActionPermission  # 操作权限


class ReservationListItem(BaseModel):
    """预约列表项"""
    # 基本信息
    order_id: int
    order_item_id: int
    title: str
    person: str
    date: str
    people: int
    price: float
    reservation_status: str

    # 显示信息
    display_info: OrderDisplayInfo

    # 商务餐特有信息
    biz_info: Optional[Dict[str, Any]] = None
