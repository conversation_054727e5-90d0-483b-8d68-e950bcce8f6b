from datetime import datetime
from typing import Optional, List, Dict, Any

from pydantic import BaseModel, ConfigDict, field_serializer

from app.models.enum import Status
from app.models.rule import RuleType, RuleScope, RuleOrderType


# RuleItem schemas
class RuleItemBase(BaseModel):
    """规则项基础schema"""
    name: str
    alias: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    quantity: Optional[int] = None
    start_time_cron_str: Optional[str] = None
    end_time_cron_str: Optional[str] = None
    order_deadline: Optional[int] = None
    cancellation_deadline: Optional[int] = None
    order: Optional[int] = 1
    generated_count: Optional[int] = 1
    allowed_operations: Optional[str] = None
    forbidden_operations: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class RuleItemCreate(RuleItemBase):
    """创建规则项的schema"""
    rule_id: int


class RuleItemUpdate(BaseModel):
    """更新规则项的schema"""
    name: Optional[str] = None
    alias: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    quantity: Optional[int] = None
    start_time_cron_str: Optional[str] = None
    end_time_cron_str: Optional[str] = None
    order_deadline: Optional[int] = None
    cancellation_deadline: Optional[int] = None
    order: Optional[int] = None
    generated_count: Optional[int] = None
    allowed_operations: Optional[str] = None
    forbidden_operations: Optional[str] = None


class RuleItemResponse(RuleItemBase):
    """规则项返回schema"""
    id: int
    rule_id: int

    @field_serializer("start_time", "end_time")
    def serialize_datetime(self, value: datetime) -> Optional[str]:
        if value is None:
            return None
        return value.strftime("%Y-%m-%d %H:%M:%S")

    model_config = ConfigDict(from_attributes=True)


class RuleItemInDB(RuleItemResponse):
    pass


class RuleItem(RuleItemInDB):
    pass


# Rule schemas
class RuleBase(BaseModel):
    """规则基础schema"""
    name: str
    status: Optional[Status] = Status.ACTIVE
    type: Optional[RuleType] = RuleType.RESERVATION
    scope: Optional[RuleScope] = RuleScope.PRODUCT
    order_type: Optional[RuleOrderType] = RuleOrderType.NONE

    model_config = ConfigDict(from_attributes=True)


class RuleCreate(RuleBase):
    """创建规则的schema"""
    pass


class RuleUpdate(RuleBase):
    """更新规则的schema"""
    name: Optional[str] = None
    status: Optional[Status] = None
    type: Optional[RuleType] = None
    scope: Optional[RuleScope] = None
    order_type: Optional[RuleOrderType] = None

    pass


class RuleResponse(RuleBase):
    """规则返回schema"""
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

    @field_serializer("created_at", "updated_at")
    def serialize_datetime(self, value: datetime) -> Optional[str]:
        if value is None:
            return None
        return value.strftime("%Y-%m-%d %H:%M:%S")


class RuleInDB(RuleResponse):
    pass


class Rule(RuleInDB):
    pass


class RuleWithItems(RuleResponse):
    """带规则项的规则返回schema"""
    rule_items: List[RuleItemResponse] = []


# 产品和规则关联的Schema
class ProductAndRule(BaseModel):
    """产品和规则的关联信息"""
    product_id: int
    rule_ids: List[int]


class RuleByProductQuery(BaseModel):
    """查询与产品绑定的规则"""
    skip: int = 0
    limit: int = 100


class ProductByRuleQuery(BaseModel):
    """查询与规则绑定的产品"""
    skip: int = 0
    limit: int = 100


# 规则状态更新请求
class RuleStatusUpdateRequest(BaseModel):
    """规则状态更新请求"""
    status: Status


# 添加规则搜索请求schema
class RuleSearchRequest(BaseModel):
    """规则搜索请求参数"""
    keyword: Optional[str] = None
    name: Optional[str] = None
    status: Optional[Status] = None
    page: int = 1
    pageSize: int = 10


class RuleListData(BaseModel):
    """产品列表响应模式"""
    total: int
    list: List[RuleResponse]

    model_config = ConfigDict(from_attributes=True)


# 规则列表格式化响应
class RuleListFormattedResponse(BaseModel):
    """规则列表格式化响应"""
    code: int
    message: str
    data: RuleListData

    model_config = ConfigDict(from_attributes=True)


# 餐厅预订规则相关Schema
class DiningReservationRuleBase(RuleBase):
    """餐厅预订规则基础Schema"""
    type: Optional[RuleType] = RuleType.DINING_RESERVATION
    alias: Optional[str] = None
    dining_start_time_cron_str: Optional[str] = None
    dining_end_time_cron_str: Optional[str] = None
    verify_start_time_cron_str: Optional[str] = None
    verify_end_time_cron_str: Optional[str] = None
    order_deadline: Optional[int] = 0
    cancellation_deadline: Optional[int] = 0
    is_auto_verify: Optional[bool] = False
    generated_count: Optional[int] = 1
    quantity: Optional[int] = None


class DiningReservationRuleResponse(DiningReservationRuleBase, RuleResponse):
    """餐厅预订规则响应Schema"""
    pass


class DiningReservationRuleWithItems(DiningReservationRuleResponse):
    """带规则项的餐厅预订规则响应Schema"""
    rule_items: List[RuleItemResponse] = []


class DiningReservationRuleCreate(DiningReservationRuleBase, RuleCreate):
    """创建餐厅预订规则Schema"""
    pass


class DiningReservationRuleUpdate(DiningReservationRuleBase, RuleUpdate):
    """更新餐厅预订规则Schema"""
    name: Optional[str] = None
    status: Optional[Status] = None
    alias: Optional[str] = None
    dining_start_time_cron_str: Optional[str] = None
    dining_end_time_cron_str: Optional[str] = None
    verify_start_time_cron_str: Optional[str] = None
    verify_end_time_cron_str: Optional[str] = None
    order_deadline: Optional[int] = None
    cancellation_deadline: Optional[int] = None
    is_auto_verify: Optional[bool] = None
    generated_count: Optional[int] = None
    quantity: Optional[int] = None
