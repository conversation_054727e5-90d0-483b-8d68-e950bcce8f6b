from typing import List

from pydantic import BaseModel, ConfigDict


# Tag schemas
class TagBase(BaseModel):
    name: str


class TagCreate(TagBase):
    pass


class TagUpdate(TagBase):
    pass


class TagResponse(TagBase):
    id: int


class TagInDB(TagResponse):
    model_config = ConfigDict(from_attributes=True)


class Tag(TagInDB):
    pass


class MultiProductBinding(BaseModel):
    product_ids: List[int]
