from datetime import datetime, date
from typing import Optional, List, Dict, Any

from pydantic import BaseModel, ConfigDict

from app.models.user import UserType
from app.models.enum import Status


# User schemas
class UserBase(BaseModel):
    username: str
    status: Optional[Status] = None


class UserCreate(UserBase):
    pass


class UserUpdate(BaseModel):
    status: Optional[Status] = None


class UserResponse(UserBase):
    """用于API响应的用户模型"""
    id: int
    register_time: datetime
    created_at: datetime
    updated_at: datetime


class UserInDB(UserResponse):
    password: str
    type: UserType

    model_config = ConfigDict(from_attributes=True)


class User(UserInDB):
    pass


class PersonalUserBase(UserBase):
    phone: str
    email: Optional[str] = None
    address: Optional[str] = None
    real_name: Optional[str] = None
    id_card: Optional[str] = None
    status: Optional[Status] = None


class PersonalUserCreate(PersonalUserBase):
    password: Optional[str] = None


class PersonalUserUpdate(UserUpdate):
    password: Optional[str] = None
    email: Optional[str] = None
    address: Optional[str] = None
    real_name: Optional[str] = None
    id_card: Optional[str] = None


class PersonalUserResponse(UserResponse, PersonalUserBase):
    pass


class PersonalUserInDB(UserInDB, PersonalUserBase):
    wechat_id: Optional[str] = None
    password: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class PersonalUser(PersonalUserInDB):
    pass


class PersonalUserCreateWX(PersonalUserCreate):
    wechat_id: Optional[str] = None
    nickname: Optional[str] = None


class PersonalUserUpdateWX(PersonalUserUpdate):
    wechat_id: Optional[str] = None
    nickname: Optional[str] = None
    avatar_url: Optional[str] = None


class PersonalUserMsgStatusUpdate(PersonalUserUpdate):
    msg_status: bool


# Enterprise schemas
class EnterpriseBase(UserBase):
    company_name: str
    business_license: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    address: Optional[str] = None


class EnterpriseCreate(EnterpriseBase):
    pass


class EnterpriseUpdate(UserUpdate):
    company_name: Optional[str] = None
    business_license: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    address: Optional[str] = None


class EnterpriseResponse(UserResponse, EnterpriseBase):
    pass


class EnterpriseInDB(UserInDB, EnterpriseBase):
    model_config = ConfigDict(from_attributes=True)


class Enterprise(EnterpriseInDB):
    pass


class PersonalUserDetailData(PersonalUserResponse):
    """包含企业信息的个人用户详细响应"""
    enterprises: List[EnterpriseResponse] = []


class PersonalUserDetailFormattedResponse(BaseModel):
    """包含企业信息的个人用户详细响应"""
    code: int
    message: str
    data: PersonalUserDetailData


class PersonalUserSearchRequest(BaseModel):
    keyword: Optional[str] = None
    name: Optional[str] = None
    phone: Optional[str] = None
    status: Optional[Status] = None
    page: int = 1
    pageSize: int = 10


class PersonalUserListData(BaseModel):
    total: int
    list: List[PersonalUserResponse]


class PersonalUserListResponse(BaseModel):
    code: int
    message: str
    data: PersonalUserListData


class PersonalUserSearchResponse(BaseModel):
    code: int
    message: str
    data: PersonalUserListData


class PersonalUserCreateResponse(BaseModel):
    code: int
    message: str
    data: PersonalUserResponse


class PersonalUserUpdateResponse(BaseModel):
    code: int
    message: str
    data: PersonalUserUpdate


class PersonalUserDeleteRequest(BaseModel):
    ids: list[int] = []


class PersonalUserBatchStatusUpdate(BaseModel):
    user_ids: List[int]
    status: Status


class PersonalUserCreateWX(PersonalUserCreate):
    wechat_id: Optional[str] = None
    nickname: Optional[str] = None


class PersonalUserUpdateWX(PersonalUserUpdate):
    wechat_id: Optional[str] = None
    nickname: Optional[str] = None
    avatar_url: Optional[str] = None


class PersonalUserMsgStatusUpdate(PersonalUserUpdate):
    msg_status: bool


class EnterpriseDetailFormattedResponse(BaseModel):
    """企业详细响应"""
    code: int
    message: str
    data: EnterpriseResponse


class EnterpriseWithUsers(Enterprise):
    users: List[PersonalUserResponse] = []


class EnterpriseDetailWithUsersResponse(BaseModel):
    """包含用户信息的企业详细响应"""
    code: int
    message: str
    data: EnterpriseWithUsers


class EnterpriseSearchRequest(BaseModel):
    keyword: Optional[str] = None
    name: Optional[str] = None
    company_name: Optional[str] = None
    phone: Optional[str] = None
    status: Optional[Status] = None
    page: int = 1
    pageSize: int = 10


class EnterpriseListData(BaseModel):
    total: int
    list: List[EnterpriseResponse]


class EnterpriseListResponse(BaseModel):
    code: int
    message: str
    data: EnterpriseListData


class EnterpriseSearchResponse(EnterpriseListResponse):
    pass


class EnterpriseCreateResponse(BaseModel):
    code: int
    message: str
    data: EnterpriseResponse


class EnterpriseUpdateData(EnterpriseUpdate):
    id: int
    updated_at: datetime
    created_at: datetime


class EnterpriseUpdateResponse(BaseModel):
    code: int
    message: str
    data: EnterpriseUpdateData


class EnterpriseBatchStatusUpdate(BaseModel):
    enterprise_ids: List[int]
    status: Status


# EnterpriseUserRelation schemas
class EnterpriseUserRelationBase(BaseModel):
    enterprise_id: int
    personal_user_id: int
    is_admin: bool = False
    relation_status: Optional[Status] = Status.ACTIVE
    remark: Optional[str] = None


class EnterpriseUserRelationCreate(EnterpriseUserRelationBase):
    password: Optional[str] = None


class EnterpriseUserRelationUpdate(BaseModel):
    password: Optional[str] = None
    is_admin: Optional[bool] = None
    relation_status: Optional[Status] = None
    remark: Optional[str] = None


class EnterpriseUserRelationResponse(EnterpriseUserRelationBase):
    """用于API响应的企业用户关联模型"""
    id: int
    created_at: datetime
    updated_at: datetime


class EnterpriseUserCreateRelationResponse(BaseModel):
    """用于创建企业用户关联的响应模型"""
    code: int
    message: str
    data: EnterpriseUserRelationResponse


class EnterpriseUserGetRelationResponse(EnterpriseUserCreateRelationResponse):
    """用于更新企业用户关联的响应模型"""
    pass


class EnterpriseUserUpdateRelationResponse(EnterpriseUserCreateRelationResponse):
    """用于更新企业用户关联的响应模型"""
    pass


class EnterpriseUserRelationInDB(EnterpriseUserRelationResponse):
    password: Optional[str] = None
    model_config = ConfigDict(from_attributes=True)


class EnterpriseUserRelation(EnterpriseUserRelationInDB):
    pass


# 关联关系Schema
class PersonalUserWithEnterprises(PersonalUser):
    enterprises: List[EnterpriseResponse] = []


class EnterpriseWithUsers(Enterprise):
    users: List[PersonalUserResponse] = []


class EnterpriseUserRelationWithDetail(EnterpriseUserRelation):
    enterprise: EnterpriseResponse
    user: PersonalUserResponse


# 详细响应模型
class PersonalUserDetailResponse(PersonalUserResponse):
    """包含企业信息的个人用户详细响应"""
    enterprises: List[EnterpriseResponse] = []


class EnterpriseDetailResponse(EnterpriseResponse):
    """包含用户信息的企业详细响应"""
    users: List[PersonalUserResponse] = []


# 用户登录请求和响应
class UserLoginRequest(BaseModel):
    phone: str
    password: str


class UserLoginResponse(BaseModel):
    code: int = 200
    msg: str = "用户登录成功"
    data: Optional[Dict[str, Any]] = None


class UserNameSearchItem(BaseModel):
    """用户名称搜索项"""
    username: str
    id: int
    real_name: Optional[str] = None


class UserNameSearchResponse(BaseModel):
    """用户名称搜索响应"""
    code: int
    message: str
    data: List[UserNameSearchItem]
