from datetime import datetime
from typing import Optional, List, Any, Dict

from pydantic import BaseModel, ConfigDict, field_serializer

from app.models import PaymentMethod
from app.models.enum import Status
from app.schemas.account import UserTransactionListResponse, UserTransactionListData, UserTransactionItem


class AccountSearchQuery(BaseModel):
    """账户搜索查询参数"""
    keyword: Optional[str] = None
    name: Optional[str] = None
    phone: Optional[str] = None
    status: Optional[Status] = None
    skip: int = 0
    limit: int = 100


class PersonalUserWithBalance(BaseModel):
    """包含账户余额信息的个人用户"""
    id: int
    username: str
    register_time: datetime
    status: Status
    type: str
    created_at: datetime
    updated_at: datetime
    phone: str
    wechat_id: Optional[str] = None
    email: Optional[str] = None
    address: Optional[str] = None
    real_name: Optional[str] = None
    id_card: Optional[str] = None
    nickname: Optional[str] = None
    avatar_url: Optional[str] = None
    gender: Optional[int] = None
    country: Optional[str] = None
    province: Optional[str] = None
    city: Optional[str] = None
    msg_status: Optional[int] = None

    # 账户余额字段
    regular_balance: float
    gift_balance: float
    gift_amount: float

    model_config = ConfigDict(from_attributes=True)

    @field_serializer("register_time", "created_at", "updated_at")
    def serialize_datetime(self, value: datetime) -> str:
        if value:
            return value.strftime("%Y-%m-%d %H:%M:%S")
        return None


class AccountSearchListData(BaseModel):
    """账户搜索结果列表数据"""
    total: int
    list: List[PersonalUserWithBalance]

    model_config = ConfigDict(from_attributes=True)


class AccountSearchResponse(BaseModel):
    """账户搜索结果响应"""
    code: int = 200
    message: str = "获取用户账户信息成功"
    data: AccountSearchListData

    model_config = ConfigDict(from_attributes=True)


class PersonalUserAccountInfo(BaseModel):
    """用户账户信息"""
    id: int
    username: str
    phone: str
    real_name: Optional[str] = None
    email: Optional[str] = None
    address: Optional[str] = None
    wechat_id: Optional[str] = None
    status: Status
    register_time: datetime

    model_config = ConfigDict(from_attributes=True)

    @field_serializer("register_time")
    def serialize_datetime(self, value: datetime) -> str:
        if value:
            return value.strftime("%Y-%m-%d %H:%M:%S")
        return None


class PersonalUserAccountData(BaseModel):
    """用户账户信息数据"""
    user_info: PersonalUserAccountInfo
    account_info: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(from_attributes=True)


class PersonalUserAccountsResponse(BaseModel):
    """用户账户信息响应"""
    code: int = 200
    message: str = "获取用户账户信息成功"
    data: PersonalUserAccountData

    model_config = ConfigDict(from_attributes=True)


class AccountRechargeReq(BaseModel):
    """账户充值请求"""
    user_id: int
    amount: float
    payment_method: PaymentMethod

    model_config = ConfigDict(from_attributes=True)


# 企业账户相关模型
class EnterpriseAccountInfo(BaseModel):
    """企业账户信息"""
    id: int
    username: str
    company_name: str
    phone: Optional[str] = None
    email: Optional[str] = None
    address: Optional[str] = None
    business_license: Optional[str] = None
    status: Status
    register_time: datetime

    model_config = ConfigDict(from_attributes=True)

    @field_serializer("register_time")
    def serialize_datetime(self, value: datetime) -> str:
        if value:
            return value.strftime("%Y-%m-%d %H:%M:%S")
        return None


class EnterpriseAccountData(BaseModel):
    """企业账户信息数据"""
    enterprise_info: EnterpriseAccountInfo
    account_info: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(from_attributes=True)


class EnterpriseAccountsResponse(BaseModel):
    """企业账户信息响应"""
    code: int = 200
    message: str = "获取企业账户信息成功"
    data: EnterpriseAccountData

    model_config = ConfigDict(from_attributes=True)


class EnterpriseWithBalance(BaseModel):
    """包含账户余额信息的企业用户"""
    id: int
    username: str
    company_name: str
    business_license: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    address: Optional[str] = None
    register_time: datetime
    status: Status
    type: str
    created_at: datetime
    updated_at: datetime

    # 账户余额字段
    regular_balance: float
    gift_balance: float
    gift_amount: float

    model_config = ConfigDict(from_attributes=True)

    @field_serializer("register_time", "created_at", "updated_at")
    def serialize_datetime(self, value: datetime) -> str:
        if value:
            return value.strftime("%Y-%m-%d %H:%M:%S")
        return None


class EnterpriseSearchListData(BaseModel):
    """企业搜索结果列表数据"""
    total: int
    list: List[EnterpriseWithBalance]

    model_config = ConfigDict(from_attributes=True)


class EnterpriseSearchResponse(BaseModel):
    """企业搜索结果响应"""
    code: int = 200
    message: str = "获取企业账户信息成功"
    data: EnterpriseSearchListData

    model_config = ConfigDict(from_attributes=True)


class EnterpriseRechargeReq(AccountRechargeReq):
    """企业账户充值请求"""
    pass
