from sqlalchemy import or_, func
from sqlalchemy.orm import Session
from datetime import datetime

from app.dao.account import AccountDAO
from app.models.account import RegularAccount, GiftAccount, AccountType, AccountTransaction, Account
from app.models.order import OrderStatus, PaymentStatus, PaymentMethod, OrderType, RechargeOrder, Order
from app.models.user import PersonalUser, User, Enterprise
from app.schemas.user_account import PersonalUserAccountInfo, PersonalUserAccountData
from app.utils.common import get_current_time, create_order_no


class AccountService:
    def __init__(self):
        self.account_dao = AccountDAO()

    def get_user_accounts(self, session: Session, user_id: int) -> PersonalUserAccountData:
        """
        获取用户的账户信息
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            
        Returns:
            PersonalUserAccountData: 包含用户信息和账户余额的数据
        """
        # 获取用户信息
        user = session.query(PersonalUser).filter(PersonalUser.id == user_id).first()
        if not user:
            raise ValueError(f"用户ID {user_id} 不存在")

        # 构建用户信息
        user_info = PersonalUserAccountInfo(
            id=user.id,
            username=user.username,
            phone=user.phone,
            real_name=user.real_name,
            email=user.email,
            address=user.address,
            wechat_id=user.wechat_id,
            status=user.status,
            register_time=user.register_time
        )

        # 获取用户账户信息
        regular_account = session.query(RegularAccount).filter(
            RegularAccount.user_id == user_id,
            RegularAccount.type == AccountType.REGULAR
        ).first()

        gift_account = session.query(GiftAccount).filter(
            GiftAccount.user_id == user_id,
            GiftAccount.type == AccountType.GIFT
        ).first()

        # 构建账户余额信息
        account_info = {
            "regular_account": {
                "id": regular_account.id if regular_account else None,
                "balance": regular_account.balance if regular_account else 0.0
            },
            "gift_account": {
                "id": gift_account.id if gift_account else None,
                "balance": gift_account.balance if gift_account else 0.0,
                "gift_amount": gift_account.gift_amount if gift_account else 0.0
            }
        }

        # 返回完整的用户账户信息
        return PersonalUserAccountData(
            user_info=user_info,
            account_info=account_info
        )

    def search(self, session: Session, keyword: str = None, name: str = None, phone: str = None, status=None,
               skip: int = 0, limit: int = 100) -> dict:
        """
        根据条件搜索个人用户列表及总数，并增加用户的普通账户余额和赠送账户余额
        
        Args:
            session: 数据库会话
            keyword: 搜索关键字，可搜索用户名、手机号、邮箱、真实姓名等
            name: 用户名（精确匹配或模糊匹配）
            phone: 手机号（精确匹配或模糊匹配）
            status: 用户状态
            skip: 分页起始位置
            limit: 分页大小
            
        Returns:
            dict: 包含 'total'（搜索结果总数）和 'list'（分页后的用户列表，每个用户包含regular_balance和gift_balance）的字典
        """
        # 基础查询条件
        filter_conditions = []

        # 如果提供了关键字，则添加过滤条件
        if keyword:
            search_keyword = f"%{keyword}%"
            filter_conditions.append(
                or_(
                    PersonalUser.phone.like(search_keyword),
                    PersonalUser.email.like(search_keyword),
                    PersonalUser.real_name.like(search_keyword),
                    PersonalUser.address.like(search_keyword),
                    PersonalUser.username.like(search_keyword)
                )
            )

        # 按用户名过滤
        if name:
            search_name = f"%{name}%"
            filter_conditions.append(
                or_(
                    PersonalUser.username.like(search_name),
                    PersonalUser.real_name.like(search_name)
                )
            )

        # 按手机号过滤
        if phone:
            # 如果手机号看起来是完整的11位手机号，则进行精确匹配
            if len(phone) == 11 and phone.isdigit():
                filter_conditions.append(PersonalUser.phone == phone)
            else:
                # 否则进行模糊匹配
                filter_conditions.append(PersonalUser.phone.like(f"%{phone}%"))

        # 按状态过滤
        if status is not None:
            filter_conditions.append(PersonalUser.status == status)

        # 获取总数
        total_query = session.query(func.count(PersonalUser.id))
        if filter_conditions:
            total_query = total_query.filter(*filter_conditions)
        total = total_query.scalar()

        # 主查询: 获取用户及其账户信息
        # 使用子查询获取每个用户的普通账户和赠送账户的余额
        regular_account_subquery = (
            session.query(
                RegularAccount.user_id,
                func.coalesce(RegularAccount.balance, 0.0).label('regular_balance')
            )
            .filter(RegularAccount.type == AccountType.REGULAR)
            .subquery()
        )

        gift_account_subquery = (
            session.query(
                GiftAccount.user_id,
                func.coalesce(GiftAccount.balance, 0.0).label('gift_balance'),
                func.coalesce(GiftAccount.gift_amount, 0.0).label('gift_amount')
            )
            .filter(GiftAccount.type == AccountType.GIFT)
            .subquery()
        )

        # 构建主查询
        query = session.query(
            PersonalUser,
            func.coalesce(regular_account_subquery.c.regular_balance, 0.0).label('regular_balance'),
            func.coalesce(gift_account_subquery.c.gift_balance, 0.0).label('gift_balance'),
            func.coalesce(gift_account_subquery.c.gift_amount, 0.0).label('gift_amount')
        )

        # 左连接账户子查询
        query = query.outerjoin(
            regular_account_subquery,
            PersonalUser.id == regular_account_subquery.c.user_id
        ).outerjoin(
            gift_account_subquery,
            PersonalUser.id == gift_account_subquery.c.user_id
        )

        # 应用过滤条件
        if filter_conditions:
            query = query.filter(*filter_conditions)

        # 排序和分页
        results = query.order_by(PersonalUser.id.desc()).offset(skip).limit(limit).all()

        # 处理结果
        result_list = []
        for row in results:
            user = row[0]
            regular_balance = row[1]
            gift_balance = row[2]
            gift_amount = row[3]

            # 创建包含所有必要字段的字典
            user_dict = {
                # User 表字段
                "id": user.id,
                "username": user.username,
                "register_time": user.register_time,
                "status": user.status,
                "type": user.type.value if user.type else None,
                "created_at": user.created_at,
                "updated_at": user.updated_at,

                # PersonalUser 表字段
                "phone": user.phone,
                "email": user.email,
                "real_name": user.real_name,
                "address": user.address,
                "wechat_id": user.wechat_id,
                "id_card": user.id_card,

                # 账户余额字段
                "regular_balance": regular_balance,
                "gift_balance": gift_balance,
                "gift_amount": gift_amount
            }

            result_list.append(user_dict)

        # 返回符合AccountSearchResponse的结构
        return {
            "data": {
                "total": total,
                "list": result_list
            }
        }

    def recharge(self, session: Session, user_id: int, amount: float, recharge_method: PaymentMethod):
        """
        用户充值
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            amount: 充值金额
            recharge_method: 充值方式
            
        Returns:
            Order: 创建的充值订单
            
        Raises:
            ValueError: 充值金额必须大于0，充值方式不能为账户余额
        """
        # 验证充值金额
        if amount <= 0:
            raise ValueError("充值金额必须大于0")

        # 验证充值方式
        if recharge_method == PaymentMethod.ACCOUNT_BALANCE:
            raise ValueError("不允许通过账户余额进行充值")

        # 查找用户账户
        regular_account = session.query(RegularAccount).filter(
            RegularAccount.user_id == user_id,
            RegularAccount.type == AccountType.REGULAR
        ).first()

        if not regular_account:
            raise ValueError(f"用户ID {user_id} 的普通账户不存在")

        # 创建充值订单 - 使用RechargeOrder而不是Order
        order_no = create_order_no()
        current_time = get_current_time()

        try:
            recharge_order = RechargeOrder(
                order_no=order_no,
                user_id=user_id,
                status=OrderStatus.PENDING,
                payment_status=PaymentStatus.UNPAID,
                total_amount=amount,
                payable_amount=amount,
                actual_amount_paid=amount,
                payment_method=recharge_method,
                type=OrderType.RECHARGE,
                created_at=current_time,
                updated_at=current_time
            )

            session.add(recharge_order)
            # 提交事务
            session.commit()
            session.refresh(recharge_order)
            return recharge_order
        except Exception as e:
            # 发生异常时回滚事务
            session.rollback()
            raise e

    def get_user_transactions(self, session: Session, user_id: int, skip: int = 0, limit: int = 100) -> dict:
        """
        获取用户所有账户的交易记录
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            skip: 分页起始位置
            limit: 分页大小
            
        Returns:
            dict: 包含 'total'（交易记录总数）和 'list'（分页后的交易记录列表）的字典
        """
        # 构建查询条件
        filter_condition = AccountTransaction.account_id.in_(
            session.query(Account.id).filter(Account.user_id == user_id)
        )

        # 获取交易记录总数 - 使用子查询而非先查询账户再查询交易
        total = session.query(func.count(AccountTransaction.id)).filter(filter_condition).scalar()

        # 如果没有交易记录，直接返回空结果
        if total == 0:
            return {"data": {"total": 0, "list": []}}

        # 主查询: 一次性获取所有需要的数据
        # 连接Order表获取支付方式
        query = session.query(
            AccountTransaction,
            Account.type.label("account_type"),
            User.username,
            Order.payment_method.label("payment_method"),
            Order.order_no.label("order_no")
        ).join(
            Account, AccountTransaction.account_id == Account.id
        ).join(
            User, Account.user_id == User.id
        ).join(  # 使用内连接，因为所有交易都有关联订单
            Order, AccountTransaction.order_id == Order.id
        ).filter(filter_condition)

        # 排序和分页
        results = query.order_by(AccountTransaction.transaction_time.desc()).offset(skip).limit(limit).all()

        # 处理结果
        result_list = []
        for row in results:
            transaction = row[0]
            account_type = row[1]
            username = row[2]
            payment_method = row[3]
            order_no = row[4]

            # 创建包含所有必要字段的字典
            transaction_dict = {
                "id": transaction.id,
                "username": username,
                "account_id": transaction.account_id,
                "account_type": account_type.value if account_type else None,
                "order_id": transaction.order_id,
                "order_no": order_no,
                "payment_method": payment_method.value if payment_method else None,
                "transaction_type": transaction.transaction_type.value,
                "amount": transaction.amount,
                "transaction_time": transaction.transaction_time,
                "description": transaction.description
            }

            result_list.append(transaction_dict)

        # 返回结果
        return {
            "data": {
                "total": total,
                "list": result_list
            }
        }

    def get_enterprise_accounts(self, session: Session, enterprise_id: int) -> dict:
        """
        获取企业的账户信息
        
        Args:
            session: 数据库会话
            enterprise_id: 企业ID
            
        Returns:
            dict: 包含企业信息和账户余额的数据
        """
        # 获取企业信息
        enterprise = session.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
        if not enterprise:
            raise ValueError(f"企业ID {enterprise_id} 不存在")

        # 构建企业信息
        enterprise_info = {
            "id": enterprise.id,
            "username": enterprise.username,
            "company_name": enterprise.company_name,
            "phone": enterprise.phone,
            "email": enterprise.email,
            "address": enterprise.address,
            "business_license": enterprise.business_license,
            "status": enterprise.status,
            "register_time": enterprise.register_time
        }

        # 获取企业账户信息
        regular_account = session.query(RegularAccount).filter(
            RegularAccount.user_id == enterprise_id,
            RegularAccount.type == AccountType.REGULAR
        ).first()

        gift_account = session.query(GiftAccount).filter(
            GiftAccount.user_id == enterprise_id,
            GiftAccount.type == AccountType.GIFT
        ).first()

        # 构建账户余额信息
        account_info = {
            "regular_account": {
                "id": regular_account.id if regular_account else None,
                "balance": regular_account.balance if regular_account else 0.0
            },
            "gift_account": {
                "id": gift_account.id if gift_account else None,
                "balance": gift_account.balance if gift_account else 0.0,
                "gift_amount": gift_account.gift_amount if gift_account else 0.0
            }
        }

        # 返回完整的企业账户信息
        return {
            "data": {
                "enterprise_info": enterprise_info,
                "account_info": account_info
            }
        }

    def search_enterprise(self, session: Session, keyword: str = None, name: str = None,
                          company_name: str = None, phone: str = None,
                          status=None, skip: int = 0, limit: int = 100) -> dict:
        """
        根据条件搜索企业列表及总数，并增加企业的普通账户余额和赠送账户余额
        
        Args:
            session: 数据库会话
            keyword: 搜索关键字，可搜索企业名称、手机号、邮箱等
            company_name: 企业名称（精确匹配或模糊匹配）
            phone: 手机号（精确匹配或模糊匹配）
            status: 企业状态
            skip: 分页起始位置
            limit: 分页大小
            
        Returns:
            dict: 包含 'total'（搜索结果总数）和 'list'（分页后的企业列表，每个企业包含regular_balance和gift_balance）的字典
        """
        # 基础查询条件
        filter_conditions = []

        # 如果提供了关键字，则添加过滤条件
        if keyword:
            search_keyword = f"%{keyword}%"
            filter_conditions.append(
                or_(
                    Enterprise.phone.like(search_keyword),
                    Enterprise.email.like(search_keyword),
                    Enterprise.company_name.like(search_keyword),
                    Enterprise.address.like(search_keyword),
                    Enterprise.username.like(search_keyword)
                )
            )

        # 按企业名称过滤
        if name:
            company_name = name
        if company_name:
            search_name = f"%{company_name}%"
            filter_conditions.append(Enterprise.company_name.like(search_name))

        # 按手机号过滤
        if phone:
            # 如果手机号看起来是完整的11位手机号，则进行精确匹配
            if len(phone) == 11 and phone.isdigit():
                filter_conditions.append(Enterprise.phone == phone)
            else:
                # 否则进行模糊匹配
                filter_conditions.append(Enterprise.phone.like(f"%{phone}%"))

        # 按状态过滤
        if status is not None:
            filter_conditions.append(Enterprise.status == status)

        # 获取总数
        total_query = session.query(func.count(Enterprise.id))
        if filter_conditions:
            total_query = total_query.filter(*filter_conditions)
        total = total_query.scalar()

        # 主查询: 获取企业及其账户信息
        # 使用子查询获取每个企业的普通账户和赠送账户的余额
        regular_account_subquery = (
            session.query(
                RegularAccount.user_id,
                func.coalesce(RegularAccount.balance, 0.0).label('regular_balance')
            )
            .filter(RegularAccount.type == AccountType.REGULAR)
            .subquery()
        )

        gift_account_subquery = (
            session.query(
                GiftAccount.user_id,
                func.coalesce(GiftAccount.balance, 0.0).label('gift_balance'),
                func.coalesce(GiftAccount.gift_amount, 0.0).label('gift_amount')
            )
            .filter(GiftAccount.type == AccountType.GIFT)
            .subquery()
        )

        # 构建主查询
        query = session.query(
            Enterprise,
            func.coalesce(regular_account_subquery.c.regular_balance, 0.0).label('regular_balance'),
            func.coalesce(gift_account_subquery.c.gift_balance, 0.0).label('gift_balance'),
            func.coalesce(gift_account_subquery.c.gift_amount, 0.0).label('gift_amount')
        )

        # 左连接账户子查询
        query = query.outerjoin(
            regular_account_subquery,
            Enterprise.id == regular_account_subquery.c.user_id
        ).outerjoin(
            gift_account_subquery,
            Enterprise.id == gift_account_subquery.c.user_id
        )

        # 应用过滤条件
        if filter_conditions:
            query = query.filter(*filter_conditions)

        # 排序和分页
        results = query.order_by(Enterprise.id.desc()).offset(skip).limit(limit).all()

        # 处理结果
        result_list = []
        for row in results:
            enterprise = row[0]
            regular_balance = row[1]
            gift_balance = row[2]
            gift_amount = row[3]

            # 创建包含所有必要字段的字典
            enterprise_dict = {
                # User 表字段
                "id": enterprise.id,
                "username": enterprise.username,
                "register_time": enterprise.register_time,
                "status": enterprise.status,
                "type": enterprise.type.value if enterprise.type else None,
                "created_at": enterprise.created_at,
                "updated_at": enterprise.updated_at,

                # Enterprise 表字段
                "company_name": enterprise.company_name,
                "business_license": enterprise.business_license,
                "phone": enterprise.phone,
                "email": enterprise.email,
                "address": enterprise.address,

                # 账户余额字段
                "regular_balance": regular_balance,
                "gift_balance": gift_balance,
                "gift_amount": gift_amount
            }

            result_list.append(enterprise_dict)

        # 返回结果结构
        return {
            "data": {
                "total": total,
                "list": result_list
            }
        }

    def get_enterprise_transactions(self, session: Session, enterprise_id: int, skip: int = 0,
                                    limit: int = 100) -> dict:
        """
        获取企业所有账户的交易记录
        
        Args:
            session: 数据库会话
            enterprise_id: 企业ID
            skip: 分页起始位置
            limit: 分页大小
            
        Returns:
            dict: 包含 'total'（交易记录总数）和 'list'（分页后的交易记录列表）的字典
        """
        # 构建查询条件
        filter_condition = AccountTransaction.account_id.in_(
            session.query(Account.id).filter(Account.user_id == enterprise_id)
        )

        # 获取交易记录总数 - 使用子查询而非先查询账户再查询交易
        total = session.query(func.count(AccountTransaction.id)).filter(filter_condition).scalar()

        # 如果没有交易记录，直接返回空结果
        if total == 0:
            return {"data": {"total": 0, "list": []}}

        # 主查询: 一次性获取所有需要的数据
        # 连接Order表获取支付方式
        query = session.query(
            AccountTransaction,
            Account.type.label("account_type"),
            Enterprise.username.label("username"),
            Order.payment_method.label("payment_method"),
            Order.order_no.label("order_no")
        ).join(
            Account, AccountTransaction.account_id == Account.id
        ).join(
            Enterprise, Account.user_id == Enterprise.id
        ).join(  # 使用内连接，因为所有交易都有关联订单
            Order, AccountTransaction.order_id == Order.id
        ).filter(filter_condition)

        # 排序和分页
        results = query.order_by(AccountTransaction.transaction_time.desc()).offset(skip).limit(limit).all()

        # 处理结果
        result_list = []
        for row in results:
            transaction = row[0]
            account_type = row[1]
            username = row[2]
            payment_method = row[3]
            order_no = row[4]

            # 创建包含所有必要字段的字典
            transaction_dict = {
                "id": transaction.id,
                "username": username,
                "account_id": transaction.account_id,
                "account_type": account_type.value if account_type else None,
                "order_id": transaction.order_id,
                "order_no": order_no,
                "payment_method": payment_method.value if payment_method else None,
                "transaction_type": transaction.transaction_type.value,
                "amount": transaction.amount,
                "transaction_time": transaction.transaction_time,
                "description": transaction.description
            }

            result_list.append(transaction_dict)

        # 返回结果
        return {
            "data": {
                "total": total,
                "list": result_list
            }
        }

    def search_enterprise_transactions(self, session: Session, enterprise_id: int, 
                                       start_time: datetime = None, end_time: datetime = None,
                                       skip: int = 0, limit: int = 100) -> dict:
        """
        根据时间范围搜索企业所有账户的交易记录
        
        Args:
            session: 数据库会话
            enterprise_id: 企业ID
            start_time: 开始时间（可选）
            end_time: 结束时间（可选）
            skip: 分页起始位置
            limit: 分页大小
            
        Returns:
            dict: 包含 'total'（交易记录总数）和 'list'（分页后的交易记录列表）的字典
        """
        # 构建基础查询条件
        filter_conditions = [
            AccountTransaction.account_id.in_(
                session.query(Account.id).filter(Account.user_id == enterprise_id)
            )
        ]

        # 添加时间范围过滤条件
        if start_time:
            filter_conditions.append(AccountTransaction.transaction_time >= start_time)
        if end_time:
            filter_conditions.append(AccountTransaction.transaction_time <= end_time)

        # 获取交易记录总数
        total = session.query(func.count(AccountTransaction.id)).filter(*filter_conditions).scalar()

        # 如果没有交易记录，直接返回空结果
        if total == 0:
            return {"data": {"total": 0, "list": []}}

        # 主查询: 一次性获取所有需要的数据
        # 连接Order表获取支付方式
        query = session.query(
            AccountTransaction,
            Account.type.label("account_type"),
            Enterprise.username.label("username"),
            Order.payment_method.label("payment_method"),
            Order.order_no.label("order_no")
        ).join(
            Account, AccountTransaction.account_id == Account.id
        ).join(
            Enterprise, Account.user_id == Enterprise.id
        ).join(  # 使用内连接，因为所有交易都有关联订单
            Order, AccountTransaction.order_id == Order.id
        ).filter(*filter_conditions)

        # 排序和分页
        results = query.order_by(AccountTransaction.transaction_time.desc()).offset(skip).limit(limit).all()

        # 处理结果
        result_list = []
        for row in results:
            transaction = row[0]
            account_type = row[1]
            username = row[2]
            payment_method = row[3]
            order_no = row[4]

            # 创建包含所有必要字段的字典
            transaction_dict = {
                "id": transaction.id,
                "username": username,
                "account_id": transaction.account_id,
                "account_type": account_type.value if account_type else None,
                "order_id": transaction.order_id,
                "order_no": order_no,
                "payment_method": payment_method.value if payment_method else None,
                "transaction_type": transaction.transaction_type.value,
                "amount": transaction.amount,
                "transaction_time": transaction.transaction_time,
                "description": transaction.description
            }

            result_list.append(transaction_dict)

        # 返回结果
        return {
            "data": {
                "total": total,
                "list": result_list
            }
        }


account_service = AccountService()
