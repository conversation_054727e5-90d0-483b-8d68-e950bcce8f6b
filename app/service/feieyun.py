import base64

from Crypto.Cipher import PKCS1_v1_5 as PKCS1_v1_5_cipher
from Crypto.PublicKey import RSA

# 私钥
private_key_pem = '''***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'''


class FeieyunService():

    @staticmethod
    def callback_decrypt(encrypted_content: str) -> str:
        # 加载私钥
        private_key = RSA.import_key(private_key_pem)
        cipher = PKCS1_v1_5_cipher.new(private_key)

        # Base64 解码
        decoded_content = base64.b64decode(encrypted_content)

        # RSA 解密
        sentinel = b'ERROR'
        try:
            decrypted_content = cipher.decrypt(decoded_content, sentinel)
            if decrypted_content == sentinel:
                # 解密失败，抛出异常
                raise Exception("RSA 解密失败，返回哨兵值")
            else:
                return decrypted_content.decode('utf-8')
        except Exception as e:
            # 捕获异常并重新抛出，可自定义异常类型
            raise Exception(f"解密失败: {e}")

    @staticmethod
    def callback_encrypt(content: str, public_key_path: str) -> str:
        """
        使用RSA公钥加密内容并返回Base64编码的字符串

        Args:
            content: 要加密的内容
            public_key_path: 公钥文件路径

        Returns:
            str: Base64编码的加密内容
        """
        try:
            # 读取公钥文件
            with open(public_key_path, "r") as f:
                public_key_data = f.read()

            # 加载公钥
            public_key = RSA.import_key(public_key_data)
            cipher = PKCS1_v1_5_cipher.new(public_key)

            # RSA加密
            encrypted_content = cipher.encrypt(content.encode('utf-8'))

            # Base64编码
            base64_encrypted = base64.b64encode(encrypted_content).decode('utf-8')

            return base64_encrypted
        except Exception as e:
            # 捕获异常并重新抛出
            raise Exception(f"加密失败: {e}")


feieyun_service = FeieyunService()

if __name__ == "__main__":
    # 测试用的加密内容
    test_encrypted_content = "rOc9B1X0JUsXC4Q9Lda42op3vDKka8WaMWWcnZSC5ARp2/7bshnUlvWguizy5W3jkXP+Et+ywyg+YKfCgm5qmqZaA5ODNz5qMiOLynf/QNpbmpvrSb4F/hRGsbCqfgjRCztpJDl27w/KqmGGjzMmZFyZB5MeO9inrmN/0bYejH4="

    try:
        result = feieyun_service.callback_decrypt(test_encrypted_content)
        print(f"解密结果: {result}")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
