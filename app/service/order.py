import random
import string
from datetime import datetime
from typing import List, Dict, Any, Optional
import math

from sqlalchemy.orm import Session

from app.dao.order import order_dao, order_item_dao
from app.dao.product import product_dao
from app.dao.reservation import reservation_request_dao
from app.dao.user import user_dao
from app.models.enum import Status
from app.models.order import OrderStatus, PaymentStatus, PaymentMethod
from app.models.product import ProductType
from app.models.reservation import ReservationStatus
from app.schemas.order import OrderCreate, OrderItemBase, OrderUpdate, OrderDetailResponse
from app.schemas.reservation import ReservationRequestCreate
from app.service.pricing import pricing_service
from app.utils.logger import logger
from app.schemas.reservation import BizReservationRequestCreate
from app.models.reservation import ReservationType
from app.dao.reservation import biz_reservation_request_dao


class OrderService:
    """订单服务类

    负责处理订单的创建、更新、支付等业务逻辑
    """

    @staticmethod
    def generate_order_no() -> str:
        """生成订单号"""
        now = datetime.now()
        # 生成格式: 年月日时分秒+6位随机数
        random_num = ''.join(random.choices(string.digits, k=6))
        return f"O{now.strftime('%Y%m%d%H%M%S')}{random_num}"

    @staticmethod
    def create_order(session: Session, user_id: int, products: List[Dict[str, Any]],
                     rule_data: Optional[Dict[str, Any]] = None, source: str = None,
                     selected_coupons:Optional[list]=None, only_pricing: bool =False) -> OrderDetailResponse:
        """创建订单

        根据流程图:
        1. 获取订购产品列表
        2. 生成订单对象
        3. 遍历订购产品列表生成订单项
        4. 计算订单价格
        5. 应用价格策略
        6. 提交订单

        Args:
            session: 数据库会话
            user_id: 用户ID
            products: 订购产品列表，每个产品包含product_id, quantity和可选的reservation_requests
            rule_data: 规则数据（可选）
            source: 来源标识，用于现场点餐价格调整
            selected_coupons:已选择的优惠券
            only_pricing: 仅计价

        Returns:
            OrderDetailResponse: 包含详细信息的订单响应
        """
        # 验证用户是否存在
        user = user_dao.get(session, user_id)
        if not user:
            raise ValueError(f"用户ID {user_id} 不存在")

        # 初始化订单项列表和订单总金额
        order_items: List[OrderItemBase] = []
        total_amount = 0.0
        total_payable_amount = 0.0

        # 用于存储预订请求信息的临时列表
        temp_reservation_requests = []  # [(index, product_id, reservation_requests)]

        # 计算现场点餐加价
        add_price = 3.0 if source == "admin_onsite" else 0.0

        # 遍历订购产品列表生成订单项
        for product_item in products:
            product_id = product_item["product_id"]
            quantity = product_item["quantity"]

            # 检查产品有效性
            product = product_dao.get(session, product_id)
            if not product:
                raise ValueError(f"产品ID {product_id} 不存在")

            if product.status is Status.INACTIVE:
                raise ValueError(f"产品 {product.name} 无效")

            # 检查库存, 库存为 -1 代表的产品代表不需要对库存进行检查
            if -1 < product.stock < quantity:
                raise ValueError(f"产品 {product.name} 库存不足，当前库存: {product.stock}, 需求: {quantity}")

            # 初始化商品单价以及小计（默认使用产品基础价格）
            price = product.price
            subtotal = price * quantity

            # 订单项计价，传递 add_price 参数
            final_price, payable_amount, remark = pricing_service.product_pricing(session, product_id, price, quantity, add_price)
            # 生成订单项
            order_item = OrderItemBase(
                product_id=product_id,
                quantity=quantity,
                price=price,
                subtotal=subtotal,
                final_price=final_price,
                payable_amount=payable_amount,
                pricing_remark=remark
            )
            order_items.append(order_item)
            # 累加总金额
            total_amount += subtotal
            total_payable_amount += payable_amount

            # 将预订请求信息临时存储，便于后面创建预订请求
            if "reservation_requests" in product_item and product_item["reservation_requests"]:
                # 验证产品类型是否为可预订
                if product.type != ProductType.RESERVATION and product.type != ProductType.BUFFET_RESERVATION:
                    raise ValueError(f"产品 {product.name} 不支持预订")
                # 临时存储预订请求信息，包含当前订单项索引
                item_index = len(order_items) - 1
                temp_reservation_requests.append((item_index, product_id, product_item["reservation_requests"]))

        # 订单计价
        order_payable_amount, remark = pricing_service.order_pricing(session, total_payable_amount)

        # 如果订单级别优惠计算结果为0，则使用所有订单项应付金额之和作为订单应付金额
        if order_payable_amount == 0.0:
            order_payable_amount = total_payable_amount

        # 如果是商务餐预订订单，添加凑整产品
        if rule_data:
            # 计算需要凑整的金额
            #target_amount = 200  # 测试先用0.06
            target_amount = order_payable_amount
            diff_amount = 0
            #current_amount = order_payable_amount
            #diff_amount = target_amount - current_amount

            if diff_amount != 0:
                # 获取凑整产品
                if diff_amount > 0:
                    # 需要增加金额，使用id为100的产品
                    adjust_product = product_dao.get(session, 100)
                    adjust_quantity = math.ceil(diff_amount / adjust_product.price)  # 使用ceil向上取整
                else:
                    # 需要减少金额，使用id为200的产品
                    adjust_product = product_dao.get(session, 200)
                    adjust_quantity = math.ceil(abs(diff_amount) / abs(adjust_product.price))  # 使用ceil向上取整

                # 创建凑整订单项
                adjust_order_item = OrderItemBase(
                    product_id=adjust_product.id,
                    quantity=adjust_quantity,
                    price=adjust_product.price,
                    subtotal=adjust_product.price * adjust_quantity,
                    final_price=adjust_product.price,
                    payable_amount=adjust_product.price * adjust_quantity,
                    pricing_remark="商务餐预订订单凑整"
                )
                order_items.append(adjust_order_item)
                total_amount += adjust_order_item.subtotal
                total_payable_amount += adjust_order_item.payable_amount
                order_payable_amount = target_amount
                total_amount = target_amount  # 添加这一行，确保total_amount与target_amount一致

        # Todo：检查订单项目触发赠送策略
        # 检查订单项是否包含触发产品，包含则添加赠送品订单项目，价格为0
        # 检查订单总金额是否出发赠送，触发则添加赠送品订单项

        # 创建订单
        order = OrderCreate(
            user_id=user_id,
            order_no=OrderService.generate_order_no(),
            status=OrderStatus.PENDING,
            payment_status=PaymentStatus.UNPAID,
            total_amount=total_amount,
            payable_amount=order_payable_amount,
            actual_amount_paid=0,
            payment_method=PaymentMethod.ACCOUNT_BALANCE,
            items=order_items,
            original_amount=total_amount,
            pricing_remark=remark
        )
        db_order = order_dao.create(session, order)

        # 获取新创建的订单项
        db_order_items = order_item_dao.get_by_order(session, db_order.id)

        if rule_data:
            # 处理商务餐预订请求
            task_info = rule_data.get("task_info", {})
            # 直接创建商务餐预订请求
            biz_reservation = BizReservationRequestCreate(
                order_item_id=db_order_items[0].id,  # 使用已保存的订单项ID (商务餐订单只有一个订单项)
                user_id=user_id,
                orders_id=db_order.id,
                rule_id=rule_data["rule_id"],
                rule_item_id=rule_data["rule_item_id"],
                status=ReservationStatus.PENDING,
                reservation_period=rule_data["reservation_period"],
                dining_start_time=rule_data["dining_start_time"],
                dining_end_time=rule_data["dining_end_time"],
                reservation_time=datetime.now(),  # 添加当前时间作为预订时间
                type=ReservationType.BIZ_DINING_RESERVATION,  # 设置类型为商务餐预订
                # 商务餐附加信息
                name=task_info.get("contact_name", ""),
                phone=task_info.get("contact_phone", ""),
                persons=int(task_info.get("people_count", 0)),
                remark=task_info.get("remark", "")
            )

            # 直接使用biz_reservation_request_dao创建
            biz_reservation_request_dao.create(session, biz_reservation)

        else:
            # 处理自助餐预订请求
            for item_index, product_id, reservation_requests in temp_reservation_requests:
                # 获取已保存的订单项ID
                order_item_id = db_order_items[item_index].id
                for reservation_request in reservation_requests:
                    # 创建预订请求
                    reservation = ReservationRequestCreate(
                        order_item_id=order_item_id,  # 使用已保存的订单项ID
                        user_id=user_id,
                        product_id=product_id,
                        rule_id=reservation_request["rule_id"],
                        rule_item_id=reservation_request["rule_item_id"],
                        status=ReservationStatus.PENDING,
                        reservation_period=reservation_request["reservation_period"],
                        dining_start_time=reservation_request["dining_start_time"],
                        dining_end_time=reservation_request["dining_end_time"],
                        reservation_time=datetime.now()  # 添加当前时间作为预订时间
                    )
                    reservation_request_dao.create(session, reservation)
        return db_order

    # 更新产品库存
    @staticmethod
    def update_product_stock(session: Session, order_id: int):
        db_order_items = order_item_dao.get_by_order(session, order_id)
        for order_item in db_order_items:
            product = product_dao.get(session, order_item.product_id)
            # 只有非负库存才需要减库存
            if product.stock >= 0:
                logger.info(f"更新产品库存，产品ID: {product.id}, 库存: {product.stock}, 减少数量: {order_item.quantity}")
                product.stock -= order_item.quantity
                session.commit()


    @staticmethod
    def update_order_status(session: Session, order_id: int, status: OrderStatus) -> Optional[
        OrderDetailResponse]:
        """更新订单状态

        Args:
            session: 数据库会话
            order_id: 订单ID
            status: 新的订单状态

        Returns:
            Optional[OrderDetailResponse]: 更新后的订单信息，如果订单不存在则返回None
        """
        # 获取订单
        order = order_dao.get(session, order_id)
        if not order:
            return None

        # 更新状态
        update_data = OrderUpdate(status=status)
        updated_order = order_dao.update(session, order_id, update_data)

        return updated_order

    @staticmethod
    def pay_order(session: Session, order_id: int, payment_method: PaymentMethod) -> Optional[
        OrderDetailResponse]:
        """支付订单

        Args:
            session: 数据库会话
            order_id: 订单ID
            payment_method: 支付方式

        Returns:
            Optional[OrderDetailResponse]: 更新后的订单信息，如果订单不存在则返回None
        """
        # 获取订单
        order = order_dao.get(session, order_id)
        if not order:
            return None

        # 验证订单是否已支付
        if order.payment_status == PaymentStatus.PAID:
            raise ValueError("订单已支付")

        # 更新支付状态
        update_data = OrderUpdate(
            payment_status=PaymentStatus.PAID,
            payment_time=datetime.now(),
            payment_method=payment_method,
            status=OrderStatus.PAID  # 支付后自动更新订单状态为已支付
        )
        updated_order = order_dao.update(session, order_id, update_data)

        # 如果有预订请求，更新预订请求状态
        order_items = order_item_dao.get_by_order(session, order_id)
        for item in order_items:
            for reservation_request in item.reservation_requests:
                reservation_request.status = ReservationStatus.APPROVED
                session.commit()

        return updated_order

    @staticmethod
    def cancel_order(session: Session, order_id: int) -> Optional[OrderDetailResponse]:
        """取消订单

        Args:
            session: 数据库会话
            order_id: 订单ID

        Returns:
            Optional[OrderDetailResponse]: 更新后的订单信息，如果订单不存在则返回None
        """
        # 获取订单
        order = order_dao.get(session, order_id)
        if not order:
            return None

        # 验证订单是否可以取消
        if order.status not in [OrderStatus.PENDING, OrderStatus.PAID]:
            raise ValueError("订单状态不允许取消")

        # 更新订单状态
        update_data = OrderUpdate(status=OrderStatus.CANCELLED)
        updated_order = order_dao.update(session, order_id, update_data)

        # 恢复产品库存
        order_items = order_item_dao.get_by_order(session, order_id)
        for item in order_items:
            product = product_dao.get(session, item.product_id)
            product.stock += item.quantity
            session.commit()

        # 如果有预订请求，取消预订请求
        for item in order_items:
            for reservation_request in item.reservation_requests:
                reservation_request.status = ReservationStatus.REJECTED
                session.commit()

        return updated_order


# 创建服务实例
order_service = OrderService()
