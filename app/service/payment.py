from app.dao.account import account_dao, account_transaction_dao
from app.dao.order import order_dao, order_item_dao
from app.dao.reservation import reservation_request_dao
from app.dao.user import user_dao, enterprise_user_relation_dao
from app.models.account import TransactionType, AccountType, AccountTransaction
from app.models.enum import Status
from app.models.order import OrderStatus, PaymentMethod, PaymentStatus
from app.models.reservation import ReservationStatus
from app.schemas.account import AccountTransactionCreate
from app.schemas.reservation import ReservationRequestUpdate
from app.service.verify import verify_service
from app.utils.common import get_current_time
from app.utils.logger import logger


class PaymentService:
    @staticmethod
    def pay_order(session, order_id, payment_info):
        payment_method = payment_info.get("payment_method", None)
        if not payment_method:
            payment_method = PaymentMethod.ACCOUNT_BALANCE

        order = order_dao.get(session, order_id)
        # 立即保存订单属性，避免后续访问可能失效的实例
        order_status = order.status
        order_payment_status = order.payment_status
        order_user_id = order.user_id
        order_payable_amount = order.payable_amount

        if order_status != OrderStatus.PENDING:
            raise ValueError("订单状态不正确")
        if order_payment_status != PaymentStatus.UNPAID:
            raise ValueError("订单支付状态不正确")

        user = user_dao.get(session, order_user_id)

        # 个人账户支付
        if payment_method == PaymentMethod.ACCOUNT_BALANCE:
            print(f"user.id: {user.id}")
            accounts = account_dao.get_by_user_id(session, user.id)

            if not accounts:
                raise ValueError("用户账户不存在")

            regular_account = None
            for account in accounts:
                if account.type == AccountType.REGULAR:
                    regular_account = account
                elif account.type == AccountType.GIFT:
                    gitf_account = account

            if not regular_account:
                raise ValueError("用户普通账户不存在")

            if regular_account.balance < order_payable_amount:
                raise ValueError("用户余额不足")

            # 扣除金额
            regular_account.balance -= order_payable_amount

            # 重新查询订单以更新状态
            order = order_dao.get(session, order_id)
            order.payment_status = PaymentStatus.PAID
            order.status = OrderStatus.PAID
            order.payment_method = PaymentMethod.ACCOUNT_BALANCE
            order.payment_time = get_current_time()
            order.actual_amount_paid = order_payable_amount

            # 创建账户交易记录
            transaction = AccountTransactionCreate(
                account_id=regular_account.id,
                order_id=order_id,
                transaction_type=TransactionType.CONSUME,
                # transaction_type=TransactionType.PAYMENT, # 个人账户支付应该是 CONSUME
                amount=-order_payable_amount,
                description=f"订单支付：{order_id}",
                transaction_time=get_current_time()
            )
            account_transaction_dao.create(session, transaction)

            # 提交事务
            session.commit()

            # 不要使用merge，直接重新查询订单
            order = order_dao.get(session, order_id)  # 使用原始order_id而不是order.id

        # 企业账户支付
        elif payment_method == PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE:
            enterprise_id = payment_info.get("enterprise_id")
            if not enterprise_id:
                raise ValueError("缺少企业ID")

            # 检查用户与企业是否存在有效的关系
            relation = enterprise_user_relation_dao.get_by_enterprise_and_user(
                session, enterprise_id, user.id
            )

            if not relation or relation.relation_status != Status.ACTIVE:
                raise ValueError("用户与企业不存在有效关系")

            # 获取企业账户
            accounts = account_dao.get_by_user_id(session, enterprise_id)
            if not accounts:
                raise ValueError("企业账户不存在")

            enterprise_regular_account = None
            for account in accounts:
                if account.type == AccountType.REGULAR:
                    enterprise_regular_account = account
                elif account.type == AccountType.GIFT:
                    enterprise_gitf_account = account

            if not enterprise_regular_account:
                raise ValueError("企业普通账户不存在")

            # 检查企业账户余额
            if enterprise_regular_account.balance < order_payable_amount:
                raise ValueError("企业账户余额不足")

            # 扣除金额
            enterprise_regular_account.balance -= order_payable_amount

            # 重新查询订单以更新状态
            order = order_dao.get(session, order_id)
            order.payment_status = PaymentStatus.PAID
            order.status = OrderStatus.PAID
            order.payment_method = PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE
            order.payment_time = get_current_time()
            order.actual_amount_paid = order_payable_amount

            # 创建账户交易记录
            transaction = AccountTransactionCreate(
                account_id=enterprise_regular_account.id,
                order_id=order_id,
                transaction_type=TransactionType.PAYMENT,
                amount=-order_payable_amount,
                description=f"企业为用户{order_user_id}:{user.username}支付订单：{order_id} {order_payable_amount} 元",
                transaction_time=get_current_time()
            )
            account_transaction_dao.create(session, transaction)

            # 提交事务
            session.commit()

            # 不要使用merge，直接重新查询订单
            order = order_dao.get(session, order_id)  # 使用原始order_id而不是order.id

        elif payment_method == PaymentMethod.WECHAT_PAY:
            try:
                # 1. 首先验证所有必要的对象是否存在
                accounts = account_dao.get_by_user_id(session, order_user_id)
                if not accounts:
                    raise ValueError("用户账户不存在")

                # 获取微信账户
                wechat_accounts = [
                    account for account in accounts
                    if account.type == AccountType.WECHAT and account.status == Status.ACTIVE
                ]

                if not wechat_accounts:
                    raise ValueError("用户微信账户不存在")

                wechat_account = wechat_accounts[0]

                # 2. 记录详细的日志
                logger.info(f"开始处理订单支付 - 订单ID: {order_id}, 用户ID: {order_user_id}, 金额: {order_payable_amount}")
                logger.info(f"微信账户ID: {wechat_account.id}, 当前余额: {wechat_account.balance}")

                # 3. 使用事务上下文管理器
                try:
                    # 1. 先将充值金额加到微信账户
                    old_wechat_balance = wechat_account.balance
                    wechat_account.balance = wechat_account.balance + order_payable_amount
                    session.add(wechat_account)
                    logger.info(f"微信账户充值 - 账户ID: {wechat_account.id}, 原余额: {old_wechat_balance}, 新余额: {wechat_account.balance}")

                    # 创建微信账户充值记录
                    wechat_deposit = AccountTransactionCreate(
                        account_id=wechat_account.id,
                        amount=order_payable_amount,
                        transaction_type=TransactionType.DEPOSIT,
                        description=f"微信账户充值 +{order_payable_amount}元",
                        order_id=order_id,
                        transaction_time=get_current_time()
                    )
                    account_transaction_dao.create(session, wechat_deposit)

                    # 2. 从微信账户支付订单
                    old_wechat_balance = wechat_account.balance
                    wechat_account.balance = wechat_account.balance - order_payable_amount
                    session.add(wechat_account)
                    logger.info(f"微信账户支付 - 账户ID: {wechat_account.id}, 原余额: {old_wechat_balance}, 新余额: {wechat_account.balance}")

                    # 创建微信账户支付记录
                    wechat_payment = AccountTransactionCreate(
                        account_id=wechat_account.id,
                        amount=-order_payable_amount,
                        transaction_type=TransactionType.PAYMENT,
                        description=f"订单支付：{order_id}",
                        order_id=order_id,
                        transaction_time=get_current_time()
                    )
                    account_transaction_dao.create(session, wechat_payment)

                    # 更新订单状态
                    order = order_dao.get(session, order_id)
                    order.payment_status = PaymentStatus.PAID
                    order.status = OrderStatus.PAID
                    order.payment_method = PaymentMethod.WECHAT_PAY
                    order.payment_time = get_current_time()
                    order.actual_amount_paid = order_payable_amount

                    # 提交事务
                    session.commit()
                    logger.info(f"订单支付成功 - 订单ID: {order_id}, 用户ID: {order_user_id}, 金额: {order_payable_amount}")

                    # 重新查询订单
                    order = order_dao.get(session, order_id)
                    if not order:
                        raise ValueError(f"订单 {order_id} 在支付后不存在")

                except Exception as e:
                    session.rollback()
                    logger.error(f"微信支付处理失败: {str(e)}")
                    raise

            except Exception as e:
                logger.error(f"微信支付处理异常: {str(e)}")
                raise

        elif payment_method == PaymentMethod.ALIPAY:
            raise ValueError("支付宝支付不支持")
        elif payment_method == PaymentMethod.CASH:
            raise ValueError("现金支付不支持")
        elif payment_method == PaymentMethod.BANK_TRANSFER:
            raise ValueError("银行转账不支持")
        else:
            raise ValueError("支付方式不正确")

        # # 检查订单中是否包含预订请求，如果有则生成验证码
        # order_items = order_item_dao.get_by_order(session, order.id)
        # for order_item in order_items:
        #     # 获取该订单项关联的所有预订请求
        #     if hasattr(order_item, 'reservation_requests') and order_item.reservation_requests:
        #         for reservation_request in order_item.reservation_requests:
        #             # 为预订请求生成验证码
        #             verification_code = verify_service.create_verify_code()
        #             # 更新预订请求状态和验证码
        #             reservation_update = ReservationRequestUpdate(
        #                 verification_code=verification_code,
        #                 status=ReservationStatus.PAID_FULL  # 支付成功后更新为已支付定金状态
        #             )
        #             reservation_request_dao.update(session, reservation_request.id, reservation_update)

        # 重新查询订单以确保获取最新状态
        final_order = order_dao.get(session, order_id)
        if final_order:
            try:
                # 同步更新子订单状态
                order_item_dao.update_status(
                    session,
                    order_id,
                    status=OrderStatus.PAID
                )

                # 获取订单项
                order_items = order_item_dao.get_by_order(session, order_id)
                for order_item in order_items:
                    # 获取该订单项关联的所有预订请求
                    if hasattr(order_item, 'reservation_requests') and order_item.reservation_requests:
                        for reservation_request in order_item.reservation_requests:
                            # 为预订请求生成验证码
                            verification_code = verify_service.create_verify_code()
                            # 更新预订请求状态和验证码
                            reservation_update = ReservationRequestUpdate(
                                verification_code=verification_code,
                                status=ReservationStatus.PAID_FULL  # 支付成功后更新为已支付定金状态
                            )
                            reservation_request_dao.update(session, reservation_request.id, reservation_update)
            except Exception as e:
                print(f"获取订单项失败: {str(e)}")
                # 不需要在这里回滚，因为主要交易已经提交
                raise
        else:
            raise ValueError("订单处理后不存在")

        final_order_mark = order_dao.get(session, order_id)

        return final_order_mark

    @staticmethod
    def pay_order_partially(session, order_id, payment_info):
        """
        部分支付订单（用于分次支付的第一步）
        """
        payment_method = payment_info.get("payment_method")
        amount = payment_info.get("amount")

        order = order_dao.get(session, order_id)
        if not order:
            raise ValueError("订单不存在")

        if order.payment_status != PaymentStatus.UNPAID:
            raise ValueError("订单状态不正确")

        # 企业账户支付
        if payment_method == PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE:
            enterprise_id = payment_info.get("enterprise_id")
            if not enterprise_id:
                raise ValueError("缺少企业ID")

            # 进行企业支付逻辑（复用现有逻辑，但不标记订单为完全支付）
            user = user_dao.get(session, order.user_id)

            # 检查企业关系
            relation = enterprise_user_relation_dao.get_by_enterprise_and_user(
                session, enterprise_id, user.id
            )
            if not relation or relation.relation_status != Status.ACTIVE:
                raise ValueError("用户与企业不存在有效关系")

            # 获取企业账户
            accounts = account_dao.get_by_user_id(session, enterprise_id)
            if not accounts:
                raise ValueError("企业账户不存在")

            enterprise_regular_account = None
            for account in accounts:
                if account.type == AccountType.REGULAR:
                    enterprise_regular_account = account
                    break

            if not enterprise_regular_account:
                raise ValueError("企业普通账户不存在")

            # 检查企业账户余额
            if enterprise_regular_account.balance < amount:
                raise ValueError("企业账户余额不足")

            # 扣除金额
            enterprise_regular_account.balance -= amount

            # 检查是否为企业完全支付（支付金额等于应付金额）
            is_full_payment = (amount >= order.payable_amount)

            if is_full_payment:
                # 企业完全支付：更新订单状态为已支付
                order.payment_status = PaymentStatus.PAID
                order.status = OrderStatus.PAID
                order.enterprise_paid_amount = amount
                order.actual_amount_paid = amount
                order.payment_method = PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE
                order.payment_time = get_current_time()
            else:
                # 部分支付：更新订单状态为部分支付
                order.payment_status = PaymentStatus.PARTIAL_PAID
                order.enterprise_paid_amount = amount
                order.payment_method = PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE
                order.payment_time = get_current_time()

            # 创建账户交易记录
            description = f"企业为用户{user.id}:{user.username}{'完全' if is_full_payment else '部分'}支付订单：{order_id} {amount} 元"
            transaction = AccountTransactionCreate(
                account_id=enterprise_regular_account.id,
                order_id=order_id,
                transaction_type=TransactionType.PAYMENT,
                amount=-amount,
                description=description,
                transaction_time=get_current_time()
            )
            account_transaction_dao.create(session, transaction)

            session.commit()

            # 如果是企业完全支付，需要更新订单项和预订记录状态
            if is_full_payment:
                try:
                    # 同步更新子订单状态
                    order_item_dao.update_status(
                        session,
                        order_id,
                        status=OrderStatus.PAID
                    )

                    # 获取订单项并更新预订记录状态
                    order_items = order_item_dao.get_by_order(session, order_id)
                    for order_item in order_items:
                        # 获取该订单项关联的所有预订请求
                        if hasattr(order_item, 'reservation_requests') and order_item.reservation_requests:
                            for reservation_request in order_item.reservation_requests:
                                # 为预订请求生成验证码
                                verification_code = verify_service.create_verify_code()
                                # 更新预订请求状态和验证码
                                from app.schemas.reservation import ReservationRequestUpdate
                                reservation_update = ReservationRequestUpdate(
                                    verification_code=verification_code,
                                    status=ReservationStatus.PAID_FULL  # 支付成功后更新为已支付状态
                                )
                                reservation_request_dao.update(session, reservation_request.id, reservation_update)

                    session.commit()
                    logger.info(f"企业完全支付成功，已更新订单项和预订记录状态 - 订单ID: {order_id}")

                except Exception as e:
                    logger.error(f"更新订单项和预订记录状态失败: {str(e)}")
                    # 不回滚主要支付事务，但记录错误
                    pass

            return order_dao.get(session, order_id)

        else:
            raise ValueError("部分支付只支持企业账户")

    @staticmethod
    def complete_split_payment(session, order_id, payment_info):
        """
        完成分次支付（个人支付部分）
        """
        payment_method = payment_info.get("payment_method")
        amount = payment_info.get("amount")

        order = order_dao.get(session, order_id)
        if not order:
            raise ValueError("订单不存在")

        if order.payment_status != PaymentStatus.PARTIAL_PAID:
            raise ValueError("订单不在部分支付状态")

        if amount != order.requires_personal_payment:
            raise ValueError("支付金额不正确")

        # 个人账户支付
        if payment_method == PaymentMethod.ACCOUNT_BALANCE:
            user = user_dao.get(session, order.user_id)
            accounts = account_dao.get_by_user_id(session, user.id)

            if not accounts:
                raise ValueError("用户账户不存在")

            regular_account = None
            for account in accounts:
                if account.type == AccountType.REGULAR:
                    regular_account = account
                    break

            if not regular_account:
                raise ValueError("用户普通账户不存在")

            if regular_account.balance < amount:
                raise ValueError("用户余额不足")

            # 扣除金额
            regular_account.balance -= amount

            # 更新订单状态为完全支付
            order.payment_status = PaymentStatus.PAID
            order.status = OrderStatus.PAID
            order.personal_paid_amount = amount
            order.actual_amount_paid = order.enterprise_paid_amount + amount

            # 创建账户交易记录
            transaction = AccountTransactionCreate(
                account_id=regular_account.id,
                order_id=order_id,
                transaction_type=TransactionType.CONSUME,
                amount=-amount,
                description=f"订单个人部分支付：{order_id}",
                transaction_time=get_current_time()
            )
            account_transaction_dao.create(session, transaction)

            session.commit()

            # 分次支付完成后，更新订单项和预订记录状态
            try:
                # 同步更新子订单状态
                order_item_dao.update_status(
                    session,
                    order_id,
                    status=OrderStatus.PAID
                )

                # 获取订单项并更新预订记录状态
                order_items = order_item_dao.get_by_order(session, order_id)
                for order_item in order_items:
                    # 获取该订单项关联的所有预订请求
                    if hasattr(order_item, 'reservation_requests') and order_item.reservation_requests:
                        for reservation_request in order_item.reservation_requests:
                            # 为预订请求生成验证码
                            verification_code = verify_service.create_verify_code()
                            # 更新预订请求状态和验证码
                            from app.schemas.reservation import ReservationRequestUpdate
                            reservation_update = ReservationRequestUpdate(
                                verification_code=verification_code,
                                status=ReservationStatus.PAID_FULL  # 支付成功后更新为已支付状态
                            )
                            reservation_request_dao.update(session, reservation_request.id, reservation_update)

                session.commit()
                logger.info(f"分次支付完成，已更新订单项和预订记录状态 - 订单ID: {order_id}")

            except Exception as e:
                logger.error(f"更新订单项和预订记录状态失败: {str(e)}")
                # 不回滚主要支付事务，但记录错误
                pass

            return order_dao.get(session, order_id)

        else:
            raise ValueError("个人支付部分不支持该支付方式")

    @staticmethod
    def complete_split_payment_wechat(session, order_id, payment_info):
        """
        完成分次支付的微信支付部分
        """
        payment_method = payment_info.get("payment_method")
        amount = payment_info.get("amount")

        order = order_dao.get(session, order_id)
        if not order:
            raise ValueError("订单不存在")

        if order.payment_status != PaymentStatus.PARTIAL_PAID:
            raise ValueError("订单不在部分支付状态")

        if amount != order.requires_personal_payment:
            raise ValueError("支付金额不正确")

        # 微信支付处理（类似现有的微信支付逻辑）
        if payment_method == PaymentMethod.WECHAT_PAY:
            try:
                user = user_dao.get(session, order.user_id)

                # 获取用户账户
                accounts = account_dao.get_by_user_id(session, order.user_id)
                if not accounts:
                    raise ValueError("用户账户不存在")

                # 获取微信账户
                wechat_accounts = [
                    account for account in accounts
                    if account.type == AccountType.WECHAT and account.status == Status.ACTIVE
                ]

                if not wechat_accounts:
                    raise ValueError("用户微信账户不存在")

                wechat_account = wechat_accounts[0]

                logger.info(f"开始处理分次支付微信支付部分 - 订单ID: {order_id}, 用户ID: {user.id}, 金额: {amount}")
                logger.info(f"微信账户ID: {wechat_account.id}, 当前余额: {wechat_account.balance}")

                try:
                    # 1. 先将充值金额加到微信账户
                    old_wechat_balance = wechat_account.balance
                    wechat_account.balance = wechat_account.balance + amount
                    session.add(wechat_account)
                    logger.info(f"微信账户充值 - 账户ID: {wechat_account.id}, 原余额: {old_wechat_balance}, 新余额: {wechat_account.balance}")

                    # 创建微信账户充值记录
                    wechat_deposit = AccountTransactionCreate(
                        account_id=wechat_account.id,
                        amount=amount,
                        transaction_type=TransactionType.DEPOSIT,
                        description=f"分次支付微信账户充值 +{amount}元",
                        order_id=order_id,
                        transaction_time=get_current_time()
                    )
                    account_transaction_dao.create(session, wechat_deposit)

                    # 2. 从微信账户支付订单个人部分
                    old_wechat_balance = wechat_account.balance
                    wechat_account.balance = wechat_account.balance - amount
                    session.add(wechat_account)
                    logger.info(f"微信账户支付 - 账户ID: {wechat_account.id}, 原余额: {old_wechat_balance}, 新余额: {wechat_account.balance}")

                    # 创建微信账户支付记录
                    wechat_payment = AccountTransactionCreate(
                        account_id=wechat_account.id,
                        amount=-amount,
                        transaction_type=TransactionType.PAYMENT,
                        description=f"分次支付订单个人部分：{order_id}",
                        order_id=order_id,
                        transaction_time=get_current_time()
                    )
                    account_transaction_dao.create(session, wechat_payment)

                    # 3. 更新订单状态为完全支付
                    order.payment_status = PaymentStatus.PAID
                    order.status = OrderStatus.PAID
                    order.personal_paid_amount = amount
                    order.actual_amount_paid = order.enterprise_paid_amount + amount
                    # 注释掉错误的代码：分次支付的payment_method应该保持为ENTERPRISE_ACCOUNT_BALANCE
                    # order.payment_method = PaymentMethod.WECHAT_PAY  # 最终支付方式标记为微信支付
                    order.payment_time = get_current_time()

                    # 提交事务
                    session.commit()
                    logger.info(f"分次支付微信支付完成 - 订单ID: {order_id}, 用户ID: {user.id}, 金额: {amount}")

                    # 重新查询订单
                    order = order_dao.get(session, order_id)
                    if not order:
                        raise ValueError(f"订单 {order_id} 在分次支付后不存在")

                    # 微信分次支付完成后，更新订单项和预订记录状态
                    try:
                        # 同步更新子订单状态
                        order_item_dao.update_status(
                            session,
                            order_id,
                            status=OrderStatus.PAID
                        )

                        # 获取订单项并更新预订记录状态
                        order_items = order_item_dao.get_by_order(session, order_id)
                        for order_item in order_items:
                            # 获取该订单项关联的所有预订请求
                            if hasattr(order_item, 'reservation_requests') and order_item.reservation_requests:
                                for reservation_request in order_item.reservation_requests:
                                    # 为预订请求生成验证码
                                    verification_code = verify_service.create_verify_code()
                                    # 更新预订请求状态和验证码
                                    from app.schemas.reservation import ReservationRequestUpdate
                                    reservation_update = ReservationRequestUpdate(
                                        verification_code=verification_code,
                                        status=ReservationStatus.PAID_FULL  # 支付成功后更新为已支付状态
                                    )
                                    reservation_request_dao.update(session, reservation_request.id, reservation_update)

                        session.commit()
                        logger.info(f"微信分次支付完成，已更新订单项和预订记录状态 - 订单ID: {order_id}")

                    except Exception as e:
                        logger.error(f"更新订单项和预订记录状态失败: {str(e)}")
                        # 不回滚主要支付事务，但记录错误
                        pass

                    return order

                except Exception as e:
                    session.rollback()
                    logger.error(f"分次支付微信支付处理失败: {str(e)}")
                    raise

            except Exception as e:
                logger.error(f"分次支付微信支付处理异常: {str(e)}")
                raise
        else:
            raise ValueError("个人支付部分只支持微信支付")

    @staticmethod
    def recharge(session, order_id, recharge_info):
        payment_method = recharge_info.get("payment_method", None)
        if payment_method == PaymentMethod.ACCOUNT_BALANCE:
            raise ValueError("不允许通过余额进行充值")

        print(f"Payment Service - 充值开始: 订单ID {order_id}, 支付方式 {payment_method}")

        order = order_dao.get(session, order_id)
        if not order:
            print(f"订单不存在: {order_id}")
            raise ValueError(f"订单ID {order_id} 不存在")

        print(f"订单状态: {order.status}, 支付状态: {order.payment_status}")

        if order.status != OrderStatus.PENDING:
            print(f"订单状态不正确: {order.status}")
            raise ValueError("订单状态不正确")
        if order.payment_status != PaymentStatus.UNPAID:
            print(f"订单支付状态不正确: {order.payment_status}")
            raise ValueError("订单支付状态不正确")

        user = user_dao.get(session, order.user_id)
        print(f"用户信息: ID {user.id}, 用户名 {user.username}")

        try:
            # 银行转账或现金充值，都是从后台调整金额进行充值
            if payment_method in [PaymentMethod.BANK_TRANSFER, PaymentMethod.CASH]:
                accounts = account_dao.get_by_user_id(session, user.id)

                if not accounts:
                    print(f"用户账户不存在: {user.id}")
                    raise ValueError("用户账户不存在")

                regular_account = None
                for account in accounts:
                    if account.type == AccountType.REGULAR:
                        regular_account = account
                    elif account.type == AccountType.GIFT:
                        gitf_account = account

                if not regular_account:
                    print(f"用户普通账户不存在: {user.id}")
                    raise ValueError("用户普通账户不存在")

                print(f"充值前账户余额: {regular_account.balance}")

                # 进行充值
                regular_account.balance += order.payable_amount
                print(f"充值后账户余额: {regular_account.balance}")

                # 更新订单状态
                order.payment_status = PaymentStatus.PAID
                order.payment_method = payment_method
                order.payment_time = get_current_time()
                order.actual_amount_paid = order.payable_amount
                order.status = OrderStatus.PAID
                print(f"更新订单状态: {order.status}, 支付状态: {order.payment_status}")

                # 创建账户交易记录
                transaction = AccountTransactionCreate(
                    account_id=regular_account.id,
                    order_id=order.id,
                    transaction_type=TransactionType.DEPOSIT,
                    amount=order.payable_amount,
                    description=f"订单充值：{order.id}",
                    transaction_time=get_current_time()
                )

                created_transaction = account_transaction_dao.create(session, transaction)
                print(
                    f"创建交易记录: ID {created_transaction.id}, 类型 {created_transaction.transaction_type}, 金额 {created_transaction.amount}")

                session.commit()
                session.refresh(order)
                print(f"充值完成: 订单ID {order.id}, 状态 {order.status}, 支付状态 {order.payment_status}")

            elif payment_method == PaymentMethod.WECHAT_PAY:
                raise ValueError("微信充值暂不支持")
            elif payment_method == PaymentMethod.ALIPAY:
                raise ValueError("支付宝充值暂不支持")
            else:
                raise ValueError("支付方式不正确")
        except Exception as e:
            # 发生异常时回滚事务
            session.rollback()
            print(f"充值过程中出现异常: {str(e)}")
            raise e

        return order

    @staticmethod
    def refund_split_payment(session, order_id):
        """
        回退分次支付的企业支付部分
        """
        order = order_dao.get(session, order_id)
        if not order:
            raise ValueError("订单不存在")

        if order.payment_status != PaymentStatus.PARTIAL_PAID:
            raise ValueError("订单不在部分支付状态")

        if order.enterprise_paid_amount <= 0:
            raise ValueError("没有需要回退的企业支付金额")

        try:
            # 通过账户交易记录查找企业账户
            # 查找该订单的支付交易记录（金额为负数表示支出）

            payment_transactions = session.query(AccountTransaction).filter(
                AccountTransaction.order_id == order_id,
                AccountTransaction.transaction_type == TransactionType.PAYMENT,
                AccountTransaction.amount < 0  # 支出记录
            ).all()

            if not payment_transactions:
                raise ValueError("未找到企业支付交易记录")

            # 获取企业支付的交易记录（金额应该等于enterprise_paid_amount的负值）
            enterprise_transaction = None
            for transaction in payment_transactions:
                if abs(transaction.amount) == order.enterprise_paid_amount:
                    enterprise_transaction = transaction
                    break

            if not enterprise_transaction:
                raise ValueError("未找到匹配的企业支付交易记录")

            # 通过交易记录获取企业账户
            enterprise_account = enterprise_transaction.account
            if not enterprise_account:
                raise ValueError("企业账户不存在")

            # 回退金额到企业账户
            refund_amount = order.enterprise_paid_amount
            enterprise_account.balance += refund_amount

            # 重置订单状态（保持payment_method为默认值，不设置为None）
            order.payment_status = PaymentStatus.UNPAID
            order.enterprise_paid_amount = 0.0
            order.personal_paid_amount = 0.0
            order.requires_personal_payment = 0.0
            order.is_split_payment = False
            order.payment_method = PaymentMethod.ACCOUNT_BALANCE  # 设置为默认值而不是None
            order.payment_time = None

            # 创建回退交易记录
            transaction = AccountTransactionCreate(
                account_id=enterprise_account.id,
                order_id=order_id,
                transaction_type=TransactionType.REFUND,
                amount=refund_amount,
                description=f"分次支付取消，回退企业支付金额：{refund_amount} 元",
                transaction_time=get_current_time()
            )
            account_transaction_dao.create(session, transaction)

            session.commit()
            return True

        except Exception as e:
            session.rollback()
            logger.error(f"回退分次支付失败: {str(e)}")
            raise


payment_service = PaymentService()
