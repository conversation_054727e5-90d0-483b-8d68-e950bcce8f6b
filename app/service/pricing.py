from typing import Tuple

from app.dao.pricing import (
    member_price_strategy_dao,
    time_limited_strategy_dao,
    full_reduction_strategy_dao,
    discount_strategy_dao,
    pricing_strategy_dao)
from app.dao.product import product_dao
from app.models.enum import Status
from app.models.product import MealType
from app.models.pricing import PricingStrategyType, PricingStrategyScope
from app.utils.common import get_current_time
from app.utils.logger import logger


class PricingService:

    @staticmethod
    def product_pricing(session, product_id: int, price: float, quantity: int, add_price: float = 0) -> Tuple[float, float, str]:
        """
        产品定价

        Args:
            session: 数据库会话
            product_id: 产品ID
            price: 产品价格
            quantity: 产品数量
        Returns:
            Tuple[float, float, str]: 最终价格, 应付金额, 备注
        """
        # 获取产品信息，检查是否为现场点餐
        product = product_dao.get(session, product_id)
        original_price = price

        # 如果是现场点餐，价格加3元
        if product and product.meal_type == MealType.BUFFET and add_price > 0:
            logger.info(f"现场点餐价格调整: 加价 {add_price} 元")
            price = price + add_price
            logger.info(f"现场点餐价格调整: 原价 {original_price} -> 调整后 {price}")

        remark = ""
        pricing_strategies_result = product_dao.get_pricing_strategies_by_product(session, product_id)
        # 正确处理返回的字典格式，获取list字段
        product_pricing_strategies = pricing_strategies_result.get("list", []) if isinstance(pricing_strategies_result, dict) else pricing_strategies_result
        now = get_current_time()
        # 移除时区信息
        if hasattr(now, 'tzinfo') and now.tzinfo:
            now = now.replace(tzinfo=None)

        valid_strategies = list()
        # 价格策略有效性检查，必须为产品类型且状态有效以及在生效时间范围内的策略
        for strategy in product_pricing_strategies:
            # 增加类型检查，确保strategy是对象而不是字符串
            if isinstance(strategy, str):
                continue

            if not hasattr(strategy, 'scope'):
                continue

            if strategy.scope != PricingStrategyScope.PRODUCT:
                continue
            if strategy.status == Status.INACTIVE:
                continue
            # 确保时区格式一致
            strategy_start = strategy.start_time.replace(tzinfo=None) if hasattr(strategy.start_time,
                                                                                 'tzinfo') and strategy.start_time.tzinfo else strategy.start_time
            strategy_end = strategy.end_time.replace(tzinfo=None) if hasattr(strategy.end_time,
                                                                             'tzinfo') and strategy.end_time.tzinfo else strategy.end_time

            if now < strategy_start or now > strategy_end:
                continue
            valid_strategies.append(strategy)

        # 获取产品最优价格
        final_price = price
        best_price_strategy = None
        for strategy in valid_strategies:
            if strategy.type == PricingStrategyType.MEMBER_PRICE:
                member_price_strategy = member_price_strategy_dao.get(session, strategy.id)
                member_price = member_price_strategy.apply(price=price)
                if final_price > member_price:
                    final_price = member_price
                    best_price_strategy = member_price_strategy
            elif strategy.type == PricingStrategyType.TIME_LIMITED:
                time_limited_strategy = time_limited_strategy_dao.get(session, strategy.id)
                time_limited_price = time_limited_strategy.apply(price=price)
                if final_price > time_limited_price:
                    final_price = time_limited_price
                    best_price_strategy = time_limited_strategy
        if best_price_strategy:
            final_price = best_price_strategy.apply(price=price)
            remark = best_price_strategy.name

        # 计算单件商品应用满减和折扣后的价格
        unit_payable_amount = final_price
        unit_full_reduction_strategy = None
        unit_discount_strategy = None

        # 分别获取最低的满减和折扣策略（基于单个商品价格）
        for strategy in valid_strategies:
            if strategy.type == PricingStrategyType.FULL_REDUCTION:
                full_reduction_strategy = full_reduction_strategy_dao.get(session, strategy.id)
                # 计算单个商品是否满足满减条件
                if final_price >= full_reduction_strategy.full_amount:
                    unit_full_reduction_amount = final_price - full_reduction_strategy.reduction_amount
                    if unit_payable_amount > unit_full_reduction_amount:
                        unit_payable_amount = unit_full_reduction_amount
                        unit_full_reduction_strategy = full_reduction_strategy
            elif strategy.type == PricingStrategyType.DISCOUNT:
                discount_strategy = discount_strategy_dao.get(session, strategy.id)
                unit_discount_amount = discount_strategy.apply(amount=final_price)
                if unit_payable_amount > unit_discount_amount:
                    unit_payable_amount = unit_discount_amount
                    unit_discount_strategy = discount_strategy

        # 组合优惠计算（针对单件商品）
        if unit_full_reduction_strategy and unit_discount_strategy:
            # 计算满减后再打折
            full_then_discount = discount_strategy.apply(amount=final_price - unit_full_reduction_strategy.reduction_amount)
            # 计算打折后再满减
            discount_amount = unit_discount_strategy.apply(amount=final_price)
            if discount_amount >= unit_full_reduction_strategy.full_amount:
                discount_then_full = discount_amount - unit_full_reduction_strategy.reduction_amount
            else:
                discount_then_full = discount_amount

            # 选择较优惠的方案
            unit_payable_amount = min(full_then_discount, discount_then_full)

            # 更新备注信息
            if full_then_discount < discount_then_full:
                remark = ",".join([remark, unit_full_reduction_strategy.name, unit_discount_strategy.name]) if remark else ",".join([unit_full_reduction_strategy.name, unit_discount_strategy.name])
            else:
                remark = ",".join([remark, unit_discount_strategy.name, unit_full_reduction_strategy.name]) if remark else ",".join([unit_discount_strategy.name, unit_full_reduction_strategy.name])
        elif unit_full_reduction_strategy:
            remark = ",".join([remark, unit_full_reduction_strategy.name]) if remark else unit_full_reduction_strategy.name
        elif unit_discount_strategy:
            remark = ",".join([remark, unit_discount_strategy.name]) if remark else unit_discount_strategy.name

        # 计算最终总价格
        payable_amount = unit_payable_amount * quantity

        # 检查是否有适用于总价的满减策略（这些策略只对总价有效）
        total_full_reduction_strategy = None
        total_discount_strategy = None

        # 评估基于总价的满减和折扣策略
        for strategy in valid_strategies:
            if strategy.type == PricingStrategyType.FULL_REDUCTION:
                full_reduction_strategy = full_reduction_strategy_dao.get(session, strategy.id)
                # 只有当单件商品不满足但总价满足满减条件时才应用
                if final_price < full_reduction_strategy.full_amount and payable_amount >= full_reduction_strategy.full_amount:
                    full_reduction_amount = payable_amount - full_reduction_strategy.reduction_amount
                    if full_reduction_amount < payable_amount:
                        payable_amount = full_reduction_amount
                        total_full_reduction_strategy = full_reduction_strategy

        # 添加总价满减的备注
        if total_full_reduction_strategy:
            remark = ",".join([remark, total_full_reduction_strategy.name]) if remark else total_full_reduction_strategy.name

        return final_price, payable_amount, remark

    @staticmethod
    def order_pricing(session, amount: float) -> Tuple[float, str]:
        payable_amount = amount
        remark = ""
        order_pricing_strategies = pricing_strategy_dao.get_strategies_by_scope(session,
                                                                                scope=PricingStrategyScope.ORDER)
        now = get_current_time()
        # 移除时区信息
        if hasattr(now, 'tzinfo') and now.tzinfo:
            now = now.replace(tzinfo=None)

        valid_strategies = list()
        # 价格策略有效性检查，必须为订单类型且状态有效以及在生效时间范围内的策略
        for strategy in order_pricing_strategies:
            if strategy.scope != PricingStrategyScope.ORDER:
                continue
            if strategy.status == Status.INACTIVE:
                continue
            # 确保时区格式一致
            strategy_start = strategy.start_time.replace(tzinfo=None) if hasattr(strategy.start_time,
                                                                                 'tzinfo') and strategy.start_time.tzinfo else strategy.start_time
            strategy_end = strategy.end_time.replace(tzinfo=None) if hasattr(strategy.end_time,
                                                                             'tzinfo') and strategy.end_time.tzinfo else strategy.end_time
            if now < strategy_start or now > strategy_end:
                continue
            valid_strategies.append(strategy)

        # 获取最优的订单项价格
        best_full_reduction_amount = float('inf')
        best_full_reduction_strategy = None
        best_discount_amount = float('inf')
        best_discount_strategy = None
        # 分别获取最低的满减和最低的折扣策略
        for strategy in valid_strategies:
            if strategy.type == PricingStrategyType.FULL_REDUCTION:
                full_reduction_strategy = full_reduction_strategy_dao.get(session, strategy.id)
                full_reduction_amount = full_reduction_strategy.apply(amount=payable_amount)
                if full_reduction_amount < best_full_reduction_amount:
                    best_full_reduction_amount = full_reduction_amount
                    best_full_reduction_strategy = full_reduction_strategy
            elif strategy.type == PricingStrategyType.DISCOUNT:
                discount_strategy = discount_strategy_dao.get(session, strategy.id)
                discount_amount = discount_strategy.apply(amount=payable_amount)
                if discount_amount < best_discount_amount:
                    best_discount_amount = discount_amount
                    best_discount_strategy = discount_strategy

        if best_full_reduction_strategy and best_discount_strategy is None:
            # 只存在满减
            payable_amount = best_full_reduction_strategy.apply(payable_amount)
            remark = ",".join([remark, best_full_reduction_strategy.name])
        elif best_full_reduction_strategy is None and best_discount_strategy:
            # 只存在折扣
            payable_amount = best_discount_strategy.apply(payable_amount)
            remark = ",".join([remark, best_discount_strategy.name])
        elif best_full_reduction_strategy and best_discount_strategy:
            full_reduction_first = best_discount_strategy.apply(best_full_reduction_strategy.apply(payable_amount))
            discount_first = best_full_reduction_strategy.apply(best_discount_strategy.apply(payable_amount))
            if full_reduction_first < discount_first:
                payable_amount = full_reduction_first
                remark = ",".join([remark, best_full_reduction_strategy.name, best_discount_strategy.name])
            else:
                payable_amount = discount_first
                remark = ",".join([remark, best_discount_strategy.name, best_full_reduction_strategy.name])

        return payable_amount, remark


pricing_service = PricingService()
