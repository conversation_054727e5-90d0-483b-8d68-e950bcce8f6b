from datetime import datetime
from typing import List, Optional, Dict, Any, Tu<PERSON>, IO, Union
from io import BytesIO

from sqlalchemy import and_, func, text
from sqlalchemy.orm import Session, joinedload, aliased

from app.dao.reservation import reservation_request_dao
from app.models.order import OrderItem
from app.models.product import Product
from app.models.reservation import ReservationRequest, ReservationStatus
from app.models.user import PersonalUser, User
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side
from openpyxl.workbook.workbook import Workbook


class ReportService:
    @staticmethod
    def _parse_reservation_period(period: str) -> Tuple[str, str]:
        """
        解析预订时段字符串，返回日期和时间段
        
        Args:
            period: 格式为"YYMMDDHHmm_YYMMDDHHmm"的预订时段字符串
            
        Returns:
            Tuple[str, str]: 日期字符串和时间段字符串
        """
        if not period or "_" not in period:
            return "", ""
            
        start, end = period.split("_")
        
        if len(start) < 10 or len(end) < 10:
            return "", ""
            
        # 解析日期：25(YY)05(MM)13(DD)1200(HHmm)
        year = f"20{start[0:2]}"  # 假设年份为20xx
        month = start[2:4]
        day = start[4:6]
        
        # 格式化为中文日期
        formatted_date = f"{year}年{int(month)}月{int(day)}日"
        
        # 解析时间段
        start_hour = start[6:8]
        start_minute = start[8:10]
        end_hour = end[6:8]
        end_minute = end[8:10]
        
        formatted_time = f"{int(start_hour)}点{start_minute}分～{int(end_hour)}点{end_minute}分"
        
        return formatted_date, formatted_time
    
    @staticmethod
    def _get_status_text(status: str) -> str:
        """
        获取预订状态的中文描述
        
        Args:
            status: 状态枚举值
            
        Returns:
            str: 状态的中文描述
        """
        status_map = {
            "pending": "待支付",
            "PENDING": "待支付",
            "paid_deposit": "已付定金",
            "PAID_DEPOSIT": "已付定金",
            "paid_full": "已预定",
            "PAID_FULL": "已预定",
            "cancelled": "已取消",
            "CANCELLED": "已取消",
            "verified": "已就餐",
            "VERIFIED": "已就餐",
            "auto_verified": "超时自动核销",
            "AUTO_VERIFIED": "超时自动核销"
        }
        
        return status_map.get(status, status)
        
    @staticmethod
    def get_reservation_report_raw(
        db: Session,
        dining_start_time: Optional[datetime] = None,
        dining_end_time: Optional[datetime] = None,
        reservation_start_time: Optional[datetime] = None,
        reservation_end_time: Optional[datetime] = None,
        reservation_period: Optional[str] = None,
        status: Optional[Union[str, List[str]]] = None,
        real_name: Optional[str] = None,
        nick_name: Optional[str] = None,
        phone: Optional[str] = None,
        payment_enterprise: Optional[str] = None,
        page: Optional[int] = None,
        page_size: Optional[int] = None,
        user_ids: Optional[List[int]] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        使用原生SQL查询获取预订报表数据

        Args:
            db: 数据库会话
            dining_start_time: 就餐开始时间
            dining_end_time: 就餐结束时间
            reservation_start_time: 开始时间
            reservation_end_time: 结束时间
            reservation_period: 预订时段
            status: 状态，可以是单个状态字符串或状态列表
            real_name: 姓名
            nick_name: 微信昵称
            phone: 手机号
            payment_enterprise: 支付企业
            page: 页码
            page_size: 每页大小
            user_ids: 用户ID列表，用于过滤指定用户的预订记录

        Returns:
            Tuple[List[Dict[str, Any]], int]: 预订记录列表和总记录数
        """
        # 获取订单项与支付用户的映射关系
        payment_users_mapping = {}
        try:
            payment_users_mapping = ReportService.get_payment_user_mappings(db)
        except Exception as e:
            # 如果获取映射关系失败，记录错误但不影响主要功能
            print(f"获取支付用户映射失败: {e}")

        # 根据支付企业筛选出符合条件的order_item_id
        filtered_order_item_ids = None
        if payment_enterprise and payment_enterprise.strip():
            filtered_order_item_ids = []
            for order_item_id, username in payment_users_mapping.items():
                if payment_enterprise.lower() in username.lower():
                    filtered_order_item_ids.append(order_item_id)

            # 如果没有找到匹配的订单项，直接返回空结果
            if not filtered_order_item_ids:
                return [], 0

        # 构建SQL查询条件
        where_conditions = []
        params = {}
        if dining_start_time and dining_end_time:
            where_conditions.append("rr.dining_start_time >= :dining_start_time AND rr.dining_end_time <=:dining_end_time")
            params["dining_start_time"] = dining_start_time
            params["dining_end_time"] = dining_end_time
        elif dining_start_time:
            where_conditions.append("rr.dining_start_time >= :dining_start_time")
            params["dining_start_time"] = dining_start_time
        elif dining_end_time:
            where_conditions.append("rr.dining_end_time <= :dining_end_time")
            params["dining_end_time"] = dining_end_time

        if reservation_start_time and reservation_end_time:
            where_conditions.append("rr.reservation_time BETWEEN :reservation_start_time AND :reservation_end_time")
            params["reservation_start_time"] = reservation_start_time
            params["reservation_end_time"] = reservation_end_time
        elif reservation_start_time:
            where_conditions.append("rr.reservation_time >= :reservation_start_time")
            params["reservation_start_time"] = reservation_start_time
        elif reservation_end_time:
            where_conditions.append("rr.reservation_time <= :reservation_end_time")
            params["reservation_end_time"] = reservation_end_time

        if status:
            # 处理多状态查询
            if isinstance(status, list) and len(status) > 0:
                status_conditions = []
                for i, s in enumerate(status):
                    param_name = f"status_{i}"
                    status_conditions.append(f"rr.status = :{param_name}")
                    params[param_name] = s
                where_conditions.append(f"({' OR '.join(status_conditions)})")
            elif isinstance(status, str) and status.strip():
                where_conditions.append("rr.status = :status")
                params["status"] = status

        if reservation_period:
            where_conditions.append("rr.reservation_period = :reservation_period")
            params["reservation_period"] = reservation_period

        if phone and phone.strip():
            where_conditions.append("pu.phone LIKE :phone")
            params["phone"] = f"%{phone}%"

        if nick_name and nick_name.strip():
            where_conditions.append("pu.nickname LIKE :nick_name")
            params["nick_name"] = f"%{nick_name}%"

        if real_name and real_name.strip():
            where_conditions.append("pu.real_name LIKE :real_name")
            params["real_name"] = f"%{real_name}%"

        # 添加用户ID过滤条件
        if user_ids is not None and len(user_ids) > 0:
            if len(user_ids) == 1:
                where_conditions.append("rr.user_id = :user_id")
                params["user_id"] = user_ids[0]
            else:
                placeholders = ",".join([f":user_id_{i}" for i in range(len(user_ids))])
                where_conditions.append(f"rr.user_id IN ({placeholders})")
                for i, user_id in enumerate(user_ids):
                    params[f"user_id_{i}"] = user_id

        # 添加支付企业筛选条件
        if filtered_order_item_ids is not None:
            if len(filtered_order_item_ids) == 1:
                where_conditions.append("rr.order_item_id = :order_item_id")
                params["order_item_id"] = filtered_order_item_ids[0]
            else:
                placeholders = ",".join([f":order_item_id_{i}" for i in range(len(filtered_order_item_ids))])
                where_conditions.append(f"rr.order_item_id IN ({placeholders})")
                for i, item_id in enumerate(filtered_order_item_ids):
                    params[f"order_item_id_{i}"] = item_id

        # 排除product_id为空且order_item_id不为空的记录
        where_conditions.append("NOT (rr.product_id IS NULL AND rr.order_item_id IS NOT NULL)")

        # 组合WHERE条件
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # 构建基本查询SQL - 获取总记录数
        count_sql = f"""
        SELECT COUNT(*) as total
        FROM reservation_requests rr
        LEFT JOIN users u ON rr.user_id = u.id
        LEFT JOIN personal_users pu ON u.id = pu.id
        LEFT JOIN products p ON rr.product_id = p.id
        LEFT JOIN order_items oi ON rr.order_item_id = oi.id
        WHERE {where_clause}
        """

        # 执行计数查询
        count_result = db.execute(text(count_sql), params).fetchone()
        total_count = count_result[0] if count_result else 0

        # 构建详细查询SQL - 获取预订记录
        sql = f"""
        SELECT 
            rr.id as reservation_id,
            rr.reservation_period,
            rr.reservation_time,
            rr.status,
            rr.created_at as created_time,
            rr.updated_at as updated_time,
            rr.user_id,
            rr.product_id,
            rr.order_item_id,
            p.name as product_name,
            p.price as product_price,
            oi.quantity,
            oi.price,
            oi.subtotal,
            oi.final_price,
            oi.payable_amount,
            u.username,
            pu.id as personal_user_id,
            pu.nickname as nick_name,
            pu.real_name,
            pu.phone
        FROM reservation_requests rr
        LEFT JOIN users u ON rr.user_id = u.id
        LEFT JOIN personal_users pu ON u.id = pu.id
        LEFT JOIN products p ON rr.product_id = p.id
        LEFT JOIN order_items oi ON rr.order_item_id = oi.id
        WHERE {where_clause}
        ORDER BY rr.id DESC
        """

        # 应用分页
        if page is not None and page_size is not None:
            sql += " LIMIT :limit OFFSET :offset"
            params["limit"] = page_size
            params["offset"] = (page - 1) * page_size

        # 执行查询
        results = db.execute(text(sql), params).fetchall()
        print(sql)
        # 格式化结果
        reservation_list = []
        for record in results:
            # 记录字典化
            record_dict = dict(record._mapping)

            # 计算总金额
            quantity = record_dict.get('quantity') or 0
            final_price = record_dict.get('final_price') or 0
            amount = quantity * final_price

            # 处理日期时间格式
            reservation_time = record_dict.get('reservation_time')
            created_time = record_dict.get('created_time')
            updated_time = record_dict.get('updated_time')

            # 获取支付企业信息
            order_item_id = record_dict.get('order_item_id')
            payment_enterprise = payment_users_mapping.get(order_item_id, "") if order_item_id else ""

            # 设置默认值，确保字段不为None
            reservation_dict = {
                'id': record_dict.get('reservation_id'),
                'reservation_period': record_dict.get('reservation_period') or "",
                'reservation_time': reservation_time.strftime("%Y-%m-%d %H:%M:%S") if reservation_time else "",
                'created_at': created_time.strftime("%Y-%m-%d %H:%M:%S") if created_time else "",
                'updated_at': updated_time.strftime("%Y-%m-%d %H:%M:%S") if updated_time else "",
                'status': record_dict.get('status') or "unknown",

                'user_id': record_dict.get('user_id') or 0,
                'username': record_dict.get('username') or "",

                'real_name': record_dict.get('real_name') or "",
                'nick_name': record_dict.get('nick_name') or "",
                'phone': record_dict.get('phone') or "",

                'product_id': record_dict.get('product_id') or 0,
                'product_name': record_dict.get('product_name') or "未知产品",
                'product_price': record_dict.get('product_price') or 0.0,

                'order_item_id': record_dict.get('order_item_id') or 0,
                'quantity': quantity,
                'price': record_dict.get('price') or 0.0,
                'subtotal': record_dict.get('subtotal') or 0.0,
                'final_price': final_price,
                'payable_amount': record_dict.get('payable_amount') or 0.0,
                'payment_enterprise': payment_enterprise,
            }

            reservation_list.append(reservation_dict)

        return reservation_list, total_count

    @staticmethod
    def get_reservation_report_excel(
        db: Session,
        dining_start_time: Optional[datetime] = None,
        dining_end_time: Optional[datetime] = None,
        reservation_start_time: Optional[datetime] = None,
        reservation_end_time: Optional[datetime] = None,
        reservation_period: Optional[str] = None,
        status: Optional[Union[str, List[str]]] = None,
        real_name: Optional[str] = None,
        nick_name: Optional[str] = None,
        phone: Optional[str] = None,
        payment_enterprise: Optional[str] = None,
        user_ids: Optional[List[int]] = None,
    ) -> BytesIO:
        """
        生成预订报表Excel文件
        
        Args:
            db: 数据库会话
            dining_start_time: 就餐开始时间
            dining_end_time: 就餐结束时间
            reservation_start_time: 开始时间
            reservation_end_time: 结束时间
            reservation_period: 预订时段
            status: 状态，可以是单个状态字符串或状态列表
            real_name: 姓名
            nick_name: 微信昵称
            phone: 手机号
            payment_enterprise: 支付企业
            user_ids: 用户ID列表，用于过滤指定用户的预订记录

        Returns:
            BytesIO: Excel文件的二进制流
        """
        # 调用原始方法获取所有数据，忽略分页参数
        reservations, _ = ReportService.get_reservation_report_raw(
            db=db,
            dining_start_time=dining_start_time,
            dining_end_time=dining_end_time,
            reservation_start_time=reservation_start_time,
            reservation_end_time=reservation_end_time,
            reservation_period=reservation_period,
            status=status,
            real_name=real_name,
            nick_name=nick_name,
            phone=phone,
            payment_enterprise=payment_enterprise,
            page=None,
            page_size=None,
            user_ids=user_ids
        )

        # 创建Excel工作簿和工作表
        wb = Workbook()
        ws = wb.active
        ws.title = "预订就餐记录"
        
        # 设置表头
        headers = [
            "序号", "手机", "姓名", "微信昵称", "产品名称", 
            "预订日期", "预订时段", "下单时间", "数量", "费用", "状态", "支付企业"
        ]
        
        # 写入表头
        for col_num, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_num, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
        
        # 写入数据
        for row_num, record in enumerate(reservations, 2):
            # 解析预订时段
            reservation_date, reservation_time = ReportService._parse_reservation_period(record.get('reservation_period', ''))
            
            # 处理下单时间格式
            order_time = record.get('reservation_time', '')
            
            # 获取状态的中文描述
            status_text = ReportService._get_status_text(record.get('status', ''))
            
            # 写入行数据
            ws.cell(row=row_num, column=1, value=record.get('id', ''))
            ws.cell(row=row_num, column=2, value=record.get('phone', ''))
            ws.cell(row=row_num, column=3, value=record.get('real_name', ''))
            ws.cell(row=row_num, column=4, value=record.get('nick_name', ''))
            ws.cell(row=row_num, column=5, value=record.get('product_name', ''))
            ws.cell(row=row_num, column=6, value=reservation_date)
            ws.cell(row=row_num, column=7, value=reservation_time)
            ws.cell(row=row_num, column=8, value=order_time)
            ws.cell(row=row_num, column=9, value=record.get('quantity', 0))
            ws.cell(row=row_num, column=10, value=record.get('payable_amount', 0))
            ws.cell(row=row_num, column=11, value=status_text)
            ws.cell(row=row_num, column=12, value=record.get('payment_enterprise', ''))

        # 设置列宽
        column_widths = [10, 15, 12, 20, 30, 15, 25, 20, 10, 10, 15, 20]
        for i, width in enumerate(column_widths, 1):
            ws.column_dimensions[openpyxl.utils.get_column_letter(i)].width = width
        
        # 保存到内存流
        excel_file = BytesIO()
        wb.save(excel_file)
        excel_file.seek(0)
        
        return excel_file

    @staticmethod
    def get_reservation_order_report_raw(
        db: Session,
        dining_start_time: Optional[datetime] = None,
        dining_end_time: Optional[datetime] = None,
        reservation_start_time: Optional[datetime] = None,
        reservation_end_time: Optional[datetime] = None,
        reservation_period: Optional[str] = None,
        status: Optional[Union[str, List[str]]] = None,
        real_name: Optional[str] = None,
        nick_name: Optional[str] = None,
        phone: Optional[str] = None,
        payment_enterprise: Optional[str] = None,
        page: Optional[int] = None,
        page_size: Optional[int] = None,
        user_ids: Optional[List[int]] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取预订订单报表数据，关联biz_reservation_requests和orders表

        Args:
            db: 数据库会话
            dining_start_time: 就餐开始时间
            dining_end_time: 就餐结束时间
            reservation_start_time: 开始时间
            reservation_end_time: 结束时间
            reservation_period: 预订时段
            status: 状态，可以是单个状态字符串或状态列表
            real_name: 姓名
            nick_name: 微信昵称
            phone: 手机号
            payment_enterprise: 支付企业
            page: 页码
            page_size: 每页大小
            user_ids: 用户ID列表，用于过滤指定用户的预订记录

        Returns:
            Tuple[List[Dict[str, Any]], int]: 预订记录列表和总记录数
        """
        # 获取订单项与支付用户的映射关系
        payment_users_mapping = {}
        try:
            payment_users_mapping = ReportService.get_payment_user_mappings(db)
        except Exception as e:
            # 如果获取映射关系失败，记录错误但不影响主要功能
            print(f"获取支付用户映射失败: {e}")

        # 根据支付企业筛选出符合条件的order_item_id
        filtered_order_item_ids = None
        if payment_enterprise and payment_enterprise.strip():
            filtered_order_item_ids = []
            for order_item_id, username in payment_users_mapping.items():
                if payment_enterprise.lower() in username.lower():
                    filtered_order_item_ids.append(order_item_id)

            # 如果没有找到匹配的订单项，直接返回空结果
            if not filtered_order_item_ids:
                return [], 0

        # 构建SQL查询条件
        where_conditions = []
        params = {}

        if dining_start_time and dining_end_time:
            where_conditions.append("rr.dining_start_time >= :dining_start_time AND rr.dining_end_time <= :dining_end_time")
            params["dining_start_time"] = dining_start_time
            params["dining_end_time"] = dining_end_time
        elif dining_start_time:
            where_conditions.append("rr.dining_start_time >= :dining_start_time")
            params["dining_start_time"] = dining_start_time
        elif dining_end_time:
            where_conditions.append("rr.dining_end_time <= :dining_end_time")
            params["dining_end_time"] = dining_end_time

        if reservation_start_time and reservation_end_time:
            where_conditions.append("rr.reservation_time BETWEEN :reservation_start_time AND :reservation_end_time")
            params["reservation_start_time"] = reservation_start_time
            params["reservation_end_time"] = reservation_end_time
        elif reservation_start_time:
            where_conditions.append("rr.reservation_time >= :reservation_start_time")
            params["reservation_start_time"] = reservation_start_time
        elif reservation_end_time:
            where_conditions.append("rr.reservation_time <= :reservation_end_time")
            params["reservation_end_time"] = reservation_end_time

        if status:
            # 处理多状态查询
            if isinstance(status, list) and len(status) > 0:
                status_conditions = []
                for i, s in enumerate(status):
                    param_name = f"status_{i}"
                    status_conditions.append(f"rr.status = :{param_name}")
                    params[param_name] = s
                where_conditions.append(f"({' OR '.join(status_conditions)})")
            elif isinstance(status, str) and status.strip():
                where_conditions.append("rr.status = :status")
                params["status"] = status

        if reservation_period:
            where_conditions.append("rr.reservation_period = :reservation_period")
            params["reservation_period"] = reservation_period

        if phone and phone.strip():
            where_conditions.append("pu.phone LIKE :phone")
            params["phone"] = f"%{phone}%"

        if nick_name and nick_name.strip():
            where_conditions.append("pu.nickname LIKE :nick_name")
            params["nick_name"] = f"%{nick_name}%"

        if real_name and real_name.strip():
            where_conditions.append("pu.real_name LIKE :real_name")
            params["real_name"] = f"%{real_name}%"

        # 添加用户ID过滤条件
        if user_ids is not None and len(user_ids) > 0:
            if len(user_ids) == 1:
                where_conditions.append("rr.user_id = :user_id")
                params["user_id"] = user_ids[0]
            else:
                placeholders = ",".join([f":user_id_{i}" for i in range(len(user_ids))])
                where_conditions.append(f"rr.user_id IN ({placeholders})")
                for i, user_id in enumerate(user_ids):
                    params[f"user_id_{i}"] = user_id

        # 添加支付企业筛选条件
        if filtered_order_item_ids is not None:
            if len(filtered_order_item_ids) == 1:
                where_conditions.append("rr.order_item_id = :order_item_id")
                params["order_item_id"] = filtered_order_item_ids[0]
            else:
                placeholders = ",".join([f":order_item_id_{i}" for i in range(len(filtered_order_item_ids))])
                where_conditions.append(f"rr.order_item_id IN ({placeholders})")
                for i, item_id in enumerate(filtered_order_item_ids):
                    params[f"order_item_id_{i}"] = item_id

        # 过滤order_id为空的记录和product_id为空的记录
        where_conditions.append("rr.orders_id IS NOT NULL")
        where_conditions.append("rr.product_id IS NULL")

        # 组合WHERE条件
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # 构建基本查询SQL - 获取总记录数
        count_sql = f"""
        SELECT COUNT(*) as total
        FROM reservation_requests rr
        LEFT JOIN users u ON rr.user_id = u.id
        LEFT JOIN personal_users pu ON u.id = pu.id
        LEFT JOIN biz_reservation_requests brr ON rr.id = brr.id
        LEFT JOIN orders o ON rr.orders_id = o.id
        WHERE {where_clause}
        """

        # 执行计数查询
        count_result = db.execute(text(count_sql), params).fetchone()
        total_count = count_result[0] if count_result else 0

        # 构建详细查询SQL - 获取预订记录
        sql = f"""
        SELECT
            rr.id as reservation_id,
            rr.reservation_period,
            rr.reservation_time,
            rr.status,
            rr.created_at as reservation_created_at,
            rr.updated_at as reservation_updated_at,
            rr.user_id,
            rr.product_id,
            rr.orders_id,
            rr.order_item_id,
            rr.dining_start_time,
            rr.dining_end_time,

            -- biz_reservation_requests 信息
            brr.name as contact_name,
            brr.phone as contact_phone,
            brr.persons,
            brr.remark,

            -- orders 信息
            o.order_no,
            o.total_amount,
            o.payable_amount,
            o.actual_amount_paid,
            o.payment_method,
            o.payment_status,
            o.payment_time,
            o.created_at as order_created_at,
            o.updated_at as order_updated_at,

            -- 用户信息
            u.username,
            pu.id as personal_user_id,
            pu.nickname as nick_name,
            pu.real_name,
            pu.phone as user_phone
        FROM reservation_requests rr
        LEFT JOIN users u ON rr.user_id = u.id
        LEFT JOIN personal_users pu ON u.id = pu.id
        LEFT JOIN biz_reservation_requests brr ON rr.id = brr.id
        LEFT JOIN orders o ON rr.orders_id = o.id
        WHERE {where_clause}
        ORDER BY rr.id DESC
        """

        # 应用分页
        if page is not None and page_size is not None:
            sql += " LIMIT :limit OFFSET :offset"
            params["limit"] = page_size
            params["offset"] = (page - 1) * page_size

        # 执行查询
        results = db.execute(text(sql), params).fetchall()
        print(sql)

        # 获取每个预订记录对应的订单项信息
        reservation_list = []
        for record in results:
            # 记录字典化
            record_dict = dict(record._mapping)

            # 获取该订单的所有订单项
            order_id = record_dict.get('orders_id')
            order_items = []
            calculate_amount = 0
            if order_id:
                order_items_sql = """
                SELECT
                    oi.id as order_item_id,
                    oi.quantity,
                    oi.price,
                    oi.subtotal,
                    oi.final_price,
                    oi.payable_amount,
                    oi.pricing_remark,
                    oi.unpaid_quantity,
                    p.name as product_name,
                    p.price as product_price
                FROM order_items oi
                LEFT JOIN products p ON oi.product_id = p.id
                WHERE oi.order_id = :order_id
                AND oi.status in ("paid","partial_paid")
                ORDER BY oi.id
                """
                order_items_results = db.execute(text(order_items_sql), {"order_id": order_id}).fetchall()

                for item_record in order_items_results:
                    item_dict = dict(item_record._mapping)

                    # 获取支付企业信息
                    order_item_id = item_dict.get('order_item_id')
                    payment_enterprise = payment_users_mapping.get(order_item_id, "") if order_item_id else ""

                    quantity = item_dict.get('quantity') or 0
                    price = item_dict.get('price') or 0.0
                    subtotal = item_dict.get('subtotal') or 0.0
                    final_price = item_dict.get('final_price') or 0.0
                    payable_amount = item_dict.get('payable_amount') or 0.0

                    unpaid_quantity = item_dict.get('unpaid_quantity') or 0
                    if unpaid_quantity > 0:
                        quantity = quantity - unpaid_quantity
                        subtotal = price * quantity
                        payable_amount = final_price * quantity

                    order_items.append({
                        'order_item_id': item_dict.get('order_item_id') or 0,
                        'quantity': quantity,
                        'price': price,
                        'subtotal': subtotal,
                        'final_price': final_price,
                        'payable_amount': payable_amount,
                        'pricing_remark': item_dict.get('pricing_remark') or "",
                        'product_name': item_dict.get('product_name') or "未知产品",
                        'product_price': item_dict.get('product_price') or 0.0,
                        'payment_enterprise': payment_enterprise,
                    })
                    calculate_amount += payable_amount or 0.0

            # 处理日期时间格式
            reservation_time = record_dict.get('reservation_time')
            reservation_created_at = record_dict.get('reservation_created_at')
            reservation_updated_at = record_dict.get('reservation_updated_at')
            order_created_at = record_dict.get('order_created_at')
            order_updated_at = record_dict.get('order_updated_at')
            payment_time = record_dict.get('payment_time')
            dining_start_time = record_dict.get('dining_start_time')
            dining_end_time = record_dict.get('dining_end_time')

            # 设置默认值，确保字段不为None
            reservation_dict = {
                # reservation_requests 信息
                'id': record_dict.get('reservation_id'),
                'reservation_period': record_dict.get('reservation_period') or "",
                'reservation_time': reservation_time.strftime("%Y-%m-%d %H:%M:%S") if reservation_time else "",
                'status': record_dict.get('status') or "unknown",
                'created_at': reservation_created_at.strftime("%Y-%m-%d %H:%M:%S") if reservation_created_at else "",
                'updated_at': reservation_updated_at.strftime("%Y-%m-%d %H:%M:%S") if reservation_updated_at else "",
                'user_id': record_dict.get('user_id') or 0,
                'product_id': record_dict.get('product_id') or 0,
                'orders_id': record_dict.get('orders_id') or 0,
                'order_item_id': record_dict.get('order_item_id') or 0,
                'dining_start_time': dining_start_time.strftime("%Y-%m-%d %H:%M:%S") if dining_start_time else "",
                'dining_end_time': dining_end_time.strftime("%Y-%m-%d %H:%M:%S") if dining_end_time else "",

                # biz_reservation_requests 信息
                'contact_name': record_dict.get('contact_name') or "",
                'contact_phone': record_dict.get('contact_phone') or "",
                'persons': record_dict.get('persons') or 0,
                'remark': record_dict.get('remark') or "",

                # orders 信息
                'order_no': record_dict.get('order_no') or "",
                'total_amount': record_dict.get('total_amount') or 0.0,
                'payable_amount': record_dict.get('payable_amount') or 0.0,
                'actual_amount_paid': record_dict.get('actual_amount_paid') or 0.0,
                'payment_method': record_dict.get('payment_method') or "",
                'payment_status': record_dict.get('payment_status') or "",
                'payment_time': payment_time.strftime("%Y-%m-%d %H:%M:%S") if payment_time else "",
                'order_created_at': order_created_at.strftime("%Y-%m-%d %H:%M:%S") if order_created_at else "",
                'order_updated_at': order_updated_at.strftime("%Y-%m-%d %H:%M:%S") if order_updated_at else "",
                'calculate_amount': calculate_amount,

                # 用户信息
                'username': record_dict.get('username') or "",
                'real_name': record_dict.get('real_name') or "",
                'nick_name': record_dict.get('nick_name') or "",
                'phone': record_dict.get('user_phone') or "",

                # 订单项信息
                'order_items': order_items,
            }

            reservation_list.append(reservation_dict)

        return reservation_list, total_count

    @staticmethod
    def get_reservation_order_report_excel(
        db: Session,
        dining_start_time: Optional[datetime] = None,
        dining_end_time: Optional[datetime] = None,
        reservation_start_time: Optional[datetime] = None,
        reservation_end_time: Optional[datetime] = None,
        reservation_period: Optional[str] = None,
        status: Optional[Union[str, List[str]]] = None,
        real_name: Optional[str] = None,
        nick_name: Optional[str] = None,
        phone: Optional[str] = None,
        payment_enterprise: Optional[str] = None,
        user_ids: Optional[List[int]] = None,
    ) -> BytesIO:
        """
        生成预订订单报表Excel文件，使用合并单元格显示预订信息和订单项

        Args:
            db: 数据库会话
            dining_start_time: 就餐开始时间
            dining_end_time: 就餐结束时间
            reservation_start_time: 开始时间
            reservation_end_time: 结束时间
            reservation_period: 预订时段
            status: 状态，可以是单个状态字符串或状态列表
            real_name: 姓名
            nick_name: 微信昵称
            phone: 手机号
            payment_enterprise: 支付企业
            user_ids: 用户ID列表，用于过滤指定用户的预订记录

        Returns:
            BytesIO: Excel文件的二进制流
        """
        # 调用新的方法获取所有数据，忽略分页参数
        reservations, _ = ReportService.get_reservation_order_report_raw(
            db=db,
            dining_start_time=dining_start_time,
            dining_end_time=dining_end_time,
            reservation_start_time=reservation_start_time,
            reservation_end_time=reservation_end_time,
            reservation_period=reservation_period,
            status=status,
            real_name=real_name,
            nick_name=nick_name,
            phone=phone,
            payment_enterprise=payment_enterprise,
            page=None,
            page_size=None,
            user_ids=user_ids
        )

        # 创建Excel工作簿和工作表
        wb = Workbook()
        ws = wb.active
        ws.title = "预订订单记录"

        # 设置表头
        headers = [
            "预订ID", "联系人姓名", "联系人电话", "预订人数", "用户姓名", "用户电话", "微信昵称",
            "预订日期", "预订时段", "预订时间", "状态", "订单号", "订单总金额", "支付状态", "支付方式",
            "产品名称", "数量", "单价", "小计", "最终单价", "应付金额", "支付企业", "备注"
        ]

        # 写入表头
        for col_num, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_num, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 写入数据
        current_row = 2
        for record in reservations:
            # 解析预订时段
            reservation_date, reservation_time_period = ReportService._parse_reservation_period(record.get('reservation_period', ''))

            # 获取状态的中文描述
            status_text = ReportService._get_status_text(record.get('status', ''))

            # 获取订单项列表
            order_items = record.get('order_items', [])

            # 如果没有订单项，至少显示一行预订信息
            if not order_items:
                order_items = [{}]  # 空的订单项，用于显示预订信息

            # 计算需要合并的行数
            rows_to_merge = len(order_items)
            start_row = current_row
            end_row = current_row + rows_to_merge - 1

            # 预订信息（需要合并的列）
            reservation_info = [
                record.get('id', ''),  # 预订ID
                record.get('contact_name', ''),  # 联系人姓名
                record.get('contact_phone', ''),  # 联系人电话
                record.get('persons', 0),  # 预订人数
                record.get('real_name', ''),  # 用户姓名
                record.get('phone', ''),  # 用户电话
                record.get('nick_name', ''),  # 微信昵称
                reservation_date,  # 预订日期
                reservation_time_period,  # 预订时段
                record.get('reservation_time', ''),  # 预订时间
                status_text,  # 状态
                record.get('order_no', ''),  # 订单号
                record.get('total_amount', 0),  # 订单总金额
                record.get('payment_status', ''),  # 支付状态
                record.get('payment_method', ''),  # 支付方式
            ]

            # 写入每个订单项
            for item_index, item in enumerate(order_items):
                row = current_row + item_index

                # 写入预订信息（只在第一行写入，后续行会被合并）
                if item_index == 0:
                    for col_index, value in enumerate(reservation_info, 1):
                        ws.cell(row=row, column=col_index, value=value)

                # 写入订单项信息
                item_start_col = len(reservation_info) + 1  # 从预订信息后开始
                ws.cell(row=row, column=item_start_col, value=item.get('product_name', ''))  # 产品名称
                ws.cell(row=row, column=item_start_col + 1, value=item.get('quantity', 0))  # 数量
                ws.cell(row=row, column=item_start_col + 2, value=item.get('price', 0))  # 单价
                ws.cell(row=row, column=item_start_col + 3, value=item.get('subtotal', 0))  # 小计
                ws.cell(row=row, column=item_start_col + 4, value=item.get('final_price', 0))  # 最终单价
                ws.cell(row=row, column=item_start_col + 5, value=item.get('payable_amount', 0))  # 应付金额
                ws.cell(row=row, column=item_start_col + 6, value=item.get('payment_enterprise', ''))  # 支付企业
                ws.cell(row=row, column=item_start_col + 7, value=record.get('remark', ''))  # 备注

            # 合并预订信息的单元格（如果有多个订单项）
            if rows_to_merge > 1:
                for col_index in range(1, len(reservation_info) + 1):
                    ws.merge_cells(start_row=start_row, start_column=col_index,
                                 end_row=end_row, end_column=col_index)
                    # 设置合并单元格的对齐方式
                    merged_cell = ws.cell(row=start_row, column=col_index)
                    merged_cell.alignment = Alignment(horizontal='center', vertical='center')

            current_row += rows_to_merge

        # 设置列宽
        column_widths = [
            10,  # 预订ID
            15,  # 联系人姓名
            15,  # 联系人电话
            10,  # 预订人数
            12,  # 用户姓名
            15,  # 用户电话
            20,  # 微信昵称
            15,  # 预订日期
            25,  # 预订时段
            20,  # 预订时间
            15,  # 状态
            20,  # 订单号
            12,  # 订单总金额
            12,  # 支付状态
            15,  # 支付方式
            30,  # 产品名称
            8,   # 数量
            10,  # 单价
            10,  # 小计
            10,  # 最终单价
            12,  # 应付金额
            20,  # 支付企业
            30   # 备注
        ]

        for i, width in enumerate(column_widths, 1):
            ws.column_dimensions[openpyxl.utils.get_column_letter(i)].width = width

        # 设置边框样式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # 为所有单元格添加边框
        for row in ws.iter_rows(min_row=1, max_row=current_row-1, min_col=1, max_col=len(headers)):
            for cell in row:
                cell.border = thin_border

        # 保存到内存流
        excel_file = BytesIO()
        wb.save(excel_file)
        excel_file.seek(0)

        return excel_file

    @staticmethod
    def get_payment_user_mappings(db: Session) -> Dict[int, str]:
        """
        获取所有订单项与支付用户的关联结果

        Args:
            db: 数据库会话

        Returns:
            Dict[int, str]: 订单项ID到用户名的映射字典
        """
        # 步骤1：获取所有使用企业账户支付的订单
        orders_query = """
        SELECT id FROM orders WHERE payment_method = 'ENTERPRISE_ACCOUNT_BALANCE'
        """
        orders_result = db.execute(text(orders_query)).fetchall()
        if not orders_result:
            return {}

        order_ids = [row[0] for row in orders_result]

        # 步骤2：获取这些订单关联的所有订单项
        order_items_query = """
        SELECT id, order_id FROM order_items 
        WHERE order_id IN :order_ids
        """
        order_items_result = db.execute(
            text(order_items_query),
            {"order_ids": tuple(order_ids) if len(order_ids) > 1 else f"({order_ids[0]})"}).fetchall()
        if not order_items_result:
            return {}

        # 创建订单ID到订单项ID的映射
        order_id_to_items = {}
        for row in order_items_result:
            item_id, order_id = row[0], row[1]
            if order_id not in order_id_to_items:
                order_id_to_items[order_id] = []
            order_id_to_items[order_id].append(item_id)

        # 步骤3：获取订单关联的支付账户
        accounts_query = """
        SELECT order_id, account_id 
        FROM account_transactions 
        WHERE order_id IN :order_ids AND transaction_type = 'payment'
        """
        accounts_result = db.execute(
            text(accounts_query),
            {"order_ids": tuple(order_ids) if len(order_ids) > 1 else f"({order_ids[0]})"}).fetchall()

        # 创建订单ID到账户ID的映射
        order_id_to_account = {row[0]: row[1] for row in accounts_result}

        # 步骤4：获取账户关联的用户
        account_ids = list(order_id_to_account.values())
        if not account_ids:
            return {}

        users_query = """
        SELECT a.id as account_id, u.id as user_id, u.username
        FROM accounts a
        JOIN users u ON a.user_id = u.id
        WHERE a.id IN :account_ids
        """
        users_result = db.execute(
            text(users_query),
            {"account_ids": tuple(account_ids) if len(account_ids) > 1 else f"({account_ids[0]})"}).fetchall()

        # 创建账户ID到用户信息的映射
        account_to_user = {row[0]: {"user_id": row[1], "username": row[2]} for row in users_result}

        # 组装最终结果为字典
        result = {}
        for order_id, account_id in order_id_to_account.items():
            if order_id in order_id_to_items and account_id in account_to_user:
                username = account_to_user[account_id]["username"]
                for item_id in order_id_to_items[order_id]:
                    result[item_id] = username

        return result


report_service = ReportService()
