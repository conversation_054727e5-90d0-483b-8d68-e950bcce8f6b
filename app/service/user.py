from typing import Optional, <PERSON><PERSON>, Dict, Any
from sqlalchemy.orm import Session

from app.dao.user import personal_user_dao, enterprise_dao
from app.dao.account import regular_account_dao, gift_account_dao
from app.dao.user import personal_user_dao, enterprise_dao
from app.models.user import PersonalUser, Enterprise
from app.models.account import RegularAccount, GiftAccount
from app.schemas.user import PersonalUserCreate, EnterpriseCreate, PersonalUserCreateWX
from app.schemas.account import RegularAccountCreate, GiftAccountCreate
from app.schemas.user import PersonalUserCreate, EnterpriseCreate
from app.schemas.user import PersonalUserCreateWX
from app.models.account import AccountType


class UserService:
    """用户服务类，提供用户相关的业务逻辑处理"""

    @staticmethod
    def create_personal_user(
        session: Session,
        user_data: PersonalUserCreate
    ) -> Tuple[PersonalUser, Dict[str, Any]]:
        """
        创建个人用户，同时创建相关账户
        
        Args:
            session: 数据库会话
            user_data: 用户创建数据
            
        Returns:
            包含创建的用户和账户信息的元组
        """
        try:
            # 1. 创建个人用户
            personal_user = personal_user_dao.create(session, user_data)
            regular_account = None
            gift_account = None
            for account in personal_user.accounts:
                if account.type == AccountType.REGULAR:
                    regular_account = account
                elif account.type == AccountType.GIFT:
                    gift_account = account


            # # 2. 创建普通账户
            # regular_account_data = RegularAccountCreate(user_id=personal_user.id)
            # regular_account = regular_account_dao.create(session, regular_account_data)
            #
            # # 3. 创建赠送账户
            # gift_account_data = GiftAccountCreate(user_id=personal_user.id)
            # gift_account = gift_account_dao.create(session, gift_account_data)
            #
            # # 4. 提交事务
            # session.commit()

            # 5. 返回创建的用户和账户信息
            accounts = {
                "regular_account": regular_account,
                "gift_account": gift_account
            }

            return personal_user, accounts

        except Exception as e:
            # 发生异常，回滚事务
            session.rollback()
            raise e

    @staticmethod
    def wx_create_personal_user(
            session: Session,
            user_data: PersonalUserCreateWX
    ) -> Tuple[PersonalUser, Dict[str, Any]]:
        """
        创建个人用户，同时创建相关账户

        Args:
            session: 数据库会话
            user_data: 用户创建数据

        Returns:
            包含创建的用户和账户信息的元组
        """
        try:
            # 1. 创建个人用户
            personal_user = personal_user_dao.create(session, user_data)

            regular_account = None
            gift_account = None
            for account in personal_user.accounts:
                if account.type == AccountType.REGULAR:
                    regular_account = account
                elif account.type == AccountType.GIFT:
                    gift_account = account

            # # 2. 创建普通账户
            # regular_account_data = RegularAccountCreate(user_id=personal_user.id)
            # regular_account = regular_account_dao.create(session, regular_account_data)
            #
            # # 3. 创建赠送账户
            # gift_account_data = GiftAccountCreate(user_id=personal_user.id)
            # gift_account = gift_account_dao.create(session, gift_account_data)
            #
            # # 4. 提交事务
            # session.commit()

            # 5. 返回创建的用户和账户信息
            accounts = {
                "regular_account": regular_account,
                "gift_account": gift_account
            }

            return personal_user, accounts

        except Exception as e:
            # 发生异常，回滚事务
            session.rollback()
            raise e

    @staticmethod
    def create_enterprise(
            session: Session,
            enterprise_data: EnterpriseCreate
    ) -> Tuple[Enterprise, Dict[str, Any]]:
        """
        创建企业用户，同时创建相关账户
        
        Args:
            session: 数据库会话
            enterprise_data: 企业创建数据
            
        Returns:
            包含创建的企业和账户信息的元组
        """
        try:
            # 1. 创建企业用户
            enterprise = enterprise_dao.create(session, enterprise_data)

            # 2. 创建普通账户
            regular_account_data = RegularAccountCreate(user_id=enterprise.id)
            regular_account = regular_account_dao.create(session, regular_account_data)

            # 3. 创建赠送账户
            gift_account_data = GiftAccountCreate(user_id=enterprise.id)
            gift_account = gift_account_dao.create(session, gift_account_data)

            # 4. 提交事务
            session.commit()

            # 5. 返回创建的企业和账户信息
            accounts = {
                "regular_account": regular_account,
                "gift_account": gift_account
            }

            return enterprise, accounts

        except Exception as e:
            # 发生异常，回滚事务
            session.rollback()
            raise e


# 创建服务实例
user_service = UserService()
