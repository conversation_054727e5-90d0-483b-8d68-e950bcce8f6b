import uuid
from typing import Tuple

from app.schemas.callback import AudioEnum
from app.service.feieyun import feieyun_service
from app.dao.order import order_item_dao
from app.dao.reservation import reservation_request_dao
from app.models.rule import MealType as RuleMealType
from app.models.reservation import ReservationStatus
from fastapi import Depends
from app.core.deps import get_db
from app.models.order import OrderStatus
from app.utils.logger import logger
from datetime import datetime
from app.dao.rule import rule_item_dao

class VerifyService:

    @staticmethod
    def create_verify_code():
        """
        生成一个 UUID 作为验证码

        Returns:
            str: 生成的 UUID 字符串
        """
        return str(uuid.uuid4())

    @staticmethod
    def verify_and_write_off(encrypted_result: str, db, mode: str ="FEIEYUN") -> Tuple[bool, str]:
        """
        验证并核销

        Args:
            encrypted_result: 加密后的结果字符串
            db: 数据库会话对象
            mode: 校验模式，FEIEYUN: 飞鹅云回调，MINIAPP: 小程序扫描，默认为飞鹅云回调

        Returns:
            Tuple[bool, int]: 返回验证结果和对应的音频代码
                - 第一个元素: 验证成功为True，失败为False
                - 第二个元素: 音频代码
        """
        try:
            # 调用解密方法解密结果
            if mode == "FEIEYUN":
                logger.info(f"开始解密验证码: {encrypted_result}")
                decrypted_content = feieyun_service.callback_decrypt(encrypted_result)
                logger.info(f"飞鹅云解密结果: {decrypted_content}")
            elif mode == "MINIAPP":
                decrypted_content = encrypted_result
                logger.info(f"小程序扫描结果: {decrypted_content}")
            else:
                raise Exception("核销模式参数错误！！！")
            message = ""
            try:
                # 获取预订订单
                logger.info(f"开始查询预订订单: {decrypted_content}")
                reservation_request = reservation_request_dao.get_by_verification_code(db, decrypted_content)
                if not reservation_request:
                    message = f"未找到预订订单: {decrypted_content}"
                    logger.error(message)
                    return False, message

                # 验证订单状态
                logger.info(f"当前订单状态: {reservation_request.status}")
                if reservation_request.status not in [ReservationStatus.PAID_DEPOSIT, ReservationStatus.PAID_FULL]:
                    message= f"订单状态不正确: {reservation_request.status}"
                    logger.error(message)
                    return False, message

                # 检查当前时间是否在核销时间范围内
                rule_item = rule_item_dao.get(db, reservation_request.rule_item_id)
                start_time = reservation_request.dining_start_time
                current_time = datetime.now()

                # 不是当天订单，则不进行核销
                if start_time.date() != current_time.date():
                    message = f"不是当天订单: {start_time.date()}, 未能进行核销！"
                    logger.error(message)
                    return False, message

                # 不在核销时间范围内，则不进行核销
                current_minutes = current_time.hour * 60 + current_time.minute
                if current_minutes < rule_item.verified_start_time or current_minutes > rule_item.verified_end_time:
                    message =f"当前时间不在核销时间范围内: {rule_item.verified_start_time}, {rule_item.verified_end_time}, 未能进行核销！"
                    logger.error(message)
                    return False, message

                # 更新预订订单状态
                logger.info("开始更新预订订单状态为已验证")
                reservation_request.status = ReservationStatus.VERIFIED
                order_item_id = reservation_request.order_item_id

                # 更新子订单状态
                logger.info(f"开始查询子订单: {order_item_id}")
                order_item = order_item_dao.get_by_order_item_id(db, order_item_id)
                if not order_item:
                    message = f"未找到子订单: {order_item_id}"
                    db.rollback()
                    logger.error(message)
                    return False, message

                logger.info("开始更新子订单状态为已验证")
                order_item.status = OrderStatus.VERIFIED

                # 提交所有更改
                logger.info("开始提交数据库更改")
                db.commit()
                message = "验证码核销成功"
                logger.info(message)
                return True, message

            except Exception as e:
                # 发生错误时回滚事务
                message = f"处理订单时发生错误: {str(e)}"
                logger.error(message)
                db.rollback()
                return False, message

        except Exception as e:
            # 解密失败
            message = f"解密验证码失败: {str(e)}"
            logger.error(message)
            return False, message


verify_service = VerifyService()

