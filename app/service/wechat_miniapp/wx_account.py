from typing import List, Dict, Any
from app.models.account import Account, AccountTransaction, TransactionType, AccountType, RegularAccount, WechatAccount
from app.models.enum import Status
from app.models.user import PersonalUser
from app.dao.account import account_dao
from sqlalchemy.orm import Session
from datetime import datetime
from app.utils.logger import logger

class AccountService:
    @staticmethod
    def get_balance(session: Session, user_id: int) -> float:
        """获取账户余额"""
        try:
            accounts = account_dao.get_by_user_id(session, user_id)
            print(f"查询到的账户数量: {len(accounts)}")

            # 筛选 REGULAR 类型的账户
            regular_accounts = [account for account in accounts if account.type == AccountType.REGULAR and account.status == Status.ACTIVE]
            print(f"常规账户数量: {len(regular_accounts)}")
            
            if regular_accounts:
                return regular_accounts[0].balance
            return 0.0
        except Exception as e:
            print(f"发生异常: {str(e)}")
            return 0.0


    @staticmethod
    def process_recharge(session: Session, user_id: int, amount: float, order_id: int) -> bool:
        """
        处理充值
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            amount: 充值金额
            order_id: 订单ID
            
        Returns:
            bool: 处理结果
        """
        try:
            logger.info(f"开始处理充值 - 用户ID: {user_id}, 金额: {amount}, 订单ID: {order_id}")

            # 获取用户的常规账户和微信账户
            accounts = account_dao.get_by_user_id(session, user_id)
            logger.info(f"查询到用户账户数量: {len(accounts)}")

            regular_accounts = [
                account for account in accounts 
                if account.type == AccountType.REGULAR and account.status == Status.ACTIVE
            ]
            wechat_accounts = [
                account for account in accounts
                if account.type == AccountType.WECHAT and account.status == Status.ACTIVE
            ]

            logger.info(f"常规账户数量: {len(regular_accounts)}, 微信账户数量: {len(wechat_accounts)}")

            if not regular_accounts:
                logger.info(f"用户 {user_id} 不存在常规账户，开始创建")
                account = AccountService.create_regular_account(session, user_id)
            else:
                account = regular_accounts[0]
                logger.info(f"使用现有常规账户 ID: {account.id}, 当前余额: {account.balance}")

            if not wechat_accounts:
                logger.info(f"用户 {user_id} 不存在微信账户，开始创建")
                wechat_account = AccountService.create_wechat_account(session, user_id)
            else:
                wechat_account = wechat_accounts[0]
                logger.info(f"使用现有微信账户 ID: {wechat_account.id}, 当前余额: {wechat_account.balance}")

            # 1. 充值到微信账户
            old_wechat_balance = wechat_account.balance
            wechat_account.balance = wechat_account.balance + amount
            session.add(wechat_account)
            logger.info(f"微信账户充值 - 账户ID: {wechat_account.id}, 原余额: {old_wechat_balance}, 新余额: {wechat_account.balance}")

            # 创建微信账户充值记录
            wechat_deposit_transaction = AccountTransaction(
                account_id=wechat_account.id,
                amount=amount,
                transaction_type=TransactionType.DEPOSIT,
                description=f"微信账户充值 +{amount}元",
                order_id=order_id,
                transaction_time=datetime.now()
            )
            session.add(wechat_deposit_transaction)

            # 2. 从微信账户转出到常规账户
            old_wechat_balance = wechat_account.balance
            wechat_account.balance = wechat_account.balance - amount
            session.add(wechat_account)
            logger.info(f"微信账户转出 - 账户ID: {wechat_account.id}, 原余额: {old_wechat_balance}, 新余额: {wechat_account.balance}")
            
            # 创建微信账户转出记录
            wechat_transfer_out = AccountTransaction(
                account_id=wechat_account.id,
                amount=-amount,
                transaction_type=TransactionType.TRANSFER,
                description=f"转出到常规账户 -{amount}元",
                order_id=order_id,
                transaction_time=datetime.now()
            )
            session.add(wechat_transfer_out)

            # 3. 常规账户接收转入
            old_regular_balance = account.balance
            account.balance = account.balance + amount
            session.add(account)
            logger.info(f"常规账户转入 - 账户ID: {account.id}, 原余额: {old_regular_balance}, 新余额: {account.balance}")

            # 创建常规账户转入记录
            regular_transfer_in = AccountTransaction(
                account_id=account.id,
                amount=amount,
                transaction_type=TransactionType.TRANSFER,
                description=f"从微信账户转入 +{amount}元",
                order_id=order_id,
                transaction_time=datetime.now()
            )
            session.add(regular_transfer_in)
            
            # 提交事务
            session.commit()
            logger.info(f"事务提交成功 - 用户ID: {user_id}, 订单ID: {order_id}")

            logger.info(f"充值成功 - 用户ID: {user_id}, 微信账户ID: {wechat_account.id}, "
                       f"常规账户ID: {account.id}, 金额: {amount}")
            
            return True
            
        except Exception as e:
            logger.error(f"充值处理失败 - 用户ID: {user_id}, 金额: {amount}, 错误: {str(e)}")
            session.rollback()
            return False

    @staticmethod
    def create_regular_account(session: Session, user_id: int) -> Account:
        """
        为用户创建常规账户

        Args:
            session: 数据库会话
            user_id: 用户ID

        Returns:
            Account: 创建的账户对象
        """
        try:
            # 检查是否已存在常规账户
            accounts = account_dao.get_by_user_id(session, user_id)
            regular_accounts = [
                account for account in accounts
                if account.type == AccountType.REGULAR and account.status == Status.ACTIVE
            ]

            if regular_accounts:
                return regular_accounts[0]

            # 创建新的常规账户
            regular_account = RegularAccount(
                user_id=user_id,
                balance=0.0,
                status=Status.ACTIVE,
                type=AccountType.REGULAR
            )

            session.add(regular_account)
            session.commit()

            logger.info(f"成功为用户 {user_id} 创建常规账户")
            return regular_account

        except Exception as e:
            logger.error(f"创建常规账户失败 - 用户ID: {user_id}, 错误: {str(e)}")
            session.rollback()
            raise

    @staticmethod
    def create_wechat_account(session: Session, user_id: int) -> Account:
        """
        为用户创建微信账户

        Args:
            session: 数据库会话
            user_id: 用户ID

        Returns:
            Account: 创建的账户对象
        """
        try:
            # 检查是否已存在微信账户
            accounts = account_dao.get_by_user_id(session, user_id)
            wechat_accounts = [
                account for account in accounts
                if account.type == AccountType.WECHAT and account.status == Status.ACTIVE
            ]

            if wechat_accounts:
                return wechat_accounts[0]

            # 创建新的微信账户
            wechat_account = WechatAccount(
                user_id=user_id,
                balance=0.0,
                status=Status.ACTIVE,
                type=AccountType.WECHAT
            )

            session.add(wechat_account)
            session.commit()

            logger.info(f"成功为用户 {user_id} 创建微信账户")
            return wechat_account

        except Exception as e:
            logger.error(f"创建微信账户失败 - 用户ID: {user_id}, 错误: {str(e)}")
            session.rollback()
            raise
