from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import random
import uuid
from app.models.reservation import ReservationRequest, ReservationStatus
from sqlalchemy.orm import Session
from app.dao.rule import rule_dao, rule_item_dao
from app.dao.reservation import reservation_request_dao
from app.service.revervation import reservation_service as service_reservation_service
from app.utils.logger import logger
from app.service.order import order_service
from app.dao.account import account_dao
from app.dao.order import order_dao, order_item_dao
from app.dao.product import product_dao
from app.dao.user import user_dao, enterprise_user_relation_dao, enterprise_dao
from app.models.order import PaymentMethod
from app.models.product import MealType
from app.models.rule import MealType as RuleMealType
from app.dao.reservation import biz_reservation_request_dao
from app.core.config import settings
from app.models.order import OrderStatus
from pydantic import BaseModel
from typing import Optional, Dict, Any, List
from datetime import datetime

class OrderActionPermission(BaseModel):
    """订单操作权限"""
    can_show_order_detail: bool = False  # 是否显示订单按钮
    can_edit: bool = False  # 是否显示加菜/详情按钮
    can_show_qr: bool = False  # 是否显示二维码按钮
    can_cancel: bool = False  # 是否显示取消按钮
    can_checkout: bool = False  # 是否显示结账按钮

class OrderDisplayInfo(BaseModel):
    """订单显示信息"""
    status_text: str  # 状态显示文本
    status_class: str  # 状态样式类名
    cancel_deadline_text: str  # 取消截止时间提示
    action_permissions: OrderActionPermission  # 操作权限

class ReservationListItem(BaseModel):
    """预约列表项"""
    # 基本信息
    order_id: int
    order_item_id: int
    title: str
    person: str
    date: str
    people: int
    price: float
    reservation_status: str

    # 显示信息
    display_info: OrderDisplayInfo

    # 商务餐特有信息
    biz_info: Optional[Dict[str, Any]] = None

    # 其他字段保持不变...

class ReservationService:
    def __init__(self):
        pass

    @staticmethod
    def get_reservation_info(db: Session, type: str, user_id: int, source: Optional[str] = None, meal_type: Optional[str] = None, qr_type: Optional[str] = None, filter_existing_reservations: bool = True) -> Dict[str, Any]:
        """获取预约配置信息
        
        Args:
            db: 数据库会话
            type: 预约类型
            user_id: 用户ID
            source: 请求来源（topic或admin）
            meal_type: 餐食类型
            qr_type: 二维码类型
            filter_existing_reservations: 是否过滤现有预订记录
        Returns:
            Dict: 包含预约配置信息的响应
        """
        # 记录请求来源
        logger.info(f"source: {source}")
        logger.info(f"处理预约配置请求，类型: {type}，来源: {source}, 二维码类型: {qr_type}")

        if 'business' in type:
            meal_type = MealType.BUSINESS
        else:
            meal_type = MealType.BUFFET

        product_list = product_dao.get_products_by_meal_type(db, meal_type)
        logger.info(f"获取到预约配置 product_list: {len(product_list)}")

        # 根据产品获取所有自助餐预订规则
        rule_list = []
        for product in product_list:
            rule_list.extend(product.rules)
        logger.info(f"获取到预约配置 rule_list: {len(rule_list)}")

        # 获取预约配置
        rule_dict_list = []
        for rule in rule_list:
            rule_dict = rule_dao.to_dict(rule)
            rule_dict_list.append(rule_dict)
        logger.info(f"获取到预约配置 rule_dict_list: {len(rule_dict_list)}")

        task_list = {}
        for rule_dict in rule_dict_list:
            try:
                availability_list = service_reservation_service.wx_miniapp_get_availability_list(db, rule_dict["id"], source, meal_type, qr_type)
                for availability in availability_list:
                    task_list.setdefault(availability["date"], []).append(availability)
            except Exception as e:
                pass
                # logger.info(f"非自助餐预订规则，跳出！rule_dict: {rule_dict}")
        logger.info(f"获取到预约配置 task_list: {task_list}")

        date_list = []
        for date, availability_list in task_list.items():
            # 对时间段按 start_time 进行排序
            availability_list.sort(key=lambda x: x["start_time"])

            date_obj = datetime.strptime(date, '%Y-%m-%d')
            # 获取星期几，使用中文表示
            weekday_map = {0: '周一', 1: '周二', 2: '周三', 3: '周四', 4: '周五', 5: '周六', 6: '周日'}
            weekday = weekday_map[date_obj.weekday()]
            date_str = date_obj.strftime('%m-%d') + ' ' + weekday
            date_list.append({
                "date_str": date_str,
                "date": date,
                "people": "",
                "selected": False,
                "canSelect": True,
                "timeSlot": "",
                "timeSlots": availability_list
            })
        # 按日期从近到远排序
        date_list.sort(key=lambda x: x["date"])
        logger.info(f"获取到预约配置 date_list: {date_list}")
        already_date_list = []

        # 检查企业自助晚餐开放权限（仅对员工自助餐生效）
        user_enterprise_names = []
        has_dinner_permission = None
        if type == 'employee':
            # 获取用户关联的企业名称列表
            enterprise_objects = enterprise_user_relation_dao.get_by_personal_user_id(db, user_id)
            for enterprise_object in enterprise_objects:
                enterprise = enterprise_dao.get(db, enterprise_object.enterprise_id)
                if enterprise:
                    user_enterprise_names.append(enterprise.company_name)

            logger.info(f"用户关联的企业: {user_enterprise_names}")
            logger.info(f"开放企业自助晚餐的企业列表: {settings.OPEN_ENTERPRISE_LIST}")

            # 检查用户的企业是否在开放列表中
            has_dinner_permission = any(enterprise_name in settings.OPEN_ENTERPRISE_LIST for enterprise_name in user_enterprise_names)
            logger.info(f"用户是否有晚餐权限: {has_dinner_permission}")

            # 如果没有晚餐权限，需要过滤掉晚餐时间段
            if not has_dinner_permission:
                logger.info("用户企业不在开放列表中，开始过滤晚餐时间段")
                for date_item in date_list:
                    filtered_time_slots = []
                    for time_slot in date_item["timeSlots"]:
                        # 获取时间段对应的餐食类型
                        rule_item = rule_item_dao.get(db, time_slot["rule_item_id"])
                        meal_type_rule = rule_item.meal_type if rule_item else None

                        # 如果是晚餐时间段，则过滤掉
                        if meal_type_rule == RuleMealType.DINNER:
                            logger.info(f"过滤晚餐时间段: {time_slot['time']}")
                        else:
                            filtered_time_slots.append(time_slot)

                    # 更新时间段列表
                    date_item["timeSlots"] = filtered_time_slots

        # 如果是员工餐获取配置，剔除当天已存在的预订订单（按餐食类型区分）
        if type == 'employee' and filter_existing_reservations:
            for date_item in date_list:
                date_item_reservation_period_start = datetime.strptime(date_item["date"], '%Y-%m-%d')
                request_date_list = [date_item_reservation_period_start.date()]
                logger.info(f"订单消费日期列表: {request_date_list}")

                # 检查当天该餐食类型是否已经存在企业账户支付的订单
                date_has_any_blocked_slot = False
                available_time_slots = []

                for time_slot in date_item["timeSlots"]:
                    # 获取时间段对应的餐食类型
                    rule_item = rule_item_dao.get(db, time_slot["rule_item_id"])
                    meal_type = rule_item.meal_type if rule_item else None

                    logger.info(f"=== 新逻辑执行中 ===: 检查时间段 {time_slot['time']} 的餐食类型 {meal_type.value if meal_type else '未知'}")

                    # 查询该日期该餐食类型是否已经存在企业账户支付的订单
                    has_enterprise_order, reservation_period_start = order_dao.check_enterprise_order_in_day_by_meal_type(
                        db,
                        user_id,
                        request_date_list,
                        meal_type
                    )

                    if has_enterprise_order:
                        reservation_period_start_str = reservation_period_start.strftime('%Y-%m-%d')
                        logger.info(f"用户当日该餐食类型({meal_type.value if meal_type else '未知'})已使用企业账户支付，剔除时间段: {time_slot}")
                        # 不添加到可用时间段
                        date_has_any_blocked_slot = True
                    else:
                        logger.info(f"用户当日该餐食类型({meal_type.value if meal_type else '未知'})未使用企业账户支付，保留时间段")
                        available_time_slots.append(time_slot)

                # 更新时间段列表并重新排序
                date_item["timeSlots"] = available_time_slots
                date_item["timeSlots"].sort(key=lambda x: x["start_time"])

                # 如果所有时间段都被剔除，则将整个日期添加到already_date_list
                if not available_time_slots:
                    already_date_list.append(date_item)

        # 获取员工配置
        enterprise_list = []
        if type == 'personal':
            logger.info(f"获取个人自助餐配置")

        elif type == 'employee':
            logger.info(f"获取员工自助餐配置")
            enterprise_objects = enterprise_user_relation_dao.get_by_personal_user_id(db, user_id)
            for enterprise_object in enterprise_objects:
                enterprise = enterprise_dao.get(db, enterprise_object.enterprise_id)
                enterprise_data = {
                    'id': enterprise.id,
                    'company_name': enterprise.company_name,
                    'phone': enterprise.phone,
                    'email': enterprise.email,
                    'address': enterprise.address
                }
                enterprise_list.append(enterprise_data)

        elif type == 'personal_business':
            logger.info(f"获取个人商务餐配置")

        elif type == 'company_business':
            logger.info(f"获取企业商务餐配置")
            enterprise_objects = enterprise_user_relation_dao.get_by_personal_user_id(db, user_id)
            for enterprise_object in enterprise_objects:
                # 检查用户是否为企业管理员
                if source != 'admin':  # 临时点餐固定扫码，不检查管理员权限（获取企业信息）
                    if not enterprise_object.is_admin:
                        logger.info(f"用户 {user_id} 不是企业 {enterprise_object.enterprise_id} 的管理员，跳过该企业")
                        continue

                enterprise = enterprise_dao.get(db, enterprise_object.enterprise_id)
                enterprise_data = {
                    'id': enterprise.id,
                    'company_name': enterprise.company_name,
                    'phone': enterprise.phone,
                    'email': enterprise.email,
                    'address': enterprise.address
                }
                enterprise_list.append(enterprise_data)

        # 返回预约配置信息
        result = {
            "code": 200,
            "message": "success",
            "data": {
                "has_dinner_permission": has_dinner_permission,
                "dateList": date_list,
                "reservationFee": 0,
                "enterprise_list": enterprise_list,
                "already_date_list": already_date_list
            }
        }

        return result

    @staticmethod
    def submit_reservation(db: Session, bookings: List[Dict[str, Any]], user_id: int, source: str = None) -> Dict[str, Any]:
        """提交预订信息"""
        try:
            if not bookings:
                return {
                    'code': 400,
                    'message': '预订信息不能为空',
                    'data': None
                }

            products = []
            for booking in bookings:
                # 获取就餐开始时间和结束时间
                reservation_period_start, reservation_period_end, reservation_time = service_reservation_service.extract_start_end_times(booking["reservation_period"])
                # 将就餐开始时间和结束时间转换为datetime对象
                dining_start_time = datetime.strptime(reservation_period_start, "%Y-%m-%d %H:%M")
                dining_end_time = datetime.strptime(reservation_period_end, "%Y-%m-%d %H:%M")

                products.append(
                    {
                        "product_id": booking["product_id"],
                        "quantity": booking["quantity"],
                        "reservation_requests": [
                            {
                                "rule_id": booking["rule_id"],
                                "rule_item_id": booking["rule_item_id"],
                                "dining_start_time": dining_start_time,
                                "dining_end_time": dining_end_time,
                                "reservation_period": booking["reservation_period"]
                            }
                        ]
                    }
                )
            logger.info(f"创建商品列表: {products}")

            # 创建订单，传递 source 参数
            order = order_service.create_order(db, user_id, products, source=source)
            logger.info(f"创建订单: {order}")
            logger.info(f"创建的订单号: {order.order_no}")
            logger.info(f"创建的订单金额: {order.payable_amount}")

            user_balance = account_dao.get_user_balance(db, user_id)
            logger.info(f"用户余额: {user_balance}")

            return {
                    'order_no':order.order_no,
                    'order_id':order.id,
                    'payable_amount':order.payable_amount,
                    'user_balance':user_balance
                }
        except Exception as e:
            logger.error(f"预订失败: {str(e)}")
            return None

    @staticmethod
    def calculate_order_permissions(order_data: Dict[str, Any], current_time: datetime) -> OrderDisplayInfo:
        """计算订单的操作权限和显示信息"""

        # 解析时间信息
        deadline = datetime.strptime(order_data["deadline"], "%Y-%m-%d %H:%M")
        earliest_date = datetime.strptime(order_data["earliest_date"], "%Y-%m-%d %H:%M")

        # 判断是否为商务餐
        is_business_meal = bool(order_data.get("biz_info", {}).get("name"))

        # 修复支付状态判断逻辑
        # 订单项状态为 "paid" 或预订状态为 "paid_full" 都表示已支付
        is_paid = (order_data["status"] in ["paid", "paid_full"] or
                   order_data["reservation_status"] in ["paid_full", "paid_deposit"])

        is_verified = order_data["reservation_status"] == "verified"
        is_cancelled = order_data["reservation_status"] == "cancelled"
        is_auto_verified = order_data["reservation_status"] == "auto_verified"

        # 判断时间状态
        is_before_deadline = current_time <= deadline
        is_after_earliest = current_time >= earliest_date

        # 添加调试日志
        logger.info(f"订单权限计算 - 订单ID: {order_data.get('order_id')}")
        logger.info(f"当前时间: {current_time}")
        logger.info(f"截止时间: {deadline}")
        logger.info(f"最早时间: {earliest_date}")
        logger.info(f"是否已支付: {is_paid}")
        logger.info(f"是否在截止时间前: {is_before_deadline}")
        logger.info(f"是否已核销: {is_verified}")
        logger.info(f"是否已取消: {is_cancelled}")

        # 计算操作权限
        permissions = OrderActionPermission()

        # 商务餐显示订单按钮
        permissions.can_show_order_detail = is_business_meal

        # 加菜/详情按钮权限
        if is_business_meal:
            permissions.can_edit = (is_before_deadline and is_paid and
                                   not is_verified and not is_auto_verified and not is_cancelled)
        else:
            permissions.can_edit = True  # 自助餐总是可以查看详情

        # 二维码按钮权限 - 修复逻辑
        # 已支付 + 未核销 + 未取消 + 在有效时间范围内
        # 暂时剔除 is_after_earliest 和 is_before_deadline 限制 （因为需求显示可点击，核销时再限制）
        permissions.can_show_qr = (is_paid and
                                  not is_verified and
                                  not is_auto_verified and
                                  not is_cancelled)

        # 取消按钮权限 - 修复逻辑
        # 已支付 + 未核销 + 未取消 + 在取消截止时间前
        permissions.can_cancel = (is_paid and
                                 not is_verified and
                                 not is_auto_verified and
                                 not is_cancelled and
                                 is_before_deadline)

        # 结账按钮权限（仅商务餐且商品总金额不等于订单价格时显示）
        if is_business_meal and order_data.get("should_show_checkout", False):
            permissions.can_checkout = True

        # 添加调试日志
        logger.info(f"权限计算结果:")
        logger.info(f"  can_show_qr: {permissions.can_show_qr}")
        logger.info(f"  can_cancel: {permissions.can_cancel}")
        logger.info(f"  can_edit: {permissions.can_edit}")

        # 计算状态显示文本
        status_text = ""
        status_class = "error"

        if order_data["reservation_status"] == "pending":
            status_text = "待支付"
        elif order_data["reservation_status"] == "paid_full":
            if is_business_meal:
                status_text = f"已预订（当天11:00前可取消）"
            else:
                # 判断是否为午餐
                is_lunch = earliest_date.hour < 15  # 15:00前为午餐
                cancel_time = "当天9:30前可取消" if is_lunch else "当天15:00前可取消"
                status_text = f"已预订（{cancel_time}）"
            status_class = "success"
        elif order_data["reservation_status"] == "paid_deposit":
            status_text = "已部分支付"
        elif order_data["reservation_status"] == "verified":
            status_text = "已就餐"
            status_class = "success"
        elif order_data["reservation_status"] == "cancelled":
            status_text = "已取消"
        elif order_data["reservation_status"] == "auto_verified":
            status_text = "超时自动核销"

        # 取消截止时间提示
        cancel_deadline_text = ""
        if is_business_meal:
            cancel_deadline_text = "当天11:00前可取消"
        else:
            is_lunch = earliest_date.hour < 15
            cancel_deadline_text = "当天9:30前可取消" if is_lunch else "当天15:00前可取消"

        return OrderDisplayInfo(
            status_text=status_text,
            status_class=status_class,
            cancel_deadline_text=cancel_deadline_text,
            action_permissions=permissions
        )

    @staticmethod
    def get_reservation_list(db: Session, user_id: int, date: str = None) -> List[Dict[str, Any]]:
        """获取预订列表（优化版本）"""
        data_list = []

        # 根据date参数决定查询方式
        if date:
            # 如果提供了日期，查询当天的预订
            start_of_day = datetime.strptime(date, "%Y-%m-%d")
            end_of_day = start_of_day + timedelta(days=1)
            reservation_request_list = reservation_request_dao.get_by_user_dining_time_range_all(
                db,
                user_id=user_id,
                start_time=start_of_day,
                end_time=end_of_day
            )
            # 过滤出当前用户的预订
            reservation_request_list = [r for r in reservation_request_list if r.user_id == user_id]
        else:
            # 如果没有提供日期，查询前后共各14天的预订
            today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            start_date = today - timedelta(days=14)
            end_date = today + timedelta(days=14)
            reservation_request_list = reservation_request_dao.get_by_user_dining_time_range_all(
                db,
                user_id=user_id,
                start_time=start_date,
                end_time=end_date
            )
            # 过滤出当前用户的预订
            reservation_request_list = [r for r in reservation_request_list if r.user_id == user_id]

        user = user_dao.get(db, user_id)

        for reservation_request in reservation_request_list:
            try:
                product = product_dao.get(db, reservation_request.product_id)
            except Exception as e:
                logger.info(f"商务餐无产品数据，product 置为 None: {reservation_request}")
                product = None

            rule_item = rule_item_dao.get(db, reservation_request.rule_item_id)

            order_item = order_item_dao.get(db, reservation_request.order_item_id)
            order = order_dao.get(db, order_item.order_id)
            reservation_request.reservation_period.split("_")
            start_time = '20' + reservation_request.reservation_period.split("_")[0]
            end_time = '20' + reservation_request.reservation_period.split("_")[1]
            start_time = datetime.strptime(start_time, "%Y%m%d%H%M")
            end_time = datetime.strptime(end_time, "%Y%m%d%H%M")
            verification_time = start_time.strftime("%Y-%m-%d")

            if rule_item.verified_start_time and rule_item.verified_end_time:
                # 直接使用预订开始时间当天的日期，加上指定的分钟数
                today = start_time.replace(hour=0, minute=0, second=0, microsecond=0)
                earliest_date = (today + timedelta(minutes=rule_item.verified_start_time)).strftime("%Y-%m-%d %H:%M")
                deadline = (today + timedelta(minutes=rule_item.verified_end_time)).strftime("%Y-%m-%d %H:%M")
                logger.info(f"核销时间范围规则有，使用核销时间范围: {earliest_date} - {deadline}")
            else:
                if rule_item.meal_type == RuleMealType.LUNCH:
                    earliest_date = start_time.strftime("%Y-%m-%d") + " 11:00"
                    deadline = start_time.strftime("%Y-%m-%d") + " 14:00"
                elif rule_item.meal_type == RuleMealType.DINNER:
                    earliest_date = start_time.strftime("%Y-%m-%d") + " 17:00"
                    deadline = start_time.strftime("%Y-%m-%d") + " 21:00"
                else:
                    earliest_date = start_time.strftime("%Y-%m-%d") + " 10:30"
                    deadline = start_time.strftime("%Y-%m-%d") + " 22:30"
                logger.info(f"核销时间范围规则无，使用默认时间范围: {earliest_date} - {deadline}")

            if date:
                if verification_time != date:
                    continue
            if reservation_request.status == ReservationStatus.PENDING:
                continue

            if order.payment_method == PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE:
                pay_mark = "企业支付"
            elif order.payment_method == PaymentMethod.ACCOUNT_BALANCE:
                pay_mark = "帐户支付"
            elif order.payment_method == PaymentMethod.WECHAT_PAY:
                pay_mark = "微信支付"
            elif order.payment_method == PaymentMethod.ALIPAY:
                pay_mark = "支付宝支付"
            elif order.payment_method == PaymentMethod.CASH:
                pay_mark = "现金支付"
            elif order.payment_method == PaymentMethod.BANK_TRANSFER:
                pay_mark = "银行转账"
            else:
                pay_mark = "其他支付"

            people = order_item.quantity
            biz_info = {}
            if product:
                if product.meal_type == MealType.BUSINESS:
                    # 如果是商务餐，使用 BizReservationRequest 的 persons 字段
                    biz_reservation = biz_reservation_request_dao.get(db, reservation_request.id)
                    people = biz_reservation.persons if biz_reservation else order_item.quantity
                    biz_info = {
                        "name": biz_reservation.name if biz_reservation else "",
                        "phone": biz_reservation.phone if biz_reservation else "",
                        "persons": biz_reservation.persons if biz_reservation else order_item.quantity,
                        "remark": biz_reservation.remark if biz_reservation else ""
                    }
                    mark_pay_state = "商务餐 ({})".format(pay_mark)
                elif product.meal_type == MealType.BUFFET:
                    people = order_item.quantity
                    mark_pay_state = "自助餐 ({})".format(pay_mark)
                elif product.meal_type == MealType.DINE_IN:
                    mark_pay_state = "临时点餐 ({})".format(pay_mark)
                else:
                    mark_pay_state = "菜品点餐 ({})".format(pay_mark)
            else:
                mark_pay_state = "商务餐 ({})".format(pay_mark)
                # 如果是商务餐，使用 BizReservationRequest 的 persons 字段
                biz_reservation = biz_reservation_request_dao.get(db, reservation_request.id)
                people = biz_reservation.persons if biz_reservation else order_item.quantity
                biz_info = {
                    "name": biz_reservation.name if biz_reservation else "",
                    "phone": biz_reservation.phone if biz_reservation else "",
                    "persons": biz_reservation.persons if biz_reservation else order_item.quantity,
                    "remark": biz_reservation.remark if biz_reservation else ""
                }

            # 在构建 request_data 之前，根据订单类型决定使用哪个价格
            if (product and product.meal_type == MealType.BUSINESS) or product is None:
                # 商务餐：使用整个订单的总价格（包括 product 为 None 的商务餐情况）
                display_price = order.total_amount
                if product is None:
                    logger.info(f"商务餐（无产品数据）：使用整个订单的总价格: {display_price}")
                else:
                    logger.info(f"商务餐：使用整个订单的总价格: {display_price}")
            else:
                # 自助餐等：使用单个订单项的价格
                display_price = order_item.payable_amount
                logger.info(f"自助餐：使用单个订单项的价格: {display_price}")

            # 前端显示状态，保持商务餐的支付状态效果
            if biz_info and order_item.status == OrderStatus.PARTIAL_PAID:
                order_item_status = OrderStatus.PAID
            else:
                order_item_status = order_item.status

            request_data = {
                "earliest_date": earliest_date,
                "deadline": deadline,
                "title": mark_pay_state,
                "status": order_item_status,
                "person": user.nickname,
                "date": verification_time + " " + start_time.strftime("%H:%M") + "-" + end_time.strftime("%H:%M"),
                "start_time": start_time.strftime("%Y-%m-%d %H:%M"),
                "end_time": end_time.strftime("%Y-%m-%d %H:%M"),
                "people": people,
                "price": display_price,  # 使用根据订单类型决定的价格
                "order_status": reservation_request.status,
                "reservation_request_time": order.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "order_no": order.order_no,
                "verification_code": reservation_request.verification_code,
                "order_id": order.id,
                "order_item_id": order_item.id,
                "is_modified": order.is_modified,
                "product_id": product.id if product else None,
                "rule_id": reservation_request.rule_id,
                "rule_item_id": reservation_request.rule_item_id,
                "user_id": user.id,
                "reservation_request_id": reservation_request.id,
                "reservation_period": reservation_request.reservation_period,
                "reservation_time": reservation_request.reservation_time,
                "reservation_status": reservation_request.status.value,
                "dining_start_time": reservation_request.dining_start_time,
                "dining_end_time": reservation_request.dining_end_time,
                "biz_info": biz_info
            }
            data_list.append(request_data)

        # 在返回数据之前，为每个订单计算权限和显示信息
        current_time = datetime.now()

        for request_data in data_list:
            # 计算订单权限和显示信息
            display_info = ReservationService.calculate_order_permissions(request_data, current_time)
            request_data["display_info"] = display_info.dict()

            # 添加一些前端需要的标识
            request_data["is_business_meal"] = bool(request_data.get("biz_info", {}).get("name"))
            request_data["is_paid"] = request_data["status"] in ["paid", "paid_full"]
            request_data["can_show_qr"] = display_info.action_permissions.can_show_qr
            request_data["can_cancel"] = display_info.action_permissions.can_cancel
            request_data["can_edit"] = display_info.action_permissions.can_edit
            request_data["can_show_order_detail"] = display_info.action_permissions.can_show_order_detail
            request_data["can_checkout"] = display_info.action_permissions.can_checkout

        # 按dining_start_time时间排序，时间最近的优先
        data_list.sort(key=lambda x: x["dining_start_time"], reverse=False)

        return data_list

    @staticmethod
    def cancel_reservation(db: Session, reservation_request_id: int) -> Dict[str, Any]:
        """取消预订"""
        reservation_request = reservation_request_dao.get(db, reservation_request_id)
        if reservation_request:
            reservation_request.status = ReservationStatus.CANCELLED
            db.commit()
            return True
        return False

reservation_service = ReservationService()
