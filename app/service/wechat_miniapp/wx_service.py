from typing import Optional, Dict, Any
import requests
import json
import time
import secrets
from app.core.config import settings
from wechatpayv3 import We<PERSON>hatPay, WeChatPayType
from app.utils.logger import logger

class WechatService:
    def __init__(self):
        try:
            # 直接读取私钥内容
            with open(settings.WXPAY_PRIVATE_KEY_PATH, 'r') as f:
                private_key_content = f.read()

            self.wechatpay = WeChatPay(
                wechatpay_type=WeChatPayType.MINIPROG,
                mchid=settings.WXPAY_MCHID,
                private_key=private_key_content,
                cert_serial_no=settings.WXPAY_SERIAL_NO,
                apiv3_key=settings.WXPAY_API_V3_KEY,
                appid=settings.WECHAT_APPID,
                notify_url=f"{settings.BASE_URL}/api/v1/wx/wxpay/notify",
                cert_dir="certs/wechat",
                logger=None,
                partner_mode=False
            )
        except Exception as e:
            raise Exception(f"初始化微信支付失败: {str(e)}")

    def create_jsapi_payment(self, openid: str, order_no: str, amount: float, description: str) -> Optional[Dict[str, Any]]:
        """创建 JSAPI 支付参数"""
        try:
            # 调用SDK创建支付
            result = self.wechatpay.pay(
                description=description,
                out_trade_no=order_no,
                amount={"total": int(amount * 100), "currency": "CNY"},
                payer={'openid': openid},
                scene_info={'payer_client_ip': "127.0.0.1"}  # TODO: 从请求中获取IP
            )

            if result[0] != 200:
                logger.error(f"微信支付API调用失败，状态码: {result[0]}, 响应: {result[1]}")
                return None

            mark_data = json.loads(result[1])
            if 'prepay_id' not in mark_data:
                logger.error(f"微信支付API返回数据缺少prepay_id: {mark_data}")
                return None

            mark_time = str(int(time.time()))
            mark_token_hex = secrets.token_hex(16)
            
            # 生成支付参数
            paySign = self.wechatpay.sign([
                settings.WECHAT_APPID, 
                mark_time, 
                mark_token_hex, 
                'prepay_id='+mark_data['prepay_id']
            ])

            return {
                "appId": settings.WECHAT_APPID,
                "timeStamp": mark_time,
                "nonceStr": mark_token_hex,
                "package": 'prepay_id='+mark_data['prepay_id'],
                "signType": "RSA",
                "paySign": paySign
            }

        except Exception as e:
            logger.error(f"创建JSAPI支付参数失败: {str(e)}")
            return None  # 返回None而不是抛出异常

    @staticmethod
    def send_subscribe_message(openid: str, template_id: str, data: Dict[str, Any]) -> bool:
        """发送订阅消息"""
        try:
            # 获取access_token
            token_url = f"{settings.WECHAT_OFFICIAL_DOMAIN}/cgi-bin/token?grant_type=client_credential&appid={settings.WECHAT_APPID}&secret={settings.WECHAT_SECRET}"
            token_response = requests.get(token_url)
            access_token = token_response.json().get('access_token')

            if not access_token:
                return False

            # 发送订阅消息的URL
            url = f"{settings.WECHAT_OFFICIAL_DOMAIN}/cgi-bin/message/subscribe/send?access_token={access_token}"

            # 构建请求数据
            message_data = {
                "touser": openid,
                "template_id": template_id,
                "page": "pages/topic/topic",  # 点击消息后跳转的页面
                "data": data
            }

            # 发送请求
            response = requests.post(url, json=message_data)
            result = response.json()

            return result.get('errcode') == 0

        except Exception as e:
            return False

    def create_refund(self, transaction_id: str, out_refund_no: str, total_amount: float, refund_amount: float, reason: str = None) -> Optional[Dict[str, Any]]:
        """申请退款

        Args:
            transaction_id: 微信支付订单号
            out_refund_no: 商户退款单号
            total_amount: 原订单金额，单位元
            refund_amount: 退款金额，单位元
            reason: 退款原因

        Returns:
            Optional[Dict[str, Any]]: 退款结果，None表示退款失败
        """
        try:
            # 转换金额为分
            total_amount_cents = int(total_amount * 100)
            refund_amount_cents = int(refund_amount * 100)

            # 构建退款参数
            refund_params = {
                "transaction_id": transaction_id,
                "out_refund_no": out_refund_no,
                "amount": {
                    "refund": refund_amount_cents,
                    "total": total_amount_cents,
                    "currency": "CNY"
                }
            }

            # 如果有退款原因，添加到参数中
            if reason:
                refund_params["reason"] = reason

            # 调用微信支付SDK发起退款
            result = self.wechatpay.refund(**refund_params)

            # 检查退款结果
            if result[0] != 200:
                return None

            refund_data = json.loads(result[1])
            return refund_data

        except Exception as e:
            raise Exception(f"申请退款失败: {str(e)}")

    def query_refund(self, out_refund_no: str) -> Optional[Dict[str, Any]]:
        """查询退款状态

        Args:
            out_refund_no: 商户退款单号

        Returns:
            Optional[Dict[str, Any]]: 退款查询结果，None表示查询失败
        """
        try:
            # 调用微信支付SDK查询退款
            result = self.wechatpay.query_refund(out_refund_no=out_refund_no)

            # 检查查询结果
            if result[0] != 200:
                return None

            refund_data = json.loads(result[1])
            return refund_data

        except Exception as e:
            raise Exception(f"查询退款状态失败: {str(e)}")

wechat_service = WechatService()