from app.core.config import settings
from app.messager import MessageSenderFactory, Message, MessageType, MessagePriority, MessageRecipient, MessageContent


async def send_dingtalk_message(title: str, body: str):
    """
    发送钉钉消息，使用配置文件中的钉钉配置
    """
    custom_dingtalk_config = {
        "webhook_url": settings.DINGTALK_WEBHOOK_URL,
        "secret": settings.DINGTALK_SECRET or "",
        "at_mobiles": settings.DINGTALK_AT_MOBILES.split(',') if settings.DINGTALK_AT_MOBILES else [],
        "at_user_ids": settings.DINGTALK_AT_USER_IDS.split(',') if settings.DINGTALK_AT_USER_IDS else [],
        "is_at_all": settings.DINGTALK_IS_AT_ALL
    }

    dingtalk_sender = MessageSenderFactory.create_dingtalk_sender(custom_dingtalk_config)
    message = Message(
        type=MessageType.DINGTALK,
        priority=MessagePriority.NORMAL,
        recipients=[
            MessageRecipient(
                id="yh_vegan_group",
                name="乙禾素运营",
                contact="yh_vegan_group"
            )
        ],
        content=MessageContent(
            title=title,
            body=body,
            template_data={
                "at_mobiles": settings.DINGTALK_AT_MOBILES.split(',') if settings.DINGTALK_AT_MOBILES else [],
                "is_at_all": settings.DINGTALK_IS_AT_ALL
            }
        )
    )
    result = await dingtalk_sender.send_batch(message)
    return result


async def send_miniapp_message(recipients: list, template_id: str, template_data: dict):
    custom_miniapp_config = {
        "template_id": template_id,
        "miniprogram": {
            "appid": settings.WECHAT_APPID,
            "pagepath": "pages/admin/statistic/statistic"
        }
    }

    wechat_miniapp_sender = MessageSenderFactory.create_wechat_miniapp_sender(custom_miniapp_config)
    
    # 确保recipients是MessageRecipient对象列表，如果不是则转换
    processed_recipients = []
    for recipient in recipients:
        if isinstance(recipient, MessageRecipient):
            processed_recipients.append(recipient)
        elif isinstance(recipient, dict):
            processed_recipients.append(MessageRecipient(**recipient))
        else:
            # 如果是其他类型，尝试转换为字典
            try:
                if hasattr(recipient, '__dict__'):
                    processed_recipients.append(MessageRecipient(**recipient.__dict__))
                else:
                    # 跳过无法处理的recipient
                    continue
            except Exception:
                continue
    
    message = Message(
        type=MessageType.WECHAT_MINIAPP,
        priority=MessagePriority.NORMAL,
        recipients=processed_recipients,
        content=MessageContent(
            title="",
            body="",
            template_id=template_id,
            template_data=template_data
        )
    )
    result = await wechat_miniapp_sender.send_batch(message)

    print(f"自定义发送器结果: 总数={result.total_count}, 成功={result.success_count}, 失败={result.failed_count}")
