import random
import re
import string

import arrow
import secrets
import datetime
from datetime import datetime as dt
import requests
import unicodedata
import json
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.sms.v20210111 import sms_client, models
from app.core.config import settings


def get_current_time():
    """获取当前时区的时间"""
    return arrow.now('Asia/Shanghai').datetime


def get_50_years_after_current_time():
    """获取当前时区的时间"""
    return arrow.now('Asia/Shanghai').shift(years=50).datetime


def get_phone_number(code, wechat_appid=None, wechat_secret=None):
    """通过code获取用户手机号"""
    try:
        # 获取小程序全局接口调用凭据
        access_token_url = f"{settings.WECHAT_OFFICIAL_DOMAIN}/cgi-bin/token?grant_type=client_credential&appid={wechat_appid}&secret={wechat_secret}"
        response = requests.get(access_token_url)
        access_token = response.json().get('access_token')

        if not access_token:
            print("获取access_token失败")
            return None

        # 获取手机号
        phone_url = f"{settings.WECHAT_OFFICIAL_DOMAIN}/wxa/business/getuserphonenumber?access_token={access_token}"
        data = {"code": code}
        response = requests.post(phone_url, json=data)
        result = response.json()

        if result.get('errcode') == 0:
            # 成功获取到手机号
            phone_info = result.get('phone_info')
            return phone_info.get('phoneNumber')
        else:
            print(f"获取手机号失败: {result}")
            return None

    except Exception as e:
        print(f"获取手机号异常: {str(e)}")
        return None


def product_wx_token():
    """生成并保存token和过期时间到用户记录"""
    token = secrets.token_hex(16)
    token_expiry = datetime.datetime.now() + datetime.timedelta(days=30)
    return token, token_expiry


def secure_filename(filename: str) -> str:
    """
    安全地处理文件名

    Args:
        filename: 原始文件名

    Returns:
        str: 处理后的安全文件名
    """
    # 将 Unicode 文件名转换为 ASCII
    filename = unicodedata.normalize('NFKD', filename)
    filename = filename.encode('ascii', 'ignore').decode('ascii')

    # 移除除了字母、数字、点、下划线和连字符之外的所有字符
    filename = re.sub(r'[^a-zA-Z0-9._-]', '', filename)

    # 确保文件名不以点开始（避免隐藏文件）
    filename = filename.strip('.')

    # 如果文件名为空，返回默认名称
    if not filename:
        filename = 'unnamed_file'

    return filename


def allowed_file(filename: str) -> bool:
    """
    检查文件类型是否允许

    Args:
        filename: 文件名

    Returns:
        bool: 文件类型是否允许
    """
    return '.' in filename and \
        filename.rsplit('.', 1)[1].lower() in {'png', 'jpg', 'jpeg', 'gif'}


def send_sms(phone_number, validate_code):
    try:
        # 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
        cred = credential.Credential(settings.SMS_SECRET_ID, settings.SMS_SECRET_KEY)
        # 实例化一个http选项，可选的，没有特殊需求可以跳过
        httpProfile = HttpProfile()
        httpProfile.endpoint = "sms.tencentcloudapi.com"

        # 实例化一个client选项，可选的，没有特殊需求可以跳过
        clientProfile = ClientProfile()
        clientProfile.httpProfile = httpProfile

        # 实例化要请求产品的client对象,clientProfile是可选的
        client = sms_client.SmsClient(cred, "ap-guangzhou", clientProfile)

        # 实例化一个请求对象,每个接口都会对应一个request对象
        req = models.SendSmsRequest()
        params = {
            "PhoneNumberSet": [phone_number],
            "SmsSdkAppId": settings.SMS_SMSSDK_APPID,
            "SignName": settings.SMS_SIGN_NAME,
            "TemplateId": settings.SMS_TEMPLATE_ID,
            "TemplateParamSet": [validate_code],
        }
        req.from_json_string(json.dumps(params))

        # 返回的resp是一个SendSmsResponse的实例，与请求对象对应
        resp = client.SendSms(req)

        # 输出json格式的字符串回包
        result = json.loads(resp.to_json_string())
        print(f'{type(result)}, {result}')
        if result['SendStatusSet'][0]['Code'] == 'Ok':
            print('短信发送成功')
            return True
        else:
            print('短信发送失败')
            return False
    except TencentCloudSDKException as err:
        print('短信发送失败: {}'.format(err))
        return False


def create_order_no():
    now = dt.now()
    # 生成格式: 年月日时分秒+6位随机数
    random_num = ''.join(random.choices(string.digits, k=6))
    order_no = f"O{now.strftime('%Y%m%d%H%M%S')}{random_num}"
    return order_no
