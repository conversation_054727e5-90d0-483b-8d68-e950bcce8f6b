import os
import datetime
from pathlib import Path
from typing import Tuple

from fastapi import UploadFile
from PIL import Image

from app.core.config import settings


def save_uploaded_image(upload_file: UploadFile, folder_name: str = "contents") -> Tuple[str, str]:
    """
    保存上传的图片文件并生成缩略图
    
    Args:
        upload_file: 上传的文件对象
        folder_name: 子文件夹名称，默认为 'contents'
        
    Returns:
        Tuple[str, str]: 保存的图片URL和缩略图URL
    """
    # 创建日期格式的目录名 (YYMMDD)
    today = datetime.datetime.now()
    date_dir = today.strftime("%y%m%d")
    
    # 创建保存路径
    upload_dir = Path(settings.UPLOAD_FOLDER) / folder_name / date_dir
    os.makedirs(upload_dir, exist_ok=True)
    
    # 处理文件名
    file_ext = os.path.splitext(upload_file.filename)[1]
    file_name = f"{today.strftime('%H%M%S')}_{os.urandom(4).hex()}{file_ext}"
    
    # 完整的文件保存路径
    file_path = upload_dir / file_name
    thumbnail_name = f"{os.path.splitext(file_name)[0]}_thumbnail{file_ext}"
    thumbnail_path = upload_dir / thumbnail_name
    
    # 保存原图
    with open(file_path, "wb") as f:
        f.write(upload_file.file.read())
    
    # 生成缩略图
    img = Image.open(file_path)
    img.thumbnail((200, 200))  # 设置缩略图尺寸
    img.save(thumbnail_path)
    
    # 返回相对路径 (相对于静态文件根目录)
    base_path = f"{folder_name}/{date_dir}"
    image_url = f"/static/uploads/{base_path}/{file_name}"
    thumbnail_url = f"/static/uploads/{base_path}/{thumbnail_name}"
    
    return image_url, thumbnail_url 