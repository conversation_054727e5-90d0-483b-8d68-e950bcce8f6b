import logging
import os
import sys
from logging.handlers import TimedRotatingFileHandler
from pathlib import Path

from app.core.config import settings


class SQLAlchemyFilter(logging.Filter):
    """
    用于过滤 SQLAlchemy 日志的过滤器
    """
    def filter(self, record):
        return settings.DB_LOGGING_ENABLED


def setup_logger():
    """
    设置日志系统
    1. 日志保存在logs目录下，按日滚动
    2. 日志同时输出到stdout
    3. 数据库日志可配置开关
    """
    # 确保日志目录存在
    log_dir = Path("logs")
    os.makedirs(log_dir, exist_ok=True)
    
    # 创建日志格式
    log_format = logging.Formatter(
        "%(levelname)s:\t%(asctime)s - %(name)s - %(message)s"
    )
    
    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # 清除现有处理器，避免重复配置
    if root_logger.handlers:
        for handler in root_logger.handlers:
            root_logger.removeHandler(handler)
    
    # 配置文件处理器 - 按天滚动
    file_handler = TimedRotatingFileHandler(
        filename=log_dir / "app.log",
        when="midnight",
        interval=1,
        backupCount=30,  # 保留30天的日志
        encoding="utf-8",
    )
    file_handler.setFormatter(log_format)
    root_logger.addHandler(file_handler)
    
    # 配置控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(log_format)
    root_logger.addHandler(console_handler)
    
    # 配置SQLAlchemy日志过滤
    # 为所有SQLAlchemy相关的日志器应用过滤器
    for logger_name in ["sqlalchemy", "sqlalchemy.engine", "sqlalchemy.engine.base", "sqlalchemy.engine.Engine"]:
        sql_logger = logging.getLogger(logger_name)
        # 设置日志级别
        sql_logger.setLevel(logging.INFO)
        # 清除现有过滤器
        for existing_filter in list(sql_logger.filters):
            if isinstance(existing_filter, SQLAlchemyFilter):
                sql_logger.removeFilter(existing_filter)
        # 添加新的过滤器
        sql_filter = SQLAlchemyFilter()
        sql_logger.addFilter(sql_filter)

        # 如果禁用了数据库日志，则完全禁用这些日志器的传播
        if not settings.DB_LOGGING_ENABLED:
            sql_logger.propagate = False
    
    # 其他常用库的日志级别配置
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("fastapi").setLevel(logging.INFO)
    
    return root_logger


logger = setup_logger() 