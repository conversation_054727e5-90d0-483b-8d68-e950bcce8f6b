Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示优惠券列表
    visible: {
      type: Boolean,
      value: false
    },
    // 订单总金额，用于计算优惠券是否可用
    orderAmount: {
      type: Number,
      value: 0
    },
    // 已选择的优惠券ID
    selectedCouponId: {
      type: String,
      value: ''
    },
    // 是否允许多选
    allowMultiple: {
      type: Boolean,
      value: false
    },
    // 已选择的优惠券ID列表
    selectedCouponIds: {
      type: Array,
      value: []
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    loading: false,
    availableCoupons: [],
    expiredCoupons: [],
    usedCoupons: [],
    currentTab: 'available', // available, expired, used
    tabs: [
      { key: 'available', name: '可使用' },
      { key: 'expired', name: '已过期' },
      { key: 'used', name: '已使用' }
    ]
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 关闭弹窗
    onClose() {
      this.triggerEvent('close');
    },

    // 切换标签页
    onTabChange(e) {
      const tab = e.currentTarget.dataset.tab;
      this.setData({
        currentTab: tab
      });
    },

    // 选择优惠券
    onSelectCoupon(e) {
      const coupon = e.currentTarget.dataset.coupon;
      const couponId = coupon.coupon_usage_record.id.toString();

      // 检查优惠券是否可用
      if (!this.isCouponAvailable(coupon)) {
        wx.showToast({
          title: '该优惠券不可用',
          icon: 'none'
        });
        return;
      }

      const currentSelectedIds = this.properties.selectedCouponIds || [];
      
      if (this.properties.allowMultiple) {
        // 多选模式
        if (currentSelectedIds.includes(couponId)) {
          // 取消选择
          const newSelectedIds = currentSelectedIds.filter(id => id !== couponId);
          this.triggerEvent('select', { 
            couponIds: newSelectedIds,
            coupons: this.getCouponsByIds(newSelectedIds)
          });
        } else {
          // 添加选择
          const newSelectedIds = [...currentSelectedIds, couponId];
          this.triggerEvent('select', { 
            couponIds: newSelectedIds,
            coupons: this.getCouponsByIds(newSelectedIds)
          });
        }
      } else {
        // 单选模式
        if (currentSelectedIds.includes(couponId)) {
          // 取消选择
          this.triggerEvent('select', { 
            couponIds: [],
            coupons: []
          });
        } else {
          // 选择单张
          this.triggerEvent('select', { 
            couponIds: [couponId],
            coupons: [coupon]
          });
        }
      }
    },

    // 根据ID列表获取优惠券对象列表
    getCouponsByIds(couponIds) {
      const allCoupons = [
        ...this.data.availableCoupons,
        ...this.data.expiredCoupons,
        ...this.data.usedCoupons
      ];
      
      return allCoupons.filter(coupon => 
        couponIds.includes(coupon.coupon_usage_record.id.toString())
      );
    },

    // 加载优惠券列表
    loadCoupons() {
      if (this.data.loading) return;
      
      this.setData({ loading: true });
      
      const app = getApp();
      wx.request({
        url: `${app.globalData.baseUrl}/coupon/user-coupons`,
        method: 'GET',
        header: {
          'token': wx.getStorageSync('token')
        },
        success: (res) => {
          if (res.data.status === 200) {
            const data = res.data.data;

            // 预处理优惠券数据，添加格式化的折扣文本
            const processedAvailable = this.processCouponsData(data.available_coupons || []);
            const processedExpired = this.processCouponsData(data.expired_coupons || []);
            const processedUsed = this.processCouponsData(data.used_coupons || []);

            this.setData({
              availableCoupons: processedAvailable,
              expiredCoupons: processedExpired,
              usedCoupons: processedUsed
            });
          } else {
            wx.showToast({
              title: res.data.message || '加载失败',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          console.error('加载优惠券失败', err);
          wx.showToast({
            title: '网络错误',
            icon: 'none'
          });
        },
        complete: () => {
          this.setData({ loading: false });
        }
      });
    },

    // 处理优惠券数据，添加格式化的折扣文本和唯一ID
    processCouponsData(coupons) {
      return coupons.map(couponData => {
        const coupon = couponData.coupon;
        const couponBatch = couponData.coupon_batch;
        let displayText = '';
        let conditionText = '';
        let isExpired = false;

        // 判断优惠券是否过期
        const now = new Date();
        const startTime = new Date(couponBatch.start_time);
        const endTime = new Date(couponBatch.end_time);
        
        if (now < startTime) {
          // 未开始
          isExpired = false;
        } else if (now > endTime) {
          // 已过期
          isExpired = true;
        } else {
          // 有效期内
          isExpired = false;
        }

        if (coupon.type === 'cash') {
          // 现金券
          displayText = `现金券`;
          conditionText = `¥${coupon.amount}元`;
        } else if (coupon.type === 'full_reduction') {
          // 满减券
          displayText = `满减券`;
          conditionText = `满${coupon.full_amount}减${coupon.reduction_amount}`;
        } else if (coupon.type === 'discount') {
          // 折扣券
          const discount = (coupon.discount_rate * 10).toFixed(1);
          displayText = `折扣券`;
          conditionText = `${discount}折`;
        } else {
          // 其他类型
          displayText = '优惠券';
          conditionText = '';
        }

        return {
          ...couponData,
          displayText: displayText,
          conditionText: conditionText,
          isExpired: isExpired,
          formattedEndTime: couponBatch.end_time.split('T')[0], // 格式化有效期，只保留日期
          uniqueId: couponData.coupon_usage_record.id.toString() // 添加唯一ID用于wx:key
        };
      });
    },

    // 检查优惠券是否可用
    isCouponAvailable(couponData) {
      const coupon = couponData.coupon;
      const orderAmount = this.properties.orderAmount;

      // 检查是否有不可用原因
      if (couponData.unavailable_reasons && couponData.unavailable_reasons.length > 0) {
        return false;
      }

      // 检查是否过期
      if (couponData.isExpired) {
        return false;
      }

      // 检查金额条件
      if (coupon.condition_amount > 0 && orderAmount < coupon.condition_amount) {
        return false;
      }

      return true;
    },

    // 计算优惠券折扣金额
    calculateDiscount(couponData, orderAmount) {
      const coupon = couponData.coupon;

      if (!this.isCouponAvailable(couponData)) {
        return 0;
      }

      if (coupon.type === 'cash') {
        // 现金券：直接返回券面金额
        return coupon.amount;
      } else if (coupon.type === 'full_reduction') {
        // 满减券：检查是否满足满减条件
        if (orderAmount >= coupon.full_amount) {
          return coupon.reduction_amount;
        }
        return 0;
      } else if (coupon.type === 'discount') {
        // 折扣券：检查最低消费，计算折扣金额，限制最大折扣
        if (orderAmount < coupon.min_amount) {
          return 0;
        }
        
        const discountAmount = orderAmount * (1 - coupon.discount_rate);
        return Math.min(discountAmount, coupon.max_discount);
      }

      return 0;
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例进入页面节点树时执行
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 组件所在的页面被展示时执行
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'visible': function(visible) {
      if (visible) {
        this.loadCoupons();
      }
    }
  }
});
