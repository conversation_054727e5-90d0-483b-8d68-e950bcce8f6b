<view class="coupon-modal" wx:if="{{visible}}">
  <view class="coupon-mask" bindtap="onClose"></view>
  <view class="coupon-container">
    <!-- 头部 -->
    <view class="coupon-header">
      <text class="coupon-title">优惠券</text>
      <view class="close-btn" bindtap="onClose">×</view>
    </view>
    
    <!-- 标签页 -->
    <view class="coupon-tabs">
      <view 
        class="tab-item {{currentTab === item.key ? 'active' : ''}}"
        wx:for="{{tabs}}" 
        wx:key="key"
        bindtap="onTabChange"
        data-tab="{{item.key}}"
      >
        {{item.name}}
      </view>
    </view>
    
    <!-- 内容区域 -->
    <view class="coupon-content">
      <!-- 加载状态 -->
      <view class="loading-container" wx:if="{{loading}}">
        <view class="loading-spinner"></view>
        <text>加载中...</text>
      </view>
      
      <!-- 可使用优惠券 -->
      <view class="coupon-list" wx:if="{{currentTab === 'available' && !loading}}">
        <view
          class="coupon-item {{(item.unavailable_reasons && item.unavailable_reasons.length > 0) || (item.coupon.condition_amount > 0 && orderAmount < item.coupon.condition_amount) ? 'disabled' : 'available'}} {{selectedCouponId === item.coupon_usage_record.id ? 'selected' : ''}}"
          wx:for="{{availableCoupons}}"
          wx:key="uniqueId"
          bindtap="onSelectCoupon"
          data-coupon="{{item}}"
          data-type="{{item.coupon.type}}"
        >
          <view class="coupon-left">
            <view class="coupon-amount">
              {{item.displayText}}
            </view>
            <view class="coupon-condition">
              {{item.conditionText}}
            </view>
          </view>
          <view class="coupon-right">
            <view class="coupon-name">{{item.coupon.name}}</view>
            <view class="coupon-desc">{{item.coupon.description}}</view>
            <view class="coupon-time">
              有效期至 {{item.formattedEndTime}}
            </view>
            <view class="coupon-unavailable" wx:if="{{item.unavailable_reasons && item.unavailable_reasons.length > 0}}">
              {{item.unavailable_reasons[0]}}
            </view>
          </view>
          <view class="coupon-select">
            <view class="select-btn {{selectedCouponIds.includes(item.coupon_usage_record.id.toString()) ? 'selected' : ''}}">
              {{selectedCouponIds.includes(item.coupon_usage_record.id.toString()) ? '已选择' : '使用'}}
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{availableCoupons.length === 0}}">
          <text>暂无可用优惠券</text>
        </view>
      </view>
      
      <!-- 不可用优惠券 -->
      <view class="coupon-list" wx:if="{{currentTab === 'unavailable' && !loading}}">
        <view
          class="coupon-item disabled"
          wx:for="{{unavailableCoupons}}"
          wx:key="uniqueId"
          data-type="{{item.coupon.type}}"
        >
          <view class="coupon-left">
            <view class="coupon-amount">
              {{item.displayText}}
            </view>
            <view class="coupon-condition">
              {{item.conditionText}}
            </view>
          </view>
          <view class="coupon-right">
            <view class="coupon-name">{{item.coupon.name}}</view>
            <view class="coupon-desc">{{item.coupon.description}}</view>
            <view class="coupon-time">
              有效期至 {{item.formattedEndTime}}
            </view>
            <view class="coupon-unavailable" wx:if="{{item.unavailable_reasons && item.unavailable_reasons.length > 0}}">
              {{item.unavailable_reasons[0]}}
            </view>
          </view>
          <view class="coupon-select">
            <view class="select-btn unavailable">不可用</view>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{unavailableCoupons.length === 0}}">
          <text>暂无不可用优惠券</text>
        </view>
      </view>
      
      <!-- 已使用优惠券 -->
      <view class="coupon-list" wx:if="{{currentTab === 'used' && !loading}}">
        <view
          class="coupon-item disabled"
          wx:for="{{usedCoupons}}"
          wx:key="uniqueId"
          data-type="{{item.coupon.type}}"
        >
          <view class="coupon-left">
            <view class="coupon-amount">
              {{item.displayText}}
            </view>
            <view class="coupon-condition">
              {{item.conditionText}}
            </view>
          </view>
          <view class="coupon-right">
            <view class="coupon-name">{{item.coupon.name}}</view>
            <view class="coupon-desc">{{item.coupon.description}}</view>
            <view class="coupon-time">
              使用时间 {{item.coupon_usage_record.used_at}}
            </view>
          </view>
          <view class="coupon-select">
            <view class="select-btn disabled">已使用</view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" wx:if="{{usedCoupons.length === 0}}">
          <text>暂无使用记录</text>
        </view>
      </view>
    </view>
  </view>
</view>
