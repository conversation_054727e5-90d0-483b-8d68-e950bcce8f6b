@startuml 订单主流程

start
:获取订购产品列表;
:生成订单对象;

repeat :遍历订购产品列表生成订单项;
    :生成订单项;
    if (订购产品包含预订清单?) then (是)
        repeat :遍历预订清单生成请求;
            :检查可预订数量;
            if (可预订数量已满?) then (是)
                :报错返回;
                stop
            else (否)
                :生成预订请求;
                :预订请求设置为处理中;
            endif
        repeat while (还有未处理的预订?) is (否)
    else (否)
        -[#gray]->;
    endif
repeat while (还有未处理的产品?) is (否)

repeat :遍历订单项计算价格;
    if (订购产品存在价格策略?) then (是)
        :获取该订购产品价格策略;
        :计算此订单项价格;
    else (否)
        :使用产品默认价格;
    endif
    :累加至订单总金额;
repeat while (还有未计算的订单项?) is (否)

if (存在订单级价格策略?) then (是)
    :获取订单级价格策略（如满减/折扣等）;
    :根据策略重新计算订单总价格;
else (否)
    -[#gray]->;
endif

if (是否允许人工改价?) then (是)
    :人工输入调整金额;
else (否)
    -[#gray]->;
endif

:提交保存生成的订单;
stop
@enduml