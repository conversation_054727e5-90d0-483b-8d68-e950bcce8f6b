@startuml 预订检查流程
start

:获取所有传入的规则项rule_item对应的规则rule;

repeat :循环迭代每个规则rule;
    if (规则rule是否绑定了传入的产品prodct？) then (是)
        if (规则rule在可预订时间范围内？) then (是)
        else (否)
            :返回失败;
            stop
        endif
    else (否)
        :返回失败;
        stop
    endif
repeat while (是否还有规则rule未检查？) is (是)
-> 否;

repeat :循环迭代每个规则项;
    :计算规则项下有效的预约请求数;
    if (预约请求数 < 限定数量？) then (是)
    else (否)
        :返回失败;
        stop
    endif
repeat while (是否还有规则项未检查？) is (是)
-> 否;

:返回成功;
stop
@enduml