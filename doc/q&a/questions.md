# alembic修改枚举存在问题
alembic revision --autogenerate -m "修改枚举"无法对已存在的枚举进行修改
需要单独生成迁移文件，并手动写迁移脚本

例子如下：  
def upgrade() -> None:
    """Upgrade schema."""
    # 将现有的 ContentType 枚举修改为包含 DISH 选项
    op.alter_column('contents', 'type',
                    existing_type=sa.Enum('CONTENT', 'ARTICLE', name='contenttype'),
                    type_=sa.Enum('CONTENT', 'ARTICLE', 'DISH', name='contenttype'),
                    existing_nullable=True)


def downgrade() -> None:
    """Downgrade schema."""
    # 移除 DISH 选项，恢复到之前的枚举值
    op.alter_column('contents', 'type',
                    existing_type=sa.Enum('CONTENT', 'ARTICLE', 'DISH', name='contenttype'),
                    type_=sa.Enum('CONTENT', 'ARTICLE', name='contenttype'),
                    existing_nullable=True)

# 定时更新订单状态
SET GLOBAL event_scheduler = ON;

## 1.提交订单但未支付超过10分钟，设置为取消状态
CREATE EVENT cancel_unpaid_orders
ON SCHEDULE EVERY 5 MINUTE
DO
    UPDATE orders
    SET
        status = 'CANCELLED',
        updated_at = NOW()
    WHERE
        status = 'PENDING'
        AND payment_status = 'UNPAID'
        AND created_at <= DATE_SUB(NOW(), INTERVAL 10 MINUTE);

## 2.提交订单已支付的预订订单，超过就餐时间的预订订单，设置为已核销。
CREATE EVENT check_reservation_status
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_DATE + INTERVAL 13 HOUR + INTERVAL 30 MINUTE
DO
BEGIN
    UPDATE reservation_requests
    SET status = 'AUTO_VERIFIED'
    WHERE status = 'PAID_FULL'
    AND STR_TO_DATE(
        SUBSTRING_INDEX(reservation_period, '_', -1),
        '%y%m%d%H%i'
    ) < NOW();
END;

## 3.更新旧数据从 reservation_period 提取就餐开始和结束时间
SELECT 
    id,
    reservation_period,
    STR_TO_DATE(
        CONCAT(
            '20',
            SUBSTRING(reservation_period, 1, 2),
            '-',
            SUBSTRING(reservation_period, 3, 2),
            '-',
            SUBSTRING(reservation_period, 5, 2),
            ' ',
            SUBSTRING(reservation_period, 7, 2),
            ':',
            SUBSTRING(reservation_period, 9, 2),
            ':00'
        ),
        '%Y-%m-%d %H:%i:%s'
    ) as test_start_time,
    STR_TO_DATE(
        CONCAT(
            '20',
            SUBSTRING(reservation_period, 12, 2),
            '-',
            SUBSTRING(reservation_period, 14, 2),
            '-',
            SUBSTRING(reservation_period, 16, 2),
            ' ',
            SUBSTRING(reservation_period, 18, 2),
            ':',
            SUBSTRING(reservation_period, 20, 2),
            ':00'
        ),
        '%Y-%m-%d %H:%i:%s'
    ) as test_end_time
FROM reservation_requests
WHERE 
    reservation_period IS NOT NULL 
    AND LENGTH(reservation_period) = 21
    AND dining_start_time IS NULL
LIMIT 5;

UPDATE reservation_requests
SET 
    dining_start_time = STR_TO_DATE(
        CONCAT(
            '20',
            SUBSTRING(reservation_period, 1, 2),  -- 年份
            '-',
            SUBSTRING(reservation_period, 3, 2),  -- 月份
            '-',
            SUBSTRING(reservation_period, 5, 2),  -- 日期
            ' ',
            SUBSTRING(reservation_period, 7, 2),  -- 小时
            ':',
            SUBSTRING(reservation_period, 9, 2),  -- 分钟
            ':00'
        ),
        '%Y-%m-%d %H:%i:%s'
    ),
    dining_end_time = STR_TO_DATE(
        CONCAT(
            '20',
            SUBSTRING(reservation_period, 12, 2),  -- 年份
            '-',
            SUBSTRING(reservation_period, 14, 2),  -- 月份
            '-',
            SUBSTRING(reservation_period, 16, 2),  -- 日期
            ' ',
            SUBSTRING(reservation_period, 18, 2),  -- 小时
            ':',
            SUBSTRING(reservation_period, 20, 2),  -- 分钟
            ':00'
        ),
        '%Y-%m-%d %H:%i:%s'
    )
WHERE 
    reservation_period IS NOT NULL 
    AND LENGTH(reservation_period) = 21  -- 确保格式正确 (YYMMDDHHMM_YYMMDDHHMM)
    AND dining_start_time IS NULL;  -- 只更新未设置的数据


# V1.2.0
## 数据库定时器
### 预订订单自动核销功能，跳过商务餐。
CREATE EVENT check_reservation_status
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_DATE + INTERVAL 13 HOUR + INTERVAL 30 MINUTE
DO
BEGIN
    UPDATE reservation_requests
    SET status = 'AUTO_VERIFIED'
    WHERE status = 'PAID_FULL'
    AND type != 'biz_dining_reservation'
    AND STR_TO_DATE(
        SUBSTRING_INDEX(reservation_period, '_', -1),
        '%y%m%d%H%i'
    ) < NOW();
END;

## 配置消息订阅
### 如图：
### 安装环境依赖
pip install apscheduler

## 配置扫码跳入小程序页面订单
### 如图：

## 创建商务餐规则、规则项、菜品类别、菜品内容

## 小程序素材
wget https://robby1995.com/githubusercontent/lubaogui-cn/LuBaoGuiPicBed/master/202506231531094.jpg
cp 202506231531094.jpg /static/images

# V1.2.8
### 预订订单自动核销功能，跳过商务餐，不同时间段核销不一样。
-- 午餐：13:30 自动核销
CREATE EVENT check_reservation_status
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_DATE + INTERVAL 13 HOUR + INTERVAL 30 MINUTE
DO
BEGIN
    UPDATE reservation_requests r
    JOIN rule_items ri ON r.rule_item_id = ri.id
    SET r.status = 'AUTO_VERIFIED'
    WHERE r.status = 'PAID_FULL'
    AND r.type != 'biz_dining_reservation'
    AND ri.meal_type = 'LUNCH'
    AND STR_TO_DATE(
        SUBSTRING_INDEX(r.reservation_period, '_', -1),
        '%y%m%d%H%i'
    ) < NOW();
END;

-- 晚餐：20:30 自动核销
CREATE EVENT check_reservation_dinner_status
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_DATE + INTERVAL 20 HOUR + INTERVAL 30 MINUTE
DO
BEGIN
    UPDATE reservation_requests r
    JOIN rule_items ri ON r.rule_item_id = ri.id
    SET r.status = 'AUTO_VERIFIED'
    WHERE r.status = 'PAID_FULL'
    AND r.type != 'biz_dining_reservation'
    AND ri.meal_type = 'DINNER'
    AND STR_TO_DATE(
        SUBSTRING_INDEX(r.reservation_period, '_', -1),
        '%y%m%d%H%i'
    ) < NOW();
END;


-- 商务餐通用：每小时检查一次自动核销
CREATE EVENT check_biz_dining_hourly
ON SCHEDULE EVERY 1 MINUTE
STARTS CURRENT_DATE + INTERVAL 1 MINUTE
DO
BEGIN
    UPDATE reservation_requests r
    SET r.status = 'AUTO_VERIFIED'
    WHERE r.status = 'PAID_FULL'
    AND r.type = 'BIZ_DINING_RESERVATION'
    AND STR_TO_DATE(
        SUBSTRING_INDEX(r.reservation_period, '_', -1),
        '%y%m%d%H%i'
    ) < NOW();
END;

-- 提交订单但未支付超过10分钟，设置为取消状态
CREATE EVENT cancel_unpaid_orders
ON SCHEDULE EVERY 5 MINUTE
DO
    UPDATE orders
    SET
        status = 'CANCELLED',
        updated_at = NOW()
    WHERE
        status = 'PENDING'
        AND payment_status = 'UNPAID'
        AND created_at <= DATE_SUB(NOW(), INTERVAL 10 MINUTE);

# .env 配置
## 个人固定支付金额配置（单位：元）
PERSONAL_PAYMENT_FIXED_AMOUNT = 3.00

## 企业自助晚餐开放企业列表
OPEN_ENTERPRISE_LIST = ["广州乙禾素饮食文化有限公司"]


# 1.3.4
## 订单展示规则
订购截止时间（order_deadline）：为预订规则的开始时间+order_deadline
取消截止时间（cancellation_deadline）：为预订规则的截止时间+cancellation_deadline
新增下面2个字段：
核销开始时间（verified_start_time）：为当天0点+verified_start_time
核销截止时间（verified_end_time）：为当天0点+verified_end_time