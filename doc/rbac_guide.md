# RBAC权限控制系统使用指南

## 概述

本项目实现了基于角色的访问控制(Role-Based Access Control, RBAC)系统，通过Admin、Role、Permission三层模型来控制管理员对API接口的访问权限。

## 系统架构

### 数据模型

```
Admin (管理员)
├── roles (多对多) → Role (角色)
                    ├── permissions (多对多) → Permission (权限)
```

- **Admin**: 管理员表，存储管理员基本信息
- **Role**: 角色表，定义不同的管理角色（如超级管理员、产品管理员等）
- **Permission**: 权限表，定义具体的操作权限（如product:create、order:read等）
- **admin_role_relations**: Admin与Role的关联表
- **role_permission_relations**: Role与Permission的关联表

### 权限命名规范

权限采用 `模块:操作` 的命名格式：

- **模块**: product, order, user, admin, role, permission 等
- **操作**: read, create, update, delete, upload 等

示例：
- `product:read` - 查看商品权限
- `product:create` - 创建商品权限
- `order:update` - 更新订单权限
- `admin:delete` - 删除管理员权限

## 快速开始

### 1. 初始化权限数据

```bash
# 运行权限初始化脚本
python scripts/init_permissions.py
```

这将创建：
- 所有模块的基础权限（读取、创建、更新、删除）
- 6个预定义角色（超级管理员、系统管理员、产品管理员、订单管理员、业务管理员、只读用户）

### 2. 为管理员分配角色

```python
from app.dao.admin import admin_dao

# 为管理员分配超级管理员角色
admin_dao.add_role(session=db, admin_id=1, role_id=1)
```

### 3. 测试权限系统

```bash
# 运行RBAC测试脚本
python scripts/test_rbac.py
```

## 权限控制机制

### 自动路径映射

系统会根据API路径自动匹配所需权限：

| API路径 | HTTP方法 | 所需权限 |
|---------|----------|----------|
| `/api/v1/product` | GET | `product:read` |
| `/api/v1/product` | POST | `product:create` |
| `/api/v1/order/{id}` | PUT | `order:update` |
| `/api/v1/admin/roles` | DELETE | `role:delete` |

### 排除的路径

以下路径不进行权限检查：
- `/api/v1/auth/*` - 认证相关接口
- `/api/v1/wx/*` - 微信小程序接口

### 权限检查流程

1. 用户请求API接口
2. 系统提取请求路径和HTTP方法
3. 检查是否为排除路径，如果是则直接通过
4. 根据路径映射规则获取所需权限
5. 查询当前管理员的所有权限
6. 验证管理员是否具有所需权限
7. 通过则继续处理请求，否则返回403错误

## API 接口权限配置

### 当前已配置的模块权限

- **管理员模块** (`/api/v1/admin`) - admin:read/create/update/delete
- **角色模块** (`/api/v1/admin/roles`) - role:read/create/update/delete  
- **权限模块** (`/api/v1/admin/permissions`) - permission:read/create/update/delete
- **用户模块** (`/api/v1/user`) - user:read/create/update/delete
- **商品模块** (`/api/v1/product`) - product:read/create/update/delete
- **商品分类** (`/api/v1/category`) - category:read/create/update/delete
- **内容模块** (`/api/v1/content`) - content:read/create/update/delete
- **文件模块** (`/api/v1/file`) - file:read/upload/delete
- **价格模块** (`/api/v1/pricing`) - pricing:read/create/update/delete
- **规则模块** (`/api/v1/rule`) - rule:read/create/update/delete
- **菜单模块** (`/api/v1/menu`) - menu:read/create/update/delete
- **标签模块** (`/api/v1/tag`) - tag:read/create/update/delete
- **订单模块** (`/api/v1/order`) - order:read/create/update/delete
- **账户模块** (`/api/v1/account`) - account:read/create/update/delete
- **预订模块** (`/api/v1/reservation`) - reservation:read/create/update/delete
- **自助餐预订** (`/api/v1/buffet-reservation`) - buffet_reservation:read/create/update/delete
- **商务餐预订** (`/api/v1/biz-reservation`) - biz_reservation:read/create/update/delete
- **审批模块** (`/api/v1/approval`) - approval:read/create/update/delete
- **支付模块** (`/api/v1/payment`) - payment:read/create
- **报表模块** (`/api/v1/report`) - report:read
- **赠送模块** (`/api/v1/gift`) - gift:read/create/update/delete
- **优惠券模块** (`/api/v1/coupon`) - coupon:read/create/update/delete

## 预定义角色说明

### 1. 超级管理员
拥有所有权限，可以进行任何操作。

### 2. 系统管理员
负责系统配置和用户管理：
- 管理员管理（增删查改）
- 角色管理（增删查改）
- 权限管理（增删查改）
- 用户管理（增删查改）

### 3. 产品管理员
负责商品、内容、菜单等管理：
- 商品管理（增删查改）
- 商品分类管理（增删查改）
- 内容管理（增删查改）
- 菜单管理（增删查改）
- 标签管理（增删查改）
- 文件管理（上传、删除、查看）
- 价格策略管理（增删查改）

### 4. 订单管理员
负责订单和预订管理：
- 订单管理（增删查改）
- 预订管理（增删查改）
- 自助餐预订管理（增删查改）
- 商务餐预订管理（增删查改）
- 支付管理（查看、创建）
- 账户管理（查看、更新）

### 5. 业务管理员
负责业务规则和审批管理：
- 规则管理（增删查改）
- 审批管理（增删查改）
- 赠送管理（增删查改）
- 优惠券管理（增删查改）

### 6. 只读用户
只能查看信息，无法进行任何修改操作。

## 常用操作

### 创建新权限

```python
from app.dao.admin import permission_dao
from app.schemas.admin import PermissionCreate

permission = PermissionCreate(
    name="商品导出",
    code="product:export",
    permission_type="read",
    description="导出商品数据"
)
permission_dao.create(session=db, permission=permission)
```

### 创建新角色

```python
from app.dao.admin import role_dao
from app.schemas.admin import RoleCreate

role = RoleCreate(
    name="数据分析师",
    description="负责数据分析和报表"
)
created_role = role_dao.create(session=db, role=role)

# 为角色分配权限
role_dao.add_permission(session=db, role_id=created_role.id, permission_id=permission_id)
```

### 为管理员分配角色

```python
from app.dao.admin import admin_dao

admin_dao.add_role(session=db, admin_id=admin_id, role_id=role_id)
```

### 检查管理员权限

```python
from app.dao.admin import get_admin_permissions

permissions = get_admin_permissions(db=db, admin_id=admin_id)
print(f"管理员权限: {permissions}")
```

## 扩展权限映射

如果需要为新的API路径添加权限控制，可以在 `app/core/rbac.py` 中扩展权限映射：

```python
# 在 PermissionManager._init_permission_mapping() 中添加
self._permission_mapping['/api/v1/new_module'] = {
    'GET': 'new_module:read',
    'POST': 'new_module:create',
    'PUT': 'new_module:update',
    'DELETE': 'new_module:delete'
}
```

## 错误处理

当权限不足时，系统会返回HTTP 403错误：

```json
{
    "detail": "权限不足，无法访问 GET /api/v1/product，需要权限: product:read"
}
```

## 性能优化

系统已添加相关数据库索引以优化查询性能：
- `permissions.code` 索引
- `roles.name` 索引  
- `admins.username` 索引
- 关联表的外键索引

## 安全注意事项

1. **最小权限原则**: 只给管理员分配必要的权限
2. **定期审查**: 定期检查角色权限分配是否合理
3. **权限更新**: 当业务需求变化时及时更新权限配置
4. **日志监控**: 监控权限检查失败的情况，及时发现异常访问

## 故障排除

### 常见问题

1. **权限检查失败**
   - 检查管理员是否已分配相应角色
   - 检查角色是否包含所需权限
   - 检查权限码是否正确

2. **无法访问API**
   - 确认API路径是否在权限映射中
   - 检查HTTP方法是否正确
   - 验证token是否有效

3. **微信小程序接口被拦截**
   - 确认路径是否以 `/api/v1/wx/` 开头
   - 检查排除路径配置是否正确

### 调试方法

```python
from app.core.rbac import permission_manager

# 检查路径权限映射
required_permission = permission_manager.get_required_permission("/api/v1/product", "GET")
print(f"所需权限: {required_permission}")

# 检查路径是否被排除
is_excluded = permission_manager.is_path_excluded("/api/v1/wx/user")
print(f"是否排除: {is_excluded}")
``` 