# 飞鹅云扫码回调解密功能

本文档说明如何使用飞鹅云扫码回调解密功能，包括RSA密钥生成、安装依赖和使用示例。

## 1. 安装依赖

首先需要安装`pycryptodomex`库，这是RSA解密所必需的:

```bash
# 使用pip安装
pip install pycryptodomex

# 或者使用Poetry安装
poetry add pycryptodomex
```

## 2. 生成RSA密钥对

使用提供的脚本生成RSA密钥对:

```bash
# 生成默认2048位RSA密钥对
python scripts/generate_rsa_keys.py

# 自定义密钥大小
python scripts/generate_rsa_keys.py --key-size 4096

# 自定义输出目录
python scripts/generate_rsa_keys.py --output-dir /path/to/your/keys
```

这将在指定目录（默认为项目根目录下的`keys`文件夹）生成两个文件:
- `rsa_private_key.pem`: RSA私钥，用于解密
- `rsa_public_key.pem`: RSA公钥，用于加密（由飞鹅云使用）

## 3. 解密飞鹅云扫码回调数据

### 3.1 数据格式

飞鹅云扫码后会向我们的服务器推送JSON格式的数据，结构如下:

```json
{
  "result": "Base64编码并RSA加密后的字符串",
  "sn": "设备序列号"
}
```

其中，`result`是经过RSA加密并Base64编码的原始扫码内容。

### 3.2 解密过程

1. 接收回调数据
2. 使用Base64解码加密后的数据
3. 使用RSA私钥解密，得到原始扫码内容

### 3.3 代码示例

```python
from app.callback.feieyun import parse_feieyun_callback

# 假设接收到的回调数据
callback_data = {
    "result": "rOc9B1X0JUsXC4Q9Lda42op3vDKka8WaMWWcnZSC5ARp2/7bshnUlvWguizy5W3jkXP+Et+ywyg+YKfCgm5qmqZaA5ODNz5qMiOLynf/QNpbmpvrSb4F/hRGsbCqfgjRCztpJDl27w/KqmGGjzMmZFyZB5MeO9inrmN/0bYejH4=",
    "sn": "121234"
}

# 指定RSA私钥文件路径
private_key_path = "/path/to/your/rsa_private_key.pem"

# 解析回调数据并解密
original_content = parse_feieyun_callback(callback_data, private_key_path)

if original_content:
    print(f"解密后的原始扫码内容: {original_content}")
else:
    print("解密失败")
```

## 4. 运行示例

项目提供了一个完整的示例脚本，可以用于测试:

```bash
python -m app.examples.feieyun_example
```

这将使用示例数据和项目中的RSA私钥尝试解密，并输出解密结果。

## 5. 注意事项

1. 请妥善保管RSA私钥，避免泄露
2. 公钥可以提供给飞鹅云，用于加密数据
3. 解密失败可能的原因:
   - 私钥不匹配
   - 数据格式错误
   - Base64编码错误

## 6. 完整流程

1. 生成RSA密钥对
2. 将公钥提供给飞鹅云
3. 配置飞鹅云推送回调地址
4. 接收回调数据并解密
5. 处理解密后的原始扫码内容 