@startuml  乙禾素点餐管理应用

' 管理员类
class 管理员  {
  +管理员ID: Integer
  +管理员类型: String
  +最后登录时间: DateTime
  +密码: String
  +状态: String
  +创建管理员()
  +禁用管理员()
  +重置密码()
  +分配角色()
  +移除角色()
}

' 新增角色类
class 角色 {
  +角色ID: Integer
  +角色名称: String
  +角色描述: String
  +状态: String
  +创建角色()
  +修改角色()
  +删除角色()
  +分配权限()
  +移除权限()
}

' 权限类
class 权限 {
  +权限ID: Integer
  +权限名称: String
  +权限代码: String
  +权限类型: String
  +权限描述: String
  +状态: String
  +创建权限()
  +修改权限()
  +删除权限()
}


' 用户
abstract class 用户 {
  +用户ID: Integer
  +用户名: String
  +注册时间: DateTime
  +状态: String
  +登录()
  +注销()
  +修改信息()
}


class 个人用户 extends 用户 {
  +手机号: String
  +真实姓名: String
  +身份证号: String
  +邮箱: String
  +地址: String
  +微信ID: String
}

class 企业 extends 用户 {
  +企业名称: String
  +营业执照号: String
  +电话: String
  +邮箱: String
  +地址: String
}

' 企业用户关系类
class 企业用户关系 {
  +关系ID: Integer
  +企业ID: Integer
  +用户ID: Integer
  +用户名称: String
  +密码: String
  +是否企业管理员: Boolean
  +绑定时间: Datetime
  +关系状态: String
  +绑定备注: String
  +绑定用户()
  +解绑用户()
  +更新关系状态()
}

' 账户
class 账户 {
  +账户ID: Integer
  +用户ID: Integer
  +账户余额: Double
  +账户状态: String
  +充值()
  +提现()
  +查询余额()
}


class 普通账户 extends 账户 {
  +消费()
  +退款()
}

class 赠送账户 extends 账户 {
  +赠送金额: Integer
  +消费()
  +退款()
}

class 积分账户 extends 账户 {
  +积分总额: Integer
  +积分兑换()
  +积分消费()
}

class 会员账户 extends 账户 {
  +会员等级: String
  +会员积分: Integer
  +会员有效期: DateTime
  +升级会员()
  +享受折扣()
}

' 新增账户流水类
class 账户流水 {
  +流水ID: Integer
  +账户ID: Integer
  +订单ID: Integer
  +交易类型: String
  +交易金额: Double
  +交易时间: DateTime
  +交易描述: String
  +记录流水()
  +查询流水()
  +导出流水记录()
}



' 商品系统
class 商品 {
  +商品ID: Integer
  +商品名称: String
  +基本价格: Double
  +商品描述: String
  +库存数量: Integer
  +上架时间: DateTime
  +商品状态: String
  +上架()
  +下架()
  +更新信息()
}

class 内容 {
  +内容ID: Integer
  +商品ID: Integer
  +内容类型: String
  +内容描述: String
  +内容图片: String
  +内容缩略图片: String
  +排序: Integer
  +添加内容()
  +移除内容()
  +添加文件()
  +移除文件()
}

' 新增文件类
class 文件 {
  +文件ID: Integer
  +内容ID: Integer
  +文件名称: String
  +文件类型: String
  +文件路径: String
  +文件大小: Long
  +上传文件()
  +删除文件()
  +更新文件信息()
}


class 标签 {
  +标签ID: Integer
  +标签名称: String
  +添加标签()
  +移除标签()
}

' 商品类型
class 直销商品 extends 商品 {
  +运费: Double
  +立即购买()
  +添加到购物车()
}

' 修改预订商品类
class 预订商品 extends 商品 {
  +预订费: Double
  +最大预订数: Integer
  +预订截止时间: DateTime
  +预订取消截止时间: DateTime
  +是否需要审批: Boolean
  +申请预订()
  +取消预订()
}

' 新增审批单类
class 审批单 {
  +审批单ID: Integer
  +预订请求ID: Integer
  +申请用户ID: Integer
  +企业ID: Integer
  +审批状态: String
  +审批意见: String
  +审批人ID: Integer
  +审批时间: DateTime
  +创建审批单()
  +提交审批()
  +审批通过()
  +审批拒绝()
  +取消审批单()
}

' 资费策略
abstract class 资费策略 {
  +策略ID: Integer
  +策略名称: String
  +策略描述: String
  +作用范围: String
  +是否互斥: Boolean
  +开始时间: DateTime
  +结束时间: DateTime
  +计算价格()
  +应用策略()
}

class 折扣策略 extends 资费策略 {
  +折扣比例: Double
  +最低消费金额: Double
  +最大折扣金额: Double
}

class 满减策略 extends 资费策略 {
  +满足金额: Double
  +减免金额: Double
}

class 限时特价策略 extends 资费策略 {
  +特价金额: Double
  +库存限制: Integer
}

class 会员价策略 extends 资费策略 {
  +会员等级: String
  +会员价格: Double
}

' 约束条件
class 规则 {
  +规则ID: Integer
  +商品ID: Integer
  +状态: String
  +创建规则()
  +更新规则()
  +删除规则()
}

class 规则项 {
  +规则项ID: Integer
  +规则ID: Integer
  +开始时间: DateTime
  +结束时间: DateTime
  +数量: Integer
  +时段: String
  +允许的操作: String
  +禁止的操作: String
}

' 订单系统
class 订单 {
  +订单ID: Integer
  +用户ID: Integer
  +订单状态: String
  +支付状态: String
  +支付时间: DateTime
  +总金额: Double
  +实付金额: Double
  +支付方式: String
  +创建订单()
  +取消订单()
  +支付订单()
  +完成订单()
}

class 订单项 {
  +订单项ID: Integer
  +订单ID: Integer
  +商品ID: Integer
  +购买数量: Integer
  +商品单价: Double
  +小计金额: Double
  +添加订单项()
  +移除订单项()
  +修改数量()
}


class 预订请求 {
  +预订请求ID: Integer
  +订单项ID: Integer
  +用户ID: Integer
  +商品ID: Integer
  +规则项ID: Integer
  +预订状态: Enum
  +预订时段: String
  +预订时间: DateTime
  +核销码: String
  +支付定金()
  +支付尾款()
}


class 优惠券 {
  +优惠券ID: Integer
  +优惠券名称: String
  +优惠券描述: String
  +优惠券生效时间: Datetime
  +优惠券失效时间: Datetime
  +优惠券是否互斥: Boolean
  +优惠券作用范围: Enum
  +优惠券类型: Enum
  +优惠券状态: Enum
  +优惠券数量: Integer
}

class 折扣券 extends 优惠券 {
  +折扣比例: Double
  +最低消费金额: Double
  +最大折扣金额: Double
}

class 现金券 extends 优惠券 {
  +抵扣金额: Double
}

class 满减券 extends 优惠券 {
  +满足金额: Double
  +满减金额: Double
}

class 优惠券使用记录 {
  +使用记录ID: Integer
  +优惠券ID: Integer
  +用户ID: Integer
  +订单ID: Integer
  +使用时间: Datetime
  +使用状态: Enum
}

' 用户关系
个人用户 "1" -- "多" 企业用户关系 :归属 >
企业 "1" -- "多" 企业用户关系 :包含 >
用户 "1" -- "1..多" 账户 :拥有 >
用户 "1" -- "0..多" 订单 :拥有 >

' 帐户关系
账户 "1" o-- "0..多" 账户流水 :包含
账户流水 "0..多" -- "1" 订单 :归属 >

' 商品关系
商品 "0..多" o-- "1..多" 内容 :包含
商品 "0..多" -- "0..多" 标签 :关联
商品 "0..多" -- "0..多" 资费策略 :绑定
商品 "0..多" -- "0..多" 规则 :关联


' 内容关系
内容 "1..多" --- "0..多" 文件 :包含

' 商品规则关系
规则 "1" -- "1..多" 规则项 :包含

' 订单关系
订单 "1" *-- "1..多" 订单项 :包含
订单项 "0..多" -- "1" 商品 :存在 <
订单项 "1" -- "0..多" 预订请求 :存在

' 预订请求关系
预订请求 "0..多" -- "1" 用户:发起
预订请求 "0..多" -- "1" 规则项:符合

企业用户关系 "1" -- "0..多" 审批单 :管理员审批 >

审批单 "0..1" -- "1" 预订请求 :需要

' RBAC关系
管理员 "多" -- "多" 角色 :拥有
角色 "多" -- "多" 权限 :包含

优惠券 "1" -- "0..多" 优惠券使用记录 :使用
用户 "1" -- "0..多" 优惠券使用记录 :拥有  
订单 "1" -- "0..多" 优惠券使用记录 :使用

@enduml 
