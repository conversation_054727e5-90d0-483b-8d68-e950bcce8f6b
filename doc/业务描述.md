# 乙禾素餐馆点餐应用设计

## 一、餐馆面向的用户
### 用户类型
1. 个人用户
2. 企业用户
> 个人用户通过绑定关联企业成为企业用户

### 用户帐户
1. 基本帐户  
   用户基础的资金帐户，充值的金额入到该帐户内
2. 赠送帐户  
   用户充值满送，赠送部分的金额入到该帐户
3. 积分帐户
4. 会员帐户

### 帐户规则   
1. 用户金额按充值和赠送金额排列进行扣除，即用户满送活动下，用户消费时先扣除用户基本帐户金额达到充值金额，再扣除赠送帐户中，该充值金额对应的赠送部分。


## 二、餐馆提供的服务
### 餐食预订
#### 餐食类型
1. 普通餐
2. 企业用餐
   - 员工餐
   - 招待餐
#### 订餐规则
1. 普通餐所有用户均可预订，费用从个人帐户中扣除
2. 企业用餐（包含员工餐和招待餐）必为企业用户方可预订，费用从用户所绑定企业的企业帐户中扣除
3. 招待餐预订时用户需要申请，并经过企业管理员审批后方可预订成功
4. 每种餐食可以存在多个可预订的用餐时间段以及每个时间段下都有各自的可预订数量，可由餐馆管理员进行设置
5. 每种餐食都存在可预订的截止时间，以及可退订的截止时间，由餐馆管理员进行设置
6. 用户成功预订的餐食将生成对应的核销二维码
### 产品销售
### 活动报名

## 三、主要流程
### 订单生成流程
1. 获取产品列表（如果是可预订产品包含预订请求）
2. 生成订单对象
3. 根据产品列表生成订单项
4. 根据产品列表获取价格策略以及订单的价格策略
5. 计算订单金额
6. 判断是否预订
7. 生成订单

### 支付流程
### 核销流程
### 订餐审批流程

## 四、复杂交互
### 商品建立维护交互
### 可预订商品的维护交互
### 订单维护交互
### 预订审批维护交互

