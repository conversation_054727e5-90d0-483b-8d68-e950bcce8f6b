@startuml
skinparam classAttributeIconSize 0

enum 优惠券类型 {
  基础优惠券(COUPON)
  折扣券(DISCOUNT)
  现金券(CASH)
  满减券(FULL_REDUCTION)
}

enum 优惠券作用范围 {
  订单范围(ORDER)
  商品范围(PRODUCT)
}

enum 优惠券使用状态 {
  有效(VALID)
  无效(INVALID)
  未生效(NOT_STARTED)
  已过期(EXPIRED)
  已使用(USED)
}

enum 周期 {
  每次订单(PER_ORDER)
  每天(PER_DAY)
  每周(PER_WEEK)
  每月(PER_MONTH)
  每年(PER_YEAR)
}

enum 状态 {
  激活(ACTIVE)
}

enum 适用支付渠道 {
  微信(WECHAT)
  个人帐户余额(PERSONAL_BALANCE)
  企业帐户余额(ENTERPRISE_BALANCE)
}

enum 发放渠道 {
  新人注册(NEW_USER)
  浏览活动(VIEW_ACTIVITY)
  分享活动(SHARE_ACTIVITY)
  销售购买(PURCHASE)
}

class 优惠券使用记录 {
  + 记录ID : Integer [PK]
  + 优惠券ID : Integer [FK]
  + 优惠券批次ID : Integer [FK]
  ---
  + 用户ID : Integer [FK]
  ---
  + 订单ID : Integer [FK, 可空]
  + 使用时间 : DateTime
  + 状态 : 优惠券使用状态
  ---
  + 创建时间 : DateTime
  + 更新时间 : DateTime
}

class 优惠券 {
  + 优惠券ID : Integer [PK]
  + 优惠券名称 : String
  + 优惠券描述 : Text
  + 优惠券类型 : 优惠券类型
  ---
  + 条件范围 : 优惠券作用范围
  + 条件对象组合: Json
  + 条件对象金额 : Float
  + 周期可使用数量: Integer
  + 使用周期 : 周期
  + 使用限制 : Integer
  + 互斥规则 : Json
  + 支付渠道 : Json
  ---
  + 作用范围 : 优惠券作用范围
  + 作用对象组合 : Json
  + 计算顺序 : Integer
  ---
  + 创建时间 : DateTime
  + 更新时间 : DateTime
  ---  
  + 数量 : Integer
  + 生效时间 : DateTime
  + 结束时间 : DateTime
  + 是否互斥 : Boolean
  + 状态 : 状态
}

class 折扣券 {
  + 关联的基础优惠券ID : Integer [PK, FK]
  + 折扣比例 : Float
  + 最低消费金额 : Float
  + 最大折扣金额 : Float
}

class 现金券 {
  + 关联的基础优惠券ID : Integer [PK, FK]
  + 抵扣金额 : Float
}

class 满减券 {
  + 关联的基础优惠券ID : Integer [PK, FK]
  + 满足金额 : Float
  + 满减金额 : Float
}

class 优惠券批次 {
  + 批次ID : Integer [PK]
  + 批次名称 : String
  + 批次描述 : Text
  + 批次: Integer
  + 数量 : Integer
  + 生效时间:DateTime
  + 结束时间:DateTime
  + 有效时长:Integer
  + 优惠券ID : Integer [FK]
  + 状态: 状态
  ---
  + 发放渠道 : Json
  + 周期发放数量 : Integer
  + 发放周期 : 周期
  ---
  + 周期可领取数量: Integer
  + 领取周期 : 周期
  + 领取开始时间 : DateTime
  + 领取结束时间 : DateTime
  + 创建时间 : DateTime
  + 更新时间 : DateTime
}

class 用户
class 订单

' 继承关系
折扣券 -|> 优惠券
现金券 -|> 优惠券
满减券 -|> 优惠券

' 关联关系
优惠券 "1" *-- "*" 优惠券使用记录 : 有多个使用记录
用户 "1" *-- "*" 优惠券使用记录 : 有多个使用记录
订单 "1" *-- "*" 优惠券使用记录 : 有多个使用记录

' 枚举关联
优惠券 -- 优惠券类型 : 使用
优惠券 -- 优惠券作用范围 : 使用
优惠券 -- 周期 : 使用
优惠券 -- 状态 : 具有
优惠券 -- 支付渠道 : 使用
优惠券使用记录 -- 优惠券使用状态 : 具有
优惠券批次 "0..n" -- "1" 优惠券 : 包含
优惠券批次 -- 发放渠道: 使用
@enduml