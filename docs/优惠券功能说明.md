# 优惠券功能说明

## 功能概述

为自助餐订单系统增加了优惠券功能，用户可以在订单清单页面选择和应用优惠券，享受相应的折扣。

## 功能特性

### 1. 优惠券组件 (coupon-list)
- **位置**: `components/coupon-list/`
- **类型**: 可复用组件
- **功能**: 
  - 显示用户可用、已过期、已使用的优惠券
  - 支持优惠券选择和取消选择
  - 实时计算优惠券可用性

### 2. 优惠券类型支持
- **满减券 (DISCOUNT)**: 满足条件后减免固定金额
- **折扣券 (PERCENTAGE)**: 按比例折扣
- **免运费券 (FREE_SHIPPING)**: 免除运费

### 3. 集成页面
- **booking页面**: 个人餐订单页面已集成优惠券功能

## 使用方法

### 在页面中使用优惠券组件

1. **在页面JSON中注册组件**:
```json
{
  "usingComponents": {
    "coupon-list": "../../components/coupon-list/coupon-list"
  }
}
```

2. **在WXML中使用组件**:
```xml
<coupon-list 
  visible="{{showCouponList}}"
  order-amount="{{totalAmount}}"
  selected-coupon-id="{{selectedCoupon ? selectedCoupon.coupon_usage_record.id : ''}}"
  bind:close="hideCouponList"
  bind:select="onCouponSelect"
></coupon-list>
```

3. **在JS中处理事件**:
```javascript
// 显示优惠券列表
showCouponList() {
  this.setData({ showCouponList: true });
},

// 隐藏优惠券列表
hideCouponList() {
  this.setData({ showCouponList: false });
},

// 处理优惠券选择
onCouponSelect(e) {
  const { coupon } = e.detail;
  // 处理优惠券选择逻辑
}
```

## API接口

### 获取用户优惠券列表
- **接口**: `GET /api/v1/wx/coupon/user-available-coupons`
- **认证**: 需要token
- **返回**: 包含可用、过期、已使用优惠券的完整列表

### 提交订单时包含优惠券
在提交订单时，需要在请求数据中包含：
```javascript
{
  bookings: [...],
  coupon_id: selectedCoupon?.coupon_usage_record.id,
  coupon_discount: calculatedDiscount
}
```

## 样式特性

- 不同类型优惠券有不同的颜色标识
- 满减券：红色渐变
- 折扣券：绿色渐变  
- 免运费券：蓝色渐变
- 支持选中状态高亮
- 不可用优惠券灰色显示

## 计算逻辑

### 优惠券可用性检查
1. 检查订单金额是否满足优惠券使用条件
2. 检查优惠券是否在有效期内
3. 检查是否达到使用限制

### 折扣计算
- **满减券**: `Math.min(优惠券金额, 订单金额)`
- **折扣券**: `订单金额 * 折扣比例`
- **免运费券**: 根据具体业务逻辑计算

## 注意事项

1. 优惠券组件会自动从API获取最新的优惠券列表
2. 选择优惠券后会实时更新订单总金额
3. 支付时使用的是扣除优惠券后的最终金额
4. 组件支持在多个页面复用，只需传入相应的属性即可
