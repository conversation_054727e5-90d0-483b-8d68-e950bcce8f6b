# 环境配置说明

## 概述

本项目实现了根据小程序版本自动切换环境配置的功能，支持开发环境、体验版和线上版本的自动识别和配置切换。

## 环境类型

### 1. 开发环境 (develop)
- **使用场景**: 开发者工具中运行
- **API地址**: `http://127.0.0.1:8000/api/v1/wx`
- **主机地址**: `http://127.0.0.1:8000`
- **调试模式**: 开启

### 2. 体验版 (trial)
- **使用场景**: 体验版小程序
- **API地址**: `https://test.yiheship.com/api/v1/wx`
- **主机地址**: `https://test.yiheship.com`
- **调试模式**: 开启

### 3. 线上版本 (release)
- **使用场景**: 正式发布的小程序
- **API地址**: `https://vegan.yiheship.com/api/v1/wx`
- **主机地址**: `https://vegan.yiheship.com`
- **调试模式**: 关闭

## 文件结构

```
├── config/
│   └── env.js                 # 环境配置文件
├── utils/
│   └── env-helper.js          # 环境辅助工具
├── pages/
│   └── env-info/              # 环境信息页面（调试用）
│       ├── env-info.js
│       ├── env-info.wxml
│       ├── env-info.wxss
│       └── env-info.json
├── service/
│   └── index.js               # 网络请求服务（已更新）
└── app.js                     # 应用入口（已更新）
```

## 核心功能

### 1. 自动环境识别
系统会自动识别当前小程序的运行环境：
- 通过 `wx.getAccountInfoSync()` 获取环境版本
- 根据 `envVersion` 自动选择对应配置

### 2. 动态配置加载
- 应用启动时自动加载对应环境的配置
- 配置信息存储在 `app.globalData` 中
- 网络请求服务自动使用当前环境配置

### 3. 调试工具
- 提供环境信息显示页面
- 支持API连接测试
- 支持配置信息复制

## 使用方法

### 1. 在页面中获取环境信息

```javascript
// 获取当前环境配置
const app = getApp()
const baseUrl = app.globalData.baseUrl
const baseUrlHost = app.globalData.baseUrlHost
const debug = app.globalData.debug

// 使用辅助工具
import { getEnvDisplayInfo } from '../../utils/env-helper'
const envInfo = getEnvDisplayInfo()
```

### 2. 在网络请求中使用

```javascript
// service/index.js 已经自动处理
// 所有通过 loginRequest 发起的请求都会自动使用当前环境配置
import { loginRequest } from '../service/index'

const response = await loginRequest.post({
  url: '/your-api-endpoint',
  data: { /* your data */ }
})
```

### 3. 查看环境信息

访问 `/pages/env-info/env-info` 页面可以查看：
- 当前环境详细信息
- 所有环境配置对比
- API连接测试结果

## 配置修改

### 修改环境配置

编辑 `config/env.js` 文件：

```javascript
const ENV_CONFIG = {
  development: {
    name: '开发环境',
    baseUrlHost: 'http://127.0.0.1:8000',
    baseUrl: 'http://127.0.0.1:8000/api/v1/wx',
    debug: true
  },
  trial: {
    name: '体验版',
    baseUrlHost: 'https://your-test-domain.com',
    baseUrl: 'https://your-test-domain.com/api/v1/wx',
    debug: true
  },
  release: {
    name: '线上版本',
    baseUrlHost: 'https://your-production-domain.com',
    baseUrl: 'https://your-production-domain.com/api/v1/wx',
    debug: false
  }
}
```

### 添加新的配置项

在配置对象中添加新的属性：

```javascript
const ENV_CONFIG = {
  development: {
    // ... 现有配置
    newProperty: 'development-value'
  },
  trial: {
    // ... 现有配置
    newProperty: 'trial-value'
  },
  release: {
    // ... 现有配置
    newProperty: 'release-value'
  }
}
```

## 注意事项

1. **环境识别**: 确保在不同环境下测试配置是否正确加载
2. **网络安全**: 线上环境必须使用 HTTPS 协议
3. **调试信息**: 线上版本建议关闭调试模式
4. **缓存问题**: 修改配置后需要重新编译小程序

## 调试技巧

1. **查看控制台**: 应用启动时会输出当前环境信息
2. **使用环境信息页面**: 访问调试页面查看详细配置
3. **API测试**: 使用内置的API连接测试功能
4. **网络监控**: 在开发者工具中监控网络请求地址

## 常见问题

### Q: 如何确认当前使用的是哪个环境？
A: 查看控制台输出或访问环境信息页面。

### Q: 修改配置后不生效怎么办？
A: 重新编译小程序，确保代码已更新。

### Q: 如何在代码中判断当前环境？
A: 使用 `app.globalData.debug` 或 `wx.getAccountInfoSync().miniProgram.envVersion`。

### Q: 可以手动切换环境吗？
A: 不建议手动切换，系统会根据小程序版本自动选择正确的环境。
