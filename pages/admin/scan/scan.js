Page({
  data: {
    scanResults: [], // 扫码结果列表
  },

  onLoad: function(options) {
    // 页面加载时的初始化逻辑
  },

  // 处理扫码核销
  handleScan: function() {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.scanCode({
      onlyFromCamera: false,
      scanType: ['qrCode', 'barCode'],
      success: (scanRes) => {
        let scannedResult = scanRes && scanRes.result ? scanRes.result : '';
        if (!scannedResult) {
          wx.showToast({
            title: '无效的扫码结果',
            icon: 'none'
          });
          return;
        }

        console.log('scannedResult', scannedResult);

        // 添加扫码结果到列表（状态为pending）
        const newResult = {
          result: scannedResult,
          time: this.formatTime(new Date()),
          status: 'pending'
        };
        
        this.setData({
          scanResults: [newResult, ...this.data.scanResults]
        });

        // 发起核销请求
        this.verifyScan(scannedResult, newResult);
      },
      fail: (err) => {
        if (err && err.errMsg) {
          console.warn('扫码失败或取消：', err.errMsg);
        }
        wx.showToast({
          title: '扫码取消',
          icon: 'none'
        });
      }
    });
  },

  // 核销扫码结果
  verifyScan: function(scannedResult, resultItem) {
    wx.showLoading({ title: '核销中...' });
    
    const app = getApp();
    wx.request({
      url: app.globalData.baseUrl + '/admin/scan',
      method: 'POST',
      header: {
        'token': wx.getStorageSync('token'),
        'content-type': 'application/json'
      },
      data: JSON.stringify(scannedResult),
      success: (res) => {
        wx.hideLoading();
        console.log('success:', res.data)
        
        // 更新结果状态
        const updatedResults = this.data.scanResults.map(item => {
          if (item === resultItem) {
            if (res.data && res.data.status === 200) {
              return {
                ...item,
                status: 'success',
                message: ""
              };
            } else {
              return {
                ...item,
                status: 'error',
                message: res.data.message
              };
            }
          }
          return item;
        });
        console.log('success end')
        this.setData({
          scanResults: updatedResults
        });

        // 显示结果提示
        // if (res.data && res.data.status === 200) {
        //   wx.showToast({
        //     title: res.data.message || '核销成功',
        //     icon: 'success'
        //   });
        // } else {
        //   wx.showToast({
        //     title: (res.data && res.data.message) ? res.data.message : '核销失败',
        //     icon: 'none'
        //   });
        // }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('扫码核销请求失败', err);
        console.log('success:', res.data)

        // 更新结果状态为失败
        const updatedResults = this.data.scanResults.map(item => {
          if (item === resultItem) {
            return {
              ...item,
              status: 'error'
            };
          }
          return item;
        });
        
        this.setData({
          scanResults: updatedResults
        });
        
        wx.showToast({
          title: '网络异常，核销失败',
          icon: 'none'
        });
      }
    });
  },

  // 格式化时间
  formatTime: function(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    const second = date.getSeconds().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
  }
});
