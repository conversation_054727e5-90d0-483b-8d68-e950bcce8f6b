<view class="scan-container">
  <view class="scan-header">
    <text class="scan-title">扫码核销</text>
  </view>
  
  <!-- 扫码结果显示区域 -->
  <view class="scan-result-container">
    <view class="result-title">扫码结果</view>
    <view class="result-content">
      <text wx:if="{{scanResults.length === 0}}" class="no-result">暂无扫码结果</text>
      <view wx:for="{{scanResults}}" wx:key="index" class="result-item">
        <view class="result-index">#{{index + 1}}</view>
        <view class="result-text">{{item.result}}</view>
        <view class="result-time">{{item.time}}</view>
        <view class="result-status {{item.status === 'success' ? 'success' : 'error'}}">
          {{item.status === 'success' ? '核销成功' : '核销失败'}}
        </view>
        <view>{{item.message}}</view>
      </view>
    </view>
  </view>
  
  <!-- 扫码核销按钮 -->
  <view class="scan-button-container">
    <button class="scan-button" bindtap="handleScan">
      <view class="scan-button-icon">📷</view>
      <text class="scan-button-text">扫码核销</text>
    </button>
  </view>
</view>
