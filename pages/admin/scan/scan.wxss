.scan-container {
  padding: 20rpx;
  padding-bottom: 120rpx; /* 为固定在底部的按钮留出空间 */
  background-color: #f5f5f5;
  min-height: 100vh;
}

.scan-header {
  text-align: center;
  padding: 40rpx 0;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.scan-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.scan-result-container {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 15rpx;
}

.result-content {
  min-height: 200rpx;
}

.no-result {
  color: #999;
  font-size: 28rpx;
  text-align: center;
  display: block;
  padding: 60rpx 0;
}

.result-item {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-left: 6rpx solid #007aff;
}

.result-index {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.result-text {
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
  margin-bottom: 10rpx;
  font-family: monospace;
  background-color: #fff;
  padding: 10rpx;
  border-radius: 6rpx;
  border: 1rpx solid #eee;
}

.result-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.result-status {
  display: inline-block;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.result-status.success {
  background-color: #d4edda;
  color: #155724;
}

.result-status.error {
  background-color: #f8d7da;
  color: #721c24;
}

.result-status.pending {
  background-color: #fff3cd;
  color: #856404;
}

.scan-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.scan-button {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 30rpx 60rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(40, 167, 69, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 0 auto;
}

.scan-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(40, 167, 69, 0.3);
}

.scan-button-icon {
  font-size: 36rpx;
  margin-right: 15rpx;
}

.scan-button-text {
  font-size: 32rpx;
}
