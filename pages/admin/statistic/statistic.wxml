<view class="container">
  <!-- 权限检查 -->
  <view wx:if="{{!isSystemAdmin}}" class="permission-check">
    <view class="permission-message">
      <text class="permission-icon">🔒</text>
      <text class="permission-text">权限检查中...</text>
    </view>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="main-content">
    <!-- 日期筛选区域 -->
    <view class="date-filter">
      <view class="date-filter-title">筛选日期</view>
      
      <!-- 快捷日期选择 -->
      <view class="quick-dates">
        <button 
          wx:for="{{quickDates}}" 
          wx:key="value"
          class="quick-date-btn {{activeQuickDate === item.value ? 'active' : ''}}"
          data-value="{{item.value}}"
          bindtap="onQuickDateSelect"
        >
          {{item.label}}
        </button>
      </view>
      
      <!-- 自定义日期选择 -->
      <view class="custom-dates">
        <view class="date-picker-group">
          <view class="date-picker-item">
            <text class="date-label">开始日期</text>
            <picker mode="date" value="{{startDate}}" bindchange="onStartDateChange">
              <view class="date-picker">
                <text class="date-value">{{startDate || '请选择'}}</text>
                <text class="date-arrow">▼</text>
              </view>
            </picker>
          </view>
          
          <view class="date-picker-item">
            <text class="date-label">结束日期</text>
            <picker mode="date" value="{{endDate}}" bindchange="onEndDateChange">
              <view class="date-picker">
                <text class="date-value">{{endDate || '请选择'}}</text>
                <text class="date-arrow">▼</text>
              </view>
            </picker>
          </view>
        </view>
      </view>
    </view>

    <!-- Tabs切换 -->
    <van-tabs active="{{ activeTab }}" bind:change="onTabChange">
      <van-tab title="自助午餐">
        <view class="tab-content">
          <!-- 自助午餐统计概览 -->
          <view class="statistics-overview">
            <view class="stat-item">
              <text class="stat-number">{{selfServiceLunchData.total}}</text>
              <text class="stat-label">统计条数</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{selfServiceLunchData.totalPaidCount}}</text>
              <text class="stat-label">已预订</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{selfServiceLunchData.totalVerifiedCount}}</text>
              <text class="stat-label">已核销</text>
            </view>
          </view>

          <!-- 自助午餐详细列表 -->
          <view class="data-list">
            <view class="list-header">
              <text class="list-title">详细数据 ({{selfServiceLunchData.list.length}}条)</text>
            </view>

            <view wx:if="{{selfServiceLunchData.loading}}" class="loading">
              <text>加载中...</text>
            </view>

            <view wx:elif="{{selfServiceLunchData.list.length === 0}}" class="empty">
              <text>暂无数据</text>
            </view>

            <view wx:else class="list-content">
              <!-- 表格头部 -->
              <view class="table-header">
                <view class="table-cell header-cell">手机</view>
                <view class="table-cell header-cell">姓名</view>
                <view class="table-cell header-cell">微信</view>
                <view class="table-cell header-cell">内容</view>
                <view class="table-cell header-cell">支付</view>
                <view class="table-cell header-cell">时段</view>
                <view class="table-cell header-cell">预订</view>
                <view class="table-cell header-cell">核销</view>
              </view>

              <!-- 表格内容 -->
              <view
                wx:for="{{selfServiceLunchData.list}}"
                wx:for-index="index"
                wx:key="index"
                class="table-row"
              >
                <view class="table-cell">{{item.username}}</view>
                <view class="table-cell">{{item.real_name}}</view>
                <view class="table-cell">{{item.nick_name}}</view>
                <view class="table-cell">{{item.product_name}}</view>
                <view class="table-cell">{{item.payment_enterprise}}</view>
                <view class="table-cell">{{item.reservation_period_time}}</view>
                <view class="table-cell paid-count">{{item.paid_full_count}}</view>
                <view class="table-cell verified-count">{{item.verified_count}}</view>
              </view>

              <!-- 合计行 -->
              <view class="table-row summary-row">
                <view class="table-cell">小计</view>
                <view class="table-cell"></view>
                <view class="table-cell"></view>
                <view class="table-cell"></view>
                <view class="table-cell"></view>
                <view class="table-cell"></view>
                <view class="table-cell paid-count">{{selfServiceLunchData.totalPaidCount}}</view>
                <view class="table-cell verified-count">{{selfServiceLunchData.totalVerifiedCount}}</view>
              </view>
            </view>
          </view>
        </view>
      </van-tab>

      <van-tab title="自助晚餐">
        <view class="tab-content">
          <!-- 自助晚餐统计概览 -->
          <view class="statistics-overview">
            <view class="stat-item">
              <text class="stat-number">{{selfServiceDinnerData.total}}</text>
              <text class="stat-label">统计条数</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{selfServiceDinnerData.totalPaidCount}}</text>
              <text class="stat-label">已预订</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{selfServiceDinnerData.totalVerifiedCount}}</text>
              <text class="stat-label">已核销</text>
            </view>
          </view>

          <!-- 自助晚餐详细列表 -->
          <view class="data-list">
            <view class="list-header">
              <text class="list-title">详细数据 ({{selfServiceDinnerData.list.length}}条)</text>
            </view>
            
            <view wx:if="{{selfServiceDinnerData.loading}}" class="loading">
              <text>加载中...</text>
            </view>
            
            <view wx:elif="{{selfServiceDinnerData.list.length === 0}}" class="empty">
              <text>暂无数据</text>
            </view>
            
            <view wx:else class="list-content">
              <!-- 表格头部 -->
              <view class="table-header">
                <view class="table-cell header-cell">手机</view>
                <view class="table-cell header-cell">姓名</view>
                <view class="table-cell header-cell">微信</view>
                <view class="table-cell header-cell">内容</view>
                <view class="table-cell header-cell">支付</view>
                <view class="table-cell header-cell">时段</view>
                <view class="table-cell header-cell">预订</view>
                <view class="table-cell header-cell">核销</view>
              </view>
              
              <!-- 表格内容 -->
              <view 
                wx:for="{{selfServiceDinnerData.list}}"
                wx:for-index="index"
                wx:key="index"
                class="table-row"
              >
                <view class="table-cell">{{item.username}}</view>
                <view class="table-cell">{{item.real_name}}</view>
                <view class="table-cell">{{item.nick_name}}</view>
                <view class="table-cell">{{item.product_name}}</view>
                <view class="table-cell">{{item.payment_enterprise}}</view>
                <view class="table-cell">{{item.reservation_period_time}}</view>
                <view class="table-cell paid-count">{{item.paid_full_count}}</view>
                <view class="table-cell verified-count">{{item.verified_count}}</view>
              </view>
              
              <!-- 合计行 -->
              <view class="table-row summary-row">
                <view class="table-cell">小计</view>
                <view class="table-cell"></view>
                <view class="table-cell"></view>
                <view class="table-cell"></view>
                <view class="table-cell"></view>
                <view class="table-cell"></view>
                <view class="table-cell paid-count">{{selfServiceDinnerData.totalPaidCount}}</view>
                <view class="table-cell verified-count">{{selfServiceDinnerData.totalVerifiedCount}}</view>
              </view>
            </view>
          </view>
        </view>
      </van-tab>

      <van-tab title="商务餐">
        <view class="tab-content">
          <!-- 商务餐统计概览 -->
          <view class="statistics-overview">
            <view class="stat-item">
              <text class="stat-number">{{businessData.total}}</text>
              <text class="stat-label">订单总数</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{businessData.totalPersons}}</text>
              <text class="stat-label">就餐人数</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">¥{{businessData.totalAmount}}</text>
              <text class="stat-label">总金额</text>
            </view>
          </view>

          <!-- 商务餐详细列表 -->
          <view class="data-list">
            <view class="list-header">
              <text class="list-title">详细数据</text>
            </view>
            
            <view wx:if="{{businessData.loading}}" class="loading">
              <text>加载中...</text>
            </view>
            
            <view wx:elif="{{businessData.list.length === 0}}" class="empty">
              <text>暂无数据</text>
            </view>
            
            <view wx:else class="list-content">
              <view 
                wx:for="{{businessData.list}}" 
                wx:key="id"
                class="list-item"
              >
                <view class="item-header">
                  <text class="item-title">订单 #{{item.order_no}}</text>
                  <text class="item-status status-{{item.status}}">
                    <text wx:if="{{item.status === 'PAID_FULL'}}">已预订</text>
                    <text wx:elif="{{item.status === 'VERIFIED'}}">已核销</text>
                    <text wx:elif="{{item.status === 'AUTO_VERIFIED'}}">自动核销</text>
                    <text wx:elif="{{item.status === 'PENDING'}}">待处理</text>
                    <text wx:else>{{item.status}}</text>
                  </text>
                </view>
                
                <view class="item-details">
                  <view class="detail-row">
                    <text class="detail-label">联系人：</text>
                    <text class="detail-value">{{item.contact_name}}</text>
                  </view>
                  <view class="detail-row">
                    <text class="detail-label">手机：</text>
                    <text class="detail-value">{{item.contact_phone}}</text>
                  </view>
                  <view class="detail-row">
                    <text class="detail-label">人数：</text>
                    <text class="detail-value">{{item.persons}}</text>
                  </view>
                  <view class="detail-row">
                    <text class="detail-label">金额：</text>
                    <text class="detail-value">¥{{item.calculate_amount}}</text>
                  </view>
                  <view class="detail-row">
                    <text class="detail-label">就餐时间：</text>
                    <text class="detail-value">{{item.dining_start_time}}</text>
                  </view>
                  <view wx:if="{{item.remark}}" class="detail-row">
                    <text class="detail-label">备注：</text>
                    <text class="detail-value">{{item.remark}}</text>
                  </view>
                  
                  <!-- 订单明细 -->
                  <view wx:if="{{item.order_items && item.order_items.length > 0}}" class="order-items">
                    <text class="order-items-title">订单明细：</text>
                    <view 
                      wx:for="{{item.order_items}}" 
                      wx:key="order_item_id"
                      wx:for-item="orderItem"
                      class="order-item"
                    >
                      <text class="order-item-name">{{orderItem.product_name}}</text>
                      <text class="order-item-quantity">×{{orderItem.quantity}}</text>
                      <text class="order-item-price">¥{{orderItem.payable_amount}}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </van-tab>
    </van-tabs>
  </view>
</view> 