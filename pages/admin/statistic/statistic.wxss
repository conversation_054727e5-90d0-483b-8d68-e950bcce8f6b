/* 订餐统计页面样式 */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 权限检查 */
.permission-check {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

.permission-message {
  text-align: center;
}

.permission-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  display: block;
}

.permission-text {
  font-size: 32rpx;
  color: #666;
}

/* 主要内容 */
.main-content {
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
}

/* 日期筛选区域 */
.date-filter {
  padding: 30rpx;
  background-color: white;
  border-bottom: 1rpx solid #eee;
}

.date-filter-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

/* 快捷日期选择 */
.quick-dates {
  display: flex;
  gap: 15rpx;
  margin-bottom: 30rpx;
  flex-wrap: wrap;
}

.quick-date-btn {
  flex: 1;
  min-width: 150rpx;
  padding: 20rpx 15rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  background-color: white;
  font-size: 28rpx;
  color: #666;
  text-align: center;
  line-height: 1.2;
}

.quick-date-btn::after {
  border: none;
}

.quick-date-btn:active {
  border-color: #1989fa;
  background-color: #f0f8ff;
  color: #1989fa;
}

.quick-date-btn.active {
  border-color: #1989fa;
  background-color: #1989fa;
  color: white;
}

/* 自定义日期选择 */
.custom-dates {
  margin-top: 20rpx;
}

.date-picker-group {
  display: flex;
  gap: 20rpx;
}

.date-picker-item {
  flex: 1;
}

.date-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.date-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  background-color: white;
}

.date-value {
  font-size: 28rpx;
  color: #333;
}

.date-arrow {
  font-size: 24rpx;
  color: #999;
}

/* Tab内容 */
.tab-content {
  padding: 30rpx;
}

/* 统计概览 */
.statistics-overview {
  display: flex;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.stat-item {
  flex: 1;
  text-align: center;
  position: relative;
}

.stat-item:not(:last-child)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1rpx;
  height: 60rpx;
  background-color: #ddd;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #1989fa;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 数据列表 */
.data-list {
  background-color: white;
  border-radius: 12rpx;
  overflow: hidden;
}

.list-header {
  padding: 30rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #eee;
}

.list-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.loading,
.empty {
  padding: 80rpx;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.list-content {
  padding: 0;
}

.list-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.list-item:last-child {
  border-bottom: none;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.item-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.item-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
}

.status-PAID_FULL {
  background-color: #52c41a;
}

.status-VERIFIED {
  background-color: #1890ff;
}

.status-AUTO_VERIFIED {
  background-color: #722ed1;
}

.status-PENDING {
  background-color: #faad14;
}

.item-details {
  margin-top: 20rpx;
}

.detail-row {
  display: flex;
  margin-bottom: 10rpx;
  align-items: flex-start;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
  min-width: 140rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

/* 订单明细 */
.order-items {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.order-items-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.order-item:last-child {
  border-bottom: none;
}

.order-item-name {
  font-size: 24rpx;
  color: #333;
  flex: 1;
}

.order-item-quantity {
  font-size: 24rpx;
  color: #666;
  margin: 0 20rpx;
}

.order-item-price {
  font-size: 24rpx;
  color: #1989fa;
  font-weight: bold;
}

/* 表格样式 */
.table-header {
  display: flex;
  background-color: #f8f9fa;
  border-bottom: 2rpx solid #dee2e6;
  font-weight: bold;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}

.table-row:nth-child(even) {
  background-color: #fafafa;
}

.table-cell {
  flex: 1;
  padding: 20rpx 10rpx;
  font-size: 24rpx;
  text-align: center;
  border-right: 1rpx solid #f0f0f0;
  word-break: break-all;
  min-height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-cell:last-child {
  border-right: none;
}

.header-cell {
  font-weight: bold;
  color: #333;
  background-color: #f8f9fa;
  font-size: 26rpx;
}

.paid-count {
  color: #1890ff;
  font-weight: bold;
}

.verified-count {
  color: #52c41a;
  font-weight: bold;
}

.summary-row {
  background-color: #f0f8ff !important;
  font-weight: bold;
  border-top: 2rpx solid #1890ff;
}

.summary-row .table-cell {
  color: #333;
  font-weight: bold;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .date-picker-group {
    flex-direction: column;
    gap: 20rpx;
  }

  .statistics-overview {
    flex-direction: column;
    gap: 20rpx;
  }

  .stat-item:not(:last-child)::after {
    display: none;
  }

  .table-cell {
    padding: 15rpx 8rpx;
    font-size: 22rpx;
  }

  .header-cell {
    font-size: 24rpx;
  }

  .quick-dates {
    gap: 10rpx;
  }

  .quick-date-btn {
    font-size: 24rpx;
    padding: 12rpx 8rpx;
    min-width: 120rpx;
  }
}
