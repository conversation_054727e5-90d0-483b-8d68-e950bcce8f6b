<view class="page-container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading"></view>
    <text>加载中...</text>
  </view>

  <block wx:else>
    <!-- 商家信息 -->
    <view class="store-info">
      <view class="store-banner">
        <image class="store-banner-image" src="https://vegan.yiheship.com/static/images/002.png" mode="aspectFill"></image>
        <view class="store-banner-overlay"></view>
      </view>
      <view class="store-card">
        <view class="store-avatar-container">
          <image class="store-avatar" src="{{storeInfo.image || '/images/default-store.png'}}" mode="aspectFill"></image>
        </view>
        <view class="store-details">
          <view class="store-header">
            <view class="store-title">
              <text class="store-name">{{storeInfo.name || '素食餐厅'}}</text>
              <view class="store-tag">营业中</view>
            </view>
            <!--<view class="store-rating">
              <text class="rating-score">{{storeInfo.rating || '5.0'}}</text>
              <text class="rating-count">{{storeInfo.ratingCount || '0'}}条评价</text>
            </view>-->
          </view>
          <view class="store-address-container">
            <view class="address-icon"></view>
            <text class="store-address">{{storeInfo.address || '地址信息'}}</text>
          </view>
          <!-- 添加联系电话 -->
          <view class="store-phone-container">
            <!-- <view class="phone-icon"></view> -->
            <text class="store-phone">联系电话: 18802053864</text>
            <view class="call-button" bindtap="makePhoneCall">拨打</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 主要内容区域：左侧分类 + 右侧商品 -->
    <view class="main-content">
      <!-- 左侧菜单分类 -->
      <view class="left-categories">
        <scroll-view scroll-y="true" class="category-list">
          <view class="category-item {{currentCategory === index ? 'active' : ''}}"
                wx:for="{{categories}}"
                wx:key="id"
                bindtap="switchCategory"
                data-index="{{index}}">
            <text class="category-name">{{item.name}}</text>
          </view>
        </scroll-view>
      </view>

      <!-- 右侧商品列表 -->
      <view class="right-products">
        <scroll-view scroll-y="true" class="product-list" bindscrolltolower="loadMoreDishes">
          <view class="product-category-title" wx:if="{{currentCategoryDishes.length > 0}}">
            {{categories[currentCategory].name}}
          </view>

          <view class="product-item" wx:for="{{currentCategoryDishes}}" wx:key="id">
            <image class="product-image" src="{{item.image || '/images/default-dish.png'}}" mode="aspectFill" bindtap="showDishDetail" data-dish="{{item}}"></image>
            <view class="product-info">
              <text class="product-name">{{item.name}}</text>
              <text class="product-desc">{{item.description}}</text>
              <view class="product-meta">
                <text class="product-sales">月售{{item.sales || Math.floor(Math.random() * 100)}}份</text>
              </view>
              <view class="product-price-action">
                <text class="product-price">¥{{item.price}}</text>
                <view class="product-action">
                  <view class="action-minus" wx:if="{{item.count > 0}}" bindtap="minusDish" data-id="{{item.id}}">-</view>
                  <view class="action-count" wx:if="{{item.count > 0}}">{{item.count}}</view>
                  <view class="action-plus" bindtap="addDish" data-id="{{item.id}}">+</view>
                </view>
              </view>
            </view>
          </view>

          <view class="empty-products" wx:if="{{currentCategoryDishes.length === 0}}">
            <text class="empty-text">该分类暂无菜品</text>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 购物车 -->
    <view class="cart-container {{cartVisible ? 'cart-expanded' : ''}}">
      <!-- 添加遮罩层 -->
      <view class="cart-mask" wx:if="{{cartVisible}}" bindtap="toggleCart"></view>

      <view class="cart-header" bindtap="toggleCart">
        <view class="cart-left">
          <view class="cart-icon-container">
            <view class="cart-icon {{cartTotal.count > 0 ? 'active' : ''}}">
              <text class="cart-icon-text">🛒</text>
              <view class="cart-count" wx:if="{{cartTotal.count > 0}}">{{cartTotal.count}}</view>
            </view>
          </view>
          <view class="cart-info">
            <text class="cart-price" wx:if="{{cartTotal.count > 0}}">¥{{cartTotal.price}}</text>
            <text class="cart-empty" wx:else>购物车空空如也</text>
          </view>
        </view>
        <view class="cart-submit {{cartTotal.count > 0 ? 'active' : ''}}" bindtap="{{cartTotal.count > 0 ? 'goToCheckout' : ''}}">
          {{cartTotal.count > 0 ? '去结算' : '请选择商品'}}
        </view>
      </view>

      <!-- 购物车展开内容 -->
      <view class="cart-content" wx:if="{{cartVisible}}">
        <view class="cart-title">
          <text>已选商品</text>
          <text class="cart-clear" bindtap="clearCart">清空购物车</text>
        </view>
        <scroll-view scroll-y="true" class="cart-items">
          <view class="cart-item" wx:for="{{cartItems}}" wx:key="id">
            <text class="cart-item-name">{{item.name}}</text>
            <view class="cart-item-price-action">
              <text class="cart-item-price">¥{{item.price}}</text>
              <view class="product-action">
                <view class="action-minus" bindtap="minusDish" data-id="{{item.id}}">-</view>
                <view class="action-count">{{item.count}}</view>
                <view class="action-plus" bindtap="addDish" data-id="{{item.id}}">+</view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </block>

  <!-- 菜品详情弹窗 -->
  <view class="dish-detail-modal" wx:if="{{showDishDetail}}">
    <view class="modal-mask" bindtap="hideDishDetail"></view>
    <view class="modal-content">
      <view class="modal-close" bindtap="hideDishDetail">×</view>
      <image class="modal-image" src="{{selectedDish.image || '/images/default-dish.png'}}" mode="aspectFill"></image>
      <view class="modal-info">
        <view class="modal-name">{{selectedDish.name}}</view>
        <text class="modal-desc">{{selectedDish.description}}</text>
        <view class="modal-price-action">
          <text class="modal-price">¥{{selectedDish.price}}</text>
          <view class="product-action">
            <view class="action-minus" wx:if="{{selectedDish.count > 0}}" bindtap="minusDishInModal" data-id="{{selectedDish.id}}">-</view>
            <view class="action-count" wx:if="{{selectedDish.count > 0}}">{{selectedDish.count}}</view>
            <view class="action-plus" bindtap="addDishInModal" data-id="{{selectedDish.id}}">+</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 结算页面 -->
  <view class="checkout-modal" wx:if="{{showCheckout}}">
    <view class="modal-mask" bindtap="hideCheckout"></view>
    <view class="checkout-content">
      <view class="checkout-header">
        <text class="checkout-title">订单确认</text>
        <view class="modal-close" bindtap="hideCheckout">×</view>
      </view>

      <scroll-view scroll-y="true" class="checkout-scroll">

        <!-- 添加报餐取消提示信息 -->
        <view class="checkout-cart">
          <!--<text class="cart-label">温馨提示:</text>-->
          <view class="cancel-notice-content">
            <view class="cancel-notice-item">1. 报餐取消时间：当天的 11:00 前</view>
            <view class="cancel-notice-item">2. 报餐取消步骤：点击 "订单记录-取消报餐"</view>
          </view>
        </view>

        <view class="checkout-cart">
          <text class="cart-label">已选商品:</text>
          <scroll-view scroll-y="true" class="checkout-items">
            <block wx:if="{{cartItems && cartItems.length > 0}}">
              <view class="checkout-item" wx:for="{{cartItems}}" wx:key="id">
                <text class="checkout-item-name">{{item.name}}</text>
                <text class="checkout-item-count">x{{item.count}}</text>
                <text class="checkout-item-price">¥{{item.price * item.count}}</text>
              </view>
            </block>
            <view class="checkout-empty" wx:else>
              <text>购物车中暂无商品</text>
            </view>
          </scroll-view>
        </view>

        <!-- 新的合并后的日期时间控件 -->
        <view class="checkout-datetime">
          <text class="datetime-label">预约日期时间: <text class="required">*</text></text>
          <view class="datetime-picker-container">
            <picker mode="multiSelector" bindchange="bindDateTimeChange"
                    value="{{dateTimeIndex}}" range="{{dateTimeArray}}">
              <view class="picker-view">
                {{bookingDate && bookingTime ? bookingDate + ' ' + bookingTime : '请选择预约日期时间'}}
              </view>
            </picker>
          </view>
        </view>

        <view class="checkout-people">
          <text class="people-label">人数: <text class="required">*</text></text>
          <input class="people-input" type="number" placeholder="请输入人数" bindinput="inputPeople" value="{{peopleCount}}"/>
        </view>

        <view class="checkout-name">
          <text class="name-label">姓名: <text class="required"></text></text>
          <input class="name-input" type="text" placeholder="请输入姓名" bindinput="inputName" value="{{contactName}}"/>
          <!-- 最近姓名+电话对标签 -->
          <view class="recent-names" wx:if="{{showRecentNames && recentNamePhonePairs.length > 0}}">
            <view class="recent-name-item" wx:for="{{recentNamePhonePairs}}" wx:key="index" bindtap="selectRecentNamePhonePair" data-index="{{index}}">
              <text class="name-text">{{item.name || '未填写姓名'}}</text>
              <text class="phone-text">{{item.phone || '未填写电话'}}</text>
            </view>
          </view>
        </view>

        <view class="checkout-contact">
          <text class="contact-label">手机号码: <text class="required"></text></text>
          <input class="contact-input" type="number" placeholder="请输入手机号码" bindinput="inputPhone" value="{{contactPhone}}"/>
        </view>

        <view class="checkout-remark">
          <text class="remark-label">备注: <text class="optional">(选填)</text></text>
          <textarea class="remark-input" placeholder="有什么特殊要求请在这里告诉我们" bindinput="inputRemark" value="{{orderRemark}}"></textarea>
        </view>

        <!-- 优惠券选择 -->
        <view class="coupon-section">
          <view class="coupon-row" bindtap="showCouponList">
            <text class="coupon-label">优惠券</text>
            <view class="coupon-content">
              <text class="coupon-text" wx:if="{{selectedCoupons.length === 0}}">请选择优惠券</text>
              <text class="coupon-text selected" wx:else>已选择{{selectedCoupons.length}}张优惠券</text>
              <text class="coupon-arrow">></text>
            </view>
          </view>
          <view class="coupon-discount" wx:if="{{couponDiscount > 0}}">
            <text class="discount-label">优惠券折扣</text>
            <text class="discount-amount">-¥{{couponDiscount}}</text>
          </view>
        </view>

        <view class="checkout-total">
          <text>合计: ¥{{finalAmount || cartTotal.price}}</text>
        </view>
      </scroll-view>

      <view class="checkout-submit" bindtap="submitOrder">提交订单</view>
    </view>
  </view>

  <!-- 订单成功提示 -->
  <view class="order-success" wx:if="{{orderSuccess}}">
    <view class="success-icon">✓</view>
    <text class="success-text">订单提交成功！</text>
    <text class="success-order-id">订单号: {{orderId}}</text>
    <view class="success-actions">
      <view class="success-btn primary" bindtap="goToOrderDetail">查看订单</view>
      <!-- <view class="success-btn" bindtap="backToMenu">继续点餐</view> -->
    </view>
  </view>

  <!-- 支付选项弹窗 -->
  <view class="payment-modal" wx:if="{{showPaymentOptions}}">
    <view class="payment-container">
      <view class="payment-header">
        <text>实付金额</text>
        <view class="close-icon" bindtap="closePayment">×</view>
      </view>
      <view class="payment-amount">¥{{payableAmount}}</view>

      <!-- 添加订金提示 -->
      <!-- <view class="deposit-tip">
        <text>商务餐预订需要收取订金200元，到店就餐后根据最终实际金额进行结算，200元订金将作抵扣金额使用。</text>
      </view> -->

      <view class="payment-methods">
        <text>选择支付方式:</text>
        <!-- 微信支付选项 -->
        <view class="payment-method {{selectedPaymentMethod === 'wxpay' ? 'selected' : ''}}"
              bindtap="selectPaymentMethod"
              data-method="wxpay">
          <view class="payment-method-left">
            <image src="https://vegan.yiheship.com/static/images/wechat_pay.png" class="method-icon"></image>
            <text class="payment-method-text">微信支付</text>
          </view>
          <view class="radio-container">
            <view class="radio-btn"></view>
          </view>
        </view>

        <!-- 个人账户选项 -->
        <view class="payment-method {{selectedPaymentMethod === 'balance' ? 'selected' : ''}} {{!canUseBalance ? 'disabled' : ''}}"
              bindtap="{{canUseBalance ? 'selectPaymentMethod' : ''}}"
              data-method="balance">
          <view class="payment-method-left">
            <image src="https://vegan.yiheship.com/static/images/balance-icon.png" class="method-icon"></image>
            <text class="payment-method-text">个人账户</text>
            <view class="balance-info">
              <text class="balance-text">余额</text>
              <text class="balance-amount">{{userBalance || 0}}元</text>
            </view>
          </view>
          <view class="radio-container">
            <view class="radio-btn"></view>
          </view>
          <text class="insufficient-tip" wx:if="{{!canUseBalance}}">余额不足</text>
        </view>

        <!-- 新增企业支付选项 -->
        <view class="payment-method {{selectedPaymentMethod === 'biz_enterprise' ? 'selected' : ''}}"
              bindtap="selectPaymentMethod"
              data-method="biz_enterprise">
          <view class="payment-method-left">
            <image src="https://vegan.yiheship.com/static/images/enterprise-icon.png" class="method-icon"></image>
            <text class="payment-method-text">企业支付</text>
          </view>
          <view class="radio-container">
            <view class="radio-btn"></view>
          </view>
        </view>

        <!-- 企业选择列表（当选择企业支付时显示） -->
        <view class="enterprise-list" wx:if="{{selectedPaymentMethod === 'biz_enterprise'}}">
          <text class="enterprise-label">选择企业:</text>
          <block wx:if="{{enterpriseList.length > 0}}">
            <view class="enterprise-item {{selectedEnterprise === enterprise.id ? 'selected' : ''}}"
                  wx:for="{{enterpriseList}}"
                  wx:key="id"
                  wx:for-item="enterprise"
                  bindtap="selectEnterprise"
                  data-id="{{enterprise.id}}">
              <text class="enterprise-name">{{enterprise.company_name}}</text>
              <view class="radio-btn"></view>
            </view>
          </block>
          <view class="no-enterprise" wx:else>
            <text>暂无可用企业</text>
          </view>
        </view>
      </view>

      <button class="confirm-payment-btn" bindtap="confirmPayment">确认支付</button>
    </view>
  </view>

  <!-- 可用时段弹窗 - 美化版 -->
  <view class="available-slots-dialog" wx:if="{{showAvailableSlotsDialog}}">
    <view class="dialog-mask" bindtap="closeAvailableSlotsDialog"></view>
    <view class="dialog-container">
      <!-- 弹窗标题栏 -->
      <view class="dialog-header">
        <text class="dialog-title">可用就餐时段</text>
        <view class="dialog-close" bindtap="closeAvailableSlotsDialog">×</view>
      </view>

      <!-- 弹窗内容区 -->
      <view class="dialog-content">
        <!-- 提示信息 -->
        <view class="dialog-message">
          <view class="message-icon">!</view>
          <view class="message-text">{{dialogMessage}}</view>
        </view>

        <!-- 时段列表 -->
        <scroll-view scroll-y class="slots-list">
          <view class="slot-item-header">
            <text class="slot-header-date">日期</text>
            <text class="slot-header-time">可用时间</text>
          </view>

          <block wx:if="{{availableSlots.length > 0}}">
            <view class="slot-item info-only" wx:for="{{availableSlots}}" wx:key="index">
              <view class="slot-date">
                <view class="date-icon"></view>
                <text>{{item.date}}</text>
              </view>
              <view class="slot-time">
                <view class="time-icon"></view>
                <text>{{item.time_slot}}</text>
              </view>
            </view>
          </block>

          <view class="slots-empty" wx:else>
            <view class="empty-icon"></view>
            <text>暂无可用时段</text>
          </view>
        </scroll-view>

        <!-- 底部提示 -->
        <view class="slots-note" wx:if="{{availableSlots.length > 0}}">
          <view class="note-icon"></view>
          <text>请关闭弹窗后重新选择上述可用时段进行预订</text>
        </view>
      </view>

      <!-- 弹窗底部按钮 -->
      <view class="dialog-footer">
        <button class="dialog-btn" bindtap="closeAvailableSlotsDialog">我知道了</button>
      </view>
    </view>
  </view>

  <!-- 优惠券列表组件 -->
  <coupon-list
    visible="{{showCouponList}}"
    order-amount="{{cartTotal.price}}"
    selected-coupon-ids="{{selectedCouponIds}}"
    allow-multiple="{{true}}"
    bind:close="hideCouponList"
    bind:select="onCouponSelect"
  ></coupon-list>
</view>