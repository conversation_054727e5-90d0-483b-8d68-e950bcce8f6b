<!--pages/booking_business_edit/booking_business_edit.wxml-->
<view class="page-container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading"></view>
    <text>加载中...</text>
  </view>
  <block wx:else>
    <!-- 商家信息 -->
    <view class="store-info">
      <view class="store-banner">
        <image class="store-banner-image" src="https://vegan.yiheship.com/static/images/002.png" mode="aspectFill"></image>
        <view class="store-banner-overlay"></view>
      </view>
      <view class="store-card">
        <view class="store-avatar-container">
          <image class="store-avatar" src="{{storeInfo.image || '/images/default-store.png'}}" mode="aspectFill"></image>
        </view>
        <view class="store-details">
          <view class="store-header">
            <view class="store-title">
              <text class="store-name">{{storeInfo.name || '素食餐厅'}}</text>
              <view class="store-tag">营业中</view>
            </view>
            <!--<view class="store-rating">
              <text class="rating-score">{{storeInfo.rating || '5.0'}}</text>
              <text class="rating-count">{{storeInfo.ratingCount || '0'}}条评价</text>
            </view>-->
          </view>
          <view class="store-address-container">
            <text class="store-address">{{storeInfo.address || '地址信息'}}</text>
          </view>
          <!-- 添加联系电话 -->
          <view class="store-phone-container">
              <text class="store-phone">联系电话: 18802053864</text>
              <view class="call-button" bindtap="makePhoneCall">拨打</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 主要内容区域：左侧分类 + 右侧商品 -->
    <view class="main-content">
      <!-- 左侧菜单分类 -->
      <view class="left-categories">
        <scroll-view scroll-y="true" class="category-list">
          <view class="category-item {{index === currentCategory ? 'active' : ''}}" 
                wx:for="{{categories}}" 
                wx:key="id" 
                data-index="{{index}}" 
                bindtap="switchCategory">
            <text class="category-name">{{item.name}}</text>
          </view>
        </scroll-view>
      </view>

      <!-- 右侧商品列表 -->
      <view class="right-products">
        <scroll-view scroll-y="true" class="product-list" bindscrolltolower="loadMoreDishes">
          <view class="product-category-title" wx:if="{{currentCategoryDishes.length > 0}}">
            {{categories[currentCategory].name}}
          </view>
          
          <view class="product-item" wx:for="{{currentCategoryDishes}}" wx:key="id">
            <image class="product-image" src="{{item.image || '/images/default-dish.png'}}" mode="aspectFill" bindtap="showDishDetail" data-dish="{{item}}"></image>
            <view class="product-info">
              <text class="product-name">{{item.name}}</text>
              <text class="product-desc">{{item.description}}</text>
              <view class="product-meta">
                <text class="product-sales">月售{{item.sales || Math.floor(Math.random() * 100)}}份</text>
              </view>
              <view class="product-price-action">
                <text class="product-price">¥{{item.price}}</text>
                <view class="product-action">
                  <!-- 使用 newCount 而不是 count -->
                  <view class="action-minus" wx:if="{{item.newCount > 0}}" bindtap="minusDish" data-id="{{item.id}}">-</view>
                  <view class="action-count" wx:if="{{item.newCount > 0}}">{{item.newCount}}</view>
                  <view class="action-plus" bindtap="addDish" data-id="{{item.id}}">+</view>
                </view>
              </view>
            </view>
          </view>

          <view class="empty-products" wx:if="{{currentCategoryDishes.length === 0}}">
            <text class="empty-text">该分类暂无菜品</text>
          </view>
          
          <!-- 底部空白区域，防止购物车遮挡 -->
          <view class="bottom-space"></view>
        </scroll-view>
      </view>
    </view>

    <!-- 购物车 -->
    <view class="cart-container {{cartVisible ? 'cart-expanded' : ''}}">
      <!-- 添加遮罩层 -->
      <view class="cart-mask" wx:if="{{cartVisible}}" bindtap="toggleCart"></view>
      
      <view class="cart-header" bindtap="toggleCart">
        <view class="cart-left">
          <view class="cart-icon-container">
            <view class="cart-icon {{newCartTotal.count > 0 ? 'active' : ''}}">
              <text class="cart-icon-text">🛒</text>
              <view class="cart-count" wx:if="{{newCartTotal.count > 0}}">{{newCartTotal.count}}</view>
            </view>
          </view>
          <view class="cart-info">
            <text class="cart-price" wx:if="{{newCartTotal.count > 0}}">新增 ¥{{newCartTotal.price}}</text>
            <text class="cart-empty" wx:else>未添加新商品</text>
          </view>
        </view>
        <view class="cart-submit {{newCartTotal.count > 0 ? 'active' : ''}}" bindtap="{{newCartTotal.count > 0 ? 'goToCheckout' : ''}}">
          {{newCartTotal.count > 0 ? btn_mark : '请选择商品'}}
        </view>
      </view>

      <!-- 购物车展开内容 -->
      <view class="cart-content" wx:if="{{cartVisible}}">
        <view class="cart-title">
          <text>新增商品</text>
          <text class="cart-clear" bindtap="clearNewItems">清空新增</text>
        </view>

        <!-- 显示原始订单信息 -->
        <view class="original-order-info" wx:if="{{originalOrderTotal > 0}}">
          <view class="original-order-title">原订单金额: ¥{{originalOrderTotal}}</view>
        </view>

        <!-- 只显示新增商品 -->
        <scroll-view scroll-y="true" class="cart-items">
          <view class="cart-item" wx:for="{{newCartItems}}" wx:key="id">
            <text class="cart-item-name">{{item.name}}</text>
            <view class="cart-item-price-action">
              <text class="cart-item-price">¥{{item.price}}</text>
              <view class="product-action">
                <view class="action-minus" bindtap="minusNewItem" data-id="{{item.id}}">-</view>
                <view class="action-count">{{item.count}}</view>
                <view class="action-plus" bindtap="addDish" data-id="{{item.id}}">+</view>
              </view>
            </view>
          </view>

          <!-- 当没有新增商品时显示提示 -->
          <view class="empty-new-items" wx:if="{{newCartItems.length === 0}}">
            <text class="empty-text">还没有添加新商品</text>
          </view>
        </scroll-view>

        <!-- 显示总计信息 -->
        <view class="cart-summary">
          <view class="summary-item">
            <text class="summary-label">原订单:</text>
            <text class="summary-value">¥{{originalOrderTotal}}</text>
          </view>
          <view class="summary-item">
            <text class="summary-label">新增:</text>
            <text class="summary-value">¥{{newCartTotal.price}}</text>
          </view>
          <view class="summary-total">
            <text class="summary-label">总计:</text>
            <text class="summary-value">¥{{cartTotal.price}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 菜品详情弹窗 -->
    <view class="dish-detail-modal" wx:if="{{showDishDetail}}">
      <view class="modal-mask" bindtap="hideDishDetail"></view>
      <view class="modal-content">
        <view class="modal-close" bindtap="hideDishDetail">×</view>
        <image class="modal-image" src="{{selectedDish.image || '/images/default-dish.png'}}" mode="aspectFill"></image>
        <view class="modal-info">
          <text class="modal-name">{{selectedDish.name}}</text>
          <view class="modal-desc">{{selectedDish.description}}</view>
          <view class="modal-price-action">
            <text class="modal-price">¥{{selectedDish.price}}</text>
            <view class="product-action">
              <!-- 使用 newCount 而不是 count -->
              <view class="action-minus" wx:if="{{selectedDish.newCount > 0}}" bindtap="minusDishInModal" data-id="{{selectedDish.id}}">-</view>
              <view class="action-count" wx:if="{{selectedDish.newCount > 0}}">{{selectedDish.newCount}}</view>
              <view class="action-plus" bindtap="addDishInModal" data-id="{{selectedDish.id}}">+</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 结算页面 -->
    <view class="checkout-modal" wx:if="{{showCheckoutModal}}">
      <view class="modal-mask"></view>
      <view class="checkout-result-content">
        <view class="checkout-header">
          <text class="checkout-title">订单提交成功</text>
          <view class="modal-close" bindtap="closeCheckoutModal">×</view>
        </view>

        <view class="checkout-result-info">
          <view class="result-item">
            <text class="result-label">订单号:</text>
            <text class="result-value">{{checkoutOrderNo}}</text>
          </view>
          <view class="result-item">
            <text class="result-label">订单ID:</text>
            <text class="result-value">{{checkoutOrderId}}</text>
          </view>
        </view>

        <view class="checkout-options" wx:if="{{!showQrcode}}">
          <text class="checkout-question">是否需要进行结账？</text>
          <view class="checkout-actions">
            <button class="checkout-btn primary" bindtap="generateQrcode">去支付</button>
            <button class="checkout-btn" bindtap="continueShopping">返回</button>
          </view>
        </view>

        <view class="qrcode-container" wx:if="{{showQrcode}}">
          <text class="qrcode-title">请扫描二维码进行结账</text>
          <canvas canvas-id="qrcode-canvas" style="width:200px;height:200px;margin:0 auto;display:block;"></canvas>
          <text class="qrcode-tip">扫描上方二维码或访问以下链接</text>
          <text class="qrcode-url">{{checkoutUrl}}</text>
        </view>
      </view>
    </view>

    <!-- 订单成功提示 -->
    <view class="order-success" wx:if="{{orderSuccess}}">
      <view class="success-icon">✓</view>
      <text class="success-text">订单提交成功！</text>
      <text class="success-order-id">订单号: {{orderId}}</text>
      <view class="success-actions">
        <view class="success-btn primary" bindtap="goToOrderDetail">查看订单</view>
        <view class="success-btn" bindtap="backToMenu">继续点餐</view>
      </view>
    </view>

    <!-- 支付选项弹窗 -->
    <view class="payment-modal" wx:if="{{showPaymentOptions}}">
      <view class="payment-container">
        <view class="payment-header">
          <text>实付金额</text>
          <view class="close-icon" bindtap="closePayment">×</view>
        </view>
        <view class="payment-amount">¥{{payableAmount}}</view>

        <!-- 添加订金提示 -->
        <!-- <view class="deposit-tip">
          <text>商务餐预订需要收取订金200元，到店就餐后根据最终实际金额进行结算，200元订金将作抵扣金额使用。</text>
        </view>-->

        <view class="payment-methods">
          <view class="payment-methods-header">
            <text>选择支付方式:</text>
            <text class="payment-notice-red">(支付方式需与原订单一致)</text>
          </view>

          <!-- 微信支付选项 - 只在原订单是微信支付时显示 -->
          <view class="payment-method {{selectedPaymentMethod === 'wxpay' ? 'selected' : ''}}"
                wx:if="{{originalPaymentMethod === 'wechat_pay'}}"
                bindtap="selectPaymentMethod"
                data-method="wxpay">
            <view class="payment-method-left">
              <image src="https://vegan.yiheship.com/static/images/wechat_pay.png" class="method-icon"></image>
              <text class="payment-method-text">微信支付</text>
            </view>
            <view class="radio-container">
              <view class="radio-btn"></view>
            </view>
          </view>

          <!-- 个人账户选项 - 只在原订单是个人账户支付时显示 -->
          <view class="payment-method {{selectedPaymentMethod === 'balance' ? 'selected' : ''}} {{!canUseBalance ? 'disabled' : ''}}"
                wx:if="{{originalPaymentMethod === 'account_balance'}}"
                bindtap="{{canUseBalance ? 'selectPaymentMethod' : ''}}"
                data-method="balance">
            <view class="payment-method-left">
              <image src="https://vegan.yiheship.com/static/images/balance-icon.png" class="method-icon"></image>
              <text class="payment-method-text">个人账户</text>
              <view class="balance-info">
                <text class="balance-text">余额</text>
                <text class="balance-amount">{{userBalance || 0}}元</text>
              </view>
            </view>
            <view class="radio-container">
              <view class="radio-btn"></view>
            </view>
            <text class="insufficient-tip" wx:if="{{!canUseBalance}}">余额不足</text>
          </view>

          <!-- 企业支付选项 - 只在原订单是企业支付时显示 -->
          <view class="payment-method {{selectedPaymentMethod === 'biz_enterprise' ? 'selected' : ''}}"
                wx:if="{{originalPaymentMethod === 'biz_enterprise' || originalPayEnterpriseId}}"
                bindtap="selectPaymentMethod"
                data-method="biz_enterprise">
            <view class="payment-method-left">
              <image src="https://vegan.yiheship.com/static/images/enterprise-icon.png" class="method-icon"></image>
              <text class="payment-method-text">企业支付</text>
            </view>
            <view class="radio-container">
              <view class="radio-btn"></view>
            </view>
          </view>

          <!-- 企业选择列表（当选择企业支付时显示，且只显示原支付企业） -->
          <view class="enterprise-list" wx:if="{{selectedPaymentMethod === 'biz_enterprise' && (originalPaymentMethod === 'biz_enterprise' || originalPayEnterpriseId)}}">
            <text class="enterprise-label">选择企业:</text>
            <block wx:if="{{enterpriseList.length > 0}}">
              <view class="enterprise-item {{selectedEnterprise === enterprise.id ? 'selected' : ''}}"
                    wx:for="{{enterpriseList}}"
                    wx:key="id"
                    wx:for-item="enterprise"
                    wx:if="{{enterprise.id === originalPayEnterpriseId}}"
                    bindtap="selectEnterprise"
                    data-id="{{enterprise.id}}">
                <text class="enterprise-name">{{enterprise.company_name}}</text>
                <view class="radio-btn"></view>
              </view>
            </block>
            <view class="no-enterprise" wx:if="{{!originalPayEnterpriseId}}">
              <text>暂无可用企业</text>
            </view>
          </view>
        </view>

        <button class="confirm-payment-btn" bindtap="confirmPayment">确认支付</button>
      </view>
    </view>

    <!-- 左下角的查看资料按钮 -->
    <view class="user-info-btn" bindtap="showUserInfoModal">
      <text>订单信息</text>
    </view>

    <!-- 修改订单信息弹窗，添加金额分解 -->
    <view class="user-info-modal" wx:if="{{showUserInfo}}">
      <view class="modal-mask" bindtap="hideUserInfoModal"></view>
      <view class="user-info-content">
        <view class="user-info-header">
          <text class="user-info-title">订单信息</text>
          <view class="modal-close" bindtap="hideUserInfoModal">×</view>
        </view>

        <!-- 温馨提示 -->
        <view class="tips-section">
          <view class="tips-content">
            <text class="tips-text">如需调整预约信息或更换套餐，可直接取消当前订单，重新预约，超时无法取消的，可联系客服电话 18802053864</text>
            <view class="call-button" bindtap="makePhoneCall">拨打</view>
          </view>
        </view>

        <scroll-view scroll-y="true" class="user-info-scroll">
          <!-- 预约信息 -->
          <view class="user-info-section">
            <view class="user-info-item">
              <text class="info-label">预约日期:</text>
              <text class="info-value">{{bookingDate || '未设置'}}</text>
            </view>
            <view class="user-info-item">
              <text class="info-label">预约时间:</text>
              <text class="info-value">{{bookingTime || '未设置'}}</text>
            </view>
            <view class="user-info-item">
              <text class="info-label">联系人:</text>
              <text class="info-value">{{contactName || '未设置'}}</text>
            </view>
            <view class="user-info-item">
              <text class="info-label">联系电话:</text>
              <text class="info-value">{{contactPhone || '未设置'}}</text>
            </view>
            <view class="user-info-item">
              <text class="info-label">用餐人数:</text>
              <text class="info-value">{{peopleCount || '未设置'}}人</text>
            </view>
            <view class="user-info-item">
              <text class="info-label">备注信息:</text>
              <text class="info-value">{{orderRemark || '无'}}</text>
            </view>
          </view>

          <!-- 订单商品列表 -->
          <view class="order-items-section" wx:if="{{orderCartItems && orderCartItems.length > 0}}">
            <view class="section-title">已购商品 (<text class="total-label">合计: </text><text class="total-amount">¥ {{originalOrderTotal || 0}} </text>元)</view>
            <view class="order-item" wx:for="{{orderCartItems}}" wx:key="id">
              <!-- 第一行：图片 + 商品名 -->
              <view class="item-row-1">
                <image class="item-image" src="{{item.image ? (item.image.indexOf('http') === 0 ? item.image : baseUrlHost + item.image) : '/images/default-dish.png'}}" mode="aspectFill"></image>
                <text class="item-name">{{item.name}}</text>
              </view>
              <!-- 第二行：价格 + 数量 + 小计 -->
              <view class="item-row-2">
                <text class="item-price">¥{{item.price}}</text>
                <text class="item-quantity">x{{item.count}}</text>
                <text class="item-subtotal">¥{{item.price * item.count}}</text>
                <text class="item-subtotal"></text>
              </view>
            </view>

          </view>

          <!-- 新增商品列表（如果有的话） -->
          <!--
          <view class="order-items-section" wx:if="{{newCartItems && newCartItems.length > 0}}">
            <view class="section-title">新增商品</view>
            <view class="order-item" wx:for="{{newCartItems}}" wx:key="id">
              <view class="item-row-1">
                <image class="item-image" src="{{item.image ? (item.image.indexOf('http') === 0 ? item.image : baseUrlHost + item.image) : '/images/default-dish.png'}}" mode="aspectFill"></image>
                <text class="item-name">{{item.name}}</text>
              </view>
              <view class="item-row-2">
                <text class="item-price">¥{{item.price}}</text>
                <text class="item-quantity">x{{item.count}}</text>
                <text class="item-subtotal">¥{{item.price * item.count}}</text>
                <text class="item-subtotal"></text>
              </view>
            </view>
          </view>
          -->

          <!-- 订单金额信息 -->
          <!--<view class="user-info-section">
            <text class="section-title">订单金额</text>
            <view class="user-info-item">
              <text class="info-label">已支付金额:</text>
              <text class="info-value">¥{{originalOrderTotal || 0}}</text>
            </view>
            <view class="user-info-item">
              <text class="info-label">新增金额:</text>
              <text class="info-value">¥{{newCartTotal.price || 0}}</text>
            </view>
            <view class="user-info-item">
              <text class="info-label">总金额:</text>
              <text class="info-value">¥{{cartTotal.price || 0}}</text>
            </view>
          </view>-->

          <!-- 订单信息 -->
          <!--
          <view class="user-info-section">
            <text class="section-title">其他</text>
            <view class="user-info-item">
              <text class="info-label">订单ID:</text>
              <text class="info-value">{{orderId || '未知'}}</text>
            </view>
          </view>
          -->
        </scroll-view>

        <view class="user-info-footer">
          <button class="close-btn" bindtap="hideUserInfoModal">关闭</button>
        </view>
      </view>
    </view>
  </block>
</view>