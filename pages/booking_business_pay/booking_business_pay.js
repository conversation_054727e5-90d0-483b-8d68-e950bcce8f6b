// pages/booking_business_pay/booking_business_pay.js
import { loginRequest } from "../../service/index"
import drawQrcode from '../../utils/weapp.qrcode.esm.js'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    loading: true,
    orderId: '',
    orderNo: '',
    storeInfo: {},
    orderItems: [],
    contactPhone: '',
    orderRemark: '',
    bookingDate: '',
    bookingTime: '',
    peopleCount: '',
    contactName: '',
    totalAmount: 0,
    currentItemsAmount: 0, // 当前商品总金额
    unpaidAmount: 0, // 未支付金额
    showPaymentOptions: false,
    selectedPaymentMethod: 'wxpay',
    canUseBalance: false,
    userBalance: 0,
    payableAmount: 0,
    paymentOrderData: null,
    enterpriseList: [],
    selectedEnterprise: null,
    showCheckoutModal: false,
    checkoutOrderId: '',
    checkoutOrderNo: '',
    checkoutUrl: '',
    showQrcode: false,
    showUserInfo: false,
    baseUrlHost: '',
    user_mark: '', // 添加用户标记字段
    // 新增：保存原订单支付信息
    originalPaymentMethod: '', // 原订单支付方式
    originalPayEnterpriseId: null, // 原订单支付企业ID
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    var orderId = '';
    var baseUrlHost = getApp().globalData.baseUrlHost;
    console.log('options:', options);
    
    // 将baseUrlHost设置到data中
    this.setData({
      baseUrlHost: baseUrlHost
    });
    
    // 处理扫描二维码的情况
    if (options.q) {
      const q = decodeURIComponent(options.q);
      console.log('扫码进入，原始链接:', q);
      console.log('解码后的链接:', q);
      console.log('扫码时间:', options.scancode_time);
      
      const orderIdMatch = q.match(/[?&]order_id=([^&]*)/);
      console.log('订单ID匹配结果:', orderIdMatch);
      if (orderIdMatch && orderIdMatch[1]) {
        orderId = orderIdMatch[1];
        console.log('提取到的订单ID:', orderId);
      } else {
        console.log('未能从链接中提取到订单ID');
      }
    } else {
      orderId = options.order_id;
    }
    
    if (!orderId) {
      wx.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({
      orderId: orderId
    });

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '商务餐结账'
    });
    
    // 先检查用户是否已登录
    this.checkLoginStatus(orderId);
  },

  // 检查登录状态
  checkLoginStatus(orderId) {
    const token = wx.getStorageSync('token');
    if (!token) {
      console.log('未登录，跳转到登录页');
      wx.navigateTo({
        url: '/pages/phoneAuth/phoneAuth?redirect=booking_business_pay&order_id=' + orderId
      });
      return;
    }

    // 已有token，验证token是否有效
    loginRequest.post({
      url: '/auth',
      header: {
        token
      }
    }).then(res => {
      if (res.message === "已登录") {
        console.log('token有效，加载订单详情');
    // 加载订单详情
    this.loadOrderDetails(orderId);
      } else {
        console.log('token无效，需要重新登录');
        // token无效，调用app的monitor_token方法重新登录
        const app = getApp();
        if (app && app.monitor_token) {
          app.monitor_token().then(() => {
            // 重新登录成功后，检查是否获取到新token
            const newToken = wx.getStorageSync('token');
            if (newToken) {
              // 加载订单详情
              this.loadOrderDetails(orderId);
            } else {
              // 仍然没有token，跳转到登录页
              wx.navigateTo({
                url: '/pages/phoneAuth/phoneAuth?redirect=booking_business_pay&order_id=' + orderId
              });
            }
          }).catch(err => {
            console.error('重新登录失败', err);
            // 登录失败，跳转到登录页
            wx.navigateTo({
              url: '/pages/phoneAuth/phoneAuth?redirect=booking_business_pay&order_id=' + orderId
            });
          });
        } else {
          // 无法获取app实例，直接跳转到登录页
          wx.navigateTo({
            url: '/pages/phoneAuth/phoneAuth?redirect=booking_business_pay&order_id=' + orderId
          });
        }
      }
    }).catch(err => {
      console.error('验证token失败', err);
      // 验证失败，跳转到登录页
      wx.navigateTo({
        url: '/pages/phoneAuth/phoneAuth?redirect=booking_business_pay&order_id=' + orderId
      });
    });
  },

  // 加载订单详情
  loadOrderDetails(orderId) {
    wx.showLoading({
      title: '加载中...',
    });

    console.log('开始请求订单详情，orderId:', orderId);

    loginRequest.get({
      url: '/biz-order/detail',
      data: { order_id: orderId, source_mark: "order_query" },
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      console.log('订单详情接口返回数据:', res);
      wx.hideLoading();
      
      // 处理订单已修改的情况
      if (res.status === 403) {
        wx.showModal({
          title: '提示',
          content: res.message || '订单已结账，无法查看',
          showCancel: false,
          complete: () => {
            setTimeout(() => {
              wx.reLaunch({
                url: '/pages/index/index'
              });
            }, 100);
          }
        });
        return;
      }
      
      if (res.status === 200 && res.data) {
        const orderData = res.data;
        
        // 过滤掉ID为100、200的商品和status为cancelled的商品
        const filteredItems = (orderData.items || []).filter(item => 
          String(item.dish_id) !== '100' && String(item.dish_id) !== '200' && item.status !== 'cancelled'
        );
        
        // 计算当前商品总金额
        const currentItemsAmount = filteredItems.reduce((total, item) => {
          return total + (item.price * item.quantity);
        }, 0);
        
        // 计算未支付金额（当前商品总金额减去已支付金额）
        const totalAmount = orderData.total_amount || 0;
        let unpaidAmount = currentItemsAmount - totalAmount;
        // 使用toFixed修复浮点数精度问题，然后转回数字类型
        unpaidAmount = parseFloat((unpaidAmount > 0 ? unpaidAmount : 0).toFixed(2));
        
        // 设置订单基本信息
        this.setData({
          loading: false,
          orderNo: orderData.order_no || '',
          totalAmount: totalAmount,
          currentItemsAmount: currentItemsAmount,
          unpaidAmount: unpaidAmount, // 已经修复精度问题的值
          storeInfo: orderData.store_info || {},
          bookingDate: orderData.booking_date,
          bookingTime: orderData.booking_time,
          peopleCount: orderData.people_count,
          contactName: orderData.contact_name,
          contactPhone: orderData.contact_phone,
          orderRemark: orderData.remark || '',
          selectedEnterprise: orderData.enterprise_id || null,
          orderItems: filteredItems, // 使用过滤后的商品列表
          payableAmount: unpaidAmount, // 更新应付金额为未支付金额
          user_mark: orderData.user_mark || '', // 保存用户标记
          // 新增：保存原订单支付信息
          originalPaymentMethod: orderData.payment_method || '',
          originalPayEnterpriseId: orderData.pay_enterprise_id || null
        });

        // 生成结算地址
        this.generateCheckoutUrl(orderId);
        
        // 不再立即获取用户余额和企业列表，而是在显示支付选项时获取
        // this.getUserBalance();
        // this.getEnterpriseList();
      } else {
        wx.showToast({
          title: res.message || '获取订单详情失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('获取订单详情失败，详细错误:', err);
      wx.hideLoading();
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  // 生成结算地址
  generateCheckoutUrl(orderId) {
    const checkoutUrl = `${getApp().globalData.baseUrlHost}/zfxcx/?order_id=${orderId}`;
    console.log('生成结算地址:', checkoutUrl);
    
    this.setData({
      checkoutUrl: checkoutUrl,
      checkoutOrderId: orderId,
      checkoutOrderNo: this.data.orderNo
    });
  },

  // 获取用户余额
  getUserBalance() {
    loginRequest.get({
      url: '/account/balance',
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      if (res.status === 200) {
        const balance = res.data.balance || 0;
        const canUseBalance = balance >= this.data.unpaidAmount; // 修改为与未支付金额比较
        
        this.setData({
          userBalance: balance,
          canUseBalance: canUseBalance
        });
      }
    }).catch(err => {
      console.error('获取用户余额失败', err);
    });
  },

  // 获取企业列表
  getEnterpriseList() {
    loginRequest.get({
      url: '/enterprise/list',
      data: {
        order_id: this.data.orderId
      },
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      if (res.status === 200) {
        this.setData({
          enterpriseList: res.data || []
        });
        
        // 如果企业列表为空，则隐藏企业支付选项
        if (!res.data || res.data.length === 0) {
          // 如果当前选择的是企业支付，则切换到微信支付
          if (this.data.selectedPaymentMethod === 'biz_enterprise') {
            this.setData({
              selectedPaymentMethod: 'wxpay'
            });
          }
        }
      }
    }).catch(err => {
      console.error('获取企业列表失败', err);
    });
  },

  // 显示支付选项
  showPaymentOptions() {
    // 首先检查 user_mark 值
    if (!this.data.user_mark || (this.data.user_mark !== 'enterprise' && this.data.user_mark !== 'user' && this.data.user_mark !== 'admin')) {
      wx.showToast({
        title: '非该订单公司员工，暂时不提供支付账单功能！',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    // 在显示支付选项前先获取最新的用户余额和企业列表
    this.getUserBalance();
    this.getEnterpriseList();
    
    // 根据原订单支付方式设置默认选项
    let defaultPaymentMethod = 'wxpay';

    // 根据原订单支付方式确定默认选项
    if (this.data.originalPaymentMethod === 'wechat_pay') {
      defaultPaymentMethod = 'wxpay';
    } else if (this.data.originalPaymentMethod === 'account_balance') {
      defaultPaymentMethod = 'balance';
    } else if (this.data.originalPaymentMethod === 'biz_enterprise' || this.data.originalPayEnterpriseId) {
      defaultPaymentMethod = 'biz_enterprise';
    }

    // 如果支付金额为0，默认选择余额支付
    if (this.data.unpaidAmount === 0) {
      defaultPaymentMethod = 'balance';
    }
    
    // 显示支付选项
    this.setData({
      showPaymentOptions: true,
      selectedPaymentMethod: defaultPaymentMethod
    });
  },

  // 关闭支付选项
  closePayment() {
    this.setData({
      showPaymentOptions: false
    });
  },

  // 选择支付方式
  selectPaymentMethod(e) {
    const method = e.currentTarget.dataset.method;
    this.setData({
      selectedPaymentMethod: method
    });
  },

  // 选择企业
  selectEnterprise(e) {
    const enterpriseId = e.currentTarget.dataset.id;
    this.setData({
      selectedEnterprise: enterpriseId
    });
  },

  // 确认支付
  confirmPayment() {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.navigateTo({
        url: '/pages/phoneAuth/phoneAuth?redirect=booking_business_pay&order_id=' + this.data.orderId
      });
      return;
    }

    const paymentMethod = this.data.selectedPaymentMethod;
    
    // 企业支付需要选择企业
    if (paymentMethod === 'biz_enterprise' && !this.data.selectedEnterprise) {
      wx.showToast({
        title: '请选择企业',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '处理中...'
    });

    // 调用支付接口
    loginRequest.post({
      url: '/biz-order/pay',
      data: {
        order_id: this.data.orderId,
        payment_method: paymentMethod,
        enterprise_id: paymentMethod === 'biz_enterprise' ? this.data.selectedEnterprise : undefined
      },
      header: {
        'token': token
      }
    }).then(res => {
      wx.hideLoading();
      
      if (res.status === 200) {
        if (paymentMethod === 'wxpay') {
          // 微信支付需要调起支付
          const payData = res.data;
          this.callWxPay(payData);
        } else {
          // 其他支付方式直接显示成功
          wx.showToast({
            title: '支付成功',
            icon: 'success'
          });
          
          setTimeout(() => {
            wx.reLaunch({
              url: '/pages/index/index'
            });
          }, 1500);
        }
      } else {
        wx.showToast({
          title: res.message || '支付失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('支付失败', err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  // 调用微信支付
  callWxPay(payData) {
    // 显示支付提示
    wx.showLoading({
      title: '正在调起支付...',
      mask: true
    });

    wx.requestPayment({
      timeStamp: payData.timeStamp,
      nonceStr: payData.nonceStr,
      package: payData.package,
      signType: payData.signType,
      paySign: payData.paySign,
      success: (res) => {
        wx.hideLoading();
        wx.showToast({
          title: '支付成功',
          icon: 'success'
        });
        
        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/index/index'
          });
        }, 1500);
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('微信支付失败', err);

        // 区分用户取消和真正的支付错误
        if (err.errMsg && err.errMsg.includes('cancel')) {
          wx.showToast({
            title: '支付已取消',
            icon: 'none',
            duration: 1500
          });
        } else {
          wx.showToast({
            title: '支付失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      },
      complete: () => {
        // 确保loading被隐藏
        wx.hideLoading();
      }
    });
  },

  // 生成二维码
  generateQrcode() {
    this.setData({
      showQrcode: true
    });
    
    // 使用微信小程序的 drawQrcode 方法生成二维码
    const options = {
      width: 200,
      height: 200,
      canvasId: 'qrcode-canvas',
      x: 0,
      y: 0,
      text: this.data.checkoutUrl,
      callback: (res) => {
        console.log('二维码生成成功');
      }
    }
    
    drawQrcode(options);
  },

  // 显示用户信息弹窗
  showUserInfoModal() {
    this.setData({
      showUserInfo: true
    });
  },

  // 隐藏用户信息弹窗
  hideUserInfoModal() {
    this.setData({
      showUserInfo: false
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时也检查登录状态
    if (this.data.orderId) {
      const token = wx.getStorageSync('token');
      if (token) {
        // 如果页面数据未加载但有token，尝试加载数据
        if (this.data.loading) {
          this.loadOrderDetails(this.data.orderId);
        }
      }
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 添加拨打电话功能
  makePhoneCall() {
    wx.makePhoneCall({
      phoneNumber: '18802053864',
      success: () => {
        console.log('拨打电话成功');
      },
      fail: (err) => {
        console.error('拨打电话失败', err);
      }
    });
  },
})