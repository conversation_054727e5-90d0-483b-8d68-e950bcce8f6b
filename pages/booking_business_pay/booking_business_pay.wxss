/* pages/booking_business_pay/booking_business_pay.wxss */

/* 总体主盒子 */
.page-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background-color: #fff;
  color: #939393;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #fff;
  color: #939393;
}

.loading {
  width: 50rpx;
  height: 50rpx;
  border: 4rpx solid #ddd;
  border-top: 4rpx solid #ff4a4a;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 商家信息卡片 */
.store-info {
  position: relative;
  margin-bottom: 20rpx;
}

.store-banner {
  position: relative;
  width: 100%;
  height: 140rpx;
  overflow: hidden;
}

.store-banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.store-banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.2));
}

.store-card {
  position: relative;
  margin: -60rpx 30rpx 0;
  padding: 80rpx 30rpx 30rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.store-avatar-container {
  position: absolute;
  top: -60rpx;
  left: 30rpx;
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  padding: 4rpx;
  background: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.store-avatar {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  object-fit: cover;
}

.store-details {
  padding-top: 8rpx;
}

.store-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.store-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.store-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
}

.store-tag {
  font-size: 22rpx;
  color: #07c160;
  background: rgba(7, 193, 96, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.store-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: #fff5f5;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
}

.rating-score {
  font-size: 26rpx;
  font-weight: 600;
  color: #ff4a4a;
}

.rating-count {
  font-size: 22rpx;
  color: #ff4a4a;
  opacity: 0.8;
}

.store-address-container {
  display: flex;
  align-items: flex-start;
  gap: 8rpx;
  margin-top: 16rpx;
  padding: 16rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
}

.address-icon {
  width: 32rpx;
  height: 32rpx;
  background: url('data:image/svg+xml;base64,PHN2ZyB0PSIxNjgxMjM0NTY3ODkwIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjQxNzMiIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIj48cGF0aCBkPSJNNTU0LjY2NjY2NyA4NTMuMzMzMzMzYzE3MC42NjY2NjctMTI4IDE3MC42NjY2NjYtMzQxLjMzMzMzMyAxNzAuNjY2NjY2LTM0MS4zMzMzMzNzLTE3MC42NjY2NjctMTcwLjY2NjY2Ny0zNDEuMzMzMzMzLTE3MC42NjY2NjdjLTE3MC42NjY2NjcgMC0zNDEuMzMzMzMzIDE3MC42NjY2NjctMzQxLjMzMzMzMzMgMTcwLjY2NjY2N3MwIDE3MC42NjY2NjcgMTcwLjY2NjY2NiAzNDEuMzMzMzMzYzE3MC42NjY2NjcgMTI4IDM0MS4zMzMzMzMgMTI4IDM0MS4zMzMzMzQgMHogbTAtNjgyLjY2NjY2NmMxMjggMCAyNTYgMTI4IDI1NiAyNTZzLTEyOCAyNTYtMjU2IDI1Ni0yNTYtMTI4LTI1Ni0yNTYgMTI4LTI1NiAyNTYtMjU2eiIgZmlsbD0iIzk5OTk5OSIgcC1pZD0iNDE3NCI+PC9wYXRoPjwvc3ZnPg==') no-repeat center;
  background-size: contain;
  flex-shrink: 0;
}

.store-address {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 商家电话容器样式 */
.store-phone-container {
  display: flex;
  align-items: flex-start;
  gap: 8rpx;
  margin-top: 16rpx;
  padding: 16rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
}

/* 电话图标 */
.phone-icon {
  width: 32rpx;
  height: 32rpx;
  background: url('data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************') no-repeat center;
  background-size: contain;
  margin-right: 12rpx;
  flex-shrink: 0;
}

/* 电话内容区域 */
.phone-content {
  display: flex;
  align-items: center;
  flex: 1;
}

/* 电话号码文本样式 */
.store-phone {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-right: 10rpx;
}

/* 拨打按钮样式 */
.call-button {
  font-size: 24rpx;
  color: #666;
  background: #fff;
  padding: 4rpx 12rpx;
  border-radius: 30rpx;
}

/* 订单信息区域 */
.order-info-section, .order-items-section, .order-amount-section {
  margin: 20rpx 30rpx;
  padding: 30rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1px solid #f0f0f0;
}

.order-info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 订单商品列表 */
.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.order-item:last-child {
  border-bottom: none;
}

.order-item-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.item-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  object-fit: cover;
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.item-price {
  font-size: 26rpx;
  color: #ff4a4a;
}

.item-quantity {
  font-size: 28rpx;
  color: #666;
}

/* 订单金额 */
.order-amount-section {
  margin: 20rpx 30rpx 10rpx; /* 减少底部margin */
  padding: 30rpx 30rpx 20rpx; /* 减少底部padding */
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.amount-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.amount-label {
  font-size: 28rpx;
  color: #666;
}

.amount-value {
  font-size: 28rpx;
  color: #333;
}

.amount-item.total {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1px solid #f0f0f0;
}

.highlight {
  font-size: 36rpx;
  color: #ff4a4a;
  font-weight: 600;
}

/* 添加空白区域样式 */
.spacer {
  height: 30rpx; /* 可以根据需要调整高度 */
}
.spacer-pay {
  height: 1rpx; /* 可以根据需要调整高度 */
}

/* 支付按钮 */
.payment-button-container {
  margin: 0 30rpx 180rpx; /* 上边距改为0，因为已经有spacer */
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  width: calc(100% - 60rpx);
}

.payment-button {
  height: 100rpx;
  border-radius: 50rpx;
  font-size: 36rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(7, 193, 96, 0.3);
  background: linear-gradient(to right, #07c160, #09d16b);
  color: #fff;
  letter-spacing: 4rpx;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  width: 100%;
}

.payment-button:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(7, 193, 96, 0.2);
}

/* 支付弹窗样式 */
.payment-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.payment-container {
  background: #fff;
  border-radius: 12px;
  width: 80%;
  padding: 20px;
}

.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.close-icon {
  font-size: 40rpx;
  color: #999;
}

.payment-amount {
  font-size: 48rpx;
  font-weight: bold;
  text-align: center;
  margin: 30rpx 0;
  color:rgb(12, 12, 12);
  text-shadow: 0 2rpx 4rpx rgba(255, 74, 74, 0.1);
}

.payment-methods {
  margin: 30rpx 0;
}

.payment-methods text {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
  display: block;
}

/* 支付方式选项 */
.payment-method {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border: 1px solid #eee;
  border-radius: 8px;
  margin-bottom: 20rpx;
  position: relative;
  justify-content: space-between;
}

.payment-method-left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.method-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.payment-method-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-right: 20rpx;
  flex-shrink: 0;
}

/* 余额信息 */
.balance-info {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.balance-text {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.balance-amount {
  font-size: 24rpx;
  color: #ff4a4a;
  font-weight: 500;
}

/* 单选按钮 */
.radio-container {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  flex-shrink: 0;
}

.radio-btn {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  position: relative;
}

.payment-method.selected {
  border-color: #07c160;
  background: rgba(7, 193, 96, 0.05);
}

.payment-method.selected .radio-btn {
  background: #07c160;
  border-color: #07c160;
}

.payment-method.selected .radio-btn::after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  background: #fff;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 企业列表 */
.enterprise-list {
  margin-top: 20rpx;
  padding: 0 20rpx;
}

.enterprise-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.enterprise-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 1px solid #eee;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  background: #fff;
}

.enterprise-item.selected {
  border-color: #07c160;
  background: rgba(7, 193, 96, 0.05);
}

.enterprise-name {
  font-size: 28rpx;
  color: #333;
}

.enterprise-item .radio-btn {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  position: relative;
}

.enterprise-item.selected .radio-btn {
  border-color: #07c160;
  background: #07c160;
}

.enterprise-item.selected .radio-btn::after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  background: #fff;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.no-enterprise {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 确认支付按钮 */
.confirm-payment-btn {
  background: #07c160;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 24rpx;
  width: 100%;
  font-size: 32rpx;
  font-weight: 500;
  margin-top: 30rpx;
  transition: all 0.3s ease;
}

.confirm-payment-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}

/* 二维码弹窗 */
.qrcode-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.qrcode-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  width: 80%;
  max-width: 600rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
}

.qrcode-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.qrcode-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
}

.qrcode-tip {
  font-size: 26rpx;
  color: #666;
  margin-top: 30rpx;
  text-align: center;
}

.qrcode-url {
  font-size: 24rpx;
  color: #07c160;
  margin-top: 10rpx;
  text-align: center;
  word-break: break-all;
}

/* 左下角查看预订信息按钮 */
.user-info-btn {
  position: fixed;
  left: 30rpx;
  bottom: 200rpx;
  background-color: #07c160;
  color: #fff;
  padding: 20rpx 30rpx;
  border-radius: 50rpx;
  box-shadow: 0 4rpx 12rpx rgba(37, 189, 115, 0.3);
  z-index: 100;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 用户信息弹窗 */
.user-info-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.user-info-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 30rpx 30rpx 0 0;
  overflow: hidden;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.user-info-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.user-info-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.user-info-scroll {
  flex: 1;
  max-height: 60vh;
  padding: 20rpx 30rpx;
}

.user-info-section {
  margin-bottom: 30rpx;
}

.user-info-item {
  display: flex;
  margin-bottom: 20rpx;
  align-items: flex-start;
}

.user-info-footer {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.close-btn {
  background-color: #07c160;
  color: #fff;
  border-radius: 50rpx;
  font-size: 30rpx;
  padding: 16rpx 0;
  width: 100%;
  text-align: center;
  border: none;
}

/* 地址文本样式 - 修改字体大小 */
.store-address {
  font-size: 26rpx; /* 原来是26rpx，改小了 */
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 在现有样式的基础上添加以下样式 */

/* 支付方式头部样式 */
.payment-methods-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #333;
}

/* 红色提示文字样式 */
.payment-notice-red {
  font-size: 26rpx !important;
  color: #ff4a4a !important;
  font-weight: 400 !important;
  margin-bottom: 0 !important;
  display: inline !important;
}

/* 支付方式选项样式优化 */
.payment-method {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border: 1px solid #eee;
  border-radius: 8px;
  margin-bottom: 20rpx;
  position: relative;
  justify-content: space-between;
}

/* 统一支付图标样式 */
.method-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

/* 支付方式左侧容器 */
.payment-method-left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

/* 支付方式文字 */
.payment-method-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-right: 20rpx;
  flex-shrink: 0;
}

/* 余额信息容器 */
.balance-info {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.balance-text {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.balance-amount {
  font-size: 24rpx;
  color: #ff4a4a;
  font-weight: 500;
}

/* 单选按钮容器 */
.radio-container {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  flex-shrink: 0;
}

.radio-btn {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  position: relative;
}

.payment-method.selected {
  border-color: #07c160;
  background: rgba(7, 193, 96, 0.05);
}

.payment-method.selected .radio-btn {
  background: #07c160;
  border-color: #07c160;
}

.payment-method.selected .radio-btn::after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  background: #fff;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 禁用状态样式 */
.payment-method.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 余额不足提示 */
.insufficient-tip {
  font-size: 24rpx;
  color: #ff4a4a;
  margin-left: 20rpx;
}

/* 企业列表样式 */
.enterprise-list {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8px;
}

.enterprise-label {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.enterprise-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15rpx 0;
  border-bottom: 1px solid #eee;
}

.enterprise-item:last-child {
  border-bottom: none;
}

.enterprise-item.selected {
  color: #07c160;
}

.enterprise-name {
  font-size: 26rpx;
  color: #333;
}

.no-enterprise {
  text-align: center;
  color: #999;
  font-size: 26rpx;
  padding: 20rpx 0;
}