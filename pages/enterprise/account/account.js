// 企业账户管理页面
const app = getApp()

Page({
  data: {
    enterprise_id: '',
    enterprise_name: '',
    accountBalance: '0.00',
    isLoading: false,
    isLoadingMore: false,
    hasMore: true,
    currentPage: 0,
    pageSize: 20,
    total: 0,
    
    // 筛选条件
    startDate: '',
    endDate: '',
    
    // 流水记录
    transactions: [],
    
    // 充值弹窗
    showRechargeModal: false,
    rechargeAmount: ''
  },

  onLoad: function(options) {
    // 获取企业ID和名称
    if (options.enterprise_id && options.enterprise_name) {
      this.setData({
        enterprise_id: options.enterprise_id,
        enterprise_name: decodeURIComponent(options.enterprise_name)
      });
      
      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: `${this.data.enterprise_name} - 账户管理`
      });
      
      // 加载账户信息和流水记录
      this.loadAccountInfo();
      this.loadTransactions();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载账户信息
  loadAccountInfo: function() {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.request({
      url: app.globalData.baseUrl + '/enterprise/account/balance',
      method: 'GET',
      header: {
        'token': token,
        'content-type': 'application/json'
      },
      data: {
        enterprise_id: this.data.enterprise_id
      },
      success: (res) => {
        console.log('账户信息响应:', res.data);
        if (res.data.status === 200) {
          this.setData({
            accountBalance: res.data.data.regular_account.balance || '0.00'
          });
        } else {
          wx.showToast({
            title: res.data.message || '获取账户信息失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('加载账户信息失败', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  // 加载流水记录
  loadTransactions: function(isLoadMore = false) {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 设置加载状态
    if (isLoadMore) {
      if (!this.data.hasMore || this.data.isLoadingMore) {
        return; // 没有更多数据或正在加载中
      }
      this.setData({ isLoadingMore: true });
    } else {
      this.setData({ 
        isLoading: true,
        currentPage: 0,
        hasMore: true 
      });
    }

    const currentPage = isLoadMore ? this.data.currentPage + 1 : 0;
    const skip = currentPage * this.data.pageSize;

    // 构建请求参数
    const requestData = {
      enterprise_id: this.data.enterprise_id,
      skip: skip,
      limit: this.data.pageSize
    };

    // 添加时间筛选条件 - 转换为API要求的格式
    if (this.data.startDate) {
      requestData.start_time = this.data.startDate; // YYYY-MM-DD格式，API会自动转换为00:00:00
    }
    if (this.data.endDate) {
      requestData.end_time = this.data.endDate; // YYYY-MM-DD格式，API会自动转换为23:59:59
    }

    wx.request({
      url: app.globalData.baseUrl + '/enterprise/account/transactions',
      method: 'GET',
      header: {
        'token': token,
        'content-type': 'application/json'
      },
      data: requestData,
      success: (res) => {
        console.log('流水记录响应:', res.data);
        if (res.data.status === 200) {
          const newTransactions = res.data.data.transactions || [];
          const total = res.data.data.total || 0;
          const currentTransactions = isLoadMore ? this.data.transactions.concat(newTransactions) : newTransactions;
          const hasMore = currentTransactions.length < total;
          
          // 处理交易数据，格式化显示
          const formattedTransactions = currentTransactions.map(item => {
            // 先确定收支类型（基于原始金额）
            const transactionType = this.getTransactionType(item);
            return {
              ...item,
              // 格式化时间显示
              created_at: this.formatTransactionTime(item.transaction_time),
              // 根据交易类型确定收支方向
              type: transactionType,
              // 格式化交易类型名称
              type_name: this.getTransactionTypeName(item),
              // 格式化金额（显示用的绝对值）
              amount: Math.abs(parseFloat(item.amount || 0)).toFixed(2),
              balance_after: parseFloat(item.balance_after || 0).toFixed(2)
            };
          });
          
          this.setData({
            transactions: formattedTransactions,
            total: total,
            currentPage: currentPage,
            hasMore: hasMore,
            isLoading: false,
            isLoadingMore: false
          });
        } else {
          wx.showToast({
            title: res.data.message || '加载失败',
            icon: 'none'
          });
          this.setData({ 
            isLoading: false,
            isLoadingMore: false 
          });
        }
      },
      fail: (err) => {
        console.error('加载流水记录失败', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        this.setData({ 
          isLoading: false,
          isLoadingMore: false 
        });
      }
    });
  },

  // 格式化交易时间
  formatTransactionTime: function(timeStr) {
    if (!timeStr) return '';
    
    // 将时间字符串转换为 iOS 兼容的格式
    // 将 "YYYY-MM-DD HH:mm:ss" 格式转换为 "YYYY/MM/DD HH:mm:ss"
    let compatibleTimeStr = timeStr;
    if (typeof timeStr === 'string' && /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(timeStr)) {
      compatibleTimeStr = timeStr.replace(/-/g, '/');
    }
    
    // 如果是完整的时间格式，只显示日期和时间
    const date = new Date(compatibleTimeStr);
    if (isNaN(date.getTime())) return timeStr;
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${month}-${day} ${hours}:${minutes}`;
  },

  // 获取交易类型（收入/支出）
  getTransactionType: function(transaction) {
    // 优先根据交易类型判断
    if (transaction.transaction_type) {
      // 充值、退款、转入等为收入
      if (['deposit', 'refund', 'transfer_in', 'recharge', 'income'].includes(transaction.transaction_type)) {
        return 'income';
      }
      // 支付、消费、转出等为支出
      if (['payment', 'consume', 'transfer_out', 'withdraw', 'expense'].includes(transaction.transaction_type)) {
        return 'expense';
      }
    }
    
    // 如果没有明确类型，根据金额正负判断
    const amount = parseFloat(transaction.amount || 0);
    return amount >= 0 ? 'income' : 'expense';
  },

  // 获取交易类型名称
  getTransactionTypeName: function(transaction) {
    const typeMap = {
      'payment': '支付',
      'refund': '退款',
      'deposit': '账户充值',
      'withdraw': '提现',
      'transfer': '转账',
      'consume': '消费'
    };
    
    return typeMap[transaction.transaction_type] || (transaction.transaction_type || '其他交易');
  },

  // 开始日期选择
  onStartDateChange: function(e) {
    this.setData({
      startDate: e.detail.value
    });
    this.loadTransactions(false);
  },

  // 结束日期选择
  onEndDateChange: function(e) {
    this.setData({
      endDate: e.detail.value
    });
    this.loadTransactions(false);
  },

  // 清空筛选条件
  clearDateFilter: function() {
    this.setData({
      startDate: '',
      endDate: ''
    });
    this.loadTransactions(false);
  },

  // 显示充值弹窗
  showRechargeModal: function() {
    this.setData({ 
      showRechargeModal: true,
      rechargeAmount: ''
    });
  },

  // 隐藏充值弹窗
  hideRechargeModal: function() {
    this.setData({ showRechargeModal: false });
  },

  // 阻止事件冒泡
  stopPropagation: function() {
    // 空函数，仅用于阻止事件冒泡
  },

  // 充值金额输入
  onRechargeAmountInput: function(e) {
    this.setData({
      rechargeAmount: e.detail.value
    });
  },

  // 快捷金额选择
  onQuickAmount: function(e) {
    const amount = e.currentTarget.dataset.amount;
    this.setData({
      rechargeAmount: amount.toString()
    });
  },

  // 执行充值 - 参考用户充值逻辑
  performRecharge: function() {
    const amount = parseFloat(this.data.rechargeAmount);
    
    // 验证金额
    if (!amount || amount <= 0) {
      wx.showToast({
        title: '请输入有效金额',
        icon: 'none'
      });
      return;
    }

    if (amount > 20000) {
      wx.showToast({
        title: '单次充值金额不能超过2万元',
        icon: 'none'
      });
      return;
    }

    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    this.createEnterpriseRechargeOrder(amount);
  },

  // 创建企业充值订单
  createEnterpriseRechargeOrder: function(amount) {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '处理中...',
    });

    wx.request({
      url: app.globalData.baseUrl + '/enterprise/account/create-recharge-order',
      method: 'POST',
      header: {
        'token': token,
        'content-type': 'application/json'
      },
      data: {
        enterprise_id: this.data.enterprise_id,
        amount: amount
      },
      success: (res) => {
        wx.hideLoading();
        console.log('企业充值订单响应:', res.data);
        
        if (res.data.status === 200) {
          const payParams = res.data.payParams;
          // 调起微信支付
          wx.requestPayment({
            ...payParams,
            success: (result) => {
              // 支付成功后，先乐观更新余额
              const currentBalance = parseFloat(this.data.accountBalance || '0');
              const newBalance = (currentBalance + amount).toFixed(2);
              
              this.setData({
                accountBalance: newBalance
              });

              wx.showToast({
                title: '充值成功',
                icon: 'success'
              });

              this.hideRechargeModal();
              
              // 重新加载账户信息和流水记录以获取最新数据
              this.loadAccountInfo();
              this.loadTransactions(false);

              // 轮询检查订单状态（可选）
              if (res.data.orderId) {
                this.checkEnterpriseOrderStatus(res.data.orderId);
              }
            },
            fail: (err) => {
              console.error('支付失败', err);
              wx.showToast({
                title: '支付失败',
                icon: 'none'
              });
            }
          });
        } else {
          wx.showToast({
            title: res.data.message || '创建订单失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('创建企业充值订单失败', err);
        wx.showToast({
          title: '创建订单失败',
          icon: 'none'
        });
      }
    });
  },

  // 轮询检查企业订单状态
  checkEnterpriseOrderStatus: function(orderId) {
    let retryCount = 0;
    const maxRetries = 3;
    const checkInterval = 2000; // 2秒检查一次

    const checkStatus = () => {
      if (retryCount >= maxRetries) {
        // 超过最大重试次数，刷新账户信息以获取实际余额
        this.loadAccountInfo();
        return;
      }

      wx.request({
        url: app.globalData.baseUrl + '/enterprise/account/check-order-status',
        method: 'GET',
        header: {
          'token': wx.getStorageSync('token'),
          'content-type': 'application/json'
        },
        data: {
          enterprise_id: this.data.enterprise_id,
          orderId: orderId
        },
        success: (res) => {
          if (res.data.status === 200) {
            if (res.data.orderStatus === 'SUCCESS') {
              // 订单确认成功，更新实际余额
              this.loadAccountInfo();
              this.loadTransactions(false);
            } else if (res.data.orderStatus === 'PENDING') {
              // 订单待处理，继续轮询
              retryCount++;
              setTimeout(checkStatus, checkInterval);
            }
          }
        },
        fail: () => {
          retryCount++;
          setTimeout(checkStatus, checkInterval);
        }
      });
    };

    // 开始轮询
    setTimeout(checkStatus, checkInterval);
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadAccountInfo();
    this.loadTransactions(false);
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (this.data.hasMore && !this.data.isLoadingMore) {
      this.loadTransactions(true);
    }
  }
}); 