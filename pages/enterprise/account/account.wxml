<!-- 企业账户管理页面 -->
<view class="container">
  <!-- 账户信息区域 -->
  <view class="account-section">
    <view class="balance-card">
      <view class="balance-header">
        <text class="balance-title">账户余额</text>
      </view>
      <view class="balance-amount">
        <text class="currency">¥</text>
        <text class="amount">{{accountBalance}}</text>
      </view>
      <view class="balance-actions">
      <!--
        <button class="recharge-btn" bindtap="showRechargeModal">充值</button>
      -->
      </view>
    </view>
  </view>

  <!-- 筛选区域 -->
  <view class="filter-section">
    <view class="filter-header">
      <text class="filter-title">交易流水</text>
      <button class="clear-filter-btn" bindtap="clearDateFilter" wx:if="{{startDate || endDate}}">清空筛选</button>
    </view>
    <view class="date-filter">
      <view class="date-item">
        <text class="date-label">开始日期</text>
        <picker mode="date" value="{{startDate}}" bindchange="onStartDateChange">
          <view class="date-picker">
            <text class="date-text">{{startDate || '请选择'}}</text>
            <text class="picker-icon">📅</text>
          </view>
        </picker>
      </view>
      <view class="date-item">
        <text class="date-label">结束日期</text>
        <picker mode="date" value="{{endDate}}" bindchange="onEndDateChange">
          <view class="date-picker">
            <text class="date-text">{{endDate || '请选择'}}</text>
            <text class="picker-icon">📅</text>
          </view>
        </picker>
      </view>
    </view>
  </view>

  <!-- 流水记录列表 -->
  <view class="transactions-section">
    <view wx:if="{{isLoading}}" class="loading">
      <text>加载中...</text>
    </view>
    
    <view wx:elif="{{transactions.length === 0}}" class="empty">
      <text>暂无交易记录</text>
    </view>
    
    <view wx:else>
      <view class="transaction-item" wx:for="{{transactions}}" wx:key="id">
        <view class="transaction-main">
          <text class="transaction-type">{{item.type_name}}</text>
          <text class="transaction-time">{{item.created_at}}</text>
          <text class="amount-text {{item.type === 'income' ? 'income' : 'expense'}}">{{item.type === 'income' ? '+' : '-'}}¥{{item.amount}}</text>
        </view>
      </view>
      
      <!-- 加载更多提示 -->
      <view class="load-more" wx:if="{{transactions.length > 0}}">
        <view wx:if="{{isLoadingMore}}" class="loading-more">
          <text>加载更多中...</text>
        </view>
        <view wx:elif="{{!hasMore}}" class="no-more">
          <text>已显示全部 {{total}} 条记录</text>
        </view>
        <view wx:else class="pull-more">
          <text>上拉加载更多</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 充值弹窗 -->
  <view class="modal-overlay" wx:if="{{showRechargeModal}}" catchtap="hideRechargeModal">
    <view class="modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">企业账户充值</text>
        <text class="modal-close" catchtap="hideRechargeModal">×</text>
      </view>
      
      <view class="modal-content">
        <view class="form-item">
          <text class="form-label">充值金额 *</text>
          <view class="amount-input-wrapper">
            <text class="currency-symbol">¥</text>
            <input 
              class="amount-input" 
              placeholder="请输入充值金额" 
              type="digit"
              value="{{rechargeAmount}}"
              bindinput="onRechargeAmountInput"
            />
          </view>
          <text class="form-hint">单次充值金额不超过10万元</text>
        </view>
        
        <view class="quick-amounts">
          <text class="quick-label">快捷金额</text>
          <view class="amount-buttons">
            <button class="amount-btn" data-amount="1000" bindtap="onQuickAmount">1,000</button>
            <button class="amount-btn" data-amount="5000" bindtap="onQuickAmount">5,000</button>
            <button class="amount-btn" data-amount="10000" bindtap="onQuickAmount">10,000</button>
            <button class="amount-btn" data-amount="20000" bindtap="onQuickAmount">20,000</button>
          </view>
        </view>
      </view>
      
      <view class="modal-footer">
        <button class="modal-btn cancel-btn" catchtap="hideRechargeModal">取消</button>
        <button class="modal-btn confirm-btn" catchtap="performRecharge">确认充值</button>
      </view>
    </view>
  </view>
</view> 