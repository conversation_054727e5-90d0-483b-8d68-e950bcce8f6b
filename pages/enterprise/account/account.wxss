/* 企业账户管理页面样式 */
.container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 0;
}

/* 账户信息区域 */
.account-section {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.balance-card {
  background: linear-gradient(135deg, #07c160 0%, #05a351 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  color: #fff;
  box-shadow: 0 8rpx 25rpx rgba(7, 193, 96, 0.3);
}

.balance-header {
  margin-bottom: 20rpx;
}

.balance-title {
  font-size: 28rpx;
  opacity: 0.9;
}

.balance-amount {
  display: flex;
  align-items: baseline;
  margin-bottom: 30rpx;
}

.currency {
  font-size: 36rpx;
  font-weight: bold;
  margin-right: 10rpx;
}

.amount {
  font-size: 68rpx;
  font-weight: bold;
  font-family: "Helvetica Neue", sans-serif;
}

.balance-actions {
  display: flex;
  justify-content: flex-end;
}

.recharge-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 30rpx;
  padding: 15rpx 40rpx;
  font-size: 28rpx;
  backdrop-filter: blur(10rpx);
}

.recharge-btn:active {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

/* 筛选区域 */
.filter-section {
  background-color: #fff;
  margin: 0 30rpx 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.05);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.filter-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.clear-filter-btn {
  background-color: #f0f0f0;
  color: #666;
  border: none;
  border-radius: 20rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.clear-filter-btn:active {
  background-color: #e0e0e0;
}

.date-filter {
  display: flex;
  gap: 20rpx;
}

.date-item {
  flex: 1;
}

.date-label {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.date-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
}

.date-text {
  font-size: 28rpx;
  color: #495057;
}

.picker-icon {
  font-size: 24rpx;
}

/* 流水记录区域 */
.transactions-section {
  padding: 0 30rpx;
}

.loading,
.empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  color: #999;
  font-size: 28rpx;
}

.transaction-item {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 15rpx;
  padding: 20rpx 25rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.transaction-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20rpx;
  height: 40rpx;
  line-height: 40rpx;
}

.transaction-type {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 40rpx;
  line-height: 40rpx;
  display: flex;
  align-items: center;
}

.transaction-time {
  font-size: 28rpx;
  color: #999;
  flex-shrink: 0;
  margin-right: 20rpx;
  height: 40rpx;
  line-height: 40rpx;
  display: flex;
  align-items: center;
}

.amount-text {
  font-size: 28rpx;
  font-weight: bold;
  flex-shrink: 0;
  height: 40rpx;
  line-height: 40rpx;
  display: flex;
  align-items: center;
}

.amount-text.income {
  color: #52c41a;
}

.amount-text.expense {
  color: #ff4d4f;
}

/* 加载更多提示 */
.load-more {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
  margin-top: 20rpx;
}

.loading-more,
.no-more,
.pull-more {
  font-size: 26rpx;
  color: #999;
}

.loading-more {
  color: #1890ff;
}

.no-more {
  color: #666;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  width: 90%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}

.modal-close:active {
  background-color: #f5f5f5;
}

.modal-content {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.amount-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border: 2rpx solid #e1e8ed;
  border-radius: 12rpx;
  padding: 0 30rpx;
}

.currency-symbol {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.amount-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  border: none;
  background: transparent;
}

.amount-input:focus {
  border-color: #07c160;
}

.form-hint {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.quick-amounts {
  margin-top: 20rpx;
}

.quick-label {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.amount-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.amount-btn {
  flex: 1;
  min-width: 120rpx;
  height: 60rpx;
  background-color: #f8f9fa;
  color: #495057;
  border: 1rpx solid #dee2e6;
  border-radius: 30rpx;
  font-size: 26rpx;
  line-height: 60rpx;
}

.amount-btn:active {
  background-color: #e9ecef;
  transform: scale(0.95);
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 30rpx 40rpx;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 40rpx;
  font-size: 30rpx;
  line-height: 80rpx;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.cancel-btn:active {
  background-color: #e8e8e8;
}

.confirm-btn {
  background: linear-gradient(135deg, #07c160, #05a351);
  color: #fff;
}

.confirm-btn:active {
  transform: scale(0.98);
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .date-filter {
    flex-direction: column;
    gap: 20rpx;
  }

  .transaction-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 10rpx;
  }

  .transaction-time {
    margin-right: 0;
    order: 2;
  }

  .amount-buttons {
    flex-direction: column;
  }

  .amount-btn {
    min-width: auto;
  }
}
