// 企业订餐统计页面
const app = getApp();

Page({
  data: {
    isEnterpriseAdmin: false,
    enterprise_id: '',
    enterprise_name: '',
    activeTab: 0, // 0: 自助午餐, 1: 自助晚餐, 2: 商务餐
    tabs: ['自助午餐', '自助晚餐', '商务餐'],
    
    // 日期筛选
    startDate: '',
    endDate: '',
    activeQuickDate: 'today', // 当前选中的快捷日期
    
    // 快捷日期选择
    quickDates: [
      { label: '今天', value: 'today' },
      { label: '明天', value: 'tomorrow' },
      { label: '本周', value: 'week' },
      { label: '本月', value: 'month' }
    ],
    
    // 自助午餐数据
    selfServiceLunchData: {
      list: [],
      total: 0,
      totalPaidCount: 0,
      totalVerifiedCount: 0,
      loading: false
    },
    
    // 自助晚餐数据
    selfServiceDinnerData: {
      list: [],
      total: 0,
      totalPaidCount: 0,
      totalVerifiedCount: 0,
      loading: false
    },
    
    // 商务餐数据
    businessData: {
      list: [],
      total: 0,
      totalAmount: 0,
      totalPersons: 0,
      loading: false
    }
  },

  onLoad: function(options) {
    // 获取企业ID和名称
    if (options.enterprise_id && options.enterprise_name) {
      this.setData({
        enterprise_id: options.enterprise_id,
        enterprise_name: decodeURIComponent(options.enterprise_name)
      });
      
      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: `${this.data.enterprise_name} - 预订统计`
      });
      
      this.checkEnterpriseAdmin();
      this.initDates();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  onShow: function() {
    this.checkEnterpriseAdmin();
  },

  // 检查企业管理员权限
  checkEnterpriseAdmin: function() {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    // 这里可以添加检查当前用户是否是该企业管理员的逻辑
    // 暂时直接设为true，实际项目中应该调用API验证
    this.setData({
      isEnterpriseAdmin: true
    });
    
    // 确保日期已初始化后再加载数据
    if (this.data.startDate && this.data.endDate) {
      this.loadData();
    }
  },

  // 初始化日期
  initDates: function() {
    const today = new Date();
    const todayStr = this.formatDate(today);
    
    this.setData({
      startDate: todayStr,
      endDate: todayStr
    });
  },

  // 格式化日期
  formatDate: function(date) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const monthStr = month < 10 ? '0' + month : '' + month;
    const dayStr = day < 10 ? '0' + day : '' + day;
    return year + '-' + monthStr + '-' + dayStr;
  },

  // 格式化日期时间为 ISO 格式
  formatDateTime: function(date, isStart = true) {
    let dateObj;
    
    // 如果传入的是字符串，直接使用；如果是Date对象，先格式化
    if (typeof date === 'string') {
      dateObj = new Date(date);
    } else {
      dateObj = date;
    }
    
    // 检查日期是否有效
    if (isNaN(dateObj.getTime())) {
      console.error('Invalid date:', date);
      // 返回今天的日期作为默认值
      dateObj = new Date();
    }
    
    const year = dateObj.getFullYear();
    const month = dateObj.getMonth() + 1;
    const day = dateObj.getDate();
    const monthStr = month < 10 ? '0' + month : '' + month;
    const dayStr = day < 10 ? '0' + day : '' + day;
    const timeStr = isStart ? '00:00:00' : '23:59:59';
    
    const dateTimeStr = year + '-' + monthStr + '-' + dayStr + 'T' + timeStr + '.000Z';
    return dateTimeStr;
  },

  // Tab切换
  onTabChange: function(e) {
    const index = e.detail.index;
    this.setData({
      activeTab: index
    });
    this.loadData();
  },

  // 开始日期选择
  onStartDateChange: function(e) {
    this.setData({
      startDate: e.detail.value,
      activeQuickDate: '' // 清除快捷日期选中状态
    });
    this.loadData();
  },

  // 结束日期选择
  onEndDateChange: function(e) {
    this.setData({
      endDate: e.detail.value,
      activeQuickDate: '' // 清除快捷日期选中状态
    });
    this.loadData();
  },

  // 快捷日期选择
  onQuickDateSelect: function(e) {
    const value = e.currentTarget.dataset.value;
    const today = new Date();
    let startDate, endDate;

    if (value === 'today') {
      startDate = endDate = this.formatDate(today);
    } else if (value === 'tomorrow') {
      const tomorrow = new Date(today);
      tomorrow.setDate(today.getDate() + 1);
      startDate = endDate = this.formatDate(tomorrow);
    } else if (value === 'week') {
      // 本周：周一到周日
      const dayOfWeek = today.getDay();
      const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek; // 如果是周日，则向前6天，否则到周一
      const monday = new Date(today);
      monday.setDate(today.getDate() + mondayOffset);
      const sunday = new Date(monday);
      sunday.setDate(monday.getDate() + 6);
      
      startDate = this.formatDate(monday);
      endDate = this.formatDate(sunday);
    } else if (value === 'month') {
      // 本月：本月第一天到最后一天
      const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
      const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
      
      startDate = this.formatDate(firstDay);
      endDate = this.formatDate(lastDay);
    }

    this.setData({
      startDate: startDate,
      endDate: endDate,
      activeQuickDate: value
    });
    this.loadData();
  },

  // 加载数据
  loadData: function() {
    console.log('loadData被调用, activeTab:', this.data.activeTab);
    console.log('当前日期范围:', this.data.startDate, '到', this.data.endDate);
    if (this.data.activeTab === 0) {
      console.log('加载自助午餐数据');
      this.loadSelfServiceData('lunch');
    } else if (this.data.activeTab === 1) {
      console.log('加载自助晚餐数据');
      this.loadSelfServiceData('dinner');
    } else {
      console.log('加载商务餐数据');
      this.loadBusinessData();
    }
  },

  // 分类预订时段（判断午餐还是晚餐）
  classifyReservationPeriod: function(reservation_period) {
    if (!reservation_period || !reservation_period.includes('_')) {
      return 'unknown';
    }
    
    try {
      const startTime = reservation_period.split('_')[0];
      if (startTime.length >= 8) {
        // 提取小时部分 (第6-8位)
        const hourStr = startTime.substring(6, 8);
        const hour = parseInt(hourStr);
        
        // 大于16:00为晚餐，小于等于16:00为午餐
        return hour > 16 ? 'dinner' : 'lunch';
      }
    } catch (e) {
      console.log('解析预订时段失败:', e);
    }
    
    return 'unknown';
  },

  // 格式化预订时段显示
  formatReservationPeriod: function(reservation_period) {
    if (!reservation_period || !reservation_period.includes('_')) {
      return reservation_period || '时段';
    }
    
    try {
      const periodParts = reservation_period.split('_');
      if (periodParts.length === 2) {
        const startTime = periodParts[0];
        const endTime = periodParts[1];
        
        if (startTime.length >= 10 && endTime.length >= 10) {
          // 提取时间部分 (第6-10位：HHmm)
          const startHour = startTime.substring(6, 8);
          const startMin = startTime.substring(8, 10);
          const endHour = endTime.substring(6, 8);
          const endMin = endTime.substring(8, 10);
          
          return startHour + ':' + startMin + '～' + endHour + ':' + endMin;
        }
      }
    } catch (e) {
      console.log('格式化预订时段失败:', e);
    }
    
    return reservation_period;
  },

  // 加载自助餐数据（午餐或晚餐）
  loadSelfServiceData: function(mealType) {
    const token = wx.getStorageSync('token');
    if (!token) return;

    // 检查日期是否已设置
    if (!this.data.startDate || !this.data.endDate) {
      console.log('日期未设置，跳过数据加载');
      return;
    }

    const dataKey = mealType === 'lunch' ? 'selfServiceLunchData' : 'selfServiceDinnerData';
    
    this.setData({
      [dataKey + '.loading']: true
    });

    const startDateObj = new Date(this.data.startDate);
    const endDateObj = new Date(this.data.endDate);
    
    // 验证日期有效性
    if (isNaN(startDateObj.getTime()) || isNaN(endDateObj.getTime())) {
      console.error('日期格式无效:', this.data.startDate, this.data.endDate);
      wx.showToast({
        title: '日期格式错误',
        icon: 'none'
      });
      this.setData({
        [dataKey + '.loading']: false
      });
      return;
    }

    const dining_start_time = this.formatDateTime(startDateObj, true);
    const dining_end_time = this.formatDateTime(endDateObj, false);

    const requestData = {
      dining_start_time: dining_start_time,
      dining_end_time: dining_end_time,
      page: 1,
      page_size: 10000,
      status: ["PAID_FULL", "VERIFIED", "AUTO_VERIFIED"]
    };

    console.log(mealType + 'API请求参数:', {
      url: app.globalData.baseUrl + '/enterprise/statistic/reservation_report',
      enterprise_id: this.data.enterprise_id,
      data: requestData
    });

    wx.request({
      url: app.globalData.baseUrl + '/enterprise/statistic/reservation_report?enterprise_id=' + this.data.enterprise_id + '&page=1',
      method: 'POST',
      header: {
        'token': token,
        'content-type': 'application/json'
      },
      data: requestData,
      success: (res) => {
        console.log(mealType + 'API响应:', res.data);
        if (res.data.status === 200) {
          const data = res.data.data;
          const list = data.list || [];
          console.log('原始数据列表:', list);
          
          // 根据时间段过滤数据
          const filteredList = list.filter(item => {
            const classification = this.classifyReservationPeriod(item.reservation_period);
            return classification === mealType;
          });
          
          console.log(mealType + '过滤后数据:', filteredList);
          
          const listData = filteredList.map((item, index) => ({
            username: item.username || '用户' + (index + 1),
            real_name: item.real_name || ``,
            nick_name: item.nick_name || ``,
            phone: item.phone || ``,
            product_name: item.product_name || ``,
            reservation_period_time: this.formatReservationPeriod(item.reservation_period),
            payment_enterprise: item.payment_enterprise || '个人支付',
            paid_full_count: item.quantity || 1,
            verified_count: (item.status === 'VERIFIED' || item.status === 'AUTO_VERIFIED') ? (item.quantity || 1) : 0,
            status: item.status || ''
          }));

          this.setData({
            [dataKey + '.list']: listData,
            [dataKey + '.total']: listData.length,
            [dataKey + '.totalPaidCount']: listData.reduce((sum, item) => sum + item.paid_full_count, 0),
            [dataKey + '.totalVerifiedCount']: listData.reduce((sum, item) => sum + item.verified_count, 0),
            [dataKey + '.loading']: false
          });          
        } else {
          console.log('API返回错误:', res.data);
          wx.showToast({
            title: res.data.message || '获取数据失败',
            icon: 'none'
          });
          this.setData({
            [dataKey + '.loading']: false
          });
        }
      },
      fail: (err) => {
        console.error('获取' + mealType + '数据失败', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        this.setData({
          [dataKey + '.loading']: false
        });
      }
    });
  },

  // 加载商务餐数据
  loadBusinessData: function() {
    const token = wx.getStorageSync('token');
    if (!token) return;

    // 检查日期是否已设置
    if (!this.data.startDate || !this.data.endDate) {
      console.log('日期未设置，跳过数据加载');
      return;
    }

    this.setData({
      'businessData.loading': true
    });

    const startDateObj = new Date(this.data.startDate);
    const endDateObj = new Date(this.data.endDate);
    
    // 验证日期有效性
    if (isNaN(startDateObj.getTime()) || isNaN(endDateObj.getTime())) {
      console.error('日期格式无效:', this.data.startDate, this.data.endDate);
      wx.showToast({
        title: '日期格式错误',
        icon: 'none'
      });
      this.setData({
        'businessData.loading': false
      });
      return;
    }

    const dining_start_time = this.formatDateTime(startDateObj, true);
    const dining_end_time = this.formatDateTime(endDateObj, false);

    const requestData = {
      dining_start_time: dining_start_time,
      dining_end_time: dining_end_time,
      page: 1,
      page_size: 10000,
      status: ["PAID_FULL", "VERIFIED", "AUTO_VERIFIED"]
    };

    console.log('商务餐API请求参数:', {
      url: app.globalData.baseUrl + '/enterprise/statistic/reservation_order_report',
      enterprise_id: this.data.enterprise_id,
      data: requestData
    });

    wx.request({
      url: app.globalData.baseUrl + '/enterprise/statistic/reservation_order_report?enterprise_id=' + this.data.enterprise_id + '&page=1',
      method: 'POST',
      header: {
        'token': token,
        'content-type': 'application/json'
      },
      data: requestData,
      success: (res) => {
        console.log('商务餐API响应:', res.data);
        if (res.data.status === 200) {
          const data = res.data.data;
          const list = data.list || [];
          
          // 计算统计数据
          let totalAmount = 0;
          let totalPersons = 0;
          
          list.forEach(item => {
            totalAmount += item.calculate_amount || 0;
            totalPersons += item.persons || 0;
          });
          
          this.setData({
            'businessData.list': list,
            'businessData.total': data.total || 0,
            'businessData.totalAmount': totalAmount,
            'businessData.totalPersons': totalPersons,
            'businessData.loading': false
          });
        } else {
          wx.showToast({
            title: res.data.message || '获取数据失败',
            icon: 'none'
          });
          this.setData({
            'businessData.loading': false
          });
        }
      },
      fail: (err) => {
        console.error('获取商务餐数据失败', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        this.setData({
          'businessData.loading': false
        });
      }
    });
  },

  // 格式化状态
  formatStatus: function(status) {
    const statusMap = {
      'PAID_FULL': '已付款',
      'VERIFIED': '已验证',
      'AUTO_VERIFIED': '自动验证',
      'PENDING': '待处理'
    };
    return statusMap[status] || status;
  },

  // 格式化时间
  formatTime: function(timeStr) {
    if (!timeStr) return '';
    const date = new Date(timeStr);
    return date.toLocaleString('zh-CN');
  }
}); 