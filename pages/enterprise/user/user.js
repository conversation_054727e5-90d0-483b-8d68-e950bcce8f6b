// 企业用户管理页面
const app = getApp()

Page({
  data: {
    enterprise_id: '',
    enterprise_name: '',
    searchKeyword: '',
    users: [],
    isLoading: false,
    isLoadingMore: false,
    hasMore: true,
    currentPage: 0,
    pageSize: 20,
    total: 0,
    showAddUserModal: false,
    showEditUserModal: false,
    newUser: {
      phone: '',
      name: '',
      is_admin: false
    },
    editUser: {
      id: '',
      phone: '',
      name: '',
      is_admin: false,
      status: 'active'
    },
    originalEditUser: {} // 保存原始数据用于比较
  },

  onLoad: function(options) {
    // 获取企业ID和名称
    if (options.enterprise_id && options.enterprise_name) {
      this.setData({
        enterprise_id: options.enterprise_id,
        enterprise_name: decodeURIComponent(options.enterprise_name)
      });
      
      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: `${this.data.enterprise_name} - 用户管理`
      });
      
      // 加载用户列表
      this.loadUsers();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载企业用户列表
  loadUsers: function(isLoadMore = false) {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 设置加载状态
    if (isLoadMore) {
      if (!this.data.hasMore || this.data.isLoadingMore) {
        return; // 没有更多数据或正在加载中
      }
      this.setData({ isLoadingMore: true });
    } else {
      this.setData({ 
        isLoading: true,
        currentPage: 0,
        hasMore: true 
      });
    }

    const currentPage = isLoadMore ? this.data.currentPage + 1 : 0;
    const skip = currentPage * this.data.pageSize;

    // 构建请求参数
    const requestData = {
      enterprise_id: this.data.enterprise_id,
      skip: skip,
      limit: this.data.pageSize
    };

    // 如果有搜索关键词，添加到请求参数
    if (this.data.searchKeyword.trim()) {
      requestData.keyword = this.data.searchKeyword.trim();
    }

    wx.request({
      url: app.globalData.baseUrl + '/enterprise/users/search',
      method: 'GET',
      header: {
        'token': token,
        'content-type': 'application/json'
      },
      data: requestData,
      success: (res) => {
        console.log('用户列表响应:', res.data);
        if (res.data.status === 200) {
          // 处理返回的用户数据，转换字段名和状态值
          const newUsers = (res.data.data.users || []).map(user => ({
            id: user.id,
            phone: user.phone,
            name: user.real_name || user.username, // 使用real_name，如果没有则使用username
            status: user.status === 1 ? 'active' : 'inactive', // 转换状态：1-正常，0-禁用
            is_admin: user.is_admin || false,
            is_audit_free: user.is_audit_free || false // API可能不返回此字段，默认为false
          }));
          
          console.log('处理后的用户数据:', newUsers);
          
          const total = res.data.data.total || 0;
          const currentUsers = isLoadMore ? this.data.users.concat(newUsers) : newUsers;
          const hasMore = currentUsers.length < total;
          
          this.setData({
            users: currentUsers,
            total: total,
            currentPage: currentPage,
            hasMore: hasMore,
            isLoading: false,
            isLoadingMore: false
          });
        } else {
          wx.showToast({
            title: res.data.message || '加载失败',
            icon: 'none'
          });
          this.setData({ 
            isLoading: false,
            isLoadingMore: false 
          });
        }
      },
      fail: (err) => {
        console.error('加载用户列表失败', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        this.setData({ 
          isLoading: false,
          isLoadingMore: false 
        });
      }
    });
  },

  // 搜索输入事件
  onSearchInput: function(e) {
    const keyword = e.detail.value.trim();
    this.setData({ searchKeyword: keyword });
    
    // 延迟搜索，避免频繁请求
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
    this.searchTimer = setTimeout(() => {
      this.performSearch();
    }, 500);
  },

  // 搜索按钮点击事件
  onSearchTap: function() {
    this.performSearch();
  },

  // 执行搜索
  performSearch: function() {
    // 清除延迟搜索的定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
      this.searchTimer = null;
    }
    
    // 重新加载数据（会自动包含搜索关键词）
    this.loadUsers(false);
  },

  // 清空搜索
  clearSearch: function() {
    this.setData({ searchKeyword: '' });
    this.loadUsers(false);
  },

  // 显示添加用户弹窗
  showAddUserModal: function() {
    this.setData({ 
      showAddUserModal: true,
      newUser: {
        phone: '',
        name: '',
        is_admin: false
      }
    });
  },

  // 隐藏添加用户弹窗
  hideAddUserModal: function() {
    this.setData({ showAddUserModal: false });
  },

  // 显示编辑用户弹窗
  showEditUserModal: function(e) {
    const user = e.currentTarget.dataset.user;
    const editUser = {
      id: user.id,
      phone: user.phone,
      name: user.name,
      is_admin: user.is_admin,
      status: user.status
    };
    
    this.setData({ 
      showEditUserModal: true,
      editUser: editUser,
      originalEditUser: JSON.parse(JSON.stringify(editUser)) // 深拷贝原始数据
    });
  },

  // 隐藏编辑用户弹窗
  hideEditUserModal: function() {
    this.setData({ showEditUserModal: false });
  },

  // 阻止事件冒泡
  stopPropagation: function() {
    // 空函数，仅用于阻止事件冒泡
  },

  // 新用户手机号输入
  onNewUserPhoneInput: function(e) {
    this.setData({
      'newUser.phone': e.detail.value
    });
  },

  // 新用户姓名输入
  onNewUserNameInput: function(e) {
    this.setData({
      'newUser.name': e.detail.value
    });
  },

  // 管理员状态切换
  onAdminToggle: function(e) {
    this.setData({
      'newUser.is_admin': e.detail.value
    });
  },

  // 编辑用户姓名输入
  onEditUserNameInput: function(e) {
    this.setData({
      'editUser.name': e.detail.value
    });
  },

  // 编辑管理员状态切换
  onEditAdminToggle: function(e) {
    this.setData({
      'editUser.is_admin': e.detail.value
    });
  },

  // 编辑用户状态切换
  onEditStatusToggle: function(e) {
    this.setData({
      'editUser.status': e.detail.value ? 'active' : 'inactive'
    });
  },

  // 添加用户
  addUser: function() {
    const { phone, name } = this.data.newUser;
    
    // 验证手机号
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phone || !phoneRegex.test(phone)) {
      wx.showToast({
        title: '请输入有效手机号',
        icon: 'none'
      });
      return;
    }

    // 验证姓名
    if (!name || name.trim().length === 0) {
      wx.showToast({
        title: '请输入用户姓名',
        icon: 'none'
      });
      return;
    }

    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '添加中...' });

    // 手动构建查询参数（小程序兼容）
    const params = {
      enterprise_id: this.data.enterprise_id,
      username: name.trim(),
      phone: phone,
      is_admin: this.data.newUser.is_admin
    };
    
    // 构建查询字符串
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    const requestUrl = app.globalData.baseUrl + '/enterprise/users/add?' + queryString;
    
    console.log('=== 添加用户请求详情 ===');
    console.log('BaseURL:', app.globalData.baseUrl);
    console.log('请求URL:', requestUrl);
    console.log('请求参数:', params);
    console.log('Token:', token);
    console.log('========================');

    wx.request({
      url: requestUrl,
      method: 'POST',
      header: {
        'token': token,
        'content-type': 'application/json'
      },
      success: (res) => {
        wx.hideLoading();
        console.log('添加用户响应 - statusCode:', res.statusCode);
        console.log('添加用户响应 - data:', res.data);
        console.log('添加用户响应 - 完整响应:', res);
        
        // 处理不同的响应状态
        if (res.statusCode === 200 && res.data.status === 200) {
          // 添加成功
          wx.showToast({
            title: res.data.message || '添加成功',
            icon: 'success'
          });
          this.hideAddUserModal();
          this.loadUsers(false); // 重新加载用户列表
        } else if (res.statusCode === 200 && res.data.status === 400) {
          // 用户已存在
          wx.showToast({
            title: res.data.message || '该用户已经是企业成员',
            icon: 'none',
            duration: 3000
          });
        } else if (res.statusCode === 401 || (res.data && res.data.status === 401)) {
          // 未登录
          wx.showToast({
            title: '登录已过期，请重新登录',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateTo({
              url: '/pages/phoneAuth/phoneAuth'
            });
          }, 1500);
        } else if (res.statusCode === 403 || (res.data && res.data.status === 403)) {
          // 权限不足
          wx.showToast({
            title: res.data.detail?.message || res.data.message || '权限不足，您不是该企业的管理员',
            icon: 'none',
            duration: 3000
          });
        } else {
          // 其他错误
          const errorMsg = res.data?.message || res.data?.detail?.message || '添加失败';
          wx.showToast({
            title: errorMsg,
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('添加用户请求失败 - 错误对象:', err);
        console.error('添加用户请求失败 - 错误类型:', typeof err);
        console.error('添加用户请求失败 - 错误信息:', JSON.stringify(err));
        
        // 根据错误类型给出不同提示
        let errorMsg = '网络错误';
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            errorMsg = '请求超时，请检查网络连接';
          } else if (err.errMsg.includes('fail')) {
            errorMsg = '网络连接失败，请检查服务器地址';
          } else {
            errorMsg = `网络错误: ${err.errMsg}`;
          }
        }
        
        wx.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        });
      }
    });
  },

  // 更新用户信息
  updateUser: function() {
    const { name } = this.data.editUser;
    
    // 验证姓名
    if (!name || name.trim().length === 0) {
      wx.showToast({
        title: '请输入用户姓名',
        icon: 'none'
      });
      return;
    }

    // 检查是否有修改
    const original = this.data.originalEditUser;
    const current = this.data.editUser;
    
    if (original.name === current.name && 
        original.is_admin === current.is_admin && 
        original.status === current.status) {
      wx.showToast({
        title: '没有任何修改',
        icon: 'none'
      });
      return;
    }

    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '更新中...' });

    // 构建查询参数
    const params = {
      enterprise_id: this.data.enterprise_id,
      user_id: this.data.editUser.id
    };

    // 只添加有变化的字段
    if (original.name !== current.name) {
      params.real_name = current.name.trim();
    }
    if (original.is_admin !== current.is_admin) {
      params.is_admin = current.is_admin;
    }
    if (original.status !== current.status) {
      params.status = current.status === 'active' ? 1 : 0; // API期望数字状态：1-启用，0-禁用
    }

    // 构建查询字符串
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    const requestUrl = app.globalData.baseUrl + '/enterprise/users/update?' + queryString;
    
    console.log('=== 更新用户请求详情 ===');
    console.log('请求URL:', requestUrl);
    console.log('请求参数:', params);
    console.log('原始数据:', original);
    console.log('当前数据:', current);
    if (params.status !== undefined) {
      console.log('状态转换:', `'${current.status}' -> ${params.status}`);
    }
    console.log('========================');

    wx.request({
      url: requestUrl,
      method: 'PUT',
      header: {
        'token': token,
        'content-type': 'application/json'
      },
      success: (res) => {
        wx.hideLoading();
        console.log('更新用户响应 - statusCode:', res.statusCode);
        console.log('更新用户响应 - data:', res.data);
        
        // 处理不同的响应状态
        if (res.statusCode === 200 && res.data.status === 200) {
          // 更新成功
          wx.showToast({
            title: res.data.message || '更新成功',
            icon: 'success'
          });
          this.hideEditUserModal();
          this.loadUsers(false); // 重新加载用户列表
        } else if (res.statusCode === 200 && res.data.status === 404) {
          // 用户不存在
          wx.showToast({
            title: res.data.message || '用户不存在',
            icon: 'none',
            duration: 3000
          });
        } else if (res.statusCode === 200 && res.data.status === 400) {
          // 参数错误
          wx.showToast({
            title: res.data.message || '参数错误',
            icon: 'none',
            duration: 3000
          });
        } else if (res.statusCode === 401 || (res.data && res.data.status === 401)) {
          // 未登录
          wx.showToast({
            title: '登录已过期，请重新登录',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateTo({
              url: '/pages/phoneAuth/phoneAuth'
            });
          }, 1500);
        } else if (res.statusCode === 403 || (res.data && res.data.status === 403)) {
          // 权限不足
          wx.showToast({
            title: res.data.detail?.message || res.data.message || '权限不足，您不是该企业的管理员',
            icon: 'none',
            duration: 3000
          });
        } else {
          // 其他错误
          const errorMsg = res.data?.message || res.data?.detail?.message || '更新失败';
          wx.showToast({
            title: errorMsg,
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('更新用户请求失败:', err);
        
        let errorMsg = '网络错误';
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            errorMsg = '请求超时，请检查网络连接';
          } else if (err.errMsg.includes('fail')) {
            errorMsg = '网络连接失败，请检查服务器地址';
          } else {
            errorMsg = `网络错误: ${err.errMsg}`;
          }
        }
        
        wx.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        });
      }
    });
  },

  // 用户状态切换
  toggleUserStatus: function(e) {
    const { userId, currentStatus } = e.currentTarget.dataset;
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    // 转换为API期望的数字状态：1-启用，0-禁用
    const apiStatus = newStatus === 'active' ? 1 : 0;
    
    wx.showModal({
      title: '确认操作',
      content: `确定${newStatus === 'active' ? '启用' : '禁用'}该用户吗？`,
      success: (res) => {
        if (res.confirm) {
          this.updateUserStatus(userId, apiStatus);
        }
      }
    });
  },

  // 更新用户状态
  updateUserStatus: function(userId, status) {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '更新中...' });

    // 构建查询参数
    const params = {
      enterprise_id: this.data.enterprise_id,
      user_ids: [userId], // API期望用户ID数组
      status: status // 1-启用，0-禁用
    };

    // 构建查询字符串
    const queryString = Object.keys(params)
      .map(key => {
        if (key === 'user_ids') {
          // 处理数组参数
          return params[key].map(id => `user_ids=${encodeURIComponent(id)}`).join('&');
        }
        return `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`;
      })
      .join('&');

    const requestUrl = app.globalData.baseUrl + '/enterprise/update/status?' + queryString;
    
    console.log('=== 更新用户状态请求详情 ===');
    console.log('请求URL:', requestUrl);
    console.log('请求参数:', params);
    console.log('用户ID:', userId, '状态:', status);
    console.log('===========================');

    wx.request({
      url: requestUrl,
      method: 'POST',
      header: {
        'token': token,
        'content-type': 'application/json'
      },
      success: (res) => {
        wx.hideLoading();
        console.log('更新用户状态响应 - statusCode:', res.statusCode);
        console.log('更新用户状态响应 - data:', res.data);
        
        // 处理不同的响应状态
        if (res.statusCode === 200 && res.data.status === 200) {
          // 更新成功
          wx.showToast({
            title: res.data.message || '更新成功',
            icon: 'success'
          });
          this.loadUsers(false); // 重新加载用户列表
        } else if (res.statusCode === 200 && res.data.status === 400) {
          // 参数错误
          wx.showToast({
            title: res.data.message || '参数错误',
            icon: 'none',
            duration: 3000
          });
        } else if (res.statusCode === 200 && res.data.status === 404) {
          // 用户不存在
          wx.showToast({
            title: res.data.message || '用户不存在',
            icon: 'none',
            duration: 3000
          });
        } else if (res.statusCode === 401 || (res.data && res.data.status === 401)) {
          // 未登录
          wx.showToast({
            title: '登录已过期，请重新登录',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateTo({
              url: '/pages/phoneAuth/phoneAuth'
            });
          }, 1500);
        } else if (res.statusCode === 403 || (res.data && res.data.status === 403)) {
          // 权限不足
          wx.showToast({
            title: res.data.detail?.message || res.data.message || '权限不足，您不是该企业的管理员',
            icon: 'none',
            duration: 3000
          });
        } else {
          // 其他错误
          const errorMsg = res.data?.message || res.data?.detail?.message || '更新失败';
          wx.showToast({
            title: errorMsg,
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('更新用户状态请求失败:', err);
        
        let errorMsg = '网络错误';
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            errorMsg = '请求超时，请检查网络连接';
          } else if (err.errMsg.includes('fail')) {
            errorMsg = '网络连接失败，请检查服务器地址';
          } else {
            errorMsg = `网络错误: ${err.errMsg}`;
          }
        }
        
        wx.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        });
      }
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadUsers(false);
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (this.data.hasMore && !this.data.isLoadingMore) {
      this.loadUsers(true);
    }
  }
}); 