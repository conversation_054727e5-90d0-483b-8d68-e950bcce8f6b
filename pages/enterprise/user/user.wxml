<!-- 企业用户管理页面 -->
<view class="container">
  <!-- 搜索区域 -->
  <view class="search-section">
    <view class="search-bar">
      <view class="search-input-wrapper">
        <input 
          class="search-input" 
          placeholder="搜索手机号或用户姓名" 
          value="{{searchKeyword}}"
          bindinput="onSearchInput"
          confirm-type="search"
          bindconfirm="onSearchTap"
        />
        <view wx:if="{{searchKeyword}}" class="clear-search" bindtap="clearSearch">×</view>
      </view>
    </view>
    <button class="add-user-btn" bindtap="showAddUserModal">+ 添加用户</button>
  </view>

  <!-- 用户列表 -->
  <view class="user-list">
    <view wx:if="{{isLoading}}" class="loading">
      <text>加载中...</text>
    </view>
    
    <view wx:elif="{{users.length === 0}}" class="empty">
      <text>暂无用户数据</text>
    </view>
    
    <view wx:else>
      <view class="user-item" wx:for="{{users}}" wx:key="id">
        <view class="user-info">
          <view class="user-main">
            <text class="user-phone">{{item.phone}}</text>
            <text class="user-name">{{item.name}}</text>
          </view>
          <view class="user-badges">
            <text class="badge admin-badge" wx:if="{{item.is_admin}}">管理员</text>
            <text class="badge audit-free-badge" wx:if="{{item.is_audit_free}}">免审核</text>
                         <text class="badge status-badge status-{{item.status}}">
               <text wx:if="{{item.status === 'active'}}">正常</text>
               <text wx:elif="{{item.status === 'inactive'}}">禁用</text>
               <text wx:else>未知</text>
             </text>
          </view>
        </view>
        <view class="user-actions">
          <button 
            class="action-btn edit-btn"
            data-user="{{item}}"
            bindtap="showEditUserModal"
          >
            编辑
          </button>
          <button 
            class="action-btn {{item.status === 'active' ? 'disable-btn' : 'enable-btn'}}"
            data-user-id="{{item.id}}"
            data-current-status="{{item.status}}"
            bindtap="toggleUserStatus"
          >
            {{item.status === 'active' ? '禁用' : '启用'}}
          </button>
        </view>
      </view>
      
      <!-- 加载更多提示 -->
      <view class="load-more" wx:if="{{users.length > 0}}">
        <view wx:if="{{isLoadingMore}}" class="loading-more">
          <text>加载更多中...</text>
        </view>
        <view wx:elif="{{!hasMore}}" class="no-more">
          <text>已显示全部 {{total}} 条记录</text>
        </view>
        <view wx:else class="pull-more">
          <text>上拉加载更多</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 添加用户弹窗 -->
  <view class="modal-overlay" wx:if="{{showAddUserModal}}" catchtap="hideAddUserModal">
    <view class="modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">添加企业用户</text>
        <text class="modal-close" catchtap="hideAddUserModal">×</text>
      </view>
      
      <view class="modal-content">
        <view class="form-item">
          <text class="form-label">手机号 *</text>
          <input 
            class="form-input" 
            placeholder="请输入手机号" 
            type="number"
            maxlength="11"
            value="{{newUser.phone}}"
            bindinput="onNewUserPhoneInput"
          />
        </view>
        
        <view class="form-item">
          <text class="form-label">用户姓名 *</text>
          <input 
            class="form-input" 
            placeholder="请输入用户姓名" 
            value="{{newUser.name}}"
            bindinput="onNewUserNameInput"
          />
        </view>
        
        <view class="form-item">
          <text class="form-label">权限设置</text>
          <view class="form-switches">
            <view class="switch-item">
              <text class="switch-label">设为管理员</text>
              <switch 
                class="switch-control"
                checked="{{newUser.is_admin}}"
                bindchange="onAdminToggle"
              />
            </view>
          </view>
        </view>
      </view>
      
      <view class="modal-footer">
        <button class="modal-btn cancel-btn" catchtap="hideAddUserModal">取消</button>
        <button class="modal-btn confirm-btn" catchtap="addUser">确定添加</button>
      </view>
    </view>
  </view>

  <!-- 编辑用户弹窗 -->
  <view class="modal-overlay" wx:if="{{showEditUserModal}}" catchtap="hideEditUserModal">
    <view class="modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">编辑用户信息</text>
        <text class="modal-close" catchtap="hideEditUserModal">×</text>
      </view>
      
      <view class="modal-content">
        <view class="form-item">
          <text class="form-label">手机号</text>
          <input 
            class="form-input disabled-input" 
            value="{{editUser.phone}}"
            disabled="true"
          />
          <text class="form-hint">手机号不可修改</text>
        </view>
        
        <view class="form-item">
          <text class="form-label">用户姓名 *</text>
          <input 
            class="form-input" 
            placeholder="请输入用户姓名" 
            value="{{editUser.name}}"
            bindinput="onEditUserNameInput"
          />
        </view>
        
        <view class="form-item">
          <text class="form-label">权限设置</text>
          <view class="form-switches">
            <view class="switch-item">
              <text class="switch-label">设为管理员</text>
              <switch 
                class="switch-control"
                checked="{{editUser.is_admin}}"
                bindchange="onEditAdminToggle"
              />
            </view>
          </view>
        </view>

        <view class="form-item">
          <text class="form-label">用户状态</text>
          <view class="form-switches">
            <view class="switch-item">
              <text class="switch-label">启用用户</text>
              <switch 
                class="switch-control"
                checked="{{editUser.status === 'active'}}"
                bindchange="onEditStatusToggle"
              />
            </view>
          </view>
        </view>
      </view>
      
      <view class="modal-footer">
        <button class="modal-btn cancel-btn" catchtap="hideEditUserModal">取消</button>
        <button class="modal-btn confirm-btn" catchtap="updateUser">保存修改</button>
      </view>
    </view>
  </view>
</view> 