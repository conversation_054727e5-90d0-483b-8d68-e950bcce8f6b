/* 企业用户管理页面样式 */
.container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 0;
}

/* 搜索区域 */
.search-section {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.search-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  gap: 20rpx;
}

.search-input-wrapper {
  position: relative;
  flex: 1;
}

.search-input {
  width: 100%;
  height: 80rpx;
  padding: 0 60rpx 0 30rpx;
  border: 2rpx solid #e1e8ed;
  border-radius: 15rpx;
  font-size: 28rpx;
  background-color: #f8f9fa;
  box-sizing: border-box;
}

.search-input:focus {
  border-color: #07c160;
  background-color: #fff;
}

.clear-search {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  background-color: #ccc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #fff;
  font-weight: bold;
}

.clear-search:active {
  background-color: #999;
}

.search-btn {
  height: 80rpx;
  padding: 0 40rpx;
  background: linear-gradient(135deg, #07c160, #05a351);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  line-height: 80rpx;
}

.search-btn:active {
  transform: scale(0.95);
}

.add-user-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #07c160, #05a351);
  color: #fff;
  border: none;
  border-radius: 15rpx;
  font-size: 30rpx;
  font-weight: bold;
  line-height: 80rpx;
}

.add-user-btn:active {
  transform: scale(0.98);
}

/* 用户列表 */
.user-list {
  padding: 0 30rpx;
}

.loading,
.empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  color: #999;
  font-size: 28rpx;
}

.user-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 20rpx 30rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 100rpx;
}

.user-info {
  flex: 1;
}

.user-main {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  gap: 20rpx;
}

.user-phone {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.user-name {
  font-size: 26rpx;
  font-weight: normal;
  color: #666;
}

.user-badges {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.badge {
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  color: #fff;
  white-space: nowrap;
}

.admin-badge {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

.audit-free-badge {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
}

.status-badge {
  font-weight: bold;
}

.status-active {
  background: linear-gradient(135deg, #52c41a, #389e0d);
}

.status-inactive {
  background: linear-gradient(135deg, #ff7875, #f5222d);
}

.status-pending {
  background: linear-gradient(135deg, #faad14, #d48806);
}

.status-unknown {
  background: linear-gradient(135deg, #d9d9d9, #bfbfbf);
}

.user-actions {
  margin-left: 20rpx;
  display: flex;
  gap: 10rpx;
  flex-direction: column;
}

.action-btn {
  padding: 5rpx 5rpx;
  border-radius: 15rpx;
  font-size: 24rpx;
  border: none;
  min-width: 100rpx;
}

.edit-btn {
  background: linear-gradient(135deg, #07c160, #05a351);
  color: #fff;
}

.enable-btn {
  background: linear-gradient(135deg, #52c41a, #389e0d);
  color: #fff;
}

.disable-btn {
  background: linear-gradient(135deg, #ff7875, #f5222d);
  color: #fff;
}

.action-btn:active {
  transform: scale(0.95);
}

/* 加载更多提示 */
.load-more {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
  margin-top: 20rpx;
}

.loading-more,
.no-more,
.pull-more {
  font-size: 26rpx;
  color: #999;
}

.loading-more {
  color: #1890ff;
}

.no-more {
  color: #666;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  width: 90%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}

.modal-close:active {
  background-color: #f5f5f5;
}

.modal-content {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 30rpx;
  border: 2rpx solid #e1e8ed;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #f8f9fa;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #07c160;
  background-color: #fff;
}

.disabled-input {
  background-color: #f5f5f5 !important;
  color: #999 !important;
}

.form-hint {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.form-switches {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.switch-label {
  font-size: 28rpx;
  color: #333;
}

.switch-control {
  transform: scale(0.8);
}

.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 30rpx 40rpx;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 40rpx;
  font-size: 30rpx;
  line-height: 80rpx;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.cancel-btn:active {
  background-color: #e8e8e8;
}

.confirm-btn {
  background: linear-gradient(135deg, #07c160, #05a351);
  color: #fff;
}

.confirm-btn:active {
  transform: scale(0.98);
}

/* 响应式设计 */
@media screen and (max-width: 750rpx) {
  .user-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .user-actions {
    margin-left: 0;
    margin-top: 20rpx;
    width: 100%;
    flex-direction: row;
  }

  .action-btn {
    flex: 1;
  }
}
