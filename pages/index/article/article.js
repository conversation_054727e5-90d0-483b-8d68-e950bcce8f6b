import { getArticleDetail } from '../../../service/article';
import { checkLogin } from '../../../service/user';

Page({
  data: {
    articleId: null,
    article: null,
    loading: true,
    error: null
  },

  onLoad: function (options) {
    const articleId = options.id || options.articleId;
    if (articleId) {
      this.setData({
        articleId: parseInt(articleId)
      });
      this.getArticleDetail(articleId);
    } else {
      this.setData({
        error: '文章ID不能为空',
        loading: false
      });
    }
  },

  /**
   * 获取文章详情
   */
  async getArticleDetail(articleId) {
    try {
      this.setData({ loading: true, error: null });
      
      const response = await getArticleDetail(articleId);

      if (response.status === 200 && response.data && response.data.article) {
        this.setData({
          article: response.data.article,
          loading: false
        });
        
        // 设置页面标题
        if (response.data.article.name) {
          wx.setNavigationBarTitle({
            title: response.data.article.name
          });
        }
      } else {
        this.setData({
          error: response.message || '获取文章详情失败',
          loading: false
        });
      }
    } catch (error) {
      console.error('获取文章详情失败:', error);
      this.setData({
        error: '网络请求失败，请稍后重试',
        loading: false
      });
    }
  },

  /**
   * 重试获取文章
   */
  onRetry() {
    if (this.data.articleId) {
      this.getArticleDetail(this.data.articleId);
    }
  },

  /**
   * 返回上一页
   */
  onBack() {
    wx.navigateBack();
  },

  /**
   * 图片点击放大预览
   */
  onImageTap() {
    const article = this.data.article;
    if (article && article.image) {
      wx.previewImage({
        current: article.image,
        urls: [article.image],
        fail: (err) => {
          console.error('图片预览失败:', err);
          wx.showToast({
            title: '图片预览失败',
            icon: 'none'
          });
        }
      });
    }
  },

  /**
   * 分享文章
   */
  onShareAppMessage() {
    const article = this.data.article;
    if (article) {
      return {
        title: article.name || '素食文章分享',
        desc: article.summary || '来看看这篇有趣的素食文章',
        path: `/pages/index/article/article?id=${this.data.articleId}`,
        imageUrl: article.thumbnail || article.image || ''
      };
    }
    return {
      title: '素食文章分享',
      path: `/pages/index/article/article?id=${this.data.articleId}`
    };
  },

  /**
   * 检查登录状态
   */
  async onShow() {
    try {
      const isLoggedIn = await checkLogin();
      if (!isLoggedIn) {
        const app = getApp();
        if (app && app.monitor_token) {
          await app.monitor_token();
        } else {
          wx.navigateTo({
            url: '/pages/index/index'
          });
        }
      }
    } catch (error) {
      console.error('检查登录状态失败', error);
    }
  }
}); 