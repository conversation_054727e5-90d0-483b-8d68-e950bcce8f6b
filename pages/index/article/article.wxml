<!-- 错误状态 -->
<view wx:if="{{error}}" class="error-container">
  <view class="error-text">{{error}}</view>
  <button class="retry-btn" bindtap="onRetry">重试</button>
</view>

<!-- 文章内容 -->
<view wx:elif="{{article}}" class="article-container">
  <!-- 文章头部 -->
  <view class="article-header">
    <!-- 文章标题 -->
    <view class="article-title">{{article.name}}</view>
    
    <!-- 文章摘要 -->
    <view wx:if="{{article.summary}}" class="article-summary">{{article.summary}}</view>
    
    <!-- 文章信息 -->
    <view class="article-meta">
      <text class="meta-time">{{article.created_at}}</text>
    </view>
  </view>

  <!-- 文章封面图 -->
  <view wx:if="{{article.image}}" class="article-image-container">
    <image class="article-image" src="{{article.image}}" mode="widthFix" bindtap="onImageTap"></image>
  </view>

  <!-- 文章内容 -->
  <view class="article-content">
    <view class="content-text">{{article.content}}</view>
  </view>

  <!-- 相关标签 -->
  <!--
  <view wx:if="{{article.tags && article.tags.length > 0}}" class="article-tags">
    <view class="tags-title">标签</view>
    <view class="tags-list">
      <text wx:for="{{article.tags}}" wx:key="id" class="tag-item">{{item.name}}</text>
    </view>
  </view>
  -->
</view>

<!-- 底部操作栏 -->
<view wx:if="{{article}}" class="article-footer">
  <button class="footer-btn back-btn" bindtap="onBack">返回</button>
  <!-- <button class="footer-btn share-btn" open-type="share">分享</button> -->
</view> 