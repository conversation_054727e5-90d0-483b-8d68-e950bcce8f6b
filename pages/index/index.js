import { checkLogin } from '../../service/user';
import { getPublishedArticles } from '../../service/article';

Page({
  data: {
    title: '乙禾素',
    articles: [],
    // 新增轮播广告数据
    swiperArticles: [],
    displayArticles: [],
    loading: true,
    error: null,
    // 轮播相关配置
    swiperCurrent: 0,
    autoplay: true,
    interval: 3000,
    duration: 500
  },
  onLoad: function (options) {
    wx.setNavigationBarTitle({
        title: '乙禾素'
    });
    this.loadArticles();
  },

  /**
   * 加载文章列表
   */
  async loadArticles() {
    try {
      this.setData({ loading: true, error: null });

      const response = await getPublishedArticles();

      if (response.status === 200 && response.data) {
        // 按order字段分组处理文章
        const allArticles = response.data;

        // 过滤并排序轮播文章（sort_order <= 10，如果没有sort_order字段则默认为999）
        const swiperArticles = allArticles
          .filter(article => (article.sort_order || 999) <= 10)
          .sort((a, b) => (a.sort_order || 999) - (b.sort_order || 999));
        console.log("swiperArticles:", swiperArticles);
        // 过滤展示文章（sort_order > 10，如果没有sort_order字段则默认为999）
        const displayArticles = allArticles
          .filter(article => (article.sort_order || 999) > 10);
        console.log("displayArticles:", displayArticles);

        this.setData({
          articles: allArticles,
          swiperArticles: swiperArticles,
          displayArticles: displayArticles,
          loading: false
        });
      } else {
        this.setData({
          error: response.message || '获取文章列表失败',
          loading: false
        });
      }
    } catch (error) {
      console.error('获取文章列表失败:', error);
      this.setData({
        error: '网络请求失败，请稍后重试',
        loading: false
      });
    }
  },

  /**
   * 轮播切换事件
   */
  onSwiperChange(e) {
    this.setData({
      swiperCurrent: e.detail.current
    });
  },

  /**
   * 点击轮播广告跳转到详情页
   */
  onSwiperClick(e) {
    const articleId = e.currentTarget.dataset.id;
    if (articleId) {
      wx.navigateTo({
        url: `/pages/index/article/article?id=${articleId}`
      });
    }
  },

  /**
   * 点击文章跳转到详情页
   */
  onArticleClick(e) {
    const articleId = e.currentTarget.dataset.id;
    if (articleId) {
      wx.navigateTo({
        url: `/pages/index/article/article?id=${articleId}`
      });
    }
  },

  /**
   * 重试加载文章
   */
  onRetry() {
    this.loadArticles();
  },
    /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  // 在各主要页面的 onShow 方法中添加
  async onShow() {
    try {
      const isLoggedIn = await checkLogin()
      if (!isLoggedIn) {
        // token 无效，重新登录
        const app = getApp()
        if (app && app.monitor_token) {
          await app.monitor_token()
          // 重新登录后重新加载文章
          this.loadArticles()
        } else {
          wx.navigateTo({
            url: '/pages/index/index'
          })
        }
      }
    } catch (error) {
      console.error('检查登录状态失败', error)
    }
  },
})