<!-- 轮播广告区域 -->
<view wx:if="{{swiperArticles.length > 0}}" class="swiper-container">
  <swiper
    indicator-dots="{{true}}"
    autoplay="{{autoplay}}"
    interval="{{interval}}"
    duration="{{duration}}"
    circular="{{true}}"
    indicator-color="rgba(255, 255, 255, 0.3)"
    indicator-active-color="#ffffff"
    bindchange="onSwiperChange"
    class="swiper-ads"
  >
    <swiper-item
      wx:for="{{swiperArticles}}"
      wx:key="id"
      class="swiper-item"
      data-id="{{item.id}}"
      bindtap="onSwiperClick"
    >
      <image
        wx:if="{{item.ad_image}}"
        class="swiper-image"
        src="{{item.ad_image}}"
        mode="aspectFill"
        lazy-load="{{true}}"
      ></image>
      <view wx:if="{{item.title}}" class="swiper-title">{{item.title}}</view>
    </swiper-item>
  </swiper>
</view>

<!-- 文章图片展示区域 -->
<view wx:if="{{displayArticles.length > 0}}" class="ad-container">
  <view
    wx:for="{{displayArticles}}"
    wx:key="id"
    class="article-item"
    data-id="{{item.id}}"
    bindtap="onArticleClick"
  >
    <image
      wx:if="{{item.ad_image}}"
      class="ad-image"
      src="{{item.ad_image}}"
      mode="widthFix"
      lazy-load="{{true}}"
    ></image>
  </view>
</view>
