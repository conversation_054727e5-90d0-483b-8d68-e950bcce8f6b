.scroll-view {
  width: 100%;
  white-space: normal;
}
.index-container {
  margin-top: 15px;
  background-color: #fff;
}

.index-header {
  text-align: center;
  font-weight: normal;
  font-size: 30rpx;
  line-height: 40rpx;
  color: #757575;
}

/*   index   swiper  */

swiper.index-swiper {
  position: relative;
  height: 420rpx;
  margin: 16rpx 0;
}

swiper .swiper-mask {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 100%;
}

swiper image {
  display: block;
  margin: 0 auto;
  height: 100%;
}

swiper .itemImg {
  z-index: 5;
  border-radius: 10px;
}

swiper .active {
  opacity: 1;
  z-index: 10;
  height: 420rpx;
  transition: all 0.2s ease-in 0s;
  top: 0%;
}

swiper .swiper-desc {
  border-radius: 10px;
  z-index: 6;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 95%;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(63, 76, 88, 0.3) 100%
  );
  color: #fff;
  line-height: 150rpx;
  margin: 0 auto;
  font-weight: 700;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 30rpx;
  text-shadow: 0px 0px 16px #646d75;
}

/*   index   swiper  end  */

.search-button {
  border-left: 0 !important;
  border-top: 1px solid #fff !important;
  border-right: 1px solid #fff !important;
  border-bottom: 1px solid #fff !important;
  background-color: #fff !important;
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  color: #296fd0 !important;
  width: 20%;
  margin: 0 !important;
  box-shadow: 1px 1px 6px #ecf0f0;
}

.search-button icon {
  position: absolute;
  margin: 25rpx 16rpx;
}

.search-input {
  background-color: #fff;
  padding: 16rpx 0 16rpx 32rpx;
  min-height: 1rem;
  font-size: 30rpx;
  border-bottom-left-radius: 4px;
  border-top-left-radius: 4px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: 0 !important;
  height: 40rpx;
  width: 80%;
  text-align: left;
  box-shadow: 1px 1px 6px #ecf0f0;
}

.share-button {
  border: 1px solid #eee !important;
  color: #296fd0 !important;
  left: 10rpx;
  height: 100rpx;
  width: 200rpx;
}

.search-pancel {
  display: flex;
  flex-direction: row;
  background-color: #eee;
  padding: 20rpx 20rpx;
}

.search-pancel image {
  width: 50rpx;
  height: 50rpx;
  margin-left: 20rpx;
  vertical-align: middle;
}

.top-Nav {
  display: flex;
  padding: 8rpx 20rpx 25rpx;
  background: #fff;
  justify-content: space-around;
}

.top-item {
  text-align: center;
}

.top-item button {
  text-align: center;
}

.top-item image {
  width: 60rpx;
  height: 60rpx;
}

.top-item text {
  line-height: 20rpx;
  font-size: 14px;
}

.share-button {
  opacity: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  top: 0;
  left: 0;
}

.cms-tit {
  margin: 15rpx 0 10rpx;
  position: relative;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  line-height: 30rpx;
  box-sizing: border-box;
  vertical-align: text-bottom;
}

.cms-tit-left {
  display: inline-block;
  width: 12rpx;
  height: 34rpx;
  line-height: 34rpx;
  margin-right: 12rpx;
  margin-bottom: -4rpx;
  background: #2f89fc;
  box-sizing: border-box;
}

.cms-tit text {
  font-size: 30rpx;
  color: #333;
  line-height: 30rpx;
  box-sizing: border-box;
}

.cms-tit-more {
  display: block;
  height: 30rpx;
  line-height: 30rpx;
  font-size: 26rpx;
  font-weight: 300;
  color: #999;
  position: absolute;
  top: 0;
  right: 10rpx;
}

.share-button {
  opacity: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  top: 0;
  left: 0;
}

.cms-container {
  padding: 50rpx 30rpx 50rpx 0;
  margin-left: 30rpx;
  box-sizing: border-box;
  position: relative;
}

.cms-tit {
  margin: 15rpx 0;
  position: relative;
  font-size: 32rpx;
  color: #333;
  line-height: 30rpx;
  box-sizing: border-box;
  vertical-align: text-bottom;
}

.cms-tit-left {
  display: inline-block;
  width: 12rpx;
  height: 34rpx;
  line-height: 34rpx;
  margin-right: 12rpx;
  margin-bottom: -4rpx;
  background: #2f89fc;
  box-sizing: border-box;
  border-radius: 20rpx;
}

.cms-tit-txt {
  display: inline-block;
  height: 32rpx;
  line-height: 32rpx;
  box-sizing: border-box;
}

.cms-tit text {
  font-size: 30rpx;
  color: #333;
  line-height: 30rpx;
  box-sizing: border-box;
}

.cms-tit-more {
  display: block;
  height: 30rpx;
  line-height: 30rpx;
  font-size: 26rpx;
  font-weight: 300;
  color: #999;
  position: absolute;
  top: 0;
  right: 30rpx;
}

.top-Nav2 {
  display: flex;
  flex-direction: row;
  padding: 10rpx;
  justify-content: center;
  margin-bottom: 10px;
}

.top-Nav2-item {
  margin: 0 20px 0 20px;
}

.top-Nav2-item image {
  width: 100rpx;
  height: 100rpx;
}

.top-Nav2-item text {
  align-items: center;
  line-height: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  display: block;
  text-align: center;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
  background-color: #f5f5f5;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 50vh;
  padding: 40rpx;
  background-color: #f5f5f5;
}

.error-text {
  color: #f56c6c;
  font-size: 28rpx;
  margin-bottom: 40rpx;
  text-align: center;
}

.retry-btn {
  background-color: #67c23a;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 无数据状态 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
  background-color: #f5f5f5;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

/* 文章容器 */
.ad-container {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  background-color: #f5f5f5;
}

/* 文章项 */
.article-item {
  background-color: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
}

.article-item:active {
  transform: scale(0.98);
}

.ad-image {
  width: 100%;
  height: 400rpx;
  border-radius: 12rpx 12rpx 0 0;
  display: block;
}

/* 文章标题 */
.article-title {
  padding: 20rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  text-align: center;
  background-color: white;
}

/* 轮播广告样式 */
.swiper-container {
  margin: 0;
  padding: 0;
  background-color: #fff;
}

.swiper-ads {
  height: 450rpx;
  width: 100%;
}

.swiper-item {
  position: relative;
  height: 100%;
  width: 100%;
}

.swiper-image {
  width: 100%;
  height: 100%;
  display: block;
}

.swiper-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0) 0%,
    rgba(0, 0, 0, 0.7) 100%
  );
  color: #fff;
  padding: 40rpx 30rpx 30rpx;
  font-size: 32rpx;
  font-weight: 600;
  line-height: 1.3;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}
