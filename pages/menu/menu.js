// pages/menu/menu.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    date: '', // 日期
    menuList: [], // 菜单列表
    loading: false, // 加载状态
    timeSlots: [], // 时间段列表
    selectedTimeSlot: null, // 选中的时间段
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    wx.setNavigationBarTitle({
      title: '菜单列表'
    });
    
    if (options.menu_list) {
      try {
        // 获取传递过来的日期参数
        const dateValue = options.item_date || '当日菜单';
        
        // 解析传递的menu_list参数
        const timeSlots = JSON.parse(decodeURIComponent(options.menu_list));
        
        this.setData({
          date: dateValue,
          timeSlots: timeSlots,
          loading: false
        });
      } catch (e) {
        console.error('处理menu_list参数失败', e);
        wx.showToast({
          title: '参数处理错误',
          icon: 'error'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },


  /**
   * 获取菜单数据
   */
  fetchMenuData: function(productId) {
    const app = getApp();
    wx.showLoading({
      title: '加载中...',
    });
    
    // 添加日志
    console.log("获取菜单数据，产品ID:", productId);
    
    wx.request({
      url: `${app.globalData.baseUrl}/menu/list`,
      method: 'GET',
      header: {
        'token': wx.getStorageSync('token')
      },
      data: {
        product_id: productId
      },
      success: (res) => {
        wx.hideLoading();
        console.log("菜单数据响应:", res.data);
        
        if (res.data.status === 200) {
          this.setData({
            menuList: res.data.data || [],
            loading: false
          });
        } else {
          this.setData({
            loading: false
          });
          wx.showToast({
            title: res.data.message || '获取菜单失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('获取菜单失败', err);
        this.setData({
          loading: false
        });
        wx.hideLoading();
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 返回预订页面
   */
  backToBooking: function() {
    wx.navigateBack();
  },

  /**
   * 点击时间段
   */
  handleTimeSlotClick: function(e) {
    const index = e.currentTarget.dataset.index;
    const timeSlot = this.data.timeSlots[index];
    
    // 如果该时间段已禁用，则不执行任何操作
    if (timeSlot.disabled) return;
    
    this.setData({
      selectedTimeSlot: timeSlot
    });
    
    // 只有当点击时间段并且有产品ID时才请求菜单数据
    if (timeSlot.product_id) {
      this.fetchMenuData(timeSlot.product_id);
    } else {
      this.setData({
        menuList: []
      });
    }
  },
})