<!--pages/menu/menu.wxml-->
<view class="menu-container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading"></view>
    <text>加载中...</text>
  </view>
  
  <block wx:else>
    <!-- 日期显示 -->
    <view class="menu-header">
      <text class="menu-date">{{date}} 菜单列表</text>
    </view>
    
    <!-- 时间段列表 -->
    <view class="timeslots-container" wx:if="{{timeSlots.length > 0}}">
      <view class="timeslots-title">可选时间段</view>
      <view class="timeslots-list">
        <view class="timeslot-item {{selectedTimeSlot && selectedTimeSlot.time === item.time ? 'selected' : ''}}" 
              wx:for="{{timeSlots}}" 
              wx:key="time"
              bindtap="handleTimeSlotClick"
              data-index="{{index}}">
          <text class="timeslot-time">{{item.time}}</text>
          <text class="timeslot-price" wx:if="{{item.price}}">￥{{item.price}}</text>
          <text class="timeslot-status {{item.disabled ? 'disabled' : ''}}">
            {{item.disabled ? '已约满' : '可预订'}}
          </text>
        </view>
      </view>
    </view>
    
    <!-- 菜单列表 -->
    <view class="menu-list" wx:if="{{menuList.length > 0}}">
      <view class="menu-item" wx:for="{{menuList}}" wx:key="index">
        <view class="food-info">
          <text class="food-name">{{item.name}}</text>
          <text class="food-type">{{item.type}}</text>
        </view>
        <view class="food-details">
          <text class="food-desc">{{item.content}}</text>
          <view class="food-nutrition" wx:if="{{item.nutrition}}">
            <text class="nutrition-title">营养成分:</text>
            <text class="nutrition-info">{{item.nutrition}}</text>
          </view>
        </view>
        <view class="food-image" wx:if="{{item.image}}">
          <image src="{{item.image}}" mode="aspectFill"></image>
        </view>
      </view>
    </view>
    
    <!-- 无菜单数据时的提示 -->
    <view class="empty-menu" wx:if="{{menuList.length === 0 && timeSlots.length === 0}}">
      <!-- <image class="empty-icon" src="/images/empty-menu.png" mode="aspectFit"></image> -->
      <text class="empty-text">暂无信息</text>
    </view>
    
    <!-- 返回按钮 -->
    <view class="menu-actions">
      <button class="back-btn" bindtap="backToBooking">返回预订</button>
    </view>
  </block>
</view>
