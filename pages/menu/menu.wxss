/* pages/menu/menu.wxss */
.menu-container {
  padding: 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
  box-sizing: border-box;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.loading {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.menu-header {
  margin: 20rpx 0 30rpx;
  text-align: center;
}

.menu-date {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 时间段列表样式 */
.timeslots-container {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.timeslots-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.timeslots-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.timeslot-item {
  width: calc(50% - 10rpx);
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 16rpx;
  box-sizing: border-box;
  border: 1rpx solid #eee;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.timeslot-item.selected {
  background-color: #e6f3ff;
  border: 1rpx solid #4080ff;
  box-shadow: 0 2rpx 8rpx rgba(64, 128, 255, 0.2);
  transform: translateY(-2rpx);
}

.timeslot-time {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.selected .timeslot-time {
  color: #4080ff;
}

.timeslot-price {
  font-size: 28rpx;
  color: #f56c6c;
  margin: 8rpx 0;
}

.timeslot-status {
  font-size: 24rpx;
  color: #67c23a;
}

.selected .timeslot-status {
  color: #4080ff;
  font-weight: bold;
}

.timeslot-status.disabled {
  color: #909399;
}

.menu-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.menu-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.food-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.food-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.food-type {
  font-size: 26rpx;
  color: #909399;
  padding: 4rpx 12rpx;
  background-color: #f0f9eb;
  border-radius: 20rpx;
}

.food-details {
  margin: 10rpx 0;
}

.food-desc {
  font-size: 28rpx;
  color: #606266;
  line-height: 1.5;
}

.food-nutrition {
  margin-top: 10rpx;
}

.nutrition-title {
  font-size: 26rpx;
  color: #909399;
}

.nutrition-info {
  font-size: 26rpx;
  color: #606266;
}

.food-image {
  width: 100%;
  height: 300rpx;
  margin-top: 16rpx;
}

.food-image image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.empty-menu {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}

.empty-icon {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 30rpx;
  color: #909399;
}

/* 返回按钮样式 */
.menu-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background-color: #fff;
  box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.back-btn {
  width: 90%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #4080ff;
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 10rpx rgba(7, 193, 96, 0.3);
  margin: 0 auto;
}