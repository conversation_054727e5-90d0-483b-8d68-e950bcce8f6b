import { checkLogin } from '../../service/user';

const app = getApp()

Page({
  data: {
    isLoading: false,
    redirect: '', // 存储回跳页面路径,
    order_id: '', // 存储订单ID
    type: '', // 存储餐类型参数
    agreedToTerms: false, // 是否同意协议
    showAgreementPopup: false, // 是否显示协议弹出框
    showToast: false // 是否显示半透明提示框
  },

  onLoad(options) {
    // 保存回跳参数
    if (options.redirect) {
      this.setData({
        redirect: options.redirect
      });
    }

    // 保存订单ID参数
    if (options.order_id) {
      this.setData({
        order_id: options.order_id
      });
    }

    // 保存餐类型参数
    if (options.type) {
      this.setData({
        type: options.type
      });
    }
  },

  // 切换协议同意状态
  toggleAgreement() {
    this.setData({
      agreedToTerms: !this.data.agreedToTerms
    })
  },

  // 显示协议内容
  showAgreement() {
    this.setData({
      showAgreementPopup: true
    })
  },

  // 隐藏协议弹出框
  hideAgreement() {
    this.setData({
      showAgreementPopup: false
    })
  },

  // 同意协议
  agreeToTerms() {
    this.setData({
      agreedToTerms: true,
      showAgreementPopup: false
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击弹出框内容时关闭弹出框
  },

  // 显示半透明提示框
  showToast() {
    this.setData({
      showToast: true
    })
    // 3秒后自动隐藏
    setTimeout(() => {
      this.hideToast()
    }, 3000)
  },

  // 隐藏半透明提示框
  hideToast() {
    this.setData({
      showToast: false
    })
  },

  // 暂不登录
  skipLogin() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  // 处理授权登录按钮点击（协议未勾选时）
  handleAuthLogin() {
    this.showToast()
  },

  // 获取手机号回调
  async getPhoneNumber(e) {

    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      this.setData({ isLoading: true })
      
      try {
        // 获取手机号的code
        const phone_code = e.detail.code
        
        // 调用app.js中的登录方法，传入手机号code
        await app.handlerLogin(phone_code, 1)
        
        // 根据redirect参数决定跳转目标
        if (this.data.redirect === 'scan_redirect') {
          // 从扫码重定向页面过来的，登录完成后跳回扫码重定向页面继续流程
          wx.redirectTo({
            url: '/pages/scan_redirect/scan_redirect?type=' + this.data.type
          });
        } else if (this.data.redirect === 'interactive_ordering') {
          // 从扫码重定向页面过来的，登录完成后跳回扫码重定向页面继续流程
          wx.redirectTo({
            url: '/pages/interactive_ordering/interactive_ordering?type=' + this.data.type
          });
        } else if (this.data.redirect === 'booking_business' ||
            this.data.redirect === 'booking_business_pay' ||
            this.data.redirect === 'booking_business_edit') {
          if (this.data.order_id) {
            // 如果有订单ID，则跳回订单页面
            wx.redirectTo({
              url: '/pages/' + this.data.redirect + '/' + this.data.redirect + '?order_id=' + this.data.order_id
            });
          } else {
            // 没有订单ID，直接返回上一页
          wx.navigateBack();
          }
        } else {
          // 默认跳转到首页
        wx.switchTab({
          url: '/pages/index/index'
          });
        }
      } catch (error) {
        console.error('登录失败', error)
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        })
      } finally {
        this.setData({ isLoading: false })
      }
    } else {
      // 用户拒绝授权时，返回上一页
      wx.navigateBack({
        delta: 1
      })
    }
  },

  // 在各主要页面的 onShow 方法中添加
  // async onShow() {
  //   try {
  //     const isLoggedIn = await checkLogin()
  //     if (!isLoggedIn) {
  //       // token 无效，重新登录
  //       const app = getApp()
  //       if (app && app.monitor_token) {
  //         await app.monitor_token()
  //       } else {
  //         wx.navigateTo({
  //           url: '/pages/index/index'
  //         })
  //       }
  //     }
  //   } catch (error) {
  //     console.error('检查登录状态失败', error)
  //   }
  // },
}) 