<view class="phone-auth-container">
  <!-- Logo图标 -->
  <view class="logo-container">
    <image class="logo-image" src="../../images/logo.png" mode="aspectFit"></image>
  </view>

  <!-- 主标题 -->
  <view class="auth-title">与自然共生，予身心纯净</view>

  <!-- 描述文字 -->
  <view class="auth-desc">轻启授权&登录，共品东方蔬食之雅</view>

  
  <view class="auth-tip auth-tip-red" wx:if="{{redirect === 'booking_business'}}">登录后即可完成商务餐预订</view>
  <!-- 授权登录按钮 -->
  <button class="auth-button" wx:if="{{!agreedToTerms}}" bindtap="handleAuthLogin">
    授权&登录
  </button>

  <!-- 真正的手机号授权按钮 -->
  <button class="auth-button" wx:if="{{agreedToTerms}}" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">
    授权&登录
  </button>

  <!-- 协议勾选 -->
  <view class="agreement-check">
    <checkbox class="checkbox" value="agree" checked="{{agreedToTerms}}" bindtap="toggleAgreement"></checkbox>
    <view class="agreement-text">
      <text>我已阅读并同意</text>
      <text class="agreement-link-text" bindtap="showAgreement">《乙禾素用户服务协议》与《隐私政策》</text>
    </view>
  </view>

  <!-- 暂不登录 -->
  <view class="skip-login" bindtap="skipLogin">
    暂不登录
  </view>

  <!-- 半透明提示框 -->
  <view class="toast-overlay" wx:if="{{showToast}}" bindtap="hideToast">
    <view class="toast-content" catchtap="stopPropagation">
      <view class="toast-text">请阅读并同意《乙禾素用户服务协议》与《隐私政策》</view>
    </view>
  </view>

  <!-- 自定义协议弹出框 -->
  <view class="agreement-popup" wx:if="{{showAgreementPopup}}" bindtap="hideAgreement">
    <view class="agreement-content" catchtap="stopPropagation">
      <view class="agreement-header">
        <text class="agreement-title">《乙禾素用户服务协议》与《隐私政策》</text>
      </view>

      <scroll-view class="agreement-body" scroll-y="true">
        <view class="agreement-section">
          <view class="content-text">欢迎您使用我们的餐厅服务系统。在使用本系统之前，请仔细阅读并同意本协议的全部条款和条件。通过使用本小程序的服务（下称为本服务），即表示您已阅读、理解并接受本协议的全部内容。</view>
        </view>

        <view class="agreement-section">
          <view class="section-title">一、乙禾素用户服务协议</view>

          <view class="subsection">
            <view class="subsection-title">（一）服务范围</view>
            <view class="content-text">我们提供包括订餐、购物、报名、统计等在内的餐厅服务。您可以通过本小程序便捷地发起服务请求。</view>
          </view>

          <view class="subsection">
            <view class="subsection-title">（二）个人信息保护</view>
            <view class="content-text">为了提升服务质量，我们可能会收集您的个人信息，如手机号码。我们承诺严格保护您的个人隐私，绝不将其用于其他目的。</view>
          </view>

          <view class="subsection">
            <view class="subsection-title">（三）用户权利</view>
            <view class="content-text">
              <view class="list-item">1、您有权拒绝提供个人信息，但这可能会影响我们为您提供的服务。</view>
              <view class="list-item">2、您也有权随时申请查阅、更正或删除您的个人信息。</view>
              <view class="list-item">3、除了本小程序外，您还可以通过电话、微信等途径向我们发出服务请求。</view>
            </view>
          </view>

          <view class="subsection">
            <view class="subsection-title">（四）免责声明</view>
            <view class="content-text">在法律允许的范围内，我们不承担因您使用本服务而导致的任何损失或损害的责任。</view>
          </view>

          <view class="subsection">
            <view class="subsection-title">（五）协议变更</view>
            <view class="content-text">我们保留随时修改本协议的权利。修改后的协议条款一旦公布即代替原协议条款。您可以在本小程序上查看最新的协议条款。请注意，您使用本服务即表示您已阅读并同意接受修改后的协议约束。如果您不同意本协议的任何内容，请立即停止使用本服务。</view>
          </view>
        </view>

        <view class="agreement-section">
          <view class="section-title">二、隐私政策</view>

          <view class="subsection">
            <view class="subsection-title">（一）信息收集</view>
            <view class="content-text">为了向您提供更好的服务，我们可能会收集和存储以下信息：您的手机号码;用于获取登录身份和相应操作权限;</view>
          </view>

          <view class="subsection">
            <view class="subsection-title">（二）信息使用</view>
            <view class="content-text">我们仅会将您的个人信息用于提供服务和改善用户体验，绝不会将其用于其他目的。我们会采取合理的安全措施保护您的个人信息安全。</view>
          </view>

          <view class="subsection">
            <view class="subsection-title">（三）信息披露</view>
            <view class="content-text">我们承诺不会将您的个人信息提供给任何第三方，除非获得您的明确同意或法律法规要求。</view>
          </view>

          <view class="subsection">
            <view class="subsection-title">（四）信息安全</view>
            <view class="content-text">我们会采取合理的安全措施保护您的个人信息，防止未经授权的访问、使用或披露。</view>
          </view>

          <view class="subsection">
            <view class="subsection-title">（五）隐私政策的更新</view>
            <view class="content-text">我们有权随时更新本隐私政策，并在本小程序上公布最新版本。更新后的隐私政策自公布之日起生效。如有任何关于隐私政策的问题，请联系我们。</view>
          </view>
        </view>
      </scroll-view>

      <view class="agreement-footer">
        <button class="cancel-btn" bindtap="hideAgreement">拒绝</button>
        <button class="confirm-btn" bindtap="agreeToTerms">同意</button>
      </view>
    </view>
  </view>
</view>