.phone-auth-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-height: 100vh;
  padding: 0 40rpx;
  background-color: #fff;
  box-sizing: border-box;
  padding-top: 20vh;
}

/* Logo样式 */
.logo-container {
  margin-bottom: 60rpx;
}

.logo-image {
  height: 180rpx;
}

/* 主标题样式 */
.auth-title {
  font-size: 34rpx;
  font-weight: 600;
  margin-bottom: 30rpx;
  color: #333;
  letter-spacing: 2rpx;
  text-align: center;
}

/* 描述文字样式 */
.auth-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 120rpx;
  text-align: center;
  line-height: 1.6;
  letter-spacing: 1rpx;
}

/* 协议链接样式 */
.agreement-link {
  background-color: #666;
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  margin-bottom: 60rpx;
  text-align: center;
  letter-spacing: 1rpx;
}

/* 授权按钮样式 */
.auth-button {
  width: 85%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #07c160;
  color: white;
  border-radius: 44rpx;
  margin-bottom: 40rpx;
  font-size: 30rpx;
  letter-spacing: 2rpx;
  box-shadow: 0 6rpx 16rpx rgba(7, 193, 96, 0.2);
  border: none;
}

.auth-button::after {
  border: none;
}

/* 协议勾选样式 */
.agreement-check {
  display: flex;
  align-items: center;
  margin-bottom: 60rpx;
  padding: 0 20rpx;
}

.checkbox {
  margin-right: 16rpx;
  transform: scale(0.8);
}

.agreement-text {
  font-size: 22rpx;
  color: #999;
  line-height: 1.4;
  flex: 1;
  display: flex;
  flex-wrap: wrap;
}

.agreement-link-text {
  color: #07c160;
  text-decoration: underline;
}

/* 暂不登录样式 */
.skip-login {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  padding: 20rpx;
  text-decoration: underline;
}

/* 半透明提示框样式 */
.toast-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(128, 128, 128, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 8888;
}

.toast-content {
  width: 80%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.toast-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  letter-spacing: 1rpx;
}

/* 自定义协议弹出框样式 */
.agreement-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.agreement-content {
  width: 92%;
  max-width: 680rpx;
  height: 85vh;
  max-height: 85vh;
  background-color: white;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
}

.agreement-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #eee;
  flex-shrink: 0;
}

.agreement-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.agreement-body {
  flex: 1;
  padding: 30rpx 25rpx;
  overflow-y: auto;
  height: calc(100% - 200rpx);
  box-sizing: border-box;
}

.agreement-section {
  margin-bottom: 40rpx;
  width: 100%;
  box-sizing: border-box;
}

.subsection {
  margin-bottom: 30rpx;
  width: 100%;
  box-sizing: border-box;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 25rpx;
  margin-top: 20rpx;
  line-height: 1.6;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  white-space: normal;
  width: 100%;
  box-sizing: border-box;
}

.subsection {
  margin-bottom: 30rpx;
}

.subsection-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 18rpx;
  margin-top: 15rpx;
  line-height: 1.5;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  white-space: normal;
  width: 100%;
  box-sizing: border-box;
}

.content-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.8;
  display: block;
  margin-bottom: 15rpx;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  white-space: normal;
  width: 100%;
  box-sizing: border-box;
}

.list-item {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.8;
  margin-bottom: 12rpx;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  white-space: normal;
  width: 100%;
  box-sizing: border-box;
}

.agreement-footer {
  display: flex;
  padding: 25rpx 30rpx 30rpx;
  border-top: 1rpx solid #eee;
  gap: 20rpx;
  flex-shrink: 0;
  background-color: white;
  position: sticky;
  bottom: 0;
}

.cancel-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #f5f5f5;
  color: #666;
  border-radius: 40rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
}

.cancel-btn::after {
  border: none;
}

.confirm-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #07c160;
  color: white;
  border-radius: 40rpx;
  font-size: 28rpx;
  text-align: center;
  border: none;
}

.confirm-btn::after {
  border: none;
}

.auth-tip {
  font-size: 14px;
  margin-bottom: 20px;
  text-align: center;
  padding: 10px 20px;
  background-color: #f8f8f8;
  border-radius: 8px;
}

.auth-tip-red {
  color: #e74c3c;
  font-weight: 500;
}