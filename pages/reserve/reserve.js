import { checkLogin, getUserInfo } from '../../service/user';
import drawQrcode from '../../utils/weapp.qrcode.esm.js'

// pages/reserve/reserve.js
Page({

  /**showDetail
   * 页面的初始数据
   */
  data: {
    currentTab: 0,
    showPopup: false,
    popupTitle: '',
    showQrCode: false,
    showCancelConfirm: false,
    showOrderDetail: false, // 添加订单详情弹窗状态
    orders: [],
    count: 0,  // 添加计数器初始值
    selectedOrder: null, // 添加选中的订单
    currentOrderId: null, // 当前操作的订单ID
    currentTime: new Date().getTime(), // 添加当前时间
    isPopupShow: false, // 添加弹窗显示状态
    showFilterPopup: false,
    filterStartDate: '',
    filterEndDate: '',
    filterOrderStatusIndex: 0,
    filterPerson: '',
    filterMealDate: '',
    filterPeople: '',
    orderStatusOptions: ['全部', '已预订', '已就餐', '已取消', '超时自动核销'],
    allOrders: [], // 用于保存原始订单数据
    filterApplied: false, // 是否应用了筛选
    originalOrders: [], // 保存原始订单数据
    baseUrlHost: '', // 添加baseUrlHost
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    wx.setNavigationBarTitle({
      title: '订单记录'
    });

    // 设置baseUrlHost
    this.setData({
      baseUrlHost: getApp().globalData.baseUrlHost
    });
    
    const token = wx.getStorageSync('token');
    console.log('当前 token：', token);
    
    // 修复变量声明问题
    const today = new Date();
    const date = today.toISOString().split('T')[0];
    
    // 添加加载状态提示
    wx.showLoading({
      title: '加载中...'
    });
    
    this.fetchOrders(date);
  },

  fetchOrders(date, isFilter = false) {
    return new Promise((resolve, reject) => {
      const token = wx.getStorageSync('token');

      if (!token) {
        wx.hideLoading();
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        reject(new Error('未登录'));
        return;
      }

      // 添加日期参数验证
      let cleanDate = date;
      if (date === undefined || date === null || date === 'undefined' || date === 'null') {
        cleanDate = ''; // 转换为空字符串，表示查询全部
      }

      console.log('=== 获取订单记录 === 原始date:', date, '清理后的date:', cleanDate);

      wx.request({
        url: `${getApp().globalData.baseUrl}/reserve/list`,
        method: 'GET',
        header: {
          token: token
        },
        data: {
          date: cleanDate
        },
        success: (res) => {
          console.log('服务器返回数据：', res.data);
          wx.hideLoading();
          if (res.statusCode === 200 && res.data) {
            // 处理订单数据
            let orders = res.data.orders || [];

            // 如果不是筛选模式，只显示 paid_full 状态的订单
            if (!isFilter) {
              orders = orders.filter(order => order.reservation_status === 'paid_full');
            }

            orders.forEach(order => {
              // 如果 end_time 是字符串格式，转换为时间戳
              if (order.end_time && typeof order.end_time === 'string') {
                // 将日期字符串转换为 iOS 兼容格式
                const dateStr = order.end_time.replace(/-/g, '/');
                order.hd_end_time = order.end_time;
                order.end_time = new Date(dateStr).getTime();
              }
              // 同样处理其他日期字段
              if (order.reservation_time && typeof order.reservation_time === 'string') {
                // const dateStr = order.reservation_time.replace(/-/g, '/');
                const dateStr = order.reservation_time.replace('T', ' ').replace('/', '-').replace('/', '-');
                order.reservation_time = dateStr;
              }

              // 处理deadline字段，转换为时间戳
              if (order.deadline && typeof order.deadline === 'string') {
                const deadlineStr = order.deadline.replace(/-/g, '/');
                order.hd_deadline = order.deadline; // 保存原始字符串格式用于显示
                order.deadline = new Date(deadlineStr).getTime();
              }

              // 处理earliest_date字段，转换为时间戳
              if (order.earliest_date && typeof order.earliest_date === 'string') {
                const earliestDateStr = order.earliest_date.replace(/-/g, '/');
                order.hd_earliest_date = order.earliest_date; // 保存原始字符串格式用于显示
                order.earliest_date = new Date(earliestDateStr).getTime();

                // 判断是午餐还是晚餐：根据earliest_date的时间
                // 如果earliest_date的时间早于15:00就是午餐，晚于15:00就是晚餐
                const earliestDate = new Date(earliestDateStr);
                const hour = earliestDate.getHours();
                order.is_lunch = hour < 15; // 15:00前为午餐，15:00及以后为晚餐
              }
            });

            this.setData({
              orders: orders,
              originalOrders: orders,
              count: orders.length,
              filterApplied: false
            });

            if (!orders || orders.length === 0) {
              wx.showToast({
                title: '暂无订单记录',
                icon: 'none'
              });
            }

            resolve(orders);
          } else {
            reject(new Error(res.data.error || '获取订单失败'));
          }
        },
        fail: (err) => {
          wx.hideLoading();
          wx.showToast({
            title: '网络请求失败',
            icon: 'none'
          });
          console.error('请求失败：', err);
          reject(err);
        }
      });
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  // 在各主要页面的 onShow 方法中添加
  async onShow() {
    // 更新当前时间
    this.setData({
      currentTime: new Date().getTime()
    });
    
    // 获取当前选中的标签对应的日期
    const today = new Date();
    let date;

    if (this.data.currentTab === 0) {
      date = today.toISOString().split('T')[0]; // 今天
    } else if (this.data.currentTab === 1) {
      const tomorrow = new Date();
      tomorrow.setDate(today.getDate() + 1);
      date = tomorrow.toISOString().split('T')[0]; // 明天
    } else if (this.data.currentTab === 2) {
      date = ''; // 全部
    }

    // 每次显示页面时刷新订单数据
    this.fetchOrders(date);

    // 原有的登录检查代码
    try {
      const isLoggedIn = await checkLogin()
      if (!isLoggedIn) {
        // token 无效，重新登录
        const app = getApp()
        if (app && app.monitor_token) {
          await app.monitor_token()
        } else {
          wx.navigateTo({
            url: '/pages/index/index'
          })
        }
      }
    } catch (error) {
      console.error('检查登录状态失败', error)
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    this.clearStatusCheckTimer();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 根据当前选项卡获取对应日期
    const today = new Date();
    let date;
    
    if (this.data.currentTab === 0) {
      date = today.toISOString().split('T')[0]; // 今天的日期，只显示已预订
    } else if (this.data.currentTab === 1) {
      const tomorrow = new Date();
      tomorrow.setDate(today.getDate() + 1);
      date = tomorrow.toISOString().split('T')[0]; // 明天的日期，只显示已预订
    } else if (this.data.currentTab === 2) {
      date = ''; // 全部，不传递日期，只显示已预订
    } else {
      date = ''; // 全部，不传递日期
    }
    
    // 重新获取订单数据
    this.fetchOrders(date);
    
    // 完成下拉刷新
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },


  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      currentTab: parseInt(tab)
    });

    const today = new Date();
    let date;

    if (this.data.currentTab === 0) {
      date = today.toISOString().split('T')[0];
      this.hideFilterPopup();
      // 添加加载提示
      wx.showLoading({
        title: '加载中...'
      });
    } else if (this.data.currentTab === 1) {
      const tomorrow = new Date();
      tomorrow.setDate(today.getDate() + 1);
      date = tomorrow.toISOString().split('T')[0];
      this.hideFilterPopup();
      // 添加加载提示
      wx.showLoading({
        title: '加载中...'
      });
    } else if (this.data.currentTab === 2) {
      date = '';
      this.hideFilterPopup();
      // 添加加载提示
      wx.showLoading({
        title: '加载中...'
      });
    } else if (this.data.currentTab === 3) {
      date = '';
      wx.showLoading({
        title: '加载中...'
      });

      this.fetchOrders(date, true)
        .then(() => {
          wx.hideLoading();
          this.showFilterPopup();
        })
        .catch(err => {
          wx.hideLoading();
          wx.showToast({
            title: '获取数据失败',
            icon: 'none'
          });
          console.error('获取数据失败：', err);
        });
      return;
    }

    this.fetchOrders(date, false);
  },

  filterOrders() {
    const today = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(today.getDate() + 1);

    console.log("当前选项卡:", this.data.currentTab);
    console.log("所有订单:", this.data.orders);

    const filteredOrders = this.data.orders.filter(order => {
      const orderDate = new Date(order.date);
      if (this.data.currentTab === 0) {
        return orderDate.toDateString() === today.toDateString();
      } else if (this.data.currentTab === 1) {
        return orderDate.toDateString() === tomorrow.toDateString();
      } else if (this.data.currentTab === 2) {
        return true;
      } else {
        return true;
      }
    });

    this.setData({
      orders: filteredOrders
    });
  },
  showDetail(e) {
    const order = e.currentTarget.dataset.order;
    if (order.reservation_time) {
      const dateStr = order.reservation_time.replace('T', ' ').replace('/', '-').replace('/', '-');
      order.reservation_time = dateStr;
    }
    this.setData({
      showPopup: true,
      popupTitle: order.title,
      selectedOrder: order,
      isPopupShow: true
    });
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0
    });
  },

  hidePopup() {
    this.setData({
      showPopup: false,
      isPopupShow: false
    });
  },

  showQrCode(e) {
    const order = e.currentTarget.dataset.order;
    // 直接使用后端返回的权限判断
    if (!order.can_show_qr) {
      wx.showToast({
        title: '当前状态无法显示二维码',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showQrCode: true,
      popupTitle: order.title,
      selectedOrder: order,
      isPopupShow: true
    });
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0
    });

    const options = {
      width: 200,
      height: 200,
      canvasId: 'myQrcode',
      x: 0,  // 设置x轴坐标为0
      y: 0,  // 设置y轴坐标为0
      text: order.biz_info && order.biz_info.name ? getApp().globalData.baseUrlHost+'/xcx/?order_id='+order.order_id : order.verification_code,
      callback: (res) => {
        console.log('二维码生成成功');
      }
    }

    drawQrcode(options);

    // 启动定时器，每3秒查询一次订单状态
    this.startStatusCheckTimer();
  },

  hideQrCode() {
    this.clearStatusCheckTimer();
    this.setData({
      showQrCode: false,
      isPopupShow: false
    });
  },

  // 添加此方法来阻止触摸穿透
  preventTouchMove() {
    return false; // 阻止页面滑动
  },

  showNoCodeTip() {
    wx.showToast({
      title: '无需核销！',
      icon: 'none',
      duration: 2000
    });
  },

  showCancelConfirm(e) {
    const order = e.currentTarget.dataset.order;
    // 直接使用后端返回的权限判断
    if (!order.can_cancel) {
      wx.showToast({
        title: '当前状态无法取消',
        icon: 'none'
      });
      return;
    }
    this.setData({
      showCancelConfirm: true,
      currentOrderId: order.id || null,  // 使用null代替undefined
      selectedOrder: order,  // 保存整个订单对象
      isPopupShow: true
    });
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0
    });
  },

  hideCancelConfirm() {
    this.setData({
      showCancelConfirm: false,
      currentOrderId: null,
      isPopupShow: false
    });
  },

  confirmCancel(e) {
    const order = e.currentTarget.dataset.order || this.data.selectedOrder;
    console.log("取消订单：", order);
    const token = wx.getStorageSync('token');

    wx.request({
      // url: `${getApp().globalData.baseUrl}/reserve/cancel`,
      url: `${getApp().globalData.baseUrl}/order/item/cancel`,
      method: 'POST',
      header: {
        token: token
      },
      data: {
        order: order,
        order_item_id: order.order_item_id,
        order_id: order.order_id,
        order_no: order.order_no
      },
      success: (res) => {
        console.log("取消订单返回：", res);

        if (res.data.status === 200) {
          wx.showToast({
            title: '取消成功',
            icon: 'success',
            duration: 2000
          });

          // 更新用户信息
          const app = getApp();
          getUserInfo().then(userInfo => {
            if (app.globalData.userInfo) {
              app.globalData.userInfo.balance = userInfo.balance || 0;
            }
          }).catch(err => {
            console.error('获取用户信息失败', err);
          });

          // 获取当前选项卡对应的日期，并刷新订单列表
          const today = new Date();
          let date;

          if (this.data.currentTab === 0) {
            date = today.toISOString().split('T')[0];
          } else if (this.data.currentTab === 1) {
            const tomorrow = new Date();
            tomorrow.setDate(today.getDate() + 1);
            date = tomorrow.toISOString().split('T')[0];
          } else {
            date = '';
          }

          this.fetchOrders(date);
        } else {
          // 处理服务器返回的错误

          wx.showToast({
            title: res.data.message || '取消失败',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (err) => {
        console.error("取消订单失败：", err);
        wx.showToast({
          title: '取消失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
    this.hideCancelConfirm();
  },

  // 筛选弹窗相关事件
  showFilterPopup() {
    this.setData({ showFilterPopup: true });
  },
  hideFilterPopup() {
    this.setData({ showFilterPopup: false });
  },
  onStartDateChange(e) {
    const value = e.detail.value;
    if (this.data.filterEndDate && value > this.data.filterEndDate) {
      wx.showToast({
        title: '开始日期不能大于结束日期',
        icon: 'none'
      });
      return;
    }
    this.setData({ filterStartDate: value });
  },
  onEndDateChange(e) {
    const value = e.detail.value;
    if (this.data.filterStartDate && value < this.data.filterStartDate) {
      wx.showToast({
        title: '结束日期不能小于开始日期',
        icon: 'none'
      });
      return;
    }
    this.setData({ filterEndDate: value });
  },
  onOrderStatusChange(e) {
    this.setData({ filterOrderStatusIndex: parseInt(e.detail.value) });
  },

  // 重置筛选
  resetFilter() {
    this.setData({
      filterStartDate: '',
      filterEndDate: '',
      filterOrderStatusIndex: 0,
      filterApplied: false,
      orders: this.data.originalOrders,
      count: this.data.originalOrders.length
    });
  },

  // 修改筛选确认方法
  onFilterConfirm() {
    const {
      filterStartDate,
      filterEndDate,
      filterOrderStatusIndex,
      orderStatusOptions,
      originalOrders
    } = this.data;

    // 如果没有选择任何筛选条件，显示提示
    if (!filterStartDate && !filterEndDate && filterOrderStatusIndex === 0) {
      wx.showToast({
        title: '请至少选择一个筛选条件',
        icon: 'none'
      });
      return;
    }

    // 对原始订单数据进行筛选
    let filteredOrders = [...originalOrders];

    // 按日期范围筛选
    if (filterStartDate || filterEndDate) {
      console.log('筛选条件：', {
        开始日期: filterStartDate,
        结束日期: filterEndDate
      });

      filteredOrders = filteredOrders.filter(order => {
        // 将日期字符串转换为iOS兼容格式，并只保留日期部分
        const formatDate = (dateStr) => {
          if (!dateStr) return null;
          // 只取日期部分（yyyy-MM-dd）
          const dateOnly = dateStr.split(' ')[0];
          // 将 yyyy-MM-dd 格式转换为 yyyy/MM/dd
          const formattedDate = dateOnly.replace(/-/g, '/');
          return new Date(formattedDate);
        };

        const orderDate = formatDate(order.date);
        const startDate = filterStartDate ? formatDate(filterStartDate) : null;
        const endDate = filterEndDate ? formatDate(filterEndDate) : null;

        let isInRange = true;
        if (startDate && endDate) {
          isInRange = orderDate >= startDate && orderDate <= endDate;
        } else if (startDate) {
          isInRange = orderDate >= startDate;
        } else if (endDate) {
          isInRange = orderDate <= endDate;
        }

        return isInRange;
      });
    }

    // 按就餐状态筛选
    if (filterOrderStatusIndex > 0) {
      const selectedStatus = orderStatusOptions[filterOrderStatusIndex];
      filteredOrders = filteredOrders.filter(order => {
        switch(selectedStatus) {
          case '已预订':
            return order.reservation_status === 'paid_full';
          case '已就餐':
            return order.reservation_status === 'verified';
          case '已取消':
            return order.reservation_status === 'cancelled';
          case '超时自动核销':
            return order.reservation_status === 'auto_verified';
          default:
            return true;
        }
      });
    }

    // 更新筛选后的订单数据
    this.setData({
      orders: filteredOrders,
      count: filteredOrders.length,
      filterApplied: true,
      showFilterPopup: false  // 关闭筛选弹窗
    });

    // 如果没有筛选结果，显示提示
    if (filteredOrders.length === 0) {
      wx.showToast({
        title: '没有符合条件的记录',
        icon: 'none'
      });
    }
  },

  // 添加定时查询订单状态的方法
  startStatusCheckTimer() {
    // 清除可能存在的旧定时器
    this.clearStatusCheckTimer();

    // 设置新的定时器，每3秒执行一次
    this.statusCheckTimer = setInterval(() => {
      this.checkOrderVerificationStatus();
    }, 3000);

    // 添加1分钟自动关闭定时器
    this.autoCloseTimer = setTimeout(() => {
      // 如果1分钟后还没核销，自动关闭弹窗
      if (this.data.showQrCode) {
        wx.showToast({
          title: '等待核销超时',
          icon: 'none',
          duration: 2000
        });
        this.hideQrCode();
      }
    }, 1 * 60 * 1000); // 1分钟 = 1 * 60 * 1000毫秒
  },

  // 清除定时器的方法
  clearStatusCheckTimer() {
    if (this.statusCheckTimer) {
      clearInterval(this.statusCheckTimer);
      this.statusCheckTimer = null;
    }

    // 同时清除自动关闭定时器
    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
      this.autoCloseTimer = null;
    }
  },

  // 检查订单核销状态
  checkOrderVerificationStatus() {
    const order = this.data.selectedOrder;
    if (!order || !order.order_item_id) {
      this.clearStatusCheckTimer();
      return;
    }

    const token = wx.getStorageSync('token');
    if (!token) {
      this.clearStatusCheckTimer();
      return;
    }

    wx.request({
      url: `${getApp().globalData.baseUrl}/reserve/list`,
      method: 'GET',
      header: {
        token: token
      },
      data: {
        order_item_id: order.order_item_id
      },
      success: (res) => {
        if (res.statusCode === 200 && res.data && res.data.orders) {
          const updatedOrders = res.data.orders;
          // 查找匹配的订单
          const updatedOrder = updatedOrders.find(item => item.order_item_id === order.order_item_id);

          if (updatedOrder && updatedOrder.reservation_status === 'verified' &&
              this.data.selectedOrder.reservation_status !== 'verified') {
            // 状态已变为已核销，更新数据并提示用户
            this.setData({
              'selectedOrder.reservation_status': 'verified'
            });

            // 显示核销成功提示
            wx.showToast({
              title: '核销成功！',
              icon: 'success',
              duration: 2000
            });

            // 停止定时器
            this.clearStatusCheckTimer();

            // 刷新订单列表
            const today = new Date();
            let date;
            if (this.data.currentTab === 0) {
              date = today.toISOString().split('T')[0];
            } else if (this.data.currentTab === 1) {
              const tomorrow = new Date();
              tomorrow.setDate(today.getDate() + 1);
              date = tomorrow.toISOString().split('T')[0];
            } else {
              date = '';
            }
            this.fetchOrders(date);
          }
        }
      },
      fail: (err) => {
        console.error('查询订单状态失败：', err);
      }
    });
  },

  /**
   * 跳转到订单详情
   */
  goToOrderDetail() {
    wx.switchTab({
      url: '/pages/reserve/reserve?tab=all'
    });
  },

  // 修改 goToEdit 方法
  goToEdit(e) {
    const order = e.currentTarget.dataset.order;
    // 直接使用后端返回的权限判断
    if (!order.can_edit) {
      wx.showToast({
        title: '当前状态无法编辑',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/booking_business_edit/booking_business_edit?order_id=${order.order_id}`
    });
  },

  // 显示订单详情
  showOrderDetail(e) {
    const order = e.currentTarget.dataset.order;
    console.log('显示订单详情:', order);

    // 如果是商务餐订单，需要获取详细的订单信息
    if (order.biz_info && order.biz_info.name) {
      this.loadOrderDetail(order.order_id);
    } else {
      // 普通订单直接显示基本信息
      this.setData({
        selectedOrder: order,
        showOrderDetail: true
      });
    }
  },

  // 加载订单详情
  loadOrderDetail(orderId) {
    wx.showLoading({
      title: '加载中...'
    });

    const token = wx.getStorageSync('token');

    wx.request({
      url: `${getApp().globalData.baseUrl}/biz-order/detail`,
      method: 'GET',
      header: {
        token: token
      },
      data: {
        order_id: orderId,
        source_mark: "order_query"
      },
      success: (res) => {
        wx.hideLoading();
        console.log('订单详情接口返回数据:', res);

        if (res.statusCode === 200 && res.data && res.data.data) {
          const orderData = res.data.data; // 注意这里要取 res.data.data

          // 过滤掉ID为100、200的商品和status为cancelled的商品
          const filteredItems = (orderData.items || []).filter(item =>
            String(item.dish_id) !== '100' && String(item.dish_id) !== '200' && item.status !== 'cancelled'
          );

          // 计算订单商品总金额
          const totalItemsAmount = filteredItems.reduce((total, item) => {
            return total + (item.price * item.quantity);
          }, 0);

          // 判断是否已结账（商品总金额等于订单价格）
          const isFullyPaid = Math.abs(totalItemsAmount - (orderData.total_amount || 0)) < 0.01;

          // 判断是否应该显示结账按钮（商品总金额不等于订单价格）
          const shouldShowCheckout = Math.abs(totalItemsAmount - (orderData.total_amount || 0)) >= 0.01;

          // 构建订单详情对象，使用实际返回的字段名
          // 根据支付状态映射预订状态
          let reservationStatus = 'pending'; // 默认为待支付
          if (orderData.status === 'paid' || orderData.payment_status === 'paid') {
            reservationStatus = 'paid_full'; // 已支付 -> 已预订
          }
          // 这里可以根据其他业务状态进一步判断，例如：
          // if (orderData.verified_status === 'verified') {
          //   reservationStatus = 'verified'; // 已就餐
          // }

          const orderDetail = {
            order_id: orderData.order_id,
            order_no: orderData.order_no,
            date: orderData.booking_date,
            booking_time: orderData.booking_time, // 添加预约时间字段
            person: orderData.contact_name,
            people: orderData.people_count,
            price: orderData.total_amount || 0,
            reservation_status: reservationStatus, // 使用正确映射的预订状态
            payment_time: orderData.created_at, // 使用创建时间作为支付时间
            biz_info: orderData.store_info,
            order_items: filteredItems,
            reservation_time: orderData.created_at,
            contact_phone: orderData.contact_phone,
            remark: orderData.remark,
            is_modified: orderData.is_modified,  // 添加is_modified字段
            totalItemsAmount: totalItemsAmount,  // 添加商品总金额
            isFullyPaid: isFullyPaid,  // 添加是否已结账标识
            shouldShowCheckout: shouldShowCheckout  // 添加是否应该显示结账按钮标识
          };

          console.log('构建的订单详情:', orderDetail);

          this.setData({
            selectedOrder: orderDetail,
            showOrderDetail: true
          });
        } else {
          wx.showToast({
            title: res.data?.message || '获取订单详情失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('获取订单详情失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 隐藏订单详情
  hideOrderDetail() {
    this.setData({
      showOrderDetail: false,
      selectedOrder: null
    });
  },

  // 跳转到结账页面
  goToCheckout(e) {
    const orderId = e.currentTarget.dataset.orderId;
    console.log('跳转到结账页面，订单ID:', orderId);

    // 关闭当前弹窗
    this.hideOrderDetail();

    // 跳转到结账页面
    wx.navigateTo({
      url: `/pages/booking_business_pay/booking_business_pay?order_id=${orderId}`,
      success: () => {
        console.log('跳转到结账页面成功');
      },
      fail: (err) => {
        console.error('跳转到结账页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 拨打客服电话
  makePhoneCall() {
    wx.makePhoneCall({
      phoneNumber: '***********',
      success: () => {
        console.log('拨打电话成功');
      },
      fail: (err) => {
        console.error('拨打电话失败', err);
        wx.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  }
})

// 添加一个日期格式转换的工具函数
function formatDateString(dateStr) {
  if (!dateStr) return '';
  // 将 yyyy-MM-dd HH:mm 转换为 yyyy/MM/dd HH:mm
  return dateStr.replace(/-/g, '/');
}