/* 价格样式 - 红色字体 */
.price-value {
  color: #ff4444 !important;
  font-weight: 500;
}

/* pages/reserve/reserve.wxss */
.container {
  padding: 0 30rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.nav {
  display: flex;
  padding: 20rpx 0;
  position: relative;
  background: #fff;
}

.nav-item {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  padding: 10rpx 0;
}

.nav-item.active {
  color: #1989fa;
  position: relative;
}

.nav-item.active::after {
  content: "";
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #1989fa;
}

.filter {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  font-size: 26rpx;
}

.date {
  padding: 20rpx 0;
  font-size: 26rpx;
  color: #666;
}

.reserve-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  margin-bottom: 15rpx;
  font-size: 28rpx;
}

.label {
  color: #666;
  width: 160rpx;
}

.value {
  color: #333;
  flex: 1;
}

.success {
  color: #07c160;
}

.error {
  color: #ff4444;
}

.actions {
  display: flex;
  margin-top: 30rpx;
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}

.action-btn {
  flex: 1;
  text-align: center;
  font-size: 26rpx;
  color: #666;
}

.popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  overflow: hidden;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.popup-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.popup-header {
  position: relative;
  padding: 30rpx;
  text-align: center;
  font-size: 32rpx;
  border-bottom: 1rpx solid #eee;
}

.close-btn {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.popup-body {
  padding: 30rpx;
  max-height: 70vh;
  overflow-y: auto;
}

.popup-body .info-row {
  margin-bottom: 20rpx;
}

.popup-body .label {
  width: 180rpx;
}

.qr-body {
  padding: 40rpx;
  text-align: center;
  position: relative;
  overflow: hidden;
  height: auto;
}

.qr-code {
  width: 400rpx;
  height: 400rpx;
  margin: 0 auto;
}

.qr-info {
  margin: 30rpx 0;
  text-align: center;
}

.expire-time {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.expire-date {
  font-size: 32rpx;
  color: #333;
}

.qr-detail {
  text-align: left;
  border-top: 1rpx solid #eee;
  margin-top: 30rpx;
  padding-top: 30rpx;
}

.qr-detail .info-row {
  margin-bottom: 15rpx;
}

.qr-detail .label {
  width: auto;
  margin-right: 20rpx;
}

.qr-popup .popup-content {
  width: 90%;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
  border-radius: 12rpx;
  z-index: 1000;
}

.cancel-popup .popup-content {
  width: 600rpx;
}

.cancel-body {
  padding: 40rpx 30rpx;
  text-align: center;
}

.cancel-tips {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 40rpx;
}

.confirm-btn {
  background: #1989fa;
  color: #fff;
  font-size: 32rpx;
  padding: 20rpx 0;
  border-radius: 8rpx;
}

.popup-header text {
  color: rgb(0, 0, 0);
  font-weight: bold;
}

.reserve-list {
  padding-bottom: 120rpx; /* 底部添加120rpx的内边距 */
}

.disabled-btn {
  color: #ccc;
  pointer-events: none;
}

/* 当弹窗显示时，禁用背景滚动 */
.popup-show {
  position: fixed;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 筛选弹窗样式优化 */
.filter-popup .popup-content {
  width: 95%;
  max-width: 700rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.filter-section {
  margin-bottom: 40rpx;
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.section-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: "";
  width: 6rpx;
  height: 28rpx;
  background: #1989fa;
  margin-right: 12rpx;
  border-radius: 3rpx;
}

.date-range {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10rpx;
  margin-top: 16rpx;
}

.date-picker {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 10rpx;
  border: 2rpx solid #e8e8e8;
  transition: all 0.3s ease;
  padding: 0;
  overflow: hidden;
  box-sizing: border-box;
}

.date-picker::before,
.date-picker::after {
  display: none;
}

.date-picker text {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  line-height: 1;
  margin: 0;
}

.date-picker.has-value {
  color: #333;
  background: #f0f9ff;
  border-color: #1989fa;
}

.date-picker.has-value::before {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB0PSIxNjgxMjM0NTY3ODkwIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjQxNzEiIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIj48cGF0aCBkPSJNODk2IDEyOEg3NjhWOTZjMC0xNy43LTE0LjMtMzItMzItMzJzLTMyIDE0LjMtMzIgMzJ2MzJIMzIwVjk2YzAtMTcuNy0xNC4zLTMyLTMyLTMycy0zMiAxNC4zLTMyIDMydjMySDEyOGMtMzUuMyAwLTY0IDI4LjctNjQgNjR2NzY4YzAgMzUuMyAyOC43IDY0IDY0IDY0aDc2OGMzNS4zIDAgNjQtMjguNyA2NC02NFYxOTJjMC0zNS4zLTI4LjctNjQtNjQtNjR6bTAgODMySDEyOFY0NDhoNzY4djUxMnpNODk2IDM4NEgxMjhWMjU2aDc2OHYxMjh6IiBmaWxsPSIjMTk4OWZhIiBwLWlkPSI0MTcyIj48L3BhdGg+PC9zdmc+");
}

.date-picker.has-value::after {
  border-top-color: #1989fa;
}

.date-separator {
  color: #999;
  font-size: 24rpx;
  padding: 0 4rpx;
  flex-shrink: 0;
  width: 4%;
}

/* 日期选择器激活状态 */
.date-picker:active {
  background: #f0f9ff;
  border-color: #1989fa;
}

/* 日期选择器禁用状态 */
.date-picker.disabled {
  background: #f5f5f5;
  border-color: #e8e8e8;
  color: #ccc;
  cursor: not-allowed;
}

.date-picker.disabled::before {
  opacity: 0.5;
}

.date-picker.disabled::after {
  border-top-color: #ccc;
}

/* 日期选择器错误状态 */
.date-picker.error {
  border-color: #ff4444;
  background: #fff;
}

.date-picker.error::after {
  border-top-color: #ff4444;
}

/* 日期选择器提示文本 */
.date-picker-placeholder {
  font-size: 24rpx;
  text-align: center;
  width: 100%;
  line-height: 1;
  padding-top: 0;
  padding-bottom: 0;
}

/* 日期选择器值文本 */
.date-picker-value {
  font-size: 24rpx;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  width: 100%;
  line-height: 1;
}

/* 日期选择器容器 */
.date-picker-container {
  position: relative;
  width: 48%;
  min-width: 0;
  display: flex;
  align-items: center;
}

/* 日期选择器标签 */
.date-picker-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
  display: block;
}

/* 日期选择器组 */
.date-picker-group {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* 日期选择器提示信息 */
.date-picker-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
  display: block;
}

/* 日期选择器错误提示 */
.date-picker-error {
  font-size: 24rpx;
  color: #ff4444;
  margin-top: 8rpx;
  display: block;
}

.status-picker {
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #999;
  border: 2rpx solid #e8e8e8;
  transition: all 0.3s ease;
  position: relative;
  padding: 0 40rpx;
  width: 100%;
}

.status-picker::after {
  content: "";
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 12rpx solid transparent;
  border-right: 12rpx solid transparent;
  border-top: 12rpx solid #999;
}

.status-picker.has-value {
  color: #333;
  background: #fff;
  border-color: #1989fa;
}

.status-picker.has-value::after {
  border-top-color: #1989fa;
}

.popup-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #eee;
  gap: 20rpx;
}

.popup-footer .btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 200rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.cancel-btn {
  background: #f5f7fa;
  color: #666;
  border: 2rpx solid #e8e8e8;
}

.cancel-btn:active {
  background: #e8e8e8;
}

.reset-btn {
  background: #f5f7fa;
  color: #666;
  border: 2rpx solid #e8e8e8;
}

.reset-btn:active {
  background: #e8e8e8;
}

.confirm-btn {
  background: #1989fa;
  color: #fff;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(25, 137, 250, 0.2);
}

.confirm-btn:active {
  background: #1677dd;
  transform: translateY(2rpx);
}

/* 优化弹窗标题样式 */
.filter-popup .popup-header {
  padding: 32rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-popup .popup-header text {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.filter-popup .close-btn {
  width: 48rpx;
  height: 48rpx;
  line-height: 48rpx;
  text-align: center;
  border-radius: 50%;
  background: #f5f7fa;
  color: #666;
  font-size: 36rpx;
  transition: all 0.3s ease;
}

.filter-popup .close-btn:active {
  background: #e8e8e8;
}

/* 优化弹窗内容区域 */
.filter-popup .popup-body {
  padding: 30rpx;
  max-height: 75vh;
}

/* 添加滚动条样式 */
.filter-popup .popup-body::-webkit-scrollbar {
  width: 6rpx;
  background: transparent;
}

.filter-popup .popup-body::-webkit-scrollbar-thumb {
  background: #ddd;
  border-radius: 3rpx;
}

/* 确保picker不影响垂直居中 */
picker {
  width: 100%;
  height: 80rpx;
  display: flex;
  align-items: center;
}

/* 调整结束日期文本靠右显示 */
.date-picker-container:last-child .date-picker text {
  justify-content: flex-end; /* 文本靠右对齐 */
  padding-right: 20rpx; /* 添加右侧内边距，避免紧贴边缘 */
}

/* 调整开始日期文本靠左显示 */
.date-picker-container:first-child .date-picker text {
  justify-content: flex-start; /* 文本靠左对齐 */
  padding-left: 20rpx; /* 添加左侧内边距，避免紧贴边缘 */
}

/* 优化日期选择器整体样式 */
.date-picker {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center; /* 垂直居中对齐 */
  background: #f8f9fa;
  border-radius: 10rpx;
  border: 2rpx solid #e8e8e8;
  transition: all 0.3s ease;
  padding: 0;
  overflow: hidden;
  box-sizing: border-box;
}

/* 优化文本容器 */
.date-picker text {
  display: flex;
  align-items: center; /* 垂直居中对齐 */
  width: 100%;
  height: 100%;
  line-height: 1;
  margin: 0;
}

.date-picker-placeholder,
.date-picker-value {
  font-size: 24rpx;
  width: 100%;
  line-height: 1;
}

/* 添加文本对齐类 */
.text-left {
  text-align: left !important;
  justify-content: flex-start !important;
  padding-left: 20rpx !important;
}

.text-right {
  text-align: right !important;
  justify-content: flex-end !important;
  padding-right: 20rpx !important;
}

/* 优化日期选择器容器 */
.start-date .date-picker,
.end-date .date-picker {
  justify-content: flex-start; /* 默认内容左对齐 */
}

/* 特别处理结束日期容器 */
.end-date .date-picker {
  justify-content: flex-end; /* 内容右对齐 */
}

/* 基础日期选择器样式 */
.date-picker {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 10rpx;
  border: 2rpx solid #e8e8e8;
  transition: all 0.3s ease;
  overflow: hidden;
  box-sizing: border-box;
}

/* 文本样式 */
.date-picker text {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  line-height: 1;
  font-size: 24rpx;
}

/* 确保文本对齐样式优先级最高 */
.date-picker text.text-right {
  justify-content: flex-end !important;
  text-align: right !important;
  padding-right: 20rpx !important;
}

.date-picker text.text-left {
  justify-content: flex-start !important;
  text-align: left !important;
  padding-left: 20rpx !important;
}

/* 红色取消提示样式 */
.cancel-tip {
  color: #ff4444;
  font-size: 24rpx;
  margin-left: 20rpx;
}

/* 订单详情弹窗样式 */
.order-detail-popup .popup-content {
  max-height: 80vh;
  overflow: hidden; /* 改为hidden，让内部内容区域处理滚动 */
  display: flex;
  flex-direction: column;
}

.order-detail-body {
  padding: 0;
  flex: 1;
  overflow-y: auto; /* 启用垂直滚动 */
  -webkit-overflow-scrolling: touch; /* 在iOS上启用平滑滚动 */
}

/* 确保弹窗内容区域可以滚动 */
.order-detail-popup .popup-body {
  max-height: none; /* 覆盖默认的max-height限制 */
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 商家信息区域 */
.store-info-section {
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.store-card {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
}

.store-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  object-fit: cover;
}

.store-details {
  flex: 1;
}

.store-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.store-address {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 订单信息区域 */
.order-info-section, .order-items-section, .payment-info-section {
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1px solid #f0f0f0;
}

.order-info-item, .payment-info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.price-value {
  color: #ff4a4a;
  font-weight: 600;
}

.success {
  color: #07c160;
}

.error {
  color: #ff4a4a;
}

/* 订单商品列表 */
.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.order-item:last-child {
  border-bottom: none;
}

.order-item-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.item-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  object-fit: cover;
}

.item-info {
  flex: 1;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.item-price {
  font-size: 26rpx;
  color: #ff4a4a;
}

.item-right {
  text-align: right;
}

.item-quantity {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.item-subtotal {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 结账按钮样式 */
.checkout-section {
  margin-top: 20px;
  padding: 0 20px;
}

.checkout-btn {
  width: 100%;
  height: 44px;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  border: none;
  border-radius: 22px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.checkout-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
}

.checkout-btn::after {
  border: none;
}

.order-detail-bottom-spacer {
  height: 32rpx; /* 你可以根据实际需要调整高度，比如 32rpx、48rpx、64rpx 等 */
}

.tips-section {
  padding: 15rpx 30rpx;
  background-color: #fff2f0;
  border-left: 4rpx solid #ff4d4f;
  margin: 0 30rpx 20rpx 30rpx;
  border-radius: 8rpx;
}

.tips-content {
  line-height: 1.6;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tips-text {
  color: #ff4d4f;
  font-size: 24rpx;
  display: block;
  flex: 1;
  margin-right: 20rpx;
}

/* 拨打按钮样式 */
.call-button {
  font-size: 24rpx;
  color: #666;
  background: #fff;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  border: 1px solid #ff4d4f;
  color: #ff4d4f;
  transition: all 0.3s ease;
  cursor: pointer;
  flex-shrink: 0;
  min-width: 80rpx;
  text-align: center;
  box-shadow: 0 2rpx 4rpx rgba(255, 77, 79, 0.1);
}

.call-button:active {
  background: #ff4d4f;
  color: #fff;
  transform: scale(0.95);
  box-shadow: 0 1rpx 2rpx rgba(255, 77, 79, 0.2);
}

.call-button:hover {
  background: rgba(255, 77, 79, 0.05);
  border-color: #ff7875;
}
