// pages/scan_redirect/scan_redirect.js
import { loginRequest } from "../../service/index"

Page({

  /**
   * 页面的初始数据
   */
  data: {
    loading: true,
    mealType: '',
    timeout: '',
    redirected: false, // 添加标记，表示是否已经重定向
    isCheckingLogin: false, // 添加标记，表示是否正在检查登录状态
    showMealSelector: false, // 显示餐类型选择弹窗
    selectedType: '', // 用户选择的餐类型
    initialLoadComplete: false // 添加标记，表示初始加载是否完成
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    var mealType = '';
    var timeout = '';

    // 处理扫描二维码的情况
    if (options.q) {
      const q = decodeURIComponent(options.q);
      console.log('扫码进入，原始链接:', q);
      console.log('解码后的链接:', q);
      console.log('扫码时间:', options.scancode_time);

      // 提取 type 参数
      const typeMatch = q.match(/[?&]type=([^&]*)/);
      console.log('餐类型匹配结果:', typeMatch);
      if (typeMatch && typeMatch[1]) {
        mealType = typeMatch[1];
        console.log('提取到的餐类型:', mealType);
      } else {
        console.log('未能从链接中提取到餐类型');
      }

      // 提取 timeout 参数
      const timeoutMatch = q.match(/[?&]timeout=([^&]*)/);
      console.log('超时时间匹配结果:', timeoutMatch);
      if (timeoutMatch && timeoutMatch[1]) {
        timeout = timeoutMatch[1];
        console.log('提取到的超时时间:', timeout);
      } else {
        console.log('未能从链接中提取到超时时间');
      }
    } else {
      mealType = options.type || '';
      timeout = options.timeout || '';
    }

    if (!mealType) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      // 跳转到首页
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        });
      }, 1500);
      return;
    }

    // 检查链接是否过期(V1.3.0调整为固定二维码，无需判断有效时间。)
    if (timeout) {
      const currentTime = Date.now();
      if (currentTime > parseInt(timeout)) {
        wx.showToast({
          title: '二维码已过期！',
          icon: 'none'
        });
        // 跳转到首页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          });
        }, 1500);
        return;
      }
    }

    // 保存参数到data中
    this.setData({
      mealType: mealType,
      timeout: timeout
    });

    // 延迟一段时间，等待系统自动登录完成
    setTimeout(() => {
      // 标记初始加载开始
      this.setData({
        initialLoadComplete: true
      });

      // 获取app实例，尝试调用自动登录方法
      const app = getApp();
      if (app && app.monitor_token) {
        console.log('尝试自动登录');
        app.monitor_token().then(() => {
          console.log('自动登录完成，检查token');
          // 自动登录完成后，检查token
          this.checkLoginStatusAfterDelay(mealType);
        }).catch(err => {
          console.error('自动登录失败', err);
          // 自动登录失败，延迟后检查token
          this.checkLoginStatusAfterDelay(mealType);
        });
      } else {
        // 无法获取app实例或没有自动登录方法，直接延迟检查token
        this.checkLoginStatusAfterDelay(mealType);
      }
    }, 1000); // 等待1秒，给系统时间自动登录
  },

  // 选择餐类型
  selectMealType(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      selectedType: type
    });

    // 直接确认选择，不需要额外的确认按钮
    this.confirmMealType();
  },

  // 隐藏餐类型选择弹窗
  hideMealSelector() {
    this.setData({
      showMealSelector: false
    });
    // 隐藏弹窗后跳转到首页
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击弹窗内容时关闭弹窗
  },

  // 确认选择
  confirmMealType() {
    if (!this.data.selectedType) {
      wx.showToast({
        title: '请选择餐类型',
        icon: 'none'
      });
      return;
    }

    // 更新mealType并隐藏选择弹窗
    this.setData({
      mealType: this.data.selectedType,
      showMealSelector: false,
      loading: true
    });

    // 开始登录流程
    this.startLoginProcess(this.data.selectedType);
  },

  // 开始登录流程
  startLoginProcess(mealType) {
    // 延迟一段时间，等待系统自动登录完成
    setTimeout(() => {
      // 获取app实例，尝试调用自动登录方法
      const app = getApp();
      if (app && app.monitor_token) {
        console.log('尝试自动登录');
        app.monitor_token().then(() => {
          console.log('自动登录完成，检查token');
          // 自动登录完成后，检查token
          this.checkLoginStatusAfterDelay(mealType);
        }).catch(err => {
          console.error('自动登录失败', err);
          // 自动登录失败，延迟后检查token
          this.checkLoginStatusAfterDelay(mealType);
        });
      } else {
        // 无法获取app实例或没有自动登录方法，直接延迟检查token
        this.checkLoginStatusAfterDelay(mealType);
      }
    }, 1000); // 等待1秒，给系统时间自动登录
  },

  // 延迟检查登录状态
  checkLoginStatusAfterDelay(mealType) {
    console.log('延迟检查登录状态');
    // 再次延迟检查，确保token已经保存到storage
    setTimeout(() => {
      const token = wx.getStorageSync('token');
      console.log('获取到token:', token ? '有' : '无');

      if (token) {
        // 有token，检查是否有效
        this.checkLoginStatus(mealType);
      } else {
        // 仍然没有token，跳转到登录页
        console.log('延迟后仍未获取到token，跳转到登录页');
        wx.navigateTo({
          url: '/pages/phoneAuth/phoneAuth?redirect=scan_redirect&type=' + mealType
        });
      }
    }, 500); // 再等待0.5秒
  },

  // 检查登录状态
  checkLoginStatus(mealType) {
    // 避免重复检查
    if (this.data.isCheckingLogin) {
      console.log('正在检查登录状态，跳过重复检查');
      return;
    }

    this.setData({
      isCheckingLogin: true
    });

    const token = wx.getStorageSync('token');
    if (!token) {
      console.log('未登录，跳转到登录页');
      this.setData({
        isCheckingLogin: false
      });
      wx.navigateTo({
        url: '/pages/phoneAuth/phoneAuth?redirect=scan_redirect&type=' + mealType
      });
      return;
    }

    console.log('开始验证token:', token);

    // 已有token，验证token是否有效
    loginRequest.post({
      url: '/auth',
      header: {
        token
      }
    }).then(res => {
      console.log('token验证结果:', res);

      if (res.message === "已登录") {
        console.log('token有效，继续跳转');

        // 先更新状态，表示检查完成
        this.setData({
          isCheckingLogin: false
        }, () => {
          // 在状态更新完成的回调中执行跳转
          this.navigateToTargetPage(mealType);
        });
      } else {
        console.log('token无效，需要重新登录');
        // token无效，调用app的monitor_token方法重新登录
        const app = getApp();
        if (app && app.monitor_token) {
          app.monitor_token().then(() => {
            // 重新登录成功后，检查是否获取到新token
            const newToken = wx.getStorageSync('token');
            if (newToken) {
              // 继续跳转到目标页面
              this.navigateToTargetPage(mealType);
            } else {
              // 仍然没有token，跳转到登录页
              this.setData({
                isCheckingLogin: false
              });
              wx.navigateTo({
                url: '/pages/phoneAuth/phoneAuth?redirect=scan_redirect&type=' + mealType
              });
            }
          }).catch(err => {
            console.error('重新登录失败', err);
            // 登录失败，跳转到登录页
            this.setData({
              isCheckingLogin: false
            });
            wx.navigateTo({
              url: '/pages/phoneAuth/phoneAuth?redirect=scan_redirect&type=' + mealType
            });
          });
        } else {
          // 无法获取app实例，直接跳转到登录页
          this.setData({
            isCheckingLogin: false
          });
          wx.navigateTo({
            url: '/pages/phoneAuth/phoneAuth?redirect=scan_redirect&type=' + mealType
          });
        }
      }
    }).catch(err => {
      console.error('验证token失败', err);
      // 验证失败，跳转到登录页
      this.setData({
        isCheckingLogin: false
      });
      wx.navigateTo({
        url: '/pages/phoneAuth/phoneAuth?redirect=scan_redirect&type=' + mealType
      });
    });
  },

  // 导航到目标页面
  navigateToTargetPage(mealType) {
    // 标记已经重定向
    this.setData({
      redirected: true
    });

    // 如果是选择类型，显示选择弹窗
    if (mealType === 'select') {
      // 直接显示弹窗，因为此时登录检查已经完成
      this.setData({
        loading: false,
        showMealSelector: true
      });
      return;
    }

    // 根据餐类型跳转到对应页面，使用redirectTo而不是navigateTo
    if (mealType === 'personal') {
      wx.redirectTo({
        url: '/pages/booking/booking?source=admin',
        success: () => {
          console.log('跳转到个人餐页面成功');
        },
        fail: (error) => {
          console.error('跳转失败：', error);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
          // 跳转失败时，跳转到首页
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/index/index'
            });
          }, 1500);
        }
      });
    } else if (mealType === 'employee') {
      wx.redirectTo({
        url: '/pages/booking_employee/booking_employee?source=admin',
        success: () => {
          console.log('跳转到企业餐页面成功');
        },
        fail: (error) => {
          console.error('跳转失败：', error);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
          // 跳转失败时，跳转到首页
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/index/index'
            });
          }, 1500);
        }
      });
    } else if (mealType === 'select') {
      // 如果是select类型，不应该到这里，但为了防御性编程，跳转到首页
      console.warn('select类型不应该到达navigateToTargetPage函数');
      wx.switchTab({
        url: '/pages/index/index'
      });
    } else {
      wx.showToast({
        title: '未知餐类型',
        icon: 'none'
      });
      // 跳转到首页
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/index/index'
        });
      }, 1500);
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 如果页面被显示，但已经重定向过，说明用户点击了返回按钮
    // 这时应该跳转到首页，避免用户看到"正在跳转..."的提示
    if (this.data.redirected) {
      wx.switchTab({
        url: '/pages/index/index'
      });
      return;
    }

    // 只有在初始加载完成后，才允许onShow进行登录检查
    if (!this.data.initialLoadComplete) {
      return;
    }
    
    // 页面显示时，如果不是正在检查登录状态，则检查登录状态
    if (!this.data.isCheckingLogin) {
      const mealType = this.data.mealType;
      if (mealType) {
        const token = wx.getStorageSync('token');
        if (token) {
          // 如果有token，尝试验证并跳转
          this.checkLoginStatus(mealType);
        }
      }
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})