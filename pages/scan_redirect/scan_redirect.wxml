<!--pages/scan_redirect/scan_redirect.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在跳转...</text>
  </view>

  <!-- 餐类型选择弹窗 -->
  <view class="dialog-mask" wx:if="{{showMealSelector}}" bindtap="hideMealSelector">
    <view class="dialog-container" catchtap="stopPropagation">
      <view class="dialog-header">
        <view class="dialog-title">选择餐类型</view>
        <view class="close-btn" bindtap="hideMealSelector">×</view>
      </view>

      <view class="meal-type-container">
        <button class="meal-type-btn {{selectedType === 'personal' ? 'active' : ''}}" bindtap="selectMealType" data-type="personal">
          <view class="btn-content">
            <text class="btn-icon">🚴🏻</text>
            <text class="btn-text">个人餐</text>
          </view>
        </button>

        <button class="meal-type-btn {{selectedType === 'employee' ? 'active' : ''}}" bindtap="selectMealType" data-type="employee">
          <view class="btn-content">
            <text class="btn-icon">🧑🏻‍💻</text>
            <text class="btn-text">企业餐</text>
          </view>
        </button>
      </view>
    </view>
  </view>
</view>