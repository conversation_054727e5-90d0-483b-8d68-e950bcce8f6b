/* pages/scan_redirect/scan_redirect.wxss */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20px;
  color: #333;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 弹窗样式优化 - 与topic页面保持一致 */
.dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.dialog-container {
  background: white;
  border-radius: 20rpx;
  width: 80%;
  padding: 50rpx 30rpx 60rpx;
  position: relative;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  transform: scale(0.9);
  animation: scaleIn 0.3s ease forwards;
}

@keyframes scaleIn {
  to {
    transform: scale(1);
  }
}

.dialog-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 50rpx;
}

.dialog-title {
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 50rpx;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  font-size: 48rpx;
  color: #999;
}

.meal-type-container {
  display: flex;
  justify-content: space-between;
  gap: 30rpx;
}

.meal-type-btn {
  flex: 1;
  background: #f8f9fa;
  border: none;
  padding: 40rpx 20rpx;
  border-radius: 16rpx;
  color: #333;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  min-height: 180rpx;
}

.meal-type-btn::after {
  border: none;
}

.btn-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.btn-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.btn-text {
  font-size: 30rpx;
  font-weight: 500;
}

.meal-type-btn.active {
  background-color: #4080ff;
  color: white;
  transform: translateY(-4rpx);
  box-shadow: 0 10rpx 20rpx rgba(64, 128, 255, 0.3);
}