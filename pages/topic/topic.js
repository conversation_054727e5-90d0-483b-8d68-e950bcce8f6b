import { checkLogin, getUserInfo } from '../../service/user';

Page({
    data: {
        text: "Page topic",
        categoriesList: {},
        floatDisplay: "none",
        openid: "",
        userInfo: null
        // 移除弹窗相关的数据
        // showDialog: false,
        // selectedType: '',
        // dialogType: ''
    },
    onLoad: function (options) {
        wx.setNavigationBarTitle({
            title: '我要预约'
        });
    },

    // 在各主要页面的 onShow 方法中添加
    async onShow() {
      try {
        const isLoggedIn = await checkLogin()
        if (!isLoggedIn) {
          // token 无效，重新登录
          const app = getApp()
          if (app && app.monitor_token) {
            await app.monitor_token()
          } else {
            wx.navigateTo({
              url: '/pages/index/index'
            })
          }
        }
      } catch (error) {
        console.error('检查登录状态失败', error)
      }
    },

    getUserProfile() {
        wx.getUserProfile({
            desc: '用于完善用户资料',
            success: (res) => {
                const app = getApp();
                app.globalData.userInfo = res.userInfo;
                this.setData({ userInfo: res.userInfo });
                
                // 登录流程
                wx.login({
                    success: (loginRes) => {
                        wx.request({
                            url: app.globalData.baseUrl + '/user-info',
                            method: 'POST',
                            data: {
                                code: loginRes.code,
                                userInfo: res.userInfo
                            }
                        });
                    }
                });
            },
            fail: (err) => {
                console.error('用户拒绝授权:', err);
            }
        });
    },

    // 移除弹窗相关的方法
    // showMealTypeDialog, hideDialog, selectMealType, stopPropagation

    // 修改：检查企业绑定并跳转到相应页面
    async navigateToEmployeeBooking() {
        try {
            // 获取用户信息，检查是否有企业绑定
            const userInfo = await getUserInfo();
            
            // 检查用户是否有企业绑定
            const hasEnterpriseBinding = userInfo.enterprise_list && userInfo.enterprise_list.length > 0;
            
            if (hasEnterpriseBinding) {
                // 有企业绑定，跳转到企业餐页面
                wx.navigateTo({
                    url: '/pages/booking_employee/booking_employee?source=topic',
                    fail: (error) => {
                        console.error('页面跳转失败：', error);
                        wx.showToast({
                            title: '页面跳转失败',
                            icon: 'none'
                        });
                    }
                });
            } else {
                // 没有企业绑定，跳转到个人餐页面
                wx.navigateTo({
                    url: '/pages/booking/booking?source=topic',
                    fail: (error) => {
                        console.error('页面跳转失败：', error);
                        wx.showToast({
                            title: '页面跳转失败',
                            icon: 'none'
                        });
                    }
                });
            }
        } catch (error) {
            console.error('获取用户信息失败：', error);
            // 如果获取用户信息失败，默认跳转到个人餐页面
            wx.navigateTo({
                url: '/pages/booking/booking?source=topic',
                fail: (error) => {
                    console.error('页面跳转失败：', error);
                    wx.showToast({
                        title: '页面跳转失败',
                        icon: 'none'
                    });
                }
            });
        }
    },

    // 直接跳转到商务餐页面
    navigateToBusinessBooking() {
        wx.navigateTo({
            url: '/pages/booking_business/booking_business?source=topic',
            fail: (error) => {
                console.error('页面跳转失败：', error);
                wx.showToast({
                    title: '页面跳转失败',
                    icon: 'none'
                });
            }
        });
    }
})
