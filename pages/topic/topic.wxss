.page-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 30rpx 20rpx;
  box-sizing: border-box;
}

.header {
  margin-bottom: 40rpx;
  text-align: center;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-bottom: 20rpx;
}

.title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #4080ff;
  border-radius: 6rpx;
}

/* 餐点选项卡片样式 */
.meal-options {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  padding: 10rpx;
  width: 92%;
  margin: 0 auto;
}

.meal-card {
  display: flex;
  align-items: center;
  padding: 50rpx 50rpx;
  width: 100%;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-sizing: border-box;
}

.meal-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.03);
}

.meal-icon-container {
  width: 135rpx;
  height: 135rpx;
  border-radius: 50%;
  background: rgba(255, 165, 0, 0.15);
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 30rpx;
  flex-shrink: 0;
}

.business {
  background: rgba(0, 191, 255, 0.15);
}

.vegan {
  background: rgba(60, 179, 113, 0.15);
}

.onsite {
  background: rgba(144, 238, 144, 0.15);
}

.meal-icon {
  font-size: 48rpx;
}

.meal-content {
  flex: 1;
}

.meal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.meal-desc {
  font-size: 24rpx;
  color: #888;
  display: block;
}

.arrow {
  font-size: 40rpx;
  color: #ccc;
  font-weight: 300;
  margin-left: 20rpx;
}

/* 弹窗样式优化 */
.dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.dialog-container {
  background: white;
  border-radius: 20rpx;
  width: 80%;
  padding: 50rpx 30rpx 60rpx;
  position: relative;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  transform: scale(0.9);
  animation: scaleIn 0.3s ease forwards;
}

@keyframes scaleIn {
  to {
    transform: scale(1);
  }
}

.dialog-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 50rpx;
}

.dialog-title {
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 50rpx;
  height: 50rpx;
  line-height: 50rpx;
  text-align: center;
  font-size: 48rpx;
  color: #999;
}

.meal-type-container {
  display: flex;
  justify-content: space-between;
  gap: 30rpx;
}

.meal-type-btn {
  flex: 1;
  background: #f8f9fa;
  border: none;
  padding: 40rpx 20rpx;
  border-radius: 16rpx;
  color: #333;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  min-height: 180rpx;
}

.meal-type-btn::after {
  border: none;
}

.btn-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.btn-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
}

.btn-text {
  font-size: 30rpx;
  font-weight: 500;
}

.meal-type-btn.active {
  background-color: #4080ff;
  color: white;
  transform: translateY(-4rpx);
  box-shadow: 0 10rpx 20rpx rgba(60, 179, 113, 0.3);
}

/* 红色提示语样式 */
.red-tip {
  color: #ff4444;
  font-size: 24rpx;
  text-align: center;
  margin-top: -20rpx;
  margin-bottom: 20rpx;
}
