import { checkLogin } from '../../service/user';

// 获取应用实例
const app = getApp()

Page({
  data: {
    isLogin: false,
    avatarUrl: '/images/gravatar.png',
    nickName: '',
    isLoading: false,
    phone: '',
    floatDisplay: 'none',
    personalInfo: {
      realName: '',
      phoneNumber: '',
      email: '',
      balance: ''
    }
  },

  onLoad: function() {
    this.checkLoginStatus();
  },

  onLoad: function (options) {
    wx.setNavigationBarTitle({
        title: '个人信息'
    });
  },

  // onShow: function() {
  //   this.checkLoginStatus();
  //   if (this.data.isLogin) {
  //     this.fetchUserInfo();
  //   }
  // },

  async onShow() {
    try {
      const isLoggedIn = await checkLogin()
      if (!isLoggedIn) {
        // token 无效，重新登录
        const app = getApp()
        if (app && app.monitor_token) {
          await app.monitor_token()
        } else {
          wx.navigateTo({
            url: '/pages/index/index'
          })
        }
      }
    } catch (error) {
      console.error('检查登录状态失败', error)
    }
    this.checkLoginStatus();
    if (this.data.isLogin) {
      this.fetchUserInfo();
    }
  },

  checkLoginStatus: function() {
    const token = wx.getStorageSync('token') || '';
    
    // 检查全局数据中是否有用户信息
    if (token && app.globalData && app.globalData.userInfo) {
      const userInfo = app.globalData.userInfo;
      
      this.setData({
        isLogin: true,
        avatarUrl: userInfo.avatarUrl || '/images/gravatar.png',
        nickName: userInfo.nickname || ''
      });
      
      console.log('用户页面已更新用户信息', userInfo);
    } else {
      this.setData({
        isLogin: !!token,
        avatarUrl: '/images/gravatar.png',
        nickName: ''
      });
    }
  },

  // 登录按钮点击事件
  handleLogin: function() {
    this.setData({
      isLoading: true
    });
    
    app.handlerLogin().then(() => {
      // 登录完成后，检查全局是否有用户信息并更新
      if (app.globalData && app.globalData.userInfo) {
        const userInfo = app.globalData.userInfo;
        
        this.setData({
          isLogin: true,
          avatarUrl: userInfo.avatarUrl || '/images/gravatar.png',
          nickName: userInfo.nickname || '',
          isLoading: false
        });
      } else {
        this.setData({
          isLogin: true,
          isLoading: false
        });
      }
    }).catch(err => {
      console.error('登录失败', err);
      this.setData({
        isLoading: false
      });
      wx.showToast({
        title: '登录失败',
        icon: 'none'
      });
    });
  },

  // 头像选择事件
  onChooseAvatar: function(e) {
    const { avatarUrl } = e.detail;
    console.log('头像选择事件', avatarUrl);
    this.setData({
      avatarUrl
    });
    
    // 上传头像到服务器
    this.uploadAvatar(avatarUrl);
  },
  
  // 上传头像到服务器
  uploadAvatar: function(avatarUrl) {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 显示上传中
    wx.showLoading({
      title: '上传中...',
    });
    
    // 使用全局baseUrl
    const app = getApp();
    const url = app.globalData.baseUrl + '/upload-avatar';
    
    console.log('准备上传头像到:', url);
    
    // 上传文件
    wx.uploadFile({
      url: url,
      filePath: avatarUrl,
      name: 'avatar',
      header: {
        'token': token
      },
      success: (res) => {
        wx.hideLoading();
        const data = JSON.parse(res.data);
        
        if (data.status === 200) {
          wx.showToast({
            title: '头像更新成功',
            icon: 'success'
          });
          
          // 更新全局用户信息
          if (app.globalData && app.globalData.userInfo) {
            app.globalData.userInfo.avatarUrl = data.avatarUrl;
          }
        } else {
          wx.showToast({
            title: data.message || '上传失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('头像上传失败', err);
        wx.showToast({
          title: '头像上传失败: ' + (err.errMsg || '未知错误'),
          icon: 'none'
        });
      }
    });
  },

  // 昵称输入事件
  bindKeyInput: function(e) {
    this.setData({
      nickName: e.detail.value
    });
    // 这里可以添加代码将昵称保存到服务器
  },

  // 退出登录
  handleLogout: function() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储的token
          wx.removeStorageSync('token');
          // 清除全局用户信息
          if (app.globalData) {
            app.globalData.userInfo = null;
          }
          // 更新页面状态
          this.setData({
            isLogin: false,
            avatarUrl: '/images/gravatar.png',
            nickName: ''
          });
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },

  // 页面跳转函数
  navigateToUserInfo: function() {
    wx.navigateTo({
      url: '/pages/userInfo/userInfo'
    });
  },

  navigateToFundDetails: function() {
    wx.navigateTo({
      url: '/pages/fundDetails/fundDetails'
    });
  },

  provideFeedback: function() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    });
  },

  contactUs: function() {
    wx.navigateTo({
      url: '/pages/contact/contact'
    });
  },

  navigateBack() {
    wx.navigateBack({
      delta: 1
    })
  },

  fetchUserInfo: function() {
    const token = wx.getStorageSync('token');
    if (!token) return;

    const app = getApp();
    wx.request({
      url: app.globalData.baseUrl + '/user',  // 使用全局baseUrl
      method: 'GET',
      header: {
        'token': token
      },
      success: (res) => {
        if (res.data.status === 200) {
          const userInfo = res.data.userInfo;
          const personal = userInfo.personal || {};
          const enterpriseList = userInfo.enterprise_list || [];
          this.setData({
            'personalInfo.realName': personal.real_name || '未设置',
            'personalInfo.phoneNumber': userInfo.phoneNumber || '未设置',
            'personalInfo.email': personal.email || '未设置',
            'nickName': userInfo.nickName || '未设置',
            'enterpriseList': enterpriseList
          });
        }
      },
      fail: (err) => {
        console.error('获取用户信息失败', err);
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      }
    });
  }
})