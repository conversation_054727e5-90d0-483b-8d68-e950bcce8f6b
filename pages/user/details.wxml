<import src="../../templates/copyright.wxml" />
<import src="../../templates/login-popup.wxml" />
<!-- <scroll-view class="scrollarea" scroll-y type="list">
    <view class="container">
      <button open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">获取手机号</button>
      <view>{{phone}}</view>
    </view>
  </scroll-view> -->
<view class="readlog-container">
  <!-- 用户信息区域 -->
  <view class='user-cell'>
    <view class="account-info">
      <!-- <view class="account-header">
        <text>用户</text>
      </view> -->
      <view class="userinfo">
        <block wx:if="{{isLogin}}">
          <view class="user-profile">
            <button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
              <image class="avatar-circle" src="{{avatarUrl}}"></image>
            </button> 
            <text class="nickname-text">{{ nickName  || '未设置昵称'}}</text>
          </view>
        </block>
        <block wx:else>
          <view class="user-profile">
            <image class="avatar-circle" src="https://vegan.yiheship.com/static/images/gravatar.png" background-size="cover"></image>
            <button class="login-btn" bindtap="handleLogin">点击登录</button>
          </view>
        </block>
      </view>
    </view>
  </view>

  <!-- 个人信息区域 -->
  <view class='user-cell' wx:if="{{isLogin}}">
    <view class="account-info">
      <view class="account-header">
        <text>个人信息</text>
      </view>
      <view class="account-details">
        <text>真实姓名</text>
        <text>{{personalInfo.realName}}</text>
      </view>
      <view class="account-details">
        <text>手机号码</text>
        <text>{{personalInfo.phoneNumber}}</text>
      </view>
      <view class="account-details">
        <text>电子邮箱</text>
        <text>{{personalInfo.email}}</text>
      </view>
    </view>
  </view>

  <!-- 公司信息区域 -->
  <view class='user-cell' wx:if="{{isLogin}}">
    <view class="account-info">
      <view class="account-header">
        <text>公司信息</text>
      </view>
      <!-- 遍历企业列表 -->
      <block wx:for="{{enterpriseList}}" wx:key="id">
        <view class="enterprise-item">
          <view class="account-details">
            <text>公司名称</text>
            <text>{{item.company_name}}</text>
          </view>
          <view class="account-details">
            <text>手机号码</text>
            <text>{{item.phone}}</text>
          </view>
          <view class="account-details">
            <text>邮箱地址</text>
            <text>{{item.email}}</text>
          </view>
          <view class="account-details">
            <text>公司地址</text>
            <text>{{item.address}}</text>
          </view>
        </view>
        <!-- 分割线，最后一个企业不显示 -->
        <view class="divider" wx:if="{{index !== enterpriseList.length - 1}}"></view>
      </block>
    </view>
  </view>

  <!-- 返回按钮放在最下面 -->
  <view class="bottom-button-container">
    <button class="back-button" bindtap="navigateBack">返回</button>
  </view>
</view>