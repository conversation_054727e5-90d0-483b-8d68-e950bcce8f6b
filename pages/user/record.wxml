<import src="../../templates/copyright.wxml" />
<import src="../../templates/login-popup.wxml" />

<view class="readlog-container">
  
  <view class='user-cell'>
    <view class="account-info">
        <block wx:if="{{isLogin}}">
          <view class="record-list">
            <view class="record-item" wx:for="{{records}}" wx:key="index">
              <view class="record-left">
                <view class="record-type">{{item.description}}</view>
                <view class="record-time">{{item.date}}</view>
              </view>
              <view class="record-right">
                <view class="record-amount {{item.transaction_type > 0 ? 'income' : 'expense'}}">
                  {{item.transaction_type > 0 ? '+' + item.amount : item.amount}}
                </view>
                <!--<view class="record-balance">账户余额: {{item.balance}} 元</view>-->
              </view>
            </view>
          </view>
        </block>
        <block wx:else>
          <view class="user-profile">
            <image class="avatar-circle" src="https://vegan.yiheship.com/static/images/gravatar.png" background-size="cover"></image>
            <button class="login-btn" bindtap="handleLogin">点击登录</button>
          </view>
        </block>
      
    </view>
  </view>

  <view class="bottom-button-container">
    <button class="back-button" bindtap="navigateBack">返回</button>
  </view>
</view>