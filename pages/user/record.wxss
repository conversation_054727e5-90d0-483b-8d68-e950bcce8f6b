.readlog-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  background-color: #f5f7fa;
  padding-bottom: 84px;
}

.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #ffffff;
  padding: 50rpx 0;
  width: 100%;
  border-bottom-left-radius: 30rpx;
  border-bottom-right-radius: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.userinfo-avatar {
  width: 150rpx;
  height: 150rpx;
  margin: 20rpx;
  border-radius: 50%;
  border: 4rpx solid #fff;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
}

.userinfo-nickname {
  color: #333;
  font-size: 36rpx;
  margin-top: 10rpx;
}

.userinfo-phone {
  color: #666;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.login-btn {
  margin-top: 30rpx;
  background-color: #fff;
  color: #07C160;
  font-size: 32rpx;
  padding: 15rpx 60rpx;
  border-radius: 40rpx;
  border: none;
  box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.login-btn:active {
  transform: scale(0.95);
}

.get-userinfo-btn,
.get-phone-btn {
  margin: 10rpx 0;
  font-size: 28rpx;
  padding: 10rpx 30rpx;
  background: #07c160;
  color: #fff;
  border-radius: 30rpx;
}

.feedback-button {
  opacity: 0;
  position: absolute;
  width: 100vw;
  height: 50%;
  margin: 0;
  padding: 0;
  top: 0;
  left: 0;
}

.contact-button {
  opacity: 0;
  position: absolute;
  width: 100vw;
  height: 50%;
  margin: 0;
  padding: 0;
  bottom: 0;
  left: 0;
}

.user-cell {
  margin: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.07);
  overflow: hidden;
  width: 92%;
}

.account-info {
  padding: 40rpx;
}

.account-header {
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 20rpx;
  color: #333;
}

.account-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
  font-size: 30rpx;
  color: #333;
}

.account-detail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
}

.account-detail-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.account-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 30rpx;
}

.recharge-button, .withdraw-button {
  width: 45%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 30rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.recharge-button {
  background: linear-gradient(135deg, #07C160, #05a351);
  color: #fff;
}

.withdraw-button {
  background-color: #f8f8f8;
  color: #333;
  border: 1rpx solid #ddd;
}

.recharge-button:active, .withdraw-button:active {
  transform: scale(0.95);
}

.menu-list {
  background-color: #fff;
  padding: 10rpx 0;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 36rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.3s;
}

.menu-item:active {
  background-color: #f5f7fa;
}

.menu-text {
  font-size: 32rpx;
  color: #333;
}

.menu-arrow {
  color: #bbb;
  font-size: 32rpx;
}

.logout {
  margin-top: 30rpx;
  border-top: 10rpx solid #f8f8f8;
}

.logout .menu-text {
  color: #ff4d4f;
  font-weight: 500;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #07C160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  box-shadow: 0 0 10rpx rgba(7, 193, 96, 0.2);
}

.loading-text {
  margin-top: 20rpx;
  color: #999;
  font-size: 28rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.copyright {
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
  text-align: center;
}

@media screen and (max-width: 375px) {
  .user-cell {
    margin: 20rpx;
  }
  
  .account-info, .menu-item {
    padding: 30rpx;
  }
}

.user-profile {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 20rpx 0;
}

.avatar-wrapper {
  background-color: transparent;
  padding: 0;
  width: 150rpx;
  height: 150rpx;
  margin: 20rpx 0;
  border-radius: 50%;
}

.avatar-wrapper::after {
  border: none;
}

.avatar-circle {
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  border: 4rpx solid #fff;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
}

.nickname-input {
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
  font-size: 32rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  text-align: center;
  margin-top: 20rpx;
  width: 60%;
}

.back-button {
  padding: 10px 15px;
  margin: 10px;
  background-color: #f8f8f8;
  border-radius: 5px;
}

.back-button text {
  color: #333;
  font-size: 14px;
}

.bottom-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background-color: #fff;
  box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.1);
}

.back-button {
  width: 90% !important;
  height: 44px;
  line-height: 44px;
  font-size: 16px;
  color: #fff;
  background: #3d7eff;
  border-radius: 22px;
  border: none;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-button:active {
  opacity: 0.8;
}

.record-list {
  width: 100%;
}

.record-item {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.record-left {
  display: flex;
  flex-direction: column;
}

.record-type {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.record-amount {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.record-amount.income {
  color: #07c160;
}

.record-amount.expense {
  color: #ff4d4f;
}

.record-balance {
  font-size: 24rpx;
  color: #999;
}

