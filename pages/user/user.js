import { updateUserInfo } from '../../service/user'; // 根据实际路径调整
import { checkLogin } from '../../service/user';
import drawQrcode from '../../utils/weapp.qrcode.esm.js'
// 获取应用实例
const app = getApp()

Page({
  data: {
    isLogin: false,
    avatarUrl: '/images/gravatar.png',
    nickName: '',
    isLoading: false,
    phone: '',
    floatDisplay: 'none',
    msg_status: 0,
    isEnterpriseAdmin: false,  // 添加企业管理员状态
    enterpriseList: [],        // 企业列表
    selectedEnterprise: null,  // 当前选中的企业
    isSystemAdmin: false,      // 系统管理员状态
    showDialog: false,         // 弹窗显示状态
    selectedType: '',          // 选中的餐类型
    dialogType: '',            // 弹窗类型
    showQrCode: false,         // 是否显示二维码
  },

  onLoad: function() {
    this.checkLoginStatus();
  },

  onLoad: function (options) {
    wx.setNavigationBarTitle({
        title: '我的信息'
    });
  },

  // onShow: function() {
  //   // 每次页面显示时检查登录状态并更新用户信息
  //   this.checkLoginStatus();
  // },

  async onShow() {
    try {
      const isLoggedIn = await checkLogin()
      if (!isLoggedIn) {
        // token 无效，重新登录
        const app = getApp()
        if (app && app.monitor_token) {
          await app.monitor_token()
        } else {
          wx.navigateTo({
            url: '/pages/index/index'
          })
        }
      }
    } catch (error) {
      console.error('检查登录状态失败', error)
    }
    this.checkLoginStatus();
    this.checkEnterpriseAdmin();
    this.checkSystemAdmin();
  },

  checkLoginStatus: function() {
    const token = wx.getStorageSync('token') || '';
    
    if (token && app.globalData && app.globalData.userInfo) {
      const userInfo = app.globalData.userInfo;
      console.log(userInfo)
      this.setData({
        isLogin: true,
        avatarUrl: userInfo.avatarUrl || '/images/gravatar.png',
        nickName: userInfo.nickName || '',
        balance: userInfo.balance || '0.0',
        msg_status: userInfo.msg_status || 0
      });
      
      console.log('用户页面已更新用户信息', userInfo);
    } else {
      this.setData({
        isLogin: !!token,
        avatarUrl: '/images/gravatar.png',
        nickName: '',
        balance: '0.0',
        msg_status: 0
      });
    }
  },

  // 登录按钮点击事件
  handleLogin: function() {
    wx.navigateTo({
      url: '/pages/phoneAuth/phoneAuth'
    })
  },

  // 消息订阅选择事件
  onSubscribeMessage: function(e) {
    // const templateId = 'uMsj6o9VGLA7t6Dv1EVbq_xbCBmu2sncEldk8KuFNmM';
    const newStatus = e.detail.value ? 1 : 0;

    // if (newStatus === 1) {
    //   wx.requestSubscribeMessage({
    //     tmplIds: [templateId],
    //     success: (res) => {
    //       if (res[templateId] == 'accept') {
    //         this.updateMsgStatus(newStatus);
    //       } else {
    //         this.setData({ msg_status: 0 });
    //         wx.showToast({
    //           title: '订阅失败',
    //           icon: 'none'
    //         });
    //       }
    //     },
    //     fail: (res) => {
    //       console.log(res);
    //       this.setData({ msg_status: 0 });
    //       wx.showToast({
    //         title: '订阅失败',
    //         icon: 'none'
    //       });
    //     }
    //   });
    // } else {
    //   this.updateMsgStatus(newStatus);
    // }

    // 直接更新消息状态，不调用微信订阅API
    this.updateMsgStatus(newStatus);
  },

  updateMsgStatus: function(status) {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    const app = getApp();
    wx.request({
      url: app.globalData.baseUrl + '/update-msg-state',
      method: 'POST',
      header: {
        'token': token,
        'content-type': 'application/json'
      },
      data: {
        msg_status: status
      },
      success: (res) => {
        if (res.data.status === 200) {
          this.setData({
            msg_status: status
          });
          if (app.globalData && app.globalData.userInfo) {
            app.globalData.userInfo.msg_status = status;
          }
          wx.showToast({
            title: status === 1 ? '订阅成功' : '已取消订阅',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: '操作失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('更新消息状态失败', err);
        wx.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    });
  },

  // 头像选择事件
  onChooseAvatar: function(e) {
    const { avatarUrl } = e.detail;
    console.log('头像选择事件', avatarUrl);
    this.setData({
      avatarUrl
    });
    
    // 上传头像到服务器
    this.uploadAvatar(avatarUrl);
  },
  
  // 上传头像到服务器
  uploadAvatar: function(avatarUrl) {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 显示上传中
    wx.showLoading({
      title: '上传中...',
    });
    
    // 使用全局baseUrl
    const app = getApp();
    const url = app.globalData.baseUrl + '/upload-avatar';
    
    console.log('准备上传头像到:', url);
    
    // 上传文件
    wx.uploadFile({
      url: url,
      filePath: avatarUrl,
      name: 'avatar',
      header: {
        'token': token
      },
      success: (res) => {
        wx.hideLoading();
        const data = JSON.parse(res.data);
        
        if (data.status === 200) {
          wx.showToast({
            title: '头像更新成功',
            icon: 'success'
          });
          
          // 更新全局用户信息
          if (app.globalData && app.globalData.userInfo) {
            app.globalData.userInfo.avatarUrl = data.avatarUrl;
          }
        } else {
          wx.showToast({
            title: data.message || '上传失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('头像上传失败', err);
        wx.showToast({
          title: '头像上传失败: ' + (err.errMsg || '未知错误'),
          icon: 'none'
        });
      }
    });
  },

  // 昵称输入事件
  bindKeyInput: function(e) {
    // 将昵称保存到服务器
    app.globalData.userInfo.nickName = e.detail.value
    console.log('======')
    console.log(e.detail.value)
    console.log('======')
    updateUserInfo(app.globalData.userInfo)
    .then(res => {
      app.globalData.userInfo = res.userInfo
    })
    .catch(err => {
      console.error('昵称更新失败', err);
    });
  },

  // 退出登录
  handleLogout: function() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 清除本地存储的token
          wx.removeStorageSync('token');
          // 清除全局用户信息
          if (app.globalData) {
            app.globalData.userInfo = null;
          }
          // 更新页面状态
          this.setData({
            isLogin: false,
            avatarUrl: '/images/gravatar.png',
            nickName: '',
            balance: userInfo.balance || '0.0'
          });
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },

  // 页面跳转函数
  navigateToUserInfo: function() {
    wx.navigateTo({
      url: '/pages/userInfo/userInfo'
    });
  },

  navigateToFundDetails: function() {
    wx.navigateTo({
      url: '/pages/fundDetails/fundDetails'
    });
  },

  provideFeedback: function() {
    wx.navigateTo({
      url: '/pages/feedback/feedback'
    });
  },

  contactUs: function() {
    wx.navigateTo({
      url: '/pages/contact/contact'
    });
  },

  navigateToDetails() {
    wx.navigateTo({
      url: '/pages/user/details'
    })
  },

  navigateToRecord() {
    wx.navigateTo({
      url: '/pages/user/record'
    })
  },

  navigateToFeedback() {
    wx.navigateTo({
      url: '/pages/user/feedback'
    })
  },
  
  navigateToAbout() {
    wx.navigateTo({
      url: '/pages/user/about'
    })
  },

  // 企业管理相关导航方法
  navigateToEnterpriseManage() {
    const enterprise = this.data.selectedEnterprise;
    if (!enterprise) {
      wx.showToast({
        title: '请先选择企业',
        icon: 'none'
      });
      return;
    }
    wx.navigateTo({
      url: `/pages/enterprise/manage?enterprise_id=${enterprise.enterprise_id}&enterprise_name=${encodeURIComponent(enterprise.enterprise_name)}`
    })
  },

  navigateToEnterpriseUser() {
    const enterprise = this.data.selectedEnterprise;
    if (!enterprise) {
      wx.showToast({
        title: '请先选择企业',
        icon: 'none'
      });
      return;
    }
    wx.navigateTo({
      url: `/pages/enterprise/user/user?enterprise_id=${enterprise.enterprise_id}&enterprise_name=${encodeURIComponent(enterprise.enterprise_name)}`
    })
  },

  navigateToEnterpriseAccount() {
    const enterprise = this.data.selectedEnterprise;
    if (!enterprise) {
      wx.showToast({
        title: '请先选择企业',
        icon: 'none'
      });
      return;
    }
    wx.navigateTo({
      url: `/pages/enterprise/account/account?enterprise_id=${enterprise.enterprise_id}&enterprise_name=${encodeURIComponent(enterprise.enterprise_name)}`
    })
  },

  navigateToEnterpriseStatistic() {
    const enterprise = this.data.selectedEnterprise;
    if (!enterprise) {
      wx.showToast({
        title: '请先选择企业',
        icon: 'none'
      });
      return;
    }
    wx.navigateTo({
      url: `/pages/enterprise/statistic/statistic?enterprise_id=${enterprise.enterprise_id}&enterprise_name=${encodeURIComponent(enterprise.enterprise_name)}`
    })
  },

  // 充值按钮点击事件
  handleRecharge: function() {
    wx.showActionSheet({
      itemList: ['充值¥300', '充值¥500', '充值¥1000', '充值¥2000', '充值¥5000', '自定义金额'],
      success: (res) => {
        const amounts = [300, 500, 1000, 2000, 5000];
        if (res.tapIndex === 5) {
          // 用户选择了自定义金额
          this.showCustomAmountInput();
        } else {
          const selectedAmount = amounts[res.tapIndex];
          this.createRechargeOrder(selectedAmount);
        }
      }
    });
  },

  // 显示自定义金额输入框
  showCustomAmountInput: function() {
    wx.showModal({
      title: '自定义充值金额',
      content: '',
      editable: true,
      placeholderText: '请输入充值金额（元）',
      success: (res) => {
        if (res.confirm) {
          // 检查输入是否为纯数字（可以包含小数点）
          const numberRegex = /^[0-9]+(\.[0-9]+)?$/;
          if (!numberRegex.test(res.content)) {
            wx.showToast({
              title: '请输入有效金额',
              icon: 'none'
            });
            return;
          }
          
          const amount = parseFloat(res.content);
          if (isNaN(amount) || amount <= 0) {
            wx.showToast({
              title: '请输入有效金额',
              icon: 'none'
            });
          } else {
            this.createRechargeOrder(amount);
          }
        }
      }
    });
  },

  // 创建充值订单
  createRechargeOrder: function(amount) {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '处理中...',
    });

    const app = getApp();
    wx.request({
      url: app.globalData.baseUrl + '/create-recharge-order',
      method: 'POST',
      header: {
        'token': token,
        'content-type': 'application/json'
      },
      data: {
        amount: amount
      },
      success: (res) => {
        wx.hideLoading();
        if (res.data.status === 200) {
          const payParams = res.data.payParams;
          // 调起微信支付
          wx.requestPayment({
            ...payParams,
            success: (result) => {
              // 支付成功后，先乐观更新余额
              const currentBalance = parseFloat(this.data.balance || '0');
              const newBalance = (currentBalance + amount).toFixed(2);
              
              this.setData({
                balance: newBalance
              });
              
              if (app.globalData && app.globalData.userInfo) {
                app.globalData.userInfo.balance = newBalance;
              }

              wx.showToast({
                title: '充值成功',
                icon: 'success'
              });

              // 轮询检查订单状态
              // this.checkOrderStatus(res.data.orderId);
            },
            fail: (err) => {
              console.error('支付失败', err);
              wx.showToast({
                title: '支付失败',
                icon: 'none'
              });
            }
          });
        } else {
          wx.showToast({
            title: res.data.message || '创建订单失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('创建充值订单失败', err);
        wx.showToast({
          title: '创建订单失败',
          icon: 'none'
        });
      }
    });
  },

  // 添加轮询检查订单状态的方法
  checkOrderStatus: function(orderId) {
    let retryCount = 0;
    const maxRetries = 3;
    const checkInterval = 2000; // 2秒检查一次

    const checkStatus = () => {
      if (retryCount >= maxRetries) {
        // 超过最大重试次数，刷新用户信息以获取实际余额
        this.checkLoginStatus();
        return;
      }

      const app = getApp();
      wx.request({
        url: app.globalData.baseUrl + '/check-order-status',
        method: 'GET',
        header: {
          'token': wx.getStorageSync('token'),
          'content-type': 'application/json'
        },
        data: {
          orderId: orderId
        },
        success: (res) => {
          if (res.data.status === 200) {
            if (res.data.orderStatus === 'SUCCESS') {
              // 订单确认成功，更新实际余额
              this.checkLoginStatus();
            } else if (res.data.orderStatus === 'PENDING') {
              // 订单待处理，继续轮询
              retryCount++;
              setTimeout(checkStatus, checkInterval);
            }
          }
        },
        fail: () => {
          retryCount++;
          setTimeout(checkStatus, checkInterval);
        }
      });
    };

    // 开始轮询
    setTimeout(checkStatus, checkInterval);
  },

  // 提现按钮点击事件
  handleWithdraw: function() {
    wx.showModal({
      title: '提示',
      content: '暂未开通提现功能，\r\n如需提现请联系客服！',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 检查系统管理员状态
  checkSystemAdmin: function() {
    const token = wx.getStorageSync('token');
    if (!token) {
      console.log('用户未登录，无法检查系统管理员状态');
      return;
    }
    const app = getApp();
    wx.request({
      url: app.globalData.baseUrl + '/admin/is_system_admin',
      method: 'GET',
      header: {
        'token': token,
        'content-type': 'application/json'
      },
      success: (res) => {
        console.log('系统管理员状态检查结果:', res.data);
        if (res.data.status === 200) {
          const isSystemAdmin = res.data.data;
          console.log('系统管理员状态:', isSystemAdmin);
          // 更新页面状态
          this.setData({
            isSystemAdmin: isSystemAdmin
          });
        } else {
          console.log('系统管理员状态检查失败');
          this.setData({
            isSystemAdmin: false
          });
        }
      },
      fail: (err) => {
        console.error('检查系统管理员状态失败', err);
        this.setData({
          isSystemAdmin: false
        });
      }
    });
  },

  // 检查企业管理员状态
  checkEnterpriseAdmin: function() {
    const token = wx.getStorageSync('token');
    if (!token) {
      console.log('用户未登录，无法检查企业管理员状态');
      return;
    }

    const app = getApp();
    wx.request({
      url: app.globalData.baseUrl + '/enterprise/is_enterprise_admin',
      method: 'GET',
      header: {
        'token': token,
        'content-type': 'application/json'
      },
      success: (res) => {
        console.log('企业管理员状态检查结果:', res.data);
        if (res.data.status === 200 && res.data.data && Array.isArray(res.data.data)) {
          const enterpriseList = res.data.data;
          const isAdmin = enterpriseList.length > 0;
          
          this.setData({
            isEnterpriseAdmin: isAdmin,
            enterpriseList: enterpriseList,
            selectedEnterprise: isAdmin ? enterpriseList[0] : null
          });
        } else {
          this.setData({
            isEnterpriseAdmin: false,
            enterpriseList: [],
            selectedEnterprise: null
          });
        }
      },
      fail: (err) => {
        console.error('检查企业管理员状态失败', err);
        this.setData({
          isEnterpriseAdmin: false,
          enterpriseList: [],
          selectedEnterprise: null
        });
      }
    });
  },

  // 企业选择事件
  onEnterpriseChange: function(e) {
    const index = e.detail.value;
    const selectedEnterprise = this.data.enterpriseList[index];
    this.setData({
      selectedEnterprise: selectedEnterprise
    });
    console.log('切换到企业:', selectedEnterprise);
  },

  // 修改跳转到自助餐临时点餐页面的函数，显示选择弹窗
  navigateToSelfServiceOrder: function() {
    this.setData({
      showDialog: true,
      selectedType: '',
      dialogType: 'self'
    });
  },

  // 跳转到商务餐临时点餐页面
  navigateToBusinessOrder: function() {
    wx.navigateTo({
      url: '/pages/booking_business/booking_business?source=user',
      fail: (error) => {
        console.error('页面跳转失败：', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
  }
    });
  },

  // 隐藏弹窗
  hideDialog: function() {
    this.setData({
      showDialog: false,
      showQrCode: false
    });
  },

  // 阻止点击弹窗内容时关闭弹窗
  stopPropagation: function() {
    return;
  },

  // 选择餐类型
  selectMealType: function(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      selectedType: type,
      showQrCode: true
    });
    
    // 生成对应类型的链接，指向中间页面
    console.log('type', type)
    // 生成时间戳，设置5分钟后过期
    const timeout = Date.now() + 10 * 60 * 1000  // 当前时间 + 10分钟(毫秒)
    console.log('timeout', timeout)

    // 生成完整的小程序路径
    const qrCodeText = `${getApp().globalData.baseUrlHost}/srxcx/?type=${type}&timeout=${timeout}`;
    // const qrCodeText = `https://vegan.yiheship.com/srxcx/?type=${type}&timeout=${timeout}`;
    console.log('qrCodeText', qrCodeText)
    
    // 生成二维码
    const options = {
      width: 200,
      height: 200,
      canvasId: 'mealQrcode',
      x: 0,
      y: 0,
      text: qrCodeText,
      callback: (res) => {
        console.log('二维码生成成功');
      }
    };
    
    drawQrcode(options);
  },

  showBusinessOrderDisabledTip: function() {
    wx.showToast({
      title: '商务餐临时点餐功能暂时关闭',
      icon: 'none',
      duration: 2000
    });
  },

  // 添加隐藏二维码的方法
  hideQrCode: function() {
    this.setData({
      showQrCode: false
    });
  },

  // 导航到订餐统计页面
  navigateToStatistic: function() {
    wx.navigateTo({
      url: '/pages/admin/statistic/statistic',
      fail: (error) => {
        console.error('页面跳转失败：', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },
  
  // 扫码并核销
  scanAndVerify: function() {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }
    
    // 跳转到扫码页面
    wx.navigateTo({
      url: '/pages/admin/scan/scan',
      fail: (error) => {
        console.error('页面跳转失败：', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },
})