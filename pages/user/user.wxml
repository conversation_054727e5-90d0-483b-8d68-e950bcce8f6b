<import src="../../templates/copyright.wxml" />
<import src="../../templates/login-popup.wxml" />
<!-- <scroll-view class="scrollarea" scroll-y type="list">
    <view class="container">
      <button open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">获取手机号</button>
      <view>{{phone}}</view>
    </view>
  </scroll-view> -->
<view class="readlog-container">
  <!-- 用户信息区域 -->
  <view class='user-cell'>
    <view class="account-info">
      <!-- <view class="account-header">
        <text>用户</text>
      </view> -->
      <view class="userinfo">
        <block wx:if="{{isLogin}}">
          <view class="user-profile">
            <button class="avatar-wrapper" open-type="chooseAvatar" bind:chooseavatar="onChooseAvatar">
              <image class="avatar-circle" src="{{avatarUrl}}"></image>
            </button> 
            <input bindchange="bindKeyInput" value="{{nickName}}" type="nickname" class="nickname-input" placeholder="请输入昵称"/>
          </view>
        </block>
        <block wx:else>
          <view class="user-profile">
            <image class="avatar-circle" src="/images/gravatar.png" background-size="cover"></image>
            <button class="login-btn" bindtap="handleLogin">点击登录</button>
          </view>
        </block>
      </view>
    </view>
  </view>

  
  <!-- 账户信息区域 -->
  <view class='user-cell' wx:if="{{isLogin}}">
    <view class="account-info">
      <view class="account-header">
        <text>账户信息</text>
      </view>
      <view class="account-details">
        <text>个人账户</text>
        <text>余额：{{ balance }} 元</text>
      </view>
      <view class="account-actions">
        <button class="recharge-button" bindtap="handleRecharge">充值</button>
        <!--<button class="withdraw-button" bindtap="handleWithdraw">提现</button>-->
      </view>
    </view>
  </view>

  <view class='user-cell' wx:if="{{isSystemAdmin}}">
    <view class="account-info">
      <view class="account-header">
        <text>餐厅管理</text>
      </view>
      <view class="offline-order-container">
        <view class="offline-order-card" bindtap="navigateToSelfServiceOrder">
          <view class="offline-order-icon">🍽️</view>
          <view class="offline-order-title">自助餐</view>
        </view>
        <view class="offline-order-card disabled" bindtap="showBusinessOrderDisabledTip">
          <view class="offline-order-icon">🍲</view>
          <view class="offline-order-title">商务餐</view>
          <view class="disabled-badge">暂停服务</view>
        </view>
      </view>
      <view class="statistic-button-container">
        <button class="statistic-button" bindtap="navigateToStatistic">
          <view class="statistic-button-icon">📊</view>
          <text class="statistic-button-text">订餐统计</text>
        </button>
      </view>
      <view class="statistic-button-container">
        <button class="statistic-button" bindtap="scanAndVerify">
          <view class="statistic-button-icon">📷</view>
          <text class="statistic-button-text">扫码核销</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 企业管理区域 -->
  <view class='user-cell' wx:if="{{isLogin && isEnterpriseAdmin && enterpriseList.length > 0}}">
    <view class="account-info">
      <view class="account-header">
        <text>企业管理</text>
        <view class="enterprise-selector" wx:if="{{enterpriseList.length > 1}}">
          <picker mode="selector" range="{{enterpriseList}}" range-key="enterprise_name" bindchange="onEnterpriseChange">
            <view class="enterprise-picker">
              <text class="enterprise-name">{{selectedEnterprise.enterprise_name}}</text>
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>
        <view class="single-enterprise" wx:else>
          <text class="enterprise-name">{{selectedEnterprise.enterprise_name}}</text>
        </view>
      </view>
      <view class="account-details">
        <!--
        <view class="account-detail-item" bindtap="navigateToEnterpriseManage">
          <text>企业设置</text>
        </view>
        -->
        <view class="account-detail-item" bindtap="navigateToEnterpriseUser">
          <text>用户管理</text>
        </view>
        <view class="account-detail-item" bindtap="navigateToEnterpriseAccount">
          <text>账户管理</text>
        </view>
        <view class="account-detail-item" bindtap="navigateToEnterpriseStatistic">
          <text>订餐统计</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 功能菜单区域 -->
  <view class='user-cell' wx:if="{{isLogin}}">
    <view class="account-info">
      <view class="account-header">
        <text>系统功能</text>
        <view class="subscribe-container">
          <text class="subscribe-text">消息订阅</text>
          <switch class="subscribe-switch" checked="{{msg_status === 1}}" bindchange="onSubscribeMessage" />
        </view>
      </view>
      <view class="account-details">
        <view class="account-detail-item" bindtap="navigateToDetails">
          <image class="account-detail-icon" src="https://vegan.yiheship.com/static/images/tar-person.png"></image>
          <text>个人信息</text>
        </view>
        <view class="account-detail-item" bindtap="navigateToRecord">
          <image class="account-detail-icon" src="https://vegan.yiheship.com/static/images/tar-info.png"></image>
          <text>资金明细</text>
        </view>
        <view class="account-detail-item" bindtap="navigateToFeedback">
          <image class="account-detail-icon" src="https://vegan.yiheship.com/static/images/tar-comments.png"></image>
          <text>建议与评价</text>
        </view>
        <view class="account-detail-item" bindtap="navigateToAbout">
          <image class="account-detail-icon" src="https://vegan.yiheship.com/static/images/kefu.png"></image>
          <text>联系我们</text>
        </view>
      </view>
    </view>
  </view>
      <!-- <view class="menu-item logout" bindtap="handleLogout">
        <text class="menu-text">退出登录</text>
      </view> -->
    
  
  <!-- 加载中提示 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- <template is="login-popup" data="{{show: isLoginPopup, userInfo: userInfo}}"></template> -->

  <view class="copyright" style="display:{{floatDisplay}}">
    <template is="tempCopyright" />
  </view>
</view>

<!-- 弹窗组件 -->
<view class="dialog-mask" wx:if="{{showDialog}}" bindtap="hideDialog">
  <view class="dialog-container" catchtap="stopPropagation">
    <view class="dialog-header">
      <view class="dialog-title">选择报餐类型</view>
      <view class="close-btn" bindtap="hideDialog">×</view>
    </view>
    <view class="meal-type-container" wx:if="{{!showQrCode}}">
      <block wx:if="{{dialogType === 'self'}}">
        <button class="meal-type-btn {{selectedType === 'personal' ? 'active' : ''}}" bindtap="selectMealType" data-type="personal">
          <view class="btn-content">
            <text class="btn-icon">🚴🏻</text>
            <text class="btn-text">个人餐</text>
          </view>
        </button>
        <button class="meal-type-btn {{selectedType === 'select' ? 'active' : ''}}" bindtap="selectMealType" data-type="select">
          <view class="btn-content">
            <text class="btn-icon">🙎🏻‍♂️</text>
            <text class="btn-text">自主选择</text>
          </view>
        </button>
        <button class="meal-type-btn {{selectedType === 'employee' ? 'active' : ''}}" bindtap="selectMealType" data-type="employee">
          <view class="btn-content">
            <text class="btn-icon">🧑🏻‍💻</text>
            <text class="btn-text">企业餐</text>
          </view>
        </button>
      </block>
    </view>
    <!-- 二维码显示区域 -->
    <view class="qrcode-container" wx:if="{{showQrCode}}">
      <view class="qrcode-title">请扫描二维码进行报餐</view>
      <canvas class="qrcode-canvas" canvas-id="mealQrcode" style="width: 200px; height: 200px;"></canvas>
      <view class="qrcode-type">{{selectedType === 'personal' ? '个人餐' : selectedType === 'employee' ? '企业餐' : '自主选择'}}</view>
    </view>
  </view>
</view>