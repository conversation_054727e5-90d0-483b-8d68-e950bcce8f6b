[project]
name = "yh-vegan-admin"
version = "0.1.0"
description = ""
authors = [
    {name = "gzpanxb",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi (>=0.115.12,<0.116.0)",
    "sqlalchemy (>=2.0.40,<3.0.0)",
    "alembic (>=1.15.2,<2.0.0)",
    "pymysql (>=1.1.1,<2.0.0)",
    "python-jose (>=3.4.0,<4.0.0)",
    "passlib (>=1.7.4,<2.0.0)",
    "uvicorn (>=0.34.0,<0.35.0)",
    "pydantic-settings (>=2.8.1,<3.0.0)",
    "email-validator (>=2.2.0,<3.0.0)",
    "python-multipart (>=0.0.20,<0.0.21)",
    "httpx (>=0.27.0,<0.28.0)",
    "pytest (>=8.3.5,<9.0.0)",
    "pytz (>=2025.2,<2026.0)",
    "arrow (>=1.3.0,<2.0.0)",
    "pycryptodomex (>=3.20.0,<4.0.0)",
    "pycryptodome (>=3.22.0,<4.0.0)",
    "croniter (>=6.0.0,<7.0.0)",
    "requests (>=2.32.3,<3.0.0)",
    "wechatpayv3 (>=1.3.10,<2.0.0)",
    "tencentcloud-sdk-python (>=3.0.1364,<4.0.0)",
    "redis (>=5.2.1,<6.0.0)",
    "bcrypt (==3.2.0)",
    "pillow (>=11.2.1,<12.0.0)",
    "openpyxl (>=3.1.5,<4.0.0)",
    "apscheduler (>=3.11.0,<4.0.0)"
]

[tool.poetry]
packages = [{include = "yh_vegan_admin", from = "src"}]
package-mode = false

[tool.poetry.group.dev.dependencies]
setuptools = "^79.0.1"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

