#!/usr/bin/env python3
"""
初始化RBAC权限数据脚本
创建基础的角色和权限数据

使用方法:
1. 使用当前环境配置的数据库连接:
   python scripts/init_permissions.py

2. 使用.env.stage配置的数据库连接:
   python scripts/init_permissions.py --use-stage-env

3. 查看帮助信息:
   python scripts/init_permissions.py -h
"""
import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from pydantic_settings import BaseSettings
# 延迟导入以避免配置问题


def get_database_session(use_stage_env: bool = False) -> Session:
    """
    获取数据库会话
    Args:
        use_stage_env: 是否使用.env.stage环境配置
    Returns:
        数据库会话
    """
    if use_stage_env:
        # 使用.env.stage配置
        class StageSettings(BaseSettings):
            SQLALCHEMY_DATABASE_URI: str
            
            model_config = {
                "env_file": ".env.stage",
                "env_file_encoding": "utf-8",
                "extra": "ignore"  # 忽略额外的字段
            }
        
        try:
            stage_settings = StageSettings()
            database_uri = stage_settings.SQLALCHEMY_DATABASE_URI
            print(f"使用.env.stage配置，数据库连接: {database_uri}")
        except Exception as e:
            print(f"❌ 读取.env.stage配置失败: {e}")
            sys.exit(1)
    else:
        # 使用当前环境配置
        try:
            from app.core.config import settings
            database_uri = settings.SQLALCHEMY_DATABASE_URI
            print(f"使用当前环境配置，数据库连接: {database_uri}")
        except Exception as e:
            print(f"❌ 读取当前环境配置失败: {e}")
            print("请检查环境配置文件或使用 --use-stage-env 参数")
            sys.exit(1)
    
    # 创建数据库引擎和会话
    engine = create_engine(
        database_uri,
        pool_pre_ping=True,
    )
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal()


def init_permissions(db: Session):
    """初始化权限数据"""
    # 延迟导入以避免配置加载问题
    from app.dao.admin import permission_dao
    from app.schemas.admin import PermissionCreate
    
    permissions_data = [
        # 系统管理
        {"name": "系统管理", "code": "system:manage", "permission_type": "read", "description": "系统管理"},
        {"name": "管理员查看", "code": "admin:read", "permission_type": "read", "description": "查看管理员信息"},
        {"name": "管理员创建", "code": "admin:create", "permission_type": "write", "description": "创建管理员"},
        {"name": "管理员更新", "code": "admin:update", "permission_type": "write", "description": "更新管理员信息"},
        {"name": "管理员删除", "code": "admin:delete", "permission_type": "write", "description": "删除管理员"},
        {"name": "角色查看", "code": "role:read", "permission_type": "read", "description": "查看角色信息"},
        {"name": "角色创建", "code": "role:create", "permission_type": "write", "description": "创建角色"},
        {"name": "角色更新", "code": "role:update", "permission_type": "write", "description": "更新角色信息"},
        {"name": "角色删除", "code": "role:delete", "permission_type": "write", "description": "删除角色"},
        {"name": "权限查看", "code": "permission:read", "permission_type": "read", "description": "查看权限信息"},
        {"name": "权限创建", "code": "permission:create", "permission_type": "write", "description": "创建权限"},
        {"name": "权限更新", "code": "permission:update", "permission_type": "write", "description": "更新权限信息"},
        {"name": "权限删除", "code": "permission:delete", "permission_type": "write", "description": "删除权限"},
        
        # 用户管理
        {"name": "用户管理", "code": "user:manage", "permission_type": "read", "description": "用户管理"},
        {"name": "用户查看", "code": "user:read", "permission_type": "read", "description": "查看用户信息"},
        {"name": "用户创建", "code": "user:create", "permission_type": "write", "description": "创建用户"},
        {"name": "用户更新", "code": "user:update", "permission_type": "write", "description": "更新用户信息"},
        {"name": "用户删除", "code": "user:delete", "permission_type": "write", "description": "删除用户"},
        {"name": "个人用户查看", "code": "user:personal:read", "permission_type": "read", "description": "查看个人用户信息"},
        {"name": "个人用户创建", "code": "user:personal:create", "permission_type": "write", "description": "创建个人用户"},
        {"name": "个人用户更新", "code": "user:personal:update", "permission_type": "write", "description": "更新个人用户信息"},
        {"name": "个人用户删除", "code": "user:personal:delete", "permission_type": "write", "description": "删除个人用户"},
        {"name": "企业用户查看", "code": "user:enterprise:read", "permission_type": "read", "description": "查看企业用户信息"},
        {"name": "企业用户创建", "code": "user:enterprise:create", "permission_type": "write", "description": "创建企业用户"},
        {"name": "企业用户更新", "code": "user:enterprise:update", "permission_type": "write", "description": "更新企业用户信息"},
        {"name": "企业用户删除", "code": "user:enterprise:delete", "permission_type": "write", "description": "删除企业用户"},
        
        # 账户管理
        {"name": "账户管理", "code": "account:manage", "permission_type": "read", "description": "账户管理"},
        {"name": "账户查看", "code": "account:read", "permission_type": "read", "description": "查看账户信息"},
        {"name": "账户创建", "code": "account:create", "permission_type": "write", "description": "创建账户"},
        {"name": "账户更新", "code": "account:update", "permission_type": "write", "description": "更新账户信息"},
        {"name": "账户删除", "code": "account:delete", "permission_type": "write", "description": "删除账户"},
        {"name": "个人账户查看", "code": "account:personal:read", "permission_type": "read", "description": "查看个人账户信息"},
        {"name": "个人账户创建", "code": "account:personal:create", "permission_type": "write", "description": "创建个人账户"},
        {"name": "个人账户更新", "code": "account:personal:update", "permission_type": "write", "description": "更新个人账户信息"},
        {"name": "个人账户删除", "code": "account:personal:delete", "permission_type": "write", "description": "删除个人账户"},
        {"name": "企业账户查看", "code": "account:enterprise:read", "permission_type": "read", "description": "查看企业账户信息"},
        {"name": "企业账户创建", "code": "account:enterprise:create", "permission_type": "write", "description": "创建企业账户"},
        {"name": "企业账户更新", "code": "account:enterprise:update", "permission_type": "write", "description": "更新企业账户信息"},
        {"name": "企业账户删除", "code": "account:enterprise:delete", "permission_type": "write", "description": "删除企业账户"},
        
        # 内容管理
        {"name": "内容管理", "code": "content:manage", "permission_type": "read", "description": "内容管理"},
        {"name": "内容查看", "code": "content:read", "permission_type": "read", "description": "查看内容信息"},
        {"name": "内容创建", "code": "content:create", "permission_type": "write", "description": "创建内容"},
        {"name": "内容更新", "code": "content:update", "permission_type": "write", "description": "更新内容信息"},
        {"name": "内容删除", "code": "content:delete", "permission_type": "write", "description": "删除内容"},
        {"name": "菜品查看", "code": "dish:read", "permission_type": "read", "description": "查看菜品信息"},
        {"name": "菜品创建", "code": "dish:create", "permission_type": "write", "description": "创建菜品"},
        {"name": "菜品更新", "code": "dish:update", "permission_type": "write", "description": "更新菜品信息"},
        {"name": "菜品删除", "code": "dish:delete", "permission_type": "write", "description": "删除菜品"},
        {"name": "文章查看", "code": "article:read", "permission_type": "read", "description": "查看文章信息"},
        {"name": "文章创建", "code": "article:create", "permission_type": "write", "description": "创建文章"},
        {"name": "文章更新", "code": "article:update", "permission_type": "write", "description": "更新文章信息"},
        {"name": "文章删除", "code": "article:delete", "permission_type": "write", "description": "删除文章"},
        {"name": "菜单查看", "code": "menu:read", "permission_type": "read", "description": "查看菜单信息"},
        {"name": "菜单创建", "code": "menu:create", "permission_type": "write", "description": "创建菜单"},
        {"name": "菜单更新", "code": "menu:update", "permission_type": "write", "description": "更新菜单信息"},
        {"name": "菜单删除", "code": "menu:delete", "permission_type": "write", "description": "删除菜单"},
        
        # 产品管理
        {"name": "产品管理", "code": "product:manage", "permission_type": "read", "description": "产品管理"},
        {"name": "产品查看", "code": "product:read", "permission_type": "read", "description": "查看产品信息"},
        {"name": "产品创建", "code": "product:create", "permission_type": "write", "description": "创建产品"},
        {"name": "产品更新", "code": "product:update", "permission_type": "write", "description": "更新产品信息"},
        {"name": "产品删除", "code": "product:delete", "permission_type": "write", "description": "删除产品"},
        {"name": "产品分类查看", "code": "category:read", "permission_type": "read", "description": "查看产品分类信息"},
        {"name": "产品分类创建", "code": "category:create", "permission_type": "write", "description": "创建产品分类"},
        {"name": "产品分类更新", "code": "category:update", "permission_type": "write", "description": "更新产品分类信息"},
        {"name": "产品分类删除", "code": "category:delete", "permission_type": "write", "description": "删除产品分类"},
        
        # 设置管理
        {"name": "设置管理", "code": "setting:manage", "permission_type": "read", "description": "设置管理"},
        {"name": "预订规则查看", "code": "rule:read", "permission_type": "read", "description": "查看预订规则信息"},
        {"name": "预订规则创建", "code": "rule:create", "permission_type": "write", "description": "创建预订规则"},
        {"name": "预订规则更新", "code": "rule:update", "permission_type": "write", "description": "更新预订规则信息"},
        {"name": "预订规则删除", "code": "rule:delete", "permission_type": "write", "description": "删除预订规则"},
        {"name": "价格策略查看", "code": "pricing:read", "permission_type": "read", "description": "查看价格策略信息"},
        {"name": "价格策略创建", "code": "pricing:create", "permission_type": "write", "description": "创建价格策略"},
        {"name": "价格策略更新", "code": "pricing:update", "permission_type": "write", "description": "更新价格策略信息"},
        {"name": "价格策略删除", "code": "pricing:delete", "permission_type": "write", "description": "删除价格策略"},
        {"name": "赠送规则查看", "code": "gift:read", "permission_type": "read", "description": "查看赠送规则信息"},
        {"name": "赠送规则创建", "code": "gift:create", "permission_type": "write", "description": "创建赠送规则"},
        {"name": "赠送规则更新", "code": "gift:update", "permission_type": "write", "description": "更新赠送规则信息"},
        {"name": "赠送规则删除", "code": "gift:delete", "permission_type": "write", "description": "删除赠送规则"},
        
        # 优惠券管理
        {"name": "优惠券管理", "code": "coupon:manage", "permission_type": "read", "description": "优惠券管理"},
        {"name": "优惠券查看", "code": "coupon:read", "permission_type": "read", "description": "查看优惠券信息"},
        {"name": "优惠券创建", "code": "coupon:create", "permission_type": "write", "description": "创建优惠券"},
        {"name": "优惠券更新", "code": "coupon:update", "permission_type": "write", "description": "更新优惠券信息"},
        {"name": "优惠券删除", "code": "coupon:delete", "permission_type": "write", "description": "删除优惠券"},
        {"name": "优惠券使用查看", "code": "coupon:usage:read", "permission_type": "read", "description": "查看优惠券使用信息"},
        {"name": "优惠券使用创建", "code": "coupon:usage:create", "permission_type": "write", "description": "创建优惠券使用"},
        {"name": "优惠券使用更新", "code": "coupon:usage:update", "permission_type": "write", "description": "更新优惠券使用信息"},
        {"name": "优惠券使用删除", "code": "coupon:usage:delete", "permission_type": "write", "description": "删除优惠券使用"},
        
        # 订单管理
        {"name": "订单管理", "code": "order:manage", "permission_type": "read", "description": "订单管理"},
        {"name": "订单查看", "code": "order:read", "permission_type": "read", "description": "查看订单信息"},
        {"name": "订单创建", "code": "order:create", "permission_type": "write", "description": "创建订单"},
        {"name": "订单更新", "code": "order:update", "permission_type": "write", "description": "更新订单信息"},
        {"name": "订单删除", "code": "order:delete", "permission_type": "write", "description": "删除订单"},
        {"name": "预订订单查看", "code": "order:reservation:read", "permission_type": "read", "description": "查看预订订单信息"},
        {"name": "预订订单创建", "code": "order:reservation:create", "permission_type": "write", "description": "创建预订订单"},
        {"name": "预订订单更新", "code": "order:reservation:update", "permission_type": "write", "description": "更新预订订单信息"},
        {"name": "预订订单删除", "code": "order:reservation:delete", "permission_type": "write", "description": "删除预订订单"},
        
        # 统计管理
        {"name": "统计管理", "code": "statistic:manage", "permission_type": "read", "description": "统计管理"},
        {"name": "统计查看", "code": "statistic:read", "permission_type": "read", "description": "查看统计信息"},
        {"name": "统计创建", "code": "statistic:create", "permission_type": "write", "description": "创建统计"},
        {"name": "统计更新", "code": "statistic:update", "permission_type": "write", "description": "更新统计信息"},
        {"name": "统计删除", "code": "statistic:delete", "permission_type": "write", "description": "删除统计"},
        {"name": "预订统计查看", "code": "statistic:reservation:read", "permission_type": "read", "description": "查看预订统计信息"},
        {"name": "预订统计创建", "code": "statistic:reservation:create", "permission_type": "write", "description": "创建预订统计"},
        {"name": "预订统计更新", "code": "statistic:reservation:update", "permission_type": "write", "description": "更新预订统计信息"},
        {"name": "预订统计删除", "code": "statistic:reservation:delete", "permission_type": "write", "description": "删除预订统计"},
        
        # 个人信息管理
        {"name": "个人信息管理", "code": "profile:manage", "permission_type": "read", "description": "个人信息管理"},
        {"name": "个人信息查看", "code": "profile:read", "permission_type": "read", "description": "查看个人信息信息"},
        {"name": "个人信息更新", "code": "profile:update", "permission_type": "write", "description": "更新个人信息信息"},
    ]
    
    created_permissions = []
    for perm_data in permissions_data:
        # 检查权限是否已存在
        existing_perm = db.query(permission_dao.model).filter(
            permission_dao.model.code == perm_data["code"]
        ).first()
        
        if not existing_perm:
            permission = PermissionCreate(**perm_data)
            created_perm = permission_dao.create(session=db, permission=permission)
            created_permissions.append(created_perm)
            print(f"创建权限: {perm_data['name']} ({perm_data['code']})")
        else:
            created_permissions.append(existing_perm)
            print(f"权限已存在: {perm_data['name']} ({perm_data['code']})")
    
    return created_permissions


def init_roles(db: Session, permissions):
    """初始化角色数据"""
    # 延迟导入以避免配置加载问题
    from app.dao.admin import role_dao, permission_dao
    from app.schemas.admin import RoleCreate
    
    roles_data = [
        {
            "name": "超级管理员",
            "description": "拥有所有权限的超级管理员",
            "permission_codes": [perm.code for perm in permissions]  # 所有权限
        }
    ]
    
    created_roles = []
    for role_data in roles_data:
        # 检查角色是否已存在
        existing_role = db.query(role_dao.model).filter(
            role_dao.model.name == role_data["name"]
        ).first()
        
        if not existing_role:
            role = RoleCreate(
                name=role_data["name"],
                description=role_data["description"]
            )
            created_role = role_dao.create(session=db, role=role)
            
            # 为角色分配权限
            for perm_code in role_data["permission_codes"]:
                permission = db.query(permission_dao.model).filter(
                    permission_dao.model.code == perm_code
                ).first()
                if permission:
                    role_dao.add_permission(session=db, role_id=created_role.id, permission_id=permission.id)
            
            created_roles.append(created_role)
            print(f"创建角色: {role_data['name']}")
        else:
            created_roles.append(existing_role)
            print(f"角色已存在: {role_data['name']}")
    
    return created_roles


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="初始化RBAC权限数据脚本")
    parser.add_argument("--use-stage-env", action="store_true", 
                       help="使用.env.stage中的数据库连接（默认使用当前环境配置）")
    args = parser.parse_args()
    
    print("开始初始化RBAC权限数据...")
    
    db = get_database_session(args.use_stage_env)
    try:
        # 初始化权限
        permissions = init_permissions(db)
        print(f"\n权限初始化完成，共处理 {len(permissions)} 个权限")
        
        # 初始化角色
        roles = init_roles(db, permissions)
        print(f"\n角色初始化完成，共处理 {len(roles)} 个角色")
        
        print("\n✅ RBAC权限数据初始化完成！")
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    main() 