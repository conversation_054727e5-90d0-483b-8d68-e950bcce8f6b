//service/index.js代码
// 封装成类 
class Request {
  constructor(baseURL) {
    this.baseURL = baseURL
  }
  
  // 获取最新的baseURL
  getBaseURL() {
    const app = getApp()
    if (app && app.globalData && app.globalData.baseUrl) {
      return app.globalData.baseUrl
    }
    return this.baseURL // 使用构造时设置的默认值
  }
  
  async request(options) {
    const { url } = options
    return new Promise((resolve, reject) => {
      wx.request({
        ...options,
        url: this.getBaseURL() + url,
        success: (res) => {
          // 处理 token 过期情况
          if (res.statusCode === 401 || 
             (res.data && res.data.message === "未登录")) {
            console.log("token 已过期，正在重新登录")
            
            // 清除本地存储的 token
            wx.removeStorageSync('token')
            
            // 调用 App 实例的 monitor_token 方法重新登录
            const app = getApp()
            if (app && app.monitor_token) {
              app.monitor_token().then(() => {
                // 重新发起当前请求
                const token = wx.getStorageSync('token')
                if (token) {
                  options.header = options.header || {}
                  options.header.token = token
                  this.request(options).then(resolve).catch(reject)
                } else {
                  reject(new Error('重新登录失败'))
                }
              }).catch(err => {
                console.error('重新登录失败', err)
                reject(err)
              })
            } else {
              // 如果无法获取 app 实例，则跳转到登录页
              wx.navigateTo({
                url: '/pages/index/index'
              })
              reject(new Error('token 过期，请重新登录'))
            }
          } else {
            resolve(res.data)
          }
        },
        fail: (err) => {
          console.log("err:", err)
          reject(err)
        }
      })
    })
  }
  get(options) {
    return this.request({ ...options, method: "get" })
  }
  post(options) {
    return this.request({ ...options, method: "post" })
  }
}
// 使用默认值初始化，但实际使用时会从app.globalData获取
export const loginRequest = new Request("https://vegan.yiheship.com/api/v1/wx")
