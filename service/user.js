import { loginRequest } from "./index";

// 获取用户信息
export const getUserInfo = () => {
  console.log('获取用户!!!!!! ROBBY - 01 !!!!!!信息成功')
  return new Promise((resolve, reject) => {
    // 获取本地存储的token
    const token = wx.getStorageSync('token') || '';
    
    if (!token) {
      reject(new Error('未登录'));
      return;
    }

    const app = getApp();
    
    // 使用封装好的请求方法获取用户信息
    loginRequest.get({
      url: '/user',
      header: {
        token: token
      }
    }).then(res => {
      if (res.status === 200) {
        console.log('获取用户!!!!!! ROBBY !!!!!!信息成功', res.userInfo)
        app.globalData.userInfo = res.userInfo
        app.globalData.isPhoneAuthorized = true
        app.globalData.isLoggedIn = true
        app.globalData.lastAuthTime = Date.now()
        console.log('已保存用户信息到全局', app.globalData.userInfo)
        resolve(res.userInfo);
      } else {
        reject(new Error(res.message));
      }
    }).catch(err => {
      reject(err);
    });
  });
};

// 更新用户资料
export const updateUserInfo = (userInfo) => {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync('token') || '';
    
    if (!token) {
      reject(new Error('未登录'));
      return;
    }
    
    loginRequest.post({
      url: '/user-info',
      data: { userInfo },
      header: {
        token: token
      }
    }).then(res => {
      if (res.status === 200) {
        resolve(res);
      } else {
        reject(new Error(res.message));
      }
    }).catch(err => {
      reject(err);
    });
  });
};

// 检查是否登录（优化版本）
export const checkLogin = () => {
  return new Promise(async (resolve, reject) => {
    const app = getApp();
    const token = wx.getStorageSync('token') || '';
    
    if (!token) {
      resolve(false);
      return;
    }
    
    // 使用全局状态，避免频繁请求
    // 如果已登录且上次验证在10分钟内，直接返回true
    if (app.globalData.isLoggedIn && 
        Date.now() - app.globalData.lastAuthTime < 30 * 60 * 1000) {
      resolve(true);
      return;
    }
    
    // 如果已经有验证正在进行中，等待该验证完成
    if (app.globalData.authInProgress) {
      // 等待验证完成后返回结果
      const checkInterval = setInterval(() => {
        if (!app.globalData.authInProgress) {
          clearInterval(checkInterval);
          resolve(app.globalData.isLoggedIn);
        }
      }, 100);
      return;
    }
    
    // 标记验证开始
    app.globalData.authInProgress = true;
    
    try {
      // 请求服务器验证token
      const res = await loginRequest.post({
        url: '/auth',
        header: {
          token: token
        }
      });
      
      // 标记验证结束
      app.globalData.authInProgress = false;
      
      if (res.message === "已登录") {
        app.globalData.isLoggedIn = true;
        app.globalData.lastAuthTime = Date.now();
        if (res.userInfo) {
          app.globalData.userInfo = res.userInfo;
          app.globalData.isPhoneAuthorized = true;
        }
        resolve(true);
      } else {
        app.globalData.isLoggedIn = false;
        resolve(false);
      }
    } catch (error) {
      // 标记验证结束
      app.globalData.authInProgress = false;
      // 请求失败，可能是token过期或其他网络错误
      app.globalData.isLoggedIn = false;
      resolve(false);
    }
  });
}; 