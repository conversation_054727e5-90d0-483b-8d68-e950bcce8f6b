#!/usr/bin/env python
"""
创建和初始化stage环境数据库的脚本
该脚本会创建一个stage环境的数据库，并运行数据库迁移，添加测试数据
"""
import argparse
import random
import string
import sys
from pathlib import Path
import datetime
import time

# 添加项目根目录到Python路径
root_dir = Path(__file__).parent.parent
sys.path.insert(0, str(root_dir))

# 导入sqlalchemy相关模块
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from alembic import command
from alembic.config import Config

# 导入项目模型和工具
from app.models.enum import Status
from app.models.user import User, PersonalUser, Enterprise, EnterpriseUserRelation
from app.models.admin import Admin
from app.models.content import Content, ContentType, Dish
from app.models.account import RegularAccount, GiftAccount, TransactionType, AccountTransaction
from app.models.order import Order, OrderType, OrderStatus, PaymentStatus, PaymentMethod, RechargeOrder, \
    DirectSaleOrder, ReservationOrder, OrderItem, ReservationStatus as OrderReservationStatus
from app.models.product import Product, ProductType, DirectSaleProduct, ReservationProduct
from app.schemas.user import PersonalUserCreate, EnterpriseCreate
from app.service import user_service
from app.dao.admin import admin_dao
from app.utils.common import get_current_time
from app.core.security import get_password_hash
from app.models.reservation import ReservationStatus as ReservationRequestStatus
from app.models.pricing import PricingStrategy, DiscountStrategy, FullReductionStrategy, MemberPriceStrategy, \
    TimeLimitedStrategy, PricingStrategyType, PricingStrategyScope, MemberLevel
from app.dao.pricing import pricing_strategy_dao, discount_strategy_dao, full_reduction_strategy_dao, \
    time_limited_strategy_dao, member_price_strategy_dao

# Stage环境数据库配置
STAGE_DATABASE_URL = "mysql+pymysql://root:123456@127.0.0.1"
# STAGE_DATABASE_URL = "mysql+pymysql://yh_vegan_user:Fa0RroinKhVt@127.0.0.1"
STAGE_DATABASE_NAME = "yh_vegan_dev_stage"
STAGE_DATABASE_FULL_URL = f"{STAGE_DATABASE_URL}/{STAGE_DATABASE_NAME}?charset=utf8mb4"


def generate_random_string(length=6):
    """生成指定长度的随机字符串"""
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))


def create_stage_database():
    """创建stage环境数据库"""
    print(f"开始创建stage环境数据库: {STAGE_DATABASE_NAME}")

    # 创建数据库引擎连接
    engine = create_engine(STAGE_DATABASE_URL)

    try:
        # 创建数据库
        with engine.connect() as conn:
            # 先删除可能存在的数据库
            conn.execute(text(f"DROP DATABASE IF EXISTS {STAGE_DATABASE_NAME}"))
            # 创建新数据库
            conn.execute(
                text(f"CREATE DATABASE {STAGE_DATABASE_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"))
            print(f"数据库 {STAGE_DATABASE_NAME} 创建成功!")
    except Exception as e:
        print(f"创建数据库失败: {e}")
        sys.exit(1)
    finally:
        engine.dispose()


def run_migrations():
    """运行数据库迁移"""
    print("开始运行数据库迁移...")

    try:
        # 配置Alembic
        alembic_cfg = Config("alembic.ini")
        alembic_cfg.set_main_option("sqlalchemy.url", STAGE_DATABASE_FULL_URL)

        # 运行迁移到最新版本
        command.upgrade(alembic_cfg, "head")
        print("数据库迁移完成!")
    except Exception as e:
        print(f"数据库迁移失败: {e}")
        sys.exit(1)


def run_upgrade(revision="head"):
    """运行数据库升级操作
    
    Args:
        revision (str): 要升级到的版本，默认为"head"（升级到最新版本）
    """
    print(f"开始运行数据库升级操作，目标版本: {revision}")

    try:
        # 配置Alembic
        alembic_cfg = Config("alembic.ini")
        alembic_cfg.set_main_option("sqlalchemy.url", STAGE_DATABASE_FULL_URL)

        # 运行升级操作
        command.upgrade(alembic_cfg, revision)
        print(f"数据库升级到版本 {revision} 完成!")
    except Exception as e:
        print(f"数据库升级失败: {e}")
        sys.exit(1)


def run_downgrade(revision="base"):
    """运行数据库回退操作
    
    Args:
        revision (str): 要回退到的版本，默认为"base"（回退到初始状态）
    """
    print(f"开始运行数据库回退操作，目标版本: {revision}")

    try:
        # 配置Alembic
        alembic_cfg = Config("alembic.ini")
        alembic_cfg.set_main_option("sqlalchemy.url", STAGE_DATABASE_FULL_URL)

        # 预检查：检查数据库连接
        print("检查数据库连接...")
        engine = create_engine(STAGE_DATABASE_FULL_URL)
        try:
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            print("✓ 数据库连接正常")
        except Exception as conn_error:
            print(f"✗ 数据库连接失败: {conn_error}")
            print(f"请检查数据库URL: {STAGE_DATABASE_FULL_URL}")
            raise
        finally:
            engine.dispose()

        # 预检查：显示当前版本
        print("获取当前数据库版本...")
        try:
            from alembic.runtime.migration import MigrationContext
            from alembic.script import ScriptDirectory
            
            engine = create_engine(STAGE_DATABASE_FULL_URL)
            with engine.connect() as conn:
                context = MigrationContext.configure(conn)
                current_rev = context.get_current_revision()
                print(f"✓ 当前数据库版本: {current_rev if current_rev else '未初始化'}")
                
                # 检查目标版本是否有效
                if revision != "base" and revision.startswith("-") == False and revision != current_rev:
                    script_dir = ScriptDirectory.from_config(alembic_cfg)
                    try:
                        script_dir.get_revision(revision)
                        print(f"✓ 目标版本 {revision} 存在")
                    except Exception as rev_error:
                        print(f"✗ 警告: 目标版本 {revision} 可能不存在: {rev_error}")
            engine.dispose()
        except Exception as check_error:
            print(f"✗ 获取版本信息失败: {check_error}")
            print("继续执行downgrade操作...")

        # 执行回退操作
        print(f"执行回退操作到版本: {revision}")
        command.downgrade(alembic_cfg, revision)
        print(f"✓ 数据库回退到版本 {revision} 完成!")
        
        # 验证回退结果
        print("验证回退结果...")
        try:
            engine = create_engine(STAGE_DATABASE_FULL_URL)
            with engine.connect() as conn:
                context = MigrationContext.configure(conn)
                new_rev = context.get_current_revision()
                print(f"✓ 回退后数据库版本: {new_rev if new_rev else '未初始化'}")
            engine.dispose()
        except Exception as verify_error:
            print(f"✗ 验证回退结果失败: {verify_error}")
        
    except Exception as e:
        import traceback
        print(f"\n{'='*60}")
        print("❌ 数据库回退操作失败!")
        print(f"{'='*60}")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误信息: {str(e)}")
        print(f"目标版本: {revision}")
        print(f"数据库URL: {STAGE_DATABASE_FULL_URL}")
        print(f"\n详细错误堆栈:")
        print("-" * 60)
        traceback.print_exc()
        print("-" * 60)
        
        # 提供常见错误的解决建议
        error_message = str(e).lower()
        print(f"\n💡 排查建议:")
        
        if "can't locate revision identified by" in error_message:
            print("- 指定的版本号不存在，请使用 --history 查看可用版本")
            print("- 检查版本号是否正确拼写")
        elif "connection" in error_message or "access denied" in error_message:
            print("- 检查数据库连接配置是否正确")
            print("- 确认数据库服务是否正在运行")
            print("- 验证数据库用户名和密码")
        elif "table" in error_message and "doesn't exist" in error_message:
            print("- 数据库迁移表可能不存在，请先运行初始化迁移")
            print("- 尝试先运行 --upgrade head 初始化迁移环境")
        elif "syntax error" in error_message:
            print("- 迁移脚本可能存在语法错误")
            print("- 检查对应版本的迁移文件")
        elif "foreign key constraint" in error_message:
            print("- 外键约束导致回退失败")
            print("- 可能需要先清理相关数据或调整迁移脚本")
        elif isinstance(e, AssertionError) and "format_constraint" in traceback.format_exc():
            print("🔥 检测到迁移脚本中的外键约束名称问题!")
            print("- 错误原因: 在 downgrade 操作中删除外键约束时没有指定约束名称")
            print("- 问题文件: 从错误堆栈可以看出是迁移文件中的 op.drop_constraint(None, ...) 调用")
            print("")
            print("🛠️  解决方案:")
            print("1. 找到出错的迁移文件:")
            
            # 从堆栈跟踪中提取迁移文件信息
            tb_lines = traceback.format_exc().split('\n')
            for line in tb_lines:
                if 'alembic/versions/' in line and '.py' in line:
                    migration_file = line.split('File "')[1].split('",')[0] if 'File "' in line else ""
                    if migration_file:
                        print(f"   文件: {migration_file}")
                        break
            
            print("2. 在该文件的 downgrade() 函数中找到类似这样的代码:")
            print("   op.drop_constraint(None, 'table_name', type_='foreignkey')")
            print("")
            print("3. 修复方法 - 将 None 替换为具体的约束名称:")
            print("   方案A: 手动指定约束名称")
            print("     op.drop_constraint('fk_constraint_name', 'table_name', type_='foreignkey')")
            print("")
            print("   方案B: 从数据库中查询约束名称")
            print("     # 先查询约束名称")
            print("     from sqlalchemy import inspect")
            print("     conn = op.get_bind()")
            print("     inspector = inspect(conn)")
            print("     fks = inspector.get_foreign_keys('table_name')")
            print("     if fks:")
            print("         constraint_name = fks[0]['name']")
            print("         op.drop_constraint(constraint_name, 'table_name', type_='foreignkey')")
            print("")
            print("   方案C: 使用批量操作模式 (推荐)")
            print("     with op.batch_alter_table('table_name') as batch_op:")
            print("         batch_op.drop_constraint('fk_constraint_name', type_='foreignkey')")
            print("")
            print("4. 如果不知道约束名称，可以:")
            print(f"   - 连接到数据库 {STAGE_DATABASE_FULL_URL}")
            print("   - 执行: SHOW CREATE TABLE table_name; 查看约束名称")
            print("   - 或执行: SELECT * FROM information_schema.KEY_COLUMN_USAGE WHERE TABLE_NAME='table_name';")
        else:
            print("- 检查alembic.ini配置文件")
            print("- 确认迁移文件是否存在和完整")
            print("- 尝试使用 --current 和 --history 查看当前状态")
        
        print(f"{'='*60}")
        sys.exit(1)


def show_migration_history():
    """显示数据库迁移历史记录"""
    print("查看数据库迁移历史记录...")

    try:
        # 配置Alembic
        alembic_cfg = Config("alembic.ini")
        alembic_cfg.set_main_option("sqlalchemy.url", STAGE_DATABASE_FULL_URL)

        # 显示迁移历史
        command.history(alembic_cfg)
    except Exception as e:
        print(f"查看迁移历史失败: {e}")
        sys.exit(1)


def show_current_revision():
    """显示当前数据库版本"""
    print("查看当前数据库版本...")

    try:
        # 配置Alembic
        alembic_cfg = Config("alembic.ini")
        alembic_cfg.set_main_option("sqlalchemy.url", STAGE_DATABASE_FULL_URL)

        # 显示当前版本
        command.current(alembic_cfg)
    except Exception as e:
        print(f"查看当前版本失败: {e}")
        sys.exit(1)


def show_table_constraints(table_name=None):
    """显示数据库表的约束信息
    
    Args:
        table_name (str): 表名，如果为None则显示所有表的约束
    """
    print(f"查看数据库表约束信息{'（表: ' + table_name + '）' if table_name else '（所有表）'}...")

    try:
        # 创建数据库连接
        engine = create_engine(STAGE_DATABASE_FULL_URL)
        
        with engine.connect() as conn:
            if table_name:
                # 查询指定表的约束信息
                constraint_query = text("""
                    SELECT 
                        TABLE_NAME,
                        CONSTRAINT_NAME,
                        CONSTRAINT_TYPE,
                        COLUMN_NAME,
                        REFERENCED_TABLE_NAME,
                        REFERENCED_COLUMN_NAME
                    FROM information_schema.KEY_COLUMN_USAGE kcu
                    LEFT JOIN information_schema.TABLE_CONSTRAINTS tc 
                        ON kcu.CONSTRAINT_NAME = tc.CONSTRAINT_NAME 
                        AND kcu.TABLE_SCHEMA = tc.TABLE_SCHEMA
                    WHERE kcu.TABLE_SCHEMA = DATABASE() 
                        AND kcu.TABLE_NAME = :table_name
                    ORDER BY kcu.TABLE_NAME, kcu.CONSTRAINT_NAME
                """)
                result = conn.execute(constraint_query, {"table_name": table_name})
            else:
                # 查询所有表的外键约束信息
                constraint_query = text("""
                    SELECT 
                        kcu.TABLE_NAME,
                        kcu.CONSTRAINT_NAME,
                        tc.CONSTRAINT_TYPE,
                        kcu.COLUMN_NAME,
                        kcu.REFERENCED_TABLE_NAME,
                        kcu.REFERENCED_COLUMN_NAME
                    FROM information_schema.KEY_COLUMN_USAGE kcu
                    LEFT JOIN information_schema.TABLE_CONSTRAINTS tc 
                        ON kcu.CONSTRAINT_NAME = tc.CONSTRAINT_NAME 
                        AND kcu.TABLE_SCHEMA = tc.TABLE_SCHEMA
                    WHERE kcu.TABLE_SCHEMA = DATABASE() 
                        AND tc.CONSTRAINT_TYPE = 'FOREIGN KEY'
                    ORDER BY kcu.TABLE_NAME, kcu.CONSTRAINT_NAME
                """)
                result = conn.execute(constraint_query)
            
            rows = result.fetchall()
            
            if not rows:
                print(f"✓ 未找到{'表 ' + table_name + ' 的' if table_name else ''}约束信息")
            else:
                print(f"✓ 找到 {len(rows)} 个约束:")
                print("-" * 100)
                print(f"{'表名':<20} {'约束名':<30} {'约束类型':<15} {'列名':<20} {'引用表':<20} {'引用列':<15}")
                print("-" * 100)
                
                for row in rows:
                    table = row[0] or ''
                    constraint = row[1] or ''
                    con_type = row[2] or ''
                    column = row[3] or ''
                    ref_table = row[4] or ''
                    ref_column = row[5] or ''
                    
                    print(f"{table:<20} {constraint:<30} {con_type:<15} {column:<20} {ref_table:<20} {ref_column:<15}")
        
        engine.dispose()
        
    except Exception as e:
        print(f"查看约束信息失败: {e}")
        sys.exit(1)


def create_pricing_strategies(db):
    """创建各种定价策略子类型的测试数据"""
    print("开始创建定价策略测试数据...")

    try:
        # 获取所有直销产品
        direct_sale_products = db.query(DirectSaleProduct).all()
        if not direct_sale_products:
            print("没有找到直销产品，跳过创建定价策略")
            return

        # 确保有足够的产品来分配策略
        if len(direct_sale_products) < 20:
            print(f"直销产品数量较少 ({len(direct_sale_products)}个)，部分策略可能无法分配足够的产品")

        # 1. 创建5个折扣策略
        print("创建折扣策略...")
        discount_strategies = [
            DiscountStrategy(
                name=f"折扣{i + 1}折",
                description=f"商品打{i + 1}折优惠",
                scope=PricingStrategyScope.PRODUCT,
                is_mutual_exclusive=True if i % 2 == 0 else False,
                status=Status.ACTIVE,
                type=PricingStrategyType.DISCOUNT,
                discount_rate=round((i + 5) / 10, 2),  # 0.5-0.9的折扣率
                min_amount=50 * (i + 1),  # 50-250的最低消费
                max_discount=100 if i % 2 == 0 else -1  # 部分有最大优惠限制
            ) for i in range(5)
        ]

        for strategy in discount_strategies:
            db.add(strategy)

        db.flush()
        print("成功创建5个折扣策略")

        # 2. 创建5个满减策略
        print("创建满减策略...")
        full_reduction_strategies = [
            FullReductionStrategy(
                name=f"满{(i + 1) * 100}减{(i + 1) * 10}",
                description=f"订单满{(i + 1) * 100}元减{(i + 1) * 10}元",
                scope=PricingStrategyScope.ORDER if i % 2 == 0 else PricingStrategyScope.PRODUCT,
                is_mutual_exclusive=True if i % 2 == 1 else False,
                status=Status.ACTIVE,
                type=PricingStrategyType.FULL_REDUCTION,
                full_amount=(i + 1) * 100,  # 100-500的满额条件
                reduction_amount=(i + 1) * 10  # 10-50的减免金额
            ) for i in range(5)
        ]

        for strategy in full_reduction_strategies:
            db.add(strategy)

        db.flush()
        print("成功创建5个满减策略")

        # 3. 创建5个会员价格策略
        print("创建会员价格策略...")
        # 会员等级顺序
        member_levels = [MemberLevel.BASIC, MemberLevel.PREMIUM, MemberLevel.VIP,
                         MemberLevel.BASIC, MemberLevel.PREMIUM]

        member_price_strategies = [
            MemberPriceStrategy(
                name=f"{member_levels[i].value}会员特价",
                description=f"{member_levels[i].value}等级会员专享价格",
                scope=PricingStrategyScope.PRODUCT,
                is_mutual_exclusive=True,
                status=Status.ACTIVE,
                type=PricingStrategyType.MEMBER_PRICE,
                member_level=member_levels[i],
                price=random.randint(50, 200) + random.random()  # 50-200的随机会员价
            ) for i in range(5)
        ]

        for strategy in member_price_strategies:
            db.add(strategy)

        db.flush()
        print("成功创建5个会员价格策略")

        # 4. 创建5个限时特价策略
        print("创建限时特价策略...")
        # 生成限时特价的起止时间
        from datetime import timedelta

        time_limited_strategies = []
        for i in range(5):
            # 特价开始时间（未来1-10天）
            start_time = get_current_time() + timedelta(days=i)
            # 特价结束时间（开始后7-30天）
            end_time = start_time + timedelta(days=random.randint(7, 30))

            strategy = TimeLimitedStrategy(
                name=f"限时特价{i + 1}",
                description=f"限时优惠，特价截止至{end_time.strftime('%Y-%m-%d')}",
                scope=PricingStrategyScope.PRODUCT,
                is_mutual_exclusive=True,
                status=Status.ACTIVE,
                type=PricingStrategyType.TIME_LIMITED,
                start_time=start_time,
                end_time=end_time,
                special_price=random.randint(30, 150) + random.random(),  # 30-150的随机特价
                stock_limit=random.randint(10, 100)  # 10-100的库存限制
            )
            time_limited_strategies.append(strategy)

        for strategy in time_limited_strategies:
            db.add(strategy)

        db.flush()
        print("成功创建5个限时特价策略")

        # 5. 为策略分配产品
        print("为策略分配产品...")

        all_strategies = (
                discount_strategies +
                full_reduction_strategies +
                member_price_strategies +
                time_limited_strategies
        )

        # 为每个策略随机分配2-5个产品
        for i, strategy in enumerate(all_strategies):
            # 随机选择2-5个产品
            product_count = random.randint(2, 5)
            selected_products = random.sample(direct_sale_products,
                                              min(product_count, len(direct_sale_products)))

            for product in selected_products:
                strategy.products.append(product)

            print(f"  为策略 '{strategy.name}' 分配了 {len(selected_products)} 个产品")

        db.commit()
        print("所有定价策略创建并分配完成!")

    except Exception as e:
        db.rollback()
        print(f"创建定价策略失败: {e}")


def create_test_data():
    """创建测试数据"""
    print("开始准备stage环境测试数据...")

    # 先删除现有数据库并重新创建
    print("先删除现有数据库并重新创建...")
    create_stage_database()

    # 运行数据库迁移
    print("重新运行数据库迁移...")
    run_migrations()

    print("开始添加stage环境测试数据...")

    # 创建数据库会话
    engine = create_engine(STAGE_DATABASE_FULL_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()

    # 添加延迟函数以确保订单号唯一性
    def generate_unique_timestamp():
        """生成唯一的时间戳，并添加小延迟确保唯一性"""
        timestamp = int(datetime.datetime.now().timestamp() * 1000)
        # 添加小延迟以确保每次调用生成不同的时间戳
        time.sleep(0.001)
        return timestamp

    try:
        # 创建管理员用户
        try:
            # 检查管理员是否已存在
            admin = admin_dao.get_by_phone(db, "15766972573")
            if not admin:
                print("创建管理员测试用户...")
                # 手动对密码进行哈希加密
                hashed_password = get_password_hash("123123")

                # 创建管理员实体对象
                admin = Admin(
                    name="系统管理员",
                    username="admin",
                    phone="15766972573",
                    email="<EMAIL>",
                    password=hashed_password,
                    status=Status.ACTIVE,
                    created_at=get_current_time(),
                    updated_at=get_current_time()
                )

                # 添加到数据库
                db.add(admin)
                db.commit()
                db.refresh(admin)
                print("管理员测试用户创建成功!")
        except Exception as e:
            print(f"创建管理员用户失败: {e}")

        # 创建20条随机DISH类型Content
        try:
            print("创建20条随机DISH类型Content...")
            # 清空已有Content记录
            db.query(Content).delete()
            db.commit()

            dish_names = [
                "素炒豆芽", "麻婆豆腐", "红烧素鸡", "素菜包子", "素三鲜水饺",
                "什锦素菜", "素炒时蔬", "素菜春卷", "素菜煎饼", "素菜沙拉",
                "蒸素菜包", "素炒米粉", "素菜粥", "素菜馄饨", "素菜汤面",
                "五谷杂粮饭", "素菜饼", "香菇青菜", "素炒方便面", "凉拌豆皮"
            ]

            dish_contents = [
                "主要食材：豆芽、胡萝卜、青椒",
                "主要食材：豆腐、豆瓣酱、香菇",
                "主要食材：素鸡、番茄酱、洋葱",
                "主要食材：面粉、白菜、香菇",
                "主要食材：面粉、豆腐、香菇、胡萝卜",
                "主要食材：青椒、胡萝卜、洋葱、木耳",
                "主要食材：西兰花、胡萝卜、玉米",
                "主要食材：面皮、豆芽、胡萝卜、木耳",
                "主要食材：面粉、豆芽、葱花",
                "主要食材：生菜、番茄、黄瓜、豆腐",
                "主要食材：面粉、白菜、香菇",
                "主要食材：米粉、豆芽、胡萝卜",
                "主要食材：大米、香菇、胡萝卜",
                "主要食材：面粉、白菜、香菇",
                "主要食材：面条、青菜、香菇",
                "主要食材：糙米、小米、绿豆、红豆、黑米",
                "主要食材：面粉、胡萝卜、香菇",
                "主要食材：香菇、青菜",
                "主要食材：方便面、青菜、胡萝卜",
                "主要食材：豆皮、黄瓜、胡萝卜"
            ]

            # 创建20条DISH类型的Content
            for i in range(20):
                random_idx = i % len(dish_names)
                dish_name = dish_names[random_idx]
                dish_content = dish_contents[random_idx]

                # 添加一些随机性
                name = f"{dish_name}_{generate_random_string(4)}"
                description = f"{dish_content}。特点：适合素食爱好者，健康美味。{generate_random_string(10)}"

                # 创建Content对象
                dish = Dish(
                    name=name,
                    content=description,
                    image=f"uploads/dish_{i + 1}.jpg",
                    thumbnail=f"uploads/thumb_dish_{i + 1}.jpg",
                    sort_order=i + 1,
                    type=ContentType.DISH,  # 使用DISH类型
                    created_at=get_current_time(),
                    updated_at=get_current_time()
                )

                # 添加到数据库
                db.add(dish)

            db.commit()
            print("成功创建20条随机DISH类型Content!")
        except Exception as e:
            print(f"创建随机DISH类型Content失败: {e}")
            db.rollback()

        # 创建30条企业用户记录
        try:
            print("创建30条企业用户测试记录...")

            # 真实素食餐厅和企业名称
            enterprise_names = [
                "如如素食", "梵净山素食", "功德林素食", "净心禅意素食", "善缘素食",
                "佛光素食", "妙善素食", "莲花素食馆", "素食大王", "心莲素食",
                "素缘素食馆", "禅悦素食", "善行素食", "绿野素食", "素约素食",
                "寿光素食", "素食家", "康乐斋素食", "禾香素食", "天然居素食",
                "本来素食", "素可泰素食", "和合素食", "素之源", "素食天地",
                "清心素食", "素缘斋", "素元素素食", "慈悲素食", "素光阴素食"
            ]

            # 企业地址
            addresses = [
                "北京市朝阳区建国路88号", "上海市静安区南京西路1266号", "广州市天河区天河路385号",
                "深圳市福田区深南大道1066号", "成都市锦江区红星路三段99号", "杭州市西湖区文三路478号",
                "南京市鼓楼区中山北路123号", "武汉市江汉区解放大道688号", "重庆市渝中区青年路77号",
                "西安市碑林区南关正街88号", "苏州市姑苏区平江路176号", "天津市和平区南京路21号",
                "青岛市市南区香港中路66号", "大连市中山区人民路16号", "沈阳市和平区太原街2号",
                "长沙市芙蓉区五一大道235号", "济南市历下区泉城路13号", "福州市鼓楼区五四路128号",
                "厦门市思明区湖滨南路12号", "郑州市金水区文化路80号", "合肥市蜀山区长江西路189号",
                "南昌市东湖区八一大道123号", "长春市南关区人民大街2088号", "哈尔滨市南岗区一曼街200号",
                "昆明市盘龙区北京路155号", "贵阳市南明区中华北路95号", "海口市龙华区国贸路56号",
                "兰州市城关区庆阳路77号", "银川市兴庆区凤凰北街65号", "乌鲁木齐市天山区人民路30号"
            ]

            for i in range(30):
                company_name = enterprise_names[i]
                username = f"enterprise{i + 1}"
                business_license = f"STGLE{100000 + i}"
                phone = f"1399{100000 + i}"
                email = f"enterprise{i + 1}@example.com"
                address = addresses[i]

                # 检查企业用户是否已存在
                existing_enterprise = db.query(Enterprise).filter(Enterprise.company_name == company_name).first()
                if not existing_enterprise:
                    enterprise_data = EnterpriseCreate(
                        username=username,
                        company_name=company_name,
                        business_license=business_license,
                        phone=phone,
                        email=email,
                        address=address
                    )
                    enterprise, accounts = user_service.create_enterprise(db, enterprise_data)

                    # 给账户随机充值
                    gift_amount = random.randint(500, 5000)
                    regular_amount = random.randint(200, 2000)

                    gift_account = accounts.get("gift_account", None)
                    regular_account = accounts.get("regular_account", None)
                    if gift_account:
                        gift_account.balance = gift_amount
                        db.commit()
                    if regular_account:
                        regular_account.balance = regular_amount
                        db.commit()

                    print(f"创建企业用户: {company_name}")

            print("成功创建企业用户测试记录!")
        except Exception as e:
            print(f"创建企业用户测试记录失败: {e}")
            db.rollback()

        # 创建25个随机个人用户
        try:
            print("创建25个随机个人用户测试记录...")

            # 常见中文姓氏
            surnames = [
                "李", "王", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴",
                "徐", "孙", "胡", "朱", "高", "林", "何", "郭", "马", "罗",
                "梁", "宋", "郑", "谢", "韩", "唐", "冯", "于", "董", "萧"
            ]

            # 常见中文名字
            names = [
                "伟", "芳", "娜", "秀英", "敏", "静", "丽", "强", "磊", "军",
                "洋", "勇", "艳", "杰", "娟", "涛", "明", "超", "秀兰", "霞",
                "平", "刚", "桂英", "文", "利", "云", "建华", "红", "华", "兰"
            ]

            # 获取所有企业
            enterprises = db.query(Enterprise).all()

            for i in range(25):
                # 生成随机姓名
                surname = random.choice(surnames)
                name = random.choice(names)
                real_name = surname + name

                # 生成随机电话号码
                phone = f"1{random.choice(['3', '5', '7', '8', '9'])}{random.randint(100000000, 999999999)}"

                # 生成随机用户名
                username = f"personal_user_{generate_random_string(6)}"

                # 生成随机ID卡号
                id_card = f"{random.randint(110000, 999999)}{random.randint(1960, 2000)}{random.randint(10, 12)}{random.randint(10, 28)}{random.randint(1000, 9999)}"

                # 生成随机邮箱
                email = f"{username}@example.com"

                # 生成随机地址
                cities = ["北京", "上海", "广州", "深圳", "成都", "杭州", "南京", "武汉", "重庆", "西安"]
                address = f"{random.choice(cities)}市{random.randint(1, 20)}区{random.randint(1, 100)}号"

                # 检查个人用户是否已存在
                existing_user = db.query(PersonalUser).filter(PersonalUser.phone == phone).first()
                if not existing_user:
                    personal_user_data = PersonalUserCreate(
                        username=username,
                        phone=phone,
                        email=email,
                        address=address,
                        real_name=real_name,
                        id_card=id_card,
                        status=Status.ACTIVE
                    )

                    personal_user, accounts = user_service.create_personal_user(db, personal_user_data)

                    # 设置账户随机余额
                    gift_amount = random.randint(50, 1000)
                    regular_amount = random.randint(max(gift_amount, 1000), 10000)

                    gift_account = accounts.get("gift_account", None)
                    regular_account = accounts.get("regular_account", None)
                    if gift_account:
                        gift_account.balance = gift_amount
                        db.commit()
                    if regular_account:
                        regular_account.balance = regular_amount
                        db.commit()

                    # 随机选择一个企业与用户建立关联关系
                    if enterprises:
                        selected_enterprise = random.choice(enterprises)
                        # 创建关联关系
                        relation = EnterpriseUserRelation(
                            enterprise_id=selected_enterprise.id,
                            personal_user_id=personal_user.id,
                            is_admin=random.choice([True, False]),
                            relation_status=Status.ACTIVE,
                            remark=f"随机关联关系 - {real_name}与{selected_enterprise.company_name}"
                        )
                        db.add(relation)
                        db.commit()
                        print(f"创建个人用户: {real_name}, 普通账户余额: {regular_amount}, 赠送账户余额: {gift_amount}, 关联企业: {selected_enterprise.company_name}")
                    else:
                        print(f"创建个人用户: {real_name}, 普通账户余额: {regular_amount}, 赠送账户余额: {gift_amount}, 无关联企业")

            print("成功创建25个随机个人用户测试记录!")
        except Exception as e:
            print(f"创建随机个人用户测试记录失败: {e}")
            db.rollback()

        # 为所有用户创建充值订单和直销订单以及相应的交易记录
        try:
            import datetime

            print("开始为所有用户创建订单和交易记录...")

            # 获取所有个人用户和企业用户
            all_users = db.query(User).all()
            # 获取所有直销产品
            direct_sale_products = db.query(DirectSaleProduct).all()

            # 如果没有直销产品，先创建几个
            if not direct_sale_products:
                print("创建测试直销产品...")
                direct_sale_product_names = [
                    "有机豆腐", "纯素蛋糕", "全麦面包", "素肉饼", "豆奶",
                    "素火腿", "素食零食礼盒", "素食调味料", "素食月饼", "素食干果",
                    "椰子油", "藜麦", "素肠", "素食酱料", "核桃奶",
                    "亚麻籽油", "杏仁奶", "素牛肉", "素鸡块", "素虾",
                    "豆干", "素食饺子", "素食汤料", "坚果混合包", "椰子酸奶",
                    "素香肠", "素食营养粉", "素食蛋白棒", "素食巧克力", "有机蔬菜包"
                ]

                for i in range(30):
                    product = DirectSaleProduct(
                        name=direct_sale_product_names[i],
                        price=random.randint(10, 100) + random.random() * 10,
                        description=f"{direct_sale_product_names[i]} - 优质素食产品，健康美味。",
                        stock=random.randint(50, 500),
                        status=Status.ACTIVE,
                        type=ProductType.DIRECT_SALE,
                        shipping_fee=random.randint(0, 10)
                    )
                    db.add(product)

                db.commit()
                direct_sale_products = db.query(DirectSaleProduct).all()

            # 创建30个预订产品
            reservation_products = db.query(ReservationProduct).all()
            if not reservation_products:
                print("创建测试预订产品...")
                from datetime import timedelta

                reservation_product_names = [
                    "素食厨艺课程", "素食烘焙班", "蔬菜种植工作坊", "素食餐厅体验", "素食宴会预订",
                    "素食烹饪培训", "素食营养咨询", "素食养生课程", "有机农场参观", "素食品鉴会",
                    "蔬菜采摘活动", "素食文化讲座", "素食瑜伽课程", "素食生活方式指导", "素食健康评估",
                    "素食主题派对", "素食餐饮规划", "素食旅行体验", "素食私厨上门", "素食产品定制",
                    "素食节日礼盒预订", "素食亲子活动", "素食减肥计划", "素食禅修课程", "素食美食节门票",
                    "素食创业指导", "素食社区活动", "素食摄影课程", "素食企业团建", "素食礼品卡"
                ]

                for i in range(30):
                    # 生成随机的预订截止时间（未来1-30天）
                    reservation_deadline = get_current_time() + timedelta(days=random.randint(1, 30))
                    # 生成随机的取消截止时间（预订截止时间前1-3天）
                    cancellation_days = random.randint(1, 3)
                    cancellation_deadline = reservation_deadline - timedelta(days=cancellation_days)

                    product = ReservationProduct(
                        name=reservation_product_names[i],
                        price=random.randint(100, 1000) + random.random() * 100,
                        description=f"{reservation_product_names[i]} - 精品素食体验活动，需提前预订。",
                        stock=random.randint(5, 50),
                        status=Status.ACTIVE,
                        type=ProductType.RESERVATION,
                        reservation_fee=random.randint(10, 100) + random.random() * 10,
                        max_reservations=random.randint(10, 100),
                        reservation_deadline=reservation_deadline,
                        cancellation_deadline=cancellation_deadline,
                        is_approval_required=random.choice([True, False])
                    )
                    db.add(product)

                db.commit()
                print("成功创建测试预订产品!")

            # 为预订产品创建预订规则
            from app.models.rule import Rule, RuleItem, RuleType
            rules = db.query(Rule).filter(Rule.type == RuleType.RESERVATION).all()
            if not rules:
                print("创建测试预订规则...")

                # 获取所有预订产品
                reservation_products = db.query(ReservationProduct).all()

                # 创建3种通用预订规则
                rule_types = [
                    {"name": "标准预订规则", "description": "标准预订时段，适用于大多数预订产品"},
                    {"name": "高峰期预订规则", "description": "适用于周末和节假日的高峰期预订"},
                    {"name": "特殊活动预订规则", "description": "适用于特殊活动和限时体验"}
                ]

                for rule_type in rule_types:
                    # 创建预订规则
                    rule = Rule(
                        name=rule_type["name"],
                        status=Status.ACTIVE,
                        type=RuleType.RESERVATION,
                        created_at=get_current_time(),
                        updated_at=get_current_time()
                    )
                    db.add(rule)
                    db.flush()

                    # 为每个规则创建时段规则项
                    if rule_type["name"] == "标准预订规则":
                        # 标准预订规则：上午、下午、晚上三个时段
                        time_slots = [
                            {"name": "上午场", "start_time_cron": "0 9 * * *", "end_time_cron": "0 12 * * *",
                             "order_deadline": 60, "cancellation_deadline": 660, "generated_count": 5, "quantity": 20,
                             "order": 1},
                            {"name": "下午场", "start_time_cron": "0 13 * * *", "end_time_cron": "0 17 * * *",
                             "order_deadline": 300, "cancellation_deadline": 1020, "generated_count": 5, "quantity": 20,
                             "order": 2},
                            {"name": "晚上场", "start_time_cron": "0 18 * * *", "end_time_cron": "0 21 * * *",
                             "order_deadline": 600, "cancellation_deadline": 1200, "generated_count": 5, "quantity": 15,
                             "order": 3}
                        ]
                    elif rule_type["name"] == "高峰期预订规则":
                        # 高峰期预订规则：周末时段划分更细致
                        time_slots = [
                            {"name": "早场", "start_time_cron": "0 8 * * 6,0", "end_time_cron": "0 10 * * 6,0",
                             "order_deadline": 60, "cancellation_deadline": 660, "generated_count": 5, "quantity": 10,
                             "order": 1},
                            {"name": "上午场", "start_time_cron": "0 10 * * 6,0", "end_time_cron": "0 12 * * 6,0",
                             "order_deadline": 60, "cancellation_deadline": 660, "generated_count": 5, "quantity": 15,
                             "order": 2},
                            {"name": "午餐场", "start_time_cron": "0 12 * * 6,0", "end_time_cron": "0 14 * * 6,0",
                             "order_deadline": 60, "cancellation_deadline": 660, "generated_count": 5, "quantity": 20,
                             "order": 3},
                            {"name": "下午场", "start_time_cron": "0 14 * * 6,0", "end_time_cron": "0 17 * * 6,0",
                             "order_deadline": 60, "cancellation_deadline": 660, "generated_count": 5, "quantity": 15,
                             "order": 4},
                            {"name": "晚餐场", "start_time_cron": "0 17 * * 6,0", "end_time_cron": "0 20 * * 6,0",
                             "order_deadline": 60, "cancellation_deadline": 660, "generated_count": 5, "quantity": 20,
                             "order": 5},
                            {"name": "夜场", "start_time_cron": "0 20 * * 6,0", "end_time_cron": "0 22 * * 6,0",
                             "order_deadline": 60, "cancellation_deadline": 660, "generated_count": 5, "quantity": 10,
                             "order": 6}
                        ]
                    else:
                        # 特殊活动预订规则：限定时段
                        time_slots = [
                            {"name": "特别体验场次一", "start_time_cron": "0 10 * * *", "end_time_cron": "0 12 * * *",
                             "order_deadline": 60, "cancellation_deadline": 660, "generated_count": 5, "quantity": 8,
                             "order": 1},
                            {"name": "特别体验场次二", "start_time_cron": "0 14 * * *", "end_time_cron": "0 16 * * *",
                             "order_deadline": 60, "cancellation_deadline": 660, "generated_count": 5, "quantity": 8,
                             "order": 2},
                            {"name": "特别体验场次三", "start_time_cron": "0 16 * * *", "end_time_cron": "0 18 * * *",
                             "order_deadline": 60, "cancellation_deadline": 660, "generated_count": 5, "quantity": 8,
                             "order": 3},
                            {"name": "VIP专场", "start_time_cron": "0 19 * * *", "end_time_cron": "0 21 * * *",
                             "order_deadline": 60, "cancellation_deadline": 660, "generated_count": 5, "quantity": 5,
                             "order": 4}
                        ]

                    for time_slot in time_slots:
                        rule_item = RuleItem(
                            rule_id=rule.id,
                            name=time_slot["name"],
                            start_time_cron_str=time_slot["start_time_cron"],
                            end_time_cron_str=time_slot["end_time_cron"],
                            order_deadline=time_slot["order_deadline"],
                            cancellation_deadline=time_slot["cancellation_deadline"],
                            generated_count=time_slot["generated_count"],
                            quantity=time_slot["quantity"],
                            order=time_slot["order"],
                            created_at=get_current_time(),
                            updated_at=get_current_time()
                        )
                        db.add(rule_item)

                    # 随机分配规则给预订产品
                    for product in random.sample(reservation_products, min(len(reservation_products), 10)):
                        product.rules.append(rule)

                db.commit()
                print("成功创建预订规则并分配给预订产品!")

            # 为每个用户创建订单
            for user in all_users:
                # 获取用户的账户
                regular_account = db.query(RegularAccount).filter(RegularAccount.user_id == user.id).first()
                gift_account = db.query(GiftAccount).filter(GiftAccount.user_id == user.id).first()

                if not regular_account or not gift_account:
                    print(f"用户 {user.id} 没有完整的账户信息，跳过")
                    continue

                # 1. 创建1-3个充值订单
                recharge_count = random.randint(1, 3)
                for i in range(recharge_count):
                    # 随机生成充值金额
                    recharge_amount = random.randint(100, 2000)

                    # 随机选择支付方式
                    payment_method = random.choice([
                        PaymentMethod.ALIPAY,
                        PaymentMethod.WECHAT_PAY,
                        PaymentMethod.BANK_TRANSFER
                    ])

                    # 生成订单号
                    order_no = f"RC{generate_unique_timestamp()}{user.id}{i}"

                    # 随机生成订单时间（最近30天内）
                    days_ago = random.randint(1, 30)
                    order_time = get_current_time() - datetime.timedelta(days=days_ago)

                    # 创建充值订单
                    recharge_order = RechargeOrder(
                        order_no=order_no,
                        user_id=user.id,
                        status=OrderStatus.COMPLETED,
                        payment_status=PaymentStatus.PAID,
                        payment_time=order_time,
                        total_amount=recharge_amount,
                        payable_amount=recharge_amount,
                        actual_amount_paid=recharge_amount,
                        payment_method=payment_method,
                        created_at=order_time,
                        updated_at=order_time
                    )

                    db.add(recharge_order)
                    db.flush()  # 刷新以获取订单ID

                    # 创建账户交易记录
                    transaction = AccountTransaction(
                        account_id=regular_account.id,
                        order_id=recharge_order.id,
                        transaction_type=TransactionType.DEPOSIT,
                        amount=recharge_amount,
                        transaction_time=order_time,
                        description=f"充值订单 {order_no}"
                    )

                    db.add(transaction)
                    print(f"为用户 {user.id} 创建充值订单 {order_no}，金额: {recharge_amount}")

                # 2. 创建1-5个直销订单
                sale_count = random.randint(1, 5)
                for i in range(sale_count):
                    # 随机选择1-3个产品
                    product_count = random.randint(1, 3)
                    selected_products = random.sample(direct_sale_products,
                                                      min(product_count, len(direct_sale_products)))

                    # 计算订单总金额
                    total_amount = 0

                    # 随机生成订单时间（最近30天内）
                    days_ago = random.randint(1, 30)
                    order_time = get_current_time() - datetime.timedelta(days=days_ago)

                    # 生成订单号
                    order_no = f"DS{generate_unique_timestamp()}{user.id}{i}"

                    # 随机选择订单状态
                    status = random.choice([
                        OrderStatus.PENDING,
                        OrderStatus.PAID,
                        OrderStatus.SHIPPED,
                        OrderStatus.DELIVERED,
                        OrderStatus.COMPLETED
                    ])

                    # 支付状态根据订单状态确定
                    payment_status = PaymentStatus.UNPAID if status == OrderStatus.PENDING else PaymentStatus.PAID

                    # 支付时间
                    payment_time = None if payment_status == PaymentStatus.UNPAID else order_time

                    # 随机选择支付方式
                    if payment_status == PaymentStatus.PAID:
                        # 80%的概率使用账户余额，20%使用其他支付方式
                        if random.random() < 0.8:
                            payment_method = random.choice([
                                PaymentMethod.ACCOUNT_BALANCE,
                                PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE,
                            ])
                        else:
                            payment_method = random.choice([
                                PaymentMethod.ALIPAY,
                                PaymentMethod.WECHAT_PAY,
                                PaymentMethod.BANK_TRANSFER,
                                PaymentMethod.CASH
                            ])
                    else:
                        payment_method = None

                    # 创建直销订单
                    order = DirectSaleOrder(
                        order_no=order_no,
                        user_id=user.id,
                        status=status,
                        payment_status=payment_status,
                        payment_time=payment_time,
                        total_amount=0,  # 暂时为0，后面计算
                        payable_amount=0,  # 暂时为0，后面计算
                        actual_amount_paid=0,  # 暂时为0，后面计算
                        payment_method=payment_method,
                        created_at=order_time,
                        updated_at=order_time
                    )

                    db.add(order)
                    db.flush()  # 刷新以获取订单ID

                    # 添加订单项
                    for product in selected_products:
                        # 随机购买数量
                        quantity = random.randint(1, 5)

                        # 计算小计金额
                        subtotal = product.price * quantity
                        total_amount += subtotal

                        # 创建订单项
                        order_item = OrderItem(
                            order_id=order.id,
                            product_id=product.id,
                            quantity=quantity,
                            price=product.price,
                            subtotal=subtotal,
                            final_price=product.price,
                            payable_amount=subtotal,
                            created_at=order_time,
                            updated_at=order_time
                        )

                        db.add(order_item)

                    # 更新订单总金额
                    order.total_amount = total_amount
                    order.payable_amount = total_amount

                    # 如果已支付，更新支付金额并创建交易记录
                    if payment_status == PaymentStatus.PAID:
                        order.actual_amount_paid = total_amount

                        # 创建账户交易记录
                        transaction_type = TransactionType.PAYMENT

                        # 选择支付账户 (70%概率使用普通账户，30%概率使用赠送账户)
                        if payment_method in [PaymentMethod.ACCOUNT_BALANCE, PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE]:
                            account_id = regular_account.id if random.random() < 0.7 else gift_account.id

                            transaction = AccountTransaction(
                                account_id=account_id,
                                order_id=order.id,
                                transaction_type=transaction_type,
                                amount=-total_amount,  # 负数表示支出
                                transaction_time=payment_time,
                                description=f"支付订单 {order_no}"
                            )

                            db.add(transaction)

                    print(f"为用户 {user.id} 创建直销订单 {order_no}，金额: {total_amount}，状态: {status.value}")

                # 3. 给部分用户创建1-2个预订订单
                from app.models.reservation import ReservationRequest
                # 50%的概率创建预订订单
                if random.random() < 0.5 and reservation_products:
                    reservation_count = random.randint(1, 2)

                    for i in range(reservation_count):
                        # 随机选择一个预订产品
                        product = random.choice(reservation_products)

                        # 确保产品有关联的规则
                        if not product.rules:
                            continue

                        # 随机选择一个规则
                        rule = random.choice(product.rules)

                        # 确保规则有关联的规则项
                        if not rule.rule_items:
                            continue

                        # 随机选择一个规则项(时间段)
                        rule_item = random.choice(rule.rule_items)

                        # 随机生成订单时间（最近30天内）
                        days_ago = random.randint(1, 30)
                        order_time = get_current_time() - datetime.timedelta(days=days_ago)

                        # 生成订单号
                        order_no = f"RV{generate_unique_timestamp()}{user.id}{i}"

                        # 随机选择预订状态
                        reservation_status = random.choice([
                            OrderReservationStatus.PENDING,
                            OrderReservationStatus.APPROVED,
                            OrderReservationStatus.REJECTED
                        ])

                        # 订单状态和支付状态根据预订状态确定
                        if reservation_status == OrderReservationStatus.PENDING:
                            status = OrderStatus.PENDING
                            payment_status = PaymentStatus.UNPAID
                            payment_time = None
                        elif reservation_status == OrderReservationStatus.REJECTED:
                            status = OrderStatus.CANCELLED
                            payment_status = PaymentStatus.REFUNDED
                            payment_time = order_time
                        else:
                            status = OrderStatus.PAID
                            payment_status = PaymentStatus.PAID
                            payment_time = order_time

                        # 随机选择支付方式
                        if payment_status == PaymentStatus.PAID:
                            payment_method = random.choice([
                                PaymentMethod.ACCOUNT_BALANCE,
                                PaymentMethod.ALIPAY,
                                PaymentMethod.WECHAT_PAY
                            ])
                        else:
                            payment_method = None

                        # 计算费用
                        total_amount = product.price
                        deposit_amount = product.reservation_fee

                        # 创建订单
                        order = ReservationOrder(
                            order_no=order_no,
                            user_id=user.id,
                            status=status,
                            payment_status=payment_status,
                            payment_time=payment_time,
                            total_amount=total_amount,
                            payable_amount=total_amount,
                            actual_amount_paid=deposit_amount if reservation_status == OrderReservationStatus.PENDING else (
                                total_amount if reservation_status == OrderReservationStatus.APPROVED else 0),
                            payment_method=payment_method,
                            reservation_status=OrderReservationStatus.PENDING if reservation_status == OrderReservationStatus.PENDING else (
                                OrderReservationStatus.REJECTED if reservation_status == OrderReservationStatus.REJECTED else
                                OrderReservationStatus.APPROVED),
                            created_at=order_time,
                            updated_at=order_time
                        )

                        db.add(order)
                        db.flush()  # 刷新以获取订单ID

                        # 创建订单项
                        order_item = OrderItem(
                            order_id=order.id,
                            product_id=product.id,
                            quantity=1,
                            price=product.price,
                            subtotal=product.price,
                            final_price=product.price,
                            payable_amount=product.price,
                            created_at=order_time,
                            updated_at=order_time
                        )

                        db.add(order_item)
                        db.flush()  # 刷新以获取订单项ID

                        # 生成预订期间（例如：日期+时段）
                        reservation_date = get_current_time() + datetime.timedelta(days=random.randint(1, 30))
                        reservation_period = f"{reservation_date.strftime('%Y-%m-%d')} {rule_item.name}"

                        if reservation_status == OrderReservationStatus.PENDING:
                            reservation_request_status = ReservationRequestStatus.PENDING
                        elif reservation_status == OrderReservationStatus.APPROVED:
                            reservation_request_status = ReservationRequestStatus.PAID_FULL
                        else:
                            reservation_request_status = ReservationRequestStatus.CANCELLED

                        # 创建预订请求
                        reservation_request = ReservationRequest(
                            order_item_id=order_item.id,
                            user_id=user.id,
                            product_id=product.id,
                            rule_id=rule.id,
                            rule_item_id=rule_item.id,
                            status=reservation_request_status,
                            reservation_period=reservation_period,
                            reservation_time=reservation_date,
                            verification_code=f"RES-{generate_random_string(8).upper()}",
                            created_at=order_time,
                            updated_at=order_time
                        )

                        db.add(reservation_request)

                        # 如果已支付，创建账户交易记录
                        if payment_status == PaymentStatus.PAID:
                            paid_amount = deposit_amount if reservation_status == OrderReservationStatus.PENDING else total_amount

                            if payment_method == PaymentMethod.ACCOUNT_BALANCE:
                                # 选择支付账户 (70%概率使用普通账户，30%概率使用赠送账户)
                                account_id = regular_account.id if random.random() < 0.7 else gift_account.id

                                transaction = AccountTransaction(
                                    account_id=account_id,
                                    order_id=order.id,
                                    transaction_type=TransactionType.PAYMENT,
                                    amount=-paid_amount,  # 负数表示支出
                                    transaction_time=payment_time,
                                    description=f"预订产品 {product.name} {reservation_status.value}"
                                )

                                db.add(transaction)

                        print(
                            f"为用户 {user.id} 创建预订订单 {order_no}，预订产品: {product.name}, 状态: {reservation_status.value}")

            print("成功创建订单和交易记录!")

            # 创建定价策略测试数据
            create_pricing_strategies(db)
        except Exception as e:
            db.rollback()
            print(f"创建订单和交易记录失败: {e}")

        # 这里可以添加更多测试数据，如产品、规则、订单等

        db.commit()
        print("所有测试数据添加完成!")
    except Exception as e:
        db.rollback()
        print(f"添加测试数据失败: {e}")
    finally:
        db.close()
        engine.dispose()


def generate_enterprise_transactions():
    """为企业用户生成更多的随机订单和交易记录"""
    print("开始为企业用户生成更多的随机订单和交易记录...")

    # 创建数据库会话
    engine = create_engine(STAGE_DATABASE_FULL_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()

    # 添加延迟函数以确保订单号唯一性
    def generate_unique_timestamp():
        """生成唯一的时间戳，并添加小延迟确保唯一性"""
        timestamp = int(datetime.datetime.now().timestamp() * 1000)
        # 添加小延迟以确保每次调用生成不同的时间戳
        time.sleep(0.001)
        return timestamp

    try:
        from app.models.reservation import ReservationRequest
        from app.models.rule import Rule, RuleItem, RuleType

        # 获取所有企业用户
        enterprise_users = db.query(Enterprise).all()

        if not enterprise_users:
            print("未找到企业用户，跳过生成订单和交易记录")
            return

        # 获取所有直销产品
        direct_sale_products = db.query(DirectSaleProduct).all()
        # 获取所有预订产品
        reservation_products = db.query(ReservationProduct).all()

        if not direct_sale_products and not reservation_products:
            print("未找到产品，跳过生成订单")
            return

        for enterprise in enterprise_users:
            print(f"为企业 {enterprise.company_name} (ID: {enterprise.id}) 生成交易记录")

            # 获取企业用户的账户
            regular_account = db.query(RegularAccount).filter(RegularAccount.user_id == enterprise.id).first()
            gift_account = db.query(GiftAccount).filter(GiftAccount.user_id == enterprise.id).first()

            if not regular_account or not gift_account:
                print(f"企业用户 {enterprise.id} 没有完整的账户信息，跳过")
                continue

            # 1. 生成5-15笔充值记录
            recharge_count = random.randint(5, 15)
            for i in range(recharge_count):
                # 生成随机充值金额 (1000-50000元)
                recharge_amount = random.randint(1000, 50000)

                # 随机选择支付方式
                payment_method = random.choice([
                    PaymentMethod.ALIPAY,
                    PaymentMethod.WECHAT_PAY,
                    PaymentMethod.BANK_TRANSFER
                ])

                # 生成订单号
                order_no = f"RE{generate_unique_timestamp()}{enterprise.id}{i}"

                # 随机生成订单时间（最近90天内）
                days_ago = random.randint(1, 90)
                order_time = get_current_time() - datetime.timedelta(days=days_ago)

                # 创建充值订单
                recharge_order = RechargeOrder(
                    order_no=order_no,
                    user_id=enterprise.id,
                    status=OrderStatus.COMPLETED,
                    payment_status=PaymentStatus.PAID,
                    payment_time=order_time,
                    total_amount=recharge_amount,
                    payable_amount=recharge_amount,
                    actual_amount_paid=recharge_amount,
                    payment_method=payment_method,
                    created_at=order_time,
                    updated_at=order_time
                )

                db.add(recharge_order)
                db.flush()  # 刷新以获取订单ID

                # 创建账户交易记录
                transaction = AccountTransaction(
                    account_id=regular_account.id,
                    order_id=recharge_order.id,
                    transaction_type=TransactionType.DEPOSIT,
                    amount=recharge_amount,
                    transaction_time=order_time,
                    description=f"企业充值 {order_no}"
                )

                db.add(transaction)
                print(f"  创建充值订单 {order_no}，金额: ¥{recharge_amount}")

            # 2. 生成10-30笔直销订单
            sale_count = random.randint(10, 30)
            for i in range(sale_count):
                # 随机选择2-5个产品
                product_count = random.randint(2, 5)
                selected_products = random.sample(direct_sale_products, min(product_count, len(direct_sale_products)))

                # 计算订单总金额
                total_amount = 0

                # 随机生成订单时间（最近90天内）
                days_ago = random.randint(1, 90)
                order_time = get_current_time() - datetime.timedelta(days=days_ago)

                # 生成订单号
                order_no = f"DS{generate_unique_timestamp()}{enterprise.id}{i}"

                # 随机选择订单状态，企业订单大多数为已完成
                status_weights = [
                    (OrderStatus.PENDING, 5),
                    (OrderStatus.PAID, 10),
                    (OrderStatus.SHIPPED, 15),
                    (OrderStatus.DELIVERED, 10),
                    (OrderStatus.COMPLETED, 60)
                ]
                status = random.choices(
                    [s[0] for s in status_weights],
                    weights=[s[1] for s in status_weights]
                )[0]

                # 支付状态根据订单状态确定
                payment_status = PaymentStatus.UNPAID if status == OrderStatus.PENDING else PaymentStatus.PAID

                # 支付时间
                payment_time = None if payment_status == PaymentStatus.UNPAID else order_time

                # 支付方式 - 企业用户更多使用账户余额
                if payment_status == PaymentStatus.PAID:
                    payment_method_weights = [
                        (PaymentMethod.ACCOUNT_BALANCE, 30),
                        (PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE, 40),
                        (PaymentMethod.ALIPAY, 10),
                        (PaymentMethod.WECHAT_PAY, 10),
                        (PaymentMethod.BANK_TRANSFER, 10)
                    ]
                    payment_method = random.choices(
                        [m[0] for m in payment_method_weights],
                        weights=[m[1] for m in payment_method_weights]
                    )[0]
                else:
                    payment_method = None

                # 创建直销订单
                order = DirectSaleOrder(
                    order_no=order_no,
                    user_id=enterprise.id,
                    status=status,
                    payment_status=payment_status,
                    payment_time=payment_time,
                    total_amount=0,  # 暂时为0，后面计算
                    payable_amount=0,  # 暂时为0，后面计算
                    actual_amount_paid=0,  # 暂时为0，后面计算
                    payment_method=payment_method,
                    created_at=order_time,
                    updated_at=order_time
                )

                db.add(order)
                db.flush()  # 刷新以获取订单ID

                # 添加订单项
                for product in selected_products:
                    # 企业用户通常购买更大数量
                    quantity = random.randint(5, 50)

                    # 企业用户可能获得批发折扣 (0.8-0.95)
                    discount = random.uniform(0.8, 0.95)
                    discounted_price = round(product.price * discount, 2)

                    # 计算小计金额
                    subtotal = product.price * quantity
                    payable_amount = discounted_price * quantity
                    total_amount += payable_amount

                    # 创建订单项
                    order_item = OrderItem(
                        order_id=order.id,
                        product_id=product.id,
                        quantity=quantity,
                        price=product.price,
                        subtotal=subtotal,
                        final_price=discounted_price,
                        payable_amount=payable_amount,
                        pricing_remark=f"企业批发折扣: {int((1 - discount) * 100)}%",
                        created_at=order_time,
                        updated_at=order_time
                    )

                    db.add(order_item)

                # 更新订单总金额
                order.total_amount = total_amount
                order.payable_amount = total_amount

                # 如果已支付，更新支付金额并创建交易记录
                if payment_status == PaymentStatus.PAID:
                    order.actual_amount_paid = total_amount

                    # 创建账户交易记录
                    transaction_type = TransactionType.PAYMENT

                    # 选择支付账户 (60%概率使用普通账户，40%概率使用赠送账户)
                    if payment_method in [PaymentMethod.ACCOUNT_BALANCE, PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE]:
                        account_id = regular_account.id if random.random() < 0.6 else gift_account.id

                        transaction = AccountTransaction(
                            account_id=account_id,
                            order_id=order.id,
                            transaction_type=transaction_type,
                            amount=-total_amount,  # 负数表示支出
                            transaction_time=payment_time,
                            description=f"企业支付订单 {order_no}"
                        )

                        db.add(transaction)

                print(f"  创建直销订单 {order_no}，金额: ¥{total_amount:.2f}，状态: {status.value}")

            # 3. 额外创建企业间转账记录 (3-8笔)
            transfer_count = random.randint(3, 8)

            # 获取其他企业用户
            other_enterprises = [e for e in enterprise_users if e.id != enterprise.id]

            if other_enterprises:
                for i in range(transfer_count):
                    # 随机选择一个企业接收方
                    recipient = random.choice(other_enterprises)
                    recipient_account = db.query(RegularAccount).filter(RegularAccount.user_id == recipient.id).first()

                    if not recipient_account:
                        continue

                    # 生成随机转账金额 (5000-20000元)
                    transfer_amount = random.randint(5000, 20000)

                    # 随机生成转账时间（最近90天内）
                    days_ago = random.randint(1, 90)
                    transaction_time = get_current_time() - datetime.timedelta(days=days_ago)

                    # 创建转出交易记录
                    outgoing_transaction = AccountTransaction(
                        account_id=regular_account.id,
                        transaction_type=TransactionType.TRANSFER,
                        amount=-transfer_amount,  # 负数表示转出
                        transaction_time=transaction_time,
                        description=f"转账给企业 {recipient.company_name}"
                    )

                    db.add(outgoing_transaction)

                    # 创建转入交易记录
                    incoming_transaction = AccountTransaction(
                        account_id=recipient_account.id,
                        transaction_type=TransactionType.TRANSFER,
                        amount=transfer_amount,  # 正数表示转入
                        transaction_time=transaction_time,
                        description=f"收到企业 {enterprise.company_name} 的转账"
                    )

                    db.add(incoming_transaction)

                    print(
                        f"  创建企业间转账记录: {enterprise.company_name} -> {recipient.company_name}, 金额: ¥{transfer_amount}")

            # 4. 创建预订订单 (企业用户更多用于团体预订)
            if reservation_products:
                # 获取规则和规则项
                rules = db.query(Rule).filter(Rule.type == RuleType.RESERVATION).all()
                if not rules:
                    print("  未找到预订规则，跳过创建预订订单")
                else:
                    # 创建3-8个预订订单
                    reservation_count = random.randint(3, 8)
                    print(f"  为企业 {enterprise.company_name} 创建 {reservation_count} 个团体预订订单")

                    for i in range(reservation_count):
                        # 随机选择一个预订产品
                        product = random.choice(reservation_products)

                        # 确保产品有关联的规则
                        product_rules = [rule for rule in rules if rule in product.rules]
                        if not product_rules:
                            continue

                        # 随机选择一个规则
                        rule = random.choice(product_rules)

                        # 确保规则有关联的规则项
                        if not rule.rule_items:
                            continue

                        # 随机选择一个规则项(时间段)
                        rule_item = random.choice(rule.rule_items)

                        # 随机生成订单时间（最近60天内）
                        days_ago = random.randint(1, 60)
                        order_time = get_current_time() - datetime.timedelta(days=days_ago)

                        # 生成订单号
                        order_no = f"EV{generate_unique_timestamp()}{enterprise.id}{i}"

                        # 企业预订大多为已付全款或已付定金状态
                        reservation_status_weights = [
                            (OrderReservationStatus.PENDING, 30),
                            (OrderReservationStatus.APPROVED, 65),
                            (OrderReservationStatus.REJECTED, 5)
                        ]
                        reservation_status = random.choices(
                            [s[0] for s in reservation_status_weights],
                            weights=[s[1] for s in reservation_status_weights]
                        )[0]

                        # 订单状态和支付状态根据预订状态确定
                        if reservation_status == OrderReservationStatus.PENDING:
                            status = OrderStatus.PENDING
                            payment_status = PaymentStatus.UNPAID
                            payment_time = None
                        elif reservation_status == OrderReservationStatus.REJECTED:
                            status = OrderStatus.CANCELLED
                            payment_status = PaymentStatus.REFUNDED
                            payment_time = order_time
                        else:
                            status = OrderStatus.PAID
                            payment_status = PaymentStatus.PAID
                            payment_time = order_time

                        # 随机选择支付方式
                        if payment_status == PaymentStatus.PAID:
                            payment_method_weights = [
                                (PaymentMethod.ACCOUNT_BALANCE, 30),
                                (PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE, 40),
                                (PaymentMethod.ALIPAY, 10),
                                (PaymentMethod.WECHAT_PAY, 10),
                                (PaymentMethod.BANK_TRANSFER, 10)
                            ]
                            payment_method = random.choices(
                                [m[0] for m in payment_method_weights],
                                weights=[m[1] for m in payment_method_weights]
                            )[0]
                        else:
                            payment_method = None

                        # 企业通常预订多人（5-50人）
                        quantity = random.randint(5, 50)

                        # 企业预订可能获得折扣 (0.85-0.95)
                        discount = random.uniform(0.85, 0.95)
                        discounted_price = round(product.price * discount, 2)

                        # 计算费用
                        base_amount = product.price * quantity
                        total_amount = discounted_price * quantity
                        deposit_amount = product.reservation_fee * quantity

                        # 创建订单
                        order = ReservationOrder(
                            order_no=order_no,
                            user_id=enterprise.id,
                            status=status,
                            payment_status=payment_status,
                            payment_time=payment_time,
                            total_amount=base_amount,
                            payable_amount=total_amount,
                            actual_amount_paid=deposit_amount if reservation_status == OrderReservationStatus.PENDING else (
                                total_amount if status == OrderStatus.PAID else 0),
                            payment_method=payment_method,
                            reservation_status=OrderReservationStatus.PENDING if reservation_status == OrderReservationStatus.PENDING else (
                                OrderReservationStatus.REJECTED if reservation_status == OrderReservationStatus.REJECTED else
                                OrderReservationStatus.APPROVED),
                            created_at=order_time,
                            updated_at=order_time
                        )

                        db.add(order)
                        db.flush()

                        # 创建订单项
                        order_item = OrderItem(
                            order_id=order.id,
                            product_id=product.id,
                            quantity=quantity,
                            price=product.price,
                            subtotal=base_amount,
                            final_price=discounted_price,
                            payable_amount=total_amount,
                            pricing_remark=f"企业团体预订折扣: {int((1 - discount) * 100)}%",
                            created_at=order_time,
                            updated_at=order_time
                        )

                        db.add(order_item)
                        db.flush()

                        # 生成预订期间（例如：日期+时段）
                        reservation_date = get_current_time() + datetime.timedelta(days=random.randint(5, 90))
                        reservation_period = f"{reservation_date.strftime('%Y-%m-%d')} {rule_item.name}"

                        if reservation_status == OrderReservationStatus.PENDING:
                            reservation_request_status = ReservationRequestStatus.PENDING
                        elif reservation_status == OrderReservationStatus.APPROVED:
                            reservation_request_status = ReservationRequestStatus.PAID_FULL
                        else:
                            reservation_request_status = ReservationRequestStatus.CANCELLED

                        # 创建预订请求
                        reservation_request = ReservationRequest(
                            order_item_id=order_item.id,
                            user_id=enterprise.id,
                            product_id=product.id,
                            rule_id=rule.id,
                            rule_item_id=rule_item.id,
                            status=reservation_request_status,
                            reservation_period=reservation_period,
                            reservation_time=reservation_date,
                            verification_code=f"ERES-{generate_random_string(8).upper()}",
                            created_at=order_time,
                            updated_at=order_time
                        )

                        db.add(reservation_request)

                        # 如果已支付，创建账户交易记录
                        if payment_status == PaymentStatus.PAID:
                            paid_amount = deposit_amount if reservation_status == OrderReservationStatus.PENDING else total_amount

                            if payment_method in [PaymentMethod.ACCOUNT_BALANCE,
                                                  PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE]:
                                # 选择支付账户 (60%概率使用普通账户，40%概率使用赠送账户)
                                account_id = regular_account.id if random.random() < 0.6 else gift_account.id

                                transaction = AccountTransaction(
                                    account_id=account_id,
                                    order_id=order.id,
                                    transaction_type=TransactionType.PAYMENT,
                                    amount=-paid_amount,
                                    transaction_time=payment_time,
                                    description=f"企业团体预订 {product.name} ({quantity}人) {reservation_status.value}"
                                )

                                db.add(transaction)

                        print(
                            f"  创建企业团体预订订单 {order_no}, 产品: {product.name}, 人数: {quantity}, 状态: {reservation_status.value}")

        db.commit()
        print("企业用户交易记录生成完成!")
    except Exception as e:
        db.rollback()
        print(f"生成企业用户交易记录失败: {e}")
    finally:
        db.close()
        engine.dispose()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="创建和初始化stage环境的数据库")
    parser.add_argument('--skip-db-create', action='store_true', help='跳过数据库创建步骤')
    parser.add_argument('--skip-migrations', action='store_true', help='跳过数据库迁移步骤')
    parser.add_argument('--skip-test-data', action='store_true', help='跳过添加测试数据步骤')
    parser.add_argument('--enterprise-transactions', action='store_true', help='为企业用户生成更多交易记录')
    
    # 添加alembic相关操作参数
    parser.add_argument('--downgrade', type=str, metavar='REVISION', 
                        help='运行数据库回退操作到指定版本（如：--downgrade -1 回退一个版本，--downgrade base 回退到初始状态）')
    parser.add_argument('--upgrade', type=str, metavar='REVISION', 
                        help='运行数据库升级操作到指定版本（如：--upgrade head 升级到最新版本，--upgrade abc123 升级到指定版本）')
    parser.add_argument('--history', action='store_true', help='显示数据库迁移历史记录')
    parser.add_argument('--current', action='store_true', help='显示当前数据库版本')
    parser.add_argument('--constraints', type=str, metavar='TABLE_NAME', nargs='?', const='', 
                        help='显示数据库表的约束信息（如：--constraints reservation_requests 显示指定表，--constraints 显示所有表的外键约束）')

    args = parser.parse_args()

    # 处理alembic相关操作
    if args.history:
        show_migration_history()
        return

    if args.current:
        show_current_revision()
        return

    if args.constraints is not None:
        # 如果传入了空字符串，显示所有表的外键约束；否则显示指定表的约束
        table_name = args.constraints if args.constraints else None
        show_table_constraints(table_name)
        return

    if args.upgrade:
        run_upgrade(args.upgrade)
        return

    if args.downgrade:
        run_downgrade(args.downgrade)
        return

    # 如果指定了生成企业交易记录
    if args.enterprise_transactions:
        generate_enterprise_transactions()
        return

    # 如果需要添加测试数据，那么数据库创建和迁移将在create_test_data函数中处理
    if not args.skip_test_data:
        create_test_data()
        print("Stage环境数据库初始化完成!")
        return

    # 如果跳过测试数据，则单独执行数据库创建和迁移
    # 执行数据库创建
    if not args.skip_db_create:
        create_stage_database()
    else:
        print("跳过数据库创建步骤")

    # 执行数据库迁移
    if not args.skip_migrations:
        run_migrations()
    else:
        print("跳过数据库迁移步骤")

    print("Stage环境数据库初始化完成!")


if __name__ == "__main__":
    main()
