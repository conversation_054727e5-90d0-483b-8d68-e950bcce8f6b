#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优惠券计价接口测试脚本

测试场景：
1. 正常计价（无优惠券）
2. 单张现金券计价
3. 单张折扣券计价
4. 单张满减券计价
5. 多张优惠券组合计价
6. 优惠券验证失败场景
"""

import requests
import json
from typing import Dict, Any, List

# 测试配置
BASE_URL = "http://localhost:8000"  # 根据实际部署调整
API_ENDPOINT = f"{BASE_URL}/api/v1/wechat-mini-app/coupon/coupon-pricing"

# 测试数据
TEST_TOKEN = "your_test_token_here"  # 需要替换为实际的测试token
TEST_USER_ID = 1  # 需要替换为实际的测试用户ID

def test_coupon_pricing(
    user_id: int,
    products: List[Dict[str, Any]],
    coupon_usage_record_ids: List[int],
    token: str,
    test_name: str
) -> None:
    """
    测试优惠券计价接口
    
    Args:
        user_id: 用户ID
        products: 产品列表
        coupon_usage_record_ids: 优惠券使用记录ID列表
        token: 认证token
        test_name: 测试名称
    """
    print(f"\n=== {test_name} ===")
    
    # 构建请求数据
    request_data = {
        "user_id": user_id,
        "products": products,
        "coupon_usage_record_ids": coupon_usage_record_ids
    }
    
    headers = {
        "Content-Type": "application/json",
        "token": token
    }
    
    try:
        # 发送请求
        response = requests.post(
            API_ENDPOINT,
            json=request_data,
            headers=headers,
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("请求成功!")
            print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # 解析计价结果
            if "data" in result and "pricing_result" in result["data"]:
                pricing_result = result["data"]["pricing_result"]
                order_info = pricing_result["order"]
                discount_info = pricing_result["discount"]
                
                print(f"\n计价结果摘要:")
                print(f"  订单总金额: {order_info['total_amount']} 元")
                print(f"  应付金额: {order_info['payable_amount']} 元")
                print(f"  优惠金额: {discount_info['total_discount']} 元")
                print(f"  使用优惠券数量: {len(discount_info['coupons'])}")
                
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {str(e)}")
    except Exception as e:
        print(f"其他异常: {str(e)}")

def run_tests():
    """运行所有测试用例"""
    
    # 测试产品数据
    test_products = [
        {"product_id": 1, "quantity": 2},
        {"product_id": 2, "quantity": 1}
    ]
    
    print("开始测试优惠券计价接口...")
    print(f"测试API地址: {API_ENDPOINT}")
    print(f"测试用户ID: {TEST_USER_ID}")
    
    # 测试1: 无优惠券计价
    test_coupon_pricing(
        user_id=TEST_USER_ID,
        products=test_products,
        coupon_usage_record_ids=[],
        token=TEST_TOKEN,
        test_name="无优惠券计价测试"
    )
    
    # 测试2: 单张优惠券计价
    test_coupon_pricing(
        user_id=TEST_USER_ID,
        products=test_products,
        coupon_usage_record_ids=[1],  # 需要替换为实际的优惠券使用记录ID
        token=TEST_TOKEN,
        test_name="单张优惠券计价测试"
    )
    
    # 测试3: 多张优惠券计价
    test_coupon_pricing(
        user_id=TEST_USER_ID,
        products=test_products,
        coupon_usage_record_ids=[1, 2],  # 需要替换为实际的优惠券使用记录ID
        token=TEST_TOKEN,
        test_name="多张优惠券计价测试"
    )
    
    # 测试4: 无效token测试
    test_coupon_pricing(
        user_id=TEST_USER_ID,
        products=test_products,
        coupon_usage_record_ids=[],
        token="invalid_token",
        test_name="无效token测试"
    )
    
    # 测试5: 空产品列表测试
    test_coupon_pricing(
        user_id=TEST_USER_ID,
        products=[],
        coupon_usage_record_ids=[],
        token=TEST_TOKEN,
        test_name="空产品列表测试"
    )
    
    print("\n=== 测试完成 ===")
    print("\n注意事项:")
    print("1. 请确保服务器正在运行")
    print("2. 请替换TEST_TOKEN为有效的认证token")
    print("3. 请替换TEST_USER_ID为有效的用户ID")
    print("4. 请替换优惠券使用记录ID为实际存在的ID")
    print("5. 请确保测试产品ID在数据库中存在")

if __name__ == "__main__":
    run_tests()
