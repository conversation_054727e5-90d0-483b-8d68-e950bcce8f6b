from app.models.enum import Status
from tests.utils import generate_random_string


# 管理员测试用例
def test_create_admin(client):
    """测试创建管理员"""
    data = {
        "name": "测试管理员",
        "status": Status.ACTIVE,
        "password": "test123456"
    }
    response = client.post("/api/v1/admin/", json=data)
    assert response.status_code == 201
    content = response.json()
    assert content["name"] == data["name"]
    assert content["status"] == data["status"]
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content
    assert "password" not in content


def test_read_admin(client):
    """测试获取管理员"""
    # 先创建一个管理员
    data = {
        "name": "测试管理员2",
        "status": Status.ACTIVE,
        "password": "test123456"
    }
    create_response = client.post("/api/v1/admin/", json=data)
    admin_id = create_response.json()["id"]

    # 获取该管理员
    response = client.get(f"/api/v1/admin/{admin_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == data["name"]
    assert content["status"] == data["status"]
    assert "roles" in content  # 验证返回了角色列表
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content
    assert "password" not in content


def test_read_admins(client):
    """测试获取管理员列表"""
    response = client.get("/api/v1/admin/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)


def test_update_admin(client):
    """测试更新管理员"""
    # 先创建一个管理员
    data = {
        "name": "测试管理员3",
        "status": Status.ACTIVE,
        "password": "test123456"
    }
    create_response = client.post("/api/v1/admin/", json=data)
    admin_id = create_response.json()["id"]

    # 更新管理员信息
    update_data = {
        "status": Status.INACTIVE
    }
    response = client.put(f"/api/v1/admin/{admin_id}", json=update_data)
    assert response.status_code == 200
    content = response.json()
    assert content["status"] == update_data["status"]
    assert "name" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content
    assert "password" not in content


def test_delete_admin(client):
    """测试删除管理员"""
    # 先创建一个管理员
    data = {
        "name": "测试管理员4",
        "status": Status.ACTIVE,
        "password": "test123456"
    }
    create_response = client.post("/api/v1/admin/", json=data)
    admin_id = create_response.json()["id"]

    # 删除该管理员
    response = client.delete(f"/api/v1/admin/{admin_id}")
    assert response.status_code == 204


# 角色测试用例
def test_create_role(client):
    """测试创建角色"""
    data = {
        "name": f"测试角色_{generate_random_string()}",
        "description": "这是一个测试角色",
        "status": Status.ACTIVE
    }
    response = client.post("/api/v1/admin/roles/", json=data)
    assert response.status_code == 201
    content = response.json()
    assert content["name"] == data["name"]
    assert content["description"] == data["description"]
    assert content["status"] == data["status"]
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_read_role(client):
    """测试获取角色"""
    # 先创建一个角色
    role_name = f"测试角色_{generate_random_string()}"
    data = {
        "name": role_name,
        "description": "这是一个测试角色",
        "status": Status.ACTIVE
    }
    create_response = client.post("/api/v1/admin/roles/", json=data)
    role_id = create_response.json()["id"]

    # 获取该角色
    response = client.get(f"/api/v1/admin/roles/{role_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == data["name"]
    assert content["description"] == data["description"]
    assert "permissions" in content  # 验证返回了权限列表
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_read_roles(client):
    """测试获取角色列表"""
    response = client.get("/api/v1/admin/roles/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)


def test_update_role(client):
    """测试更新角色"""
    # 先创建一个角色
    role_name = f"测试角色_{generate_random_string()}"
    data = {
        "name": role_name,
        "description": "这是一个测试角色",
        "status": Status.ACTIVE
    }
    create_response = client.post("/api/v1/admin/roles/", json=data)
    role_id = create_response.json()["id"]

    # 更新角色信息
    update_data = {
        "name": f"更新角色_{generate_random_string()}",
        "description": "这是更新后的角色描述",
        "status": Status.INACTIVE
    }
    response = client.put(f"/api/v1/admin/roles/{role_id}", json=update_data)
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == update_data["name"]
    assert content["description"] == update_data["description"]
    assert content["status"] == update_data["status"]
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_delete_role(client):
    """测试删除角色"""
    # 先创建一个角色
    data = {
        "name": "测试角色4",
        "description": "这是一个测试角色",
        "status": Status.ACTIVE
    }
    create_response = client.post("/api/v1/admin/roles/", json=data)
    role_id = create_response.json()["id"]

    # 删除该角色
    response = client.delete(f"/api/v1/admin/roles/{role_id}")
    assert response.status_code == 204


# 权限测试用例
def test_create_permission(client):
    """测试创建权限"""
    data = {
        "name": f"测试权限_{generate_random_string()}",
        "code": f"test:permission:{generate_random_string()}",
        "permission_type": "menu",
        "description": "这是一个测试权限",
        "status": Status.ACTIVE
    }
    response = client.post("/api/v1/admin/permissions/", json=data)
    assert response.status_code == 201
    content = response.json()
    assert content["name"] == data["name"]
    assert content["code"] == data["code"]
    assert content["permission_type"] == data["permission_type"]
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_read_permission(client):
    """测试获取权限"""
    # 先创建一个权限
    data = {
        "name": f"测试权限_{generate_random_string()}",
        "code": f"test:permission:{generate_random_string()}",
        "permission_type": "menu",
        "description": "这是一个测试权限",
        "status": Status.ACTIVE
    }
    create_response = client.post("/api/v1/admin/permissions/", json=data)
    permission_id = create_response.json()["id"]

    # 获取该权限
    response = client.get(f"/api/v1/admin/permissions/{permission_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == data["name"]
    assert content["code"] == data["code"]
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_read_permissions(client):
    """测试获取权限列表"""
    response = client.get("/api/v1/admin/permissions/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)


def test_update_permission(client):
    """测试更新权限"""
    # 先创建一个权限
    data = {
        "name": f"测试权限_{generate_random_string()}",
        "code": f"test:permission:{generate_random_string()}",
        "permission_type": "menu",
        "description": "这是一个测试权限",
        "status": Status.ACTIVE
    }
    create_response = client.post("/api/v1/admin/permissions/", json=data)
    permission_id = create_response.json()["id"]

    # 更新权限信息
    update_data = {
        "name": f"更新权限_{generate_random_string()}",
        "code": f"test:permission:updated:{generate_random_string()}",
        "permission_type": "button",
        "description": "这是更新后的权限描述",
        "status": Status.INACTIVE
    }
    response = client.put(f"/api/v1/admin/permissions/{permission_id}", json=update_data)
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == update_data["name"]
    assert content["code"] == update_data["code"]
    assert content["permission_type"] == update_data["permission_type"]
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_delete_permission(client):
    """测试删除权限"""
    # 先创建一个权限
    data = {
        "name": f"测试权限_{generate_random_string()}",
        "code": f"test:permission:{generate_random_string()}",
        "permission_type": "menu",
        "description": "这是一个测试权限",
        "status": Status.ACTIVE
    }
    create_response = client.post("/api/v1/admin/permissions/", json=data)
    permission_id = create_response.json()["id"]

    # 删除该权限
    response = client.delete(f"/api/v1/admin/permissions/{permission_id}")
    assert response.status_code == 204


# 管理员-角色关联测试用例
def test_add_role_to_admin(client):
    """测试为管理员添加角色"""
    # 先创建一个管理员
    admin_data = {
        "name": f"测试管理员_{generate_random_string()}",
        "status": Status.ACTIVE,
        "password": "test123456"
    }
    admin_response = client.post("/api/v1/admin/", json=admin_data)
    admin_id = admin_response.json()["id"]

    # 创建一个角色
    role_data = {
        "name": f"测试角色_{generate_random_string()}",
        "description": "这是一个测试角色",
        "status": Status.ACTIVE
    }
    role_response = client.post("/api/v1/admin/roles/", json=role_data)
    role_id = role_response.json()["id"]

    # 为管理员添加角色
    response = client.post(f"/api/v1/admin/{admin_id}/roles/{role_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["id"] == admin_id

    # 验证角色是否已添加
    role_found = False
    for role in content["roles"]:
        if role["id"] == role_id:
            role_found = True
            break
    assert role_found


def test_remove_role_from_admin(client):
    """测试从管理员移除角色"""
    # 先创建一个管理员
    admin_data = {
        "name": f"测试管理员_{generate_random_string()}",
        "status": Status.ACTIVE,
        "password": "test123456"
    }
    admin_response = client.post("/api/v1/admin/", json=admin_data)
    admin_id = admin_response.json()["id"]

    # 创建一个角色
    role_data = {
        "name": f"测试角色_{generate_random_string()}",
        "description": "这是一个测试角色",
        "status": Status.ACTIVE
    }
    role_response = client.post("/api/v1/admin/roles/", json=role_data)
    role_id = role_response.json()["id"]

    # 为管理员添加角色
    client.post(f"/api/v1/admin/{admin_id}/roles/{role_id}")

    # 从管理员移除角色
    response = client.delete(f"/api/v1/admin/{admin_id}/roles/{role_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["id"] == admin_id

    # 验证角色是否已移除
    role_found = False
    for role in content["roles"]:
        if role["id"] == role_id:
            role_found = True
            break
    assert not role_found


def test_get_admin_roles(client):
    """测试获取管理员的所有角色"""
    # 先创建一个管理员
    admin_data = {
        "name": f"测试管理员_{generate_random_string()}",
        "status": Status.ACTIVE,
        "password": "test123456"
    }
    admin_response = client.post("/api/v1/admin/", json=admin_data)
    admin_id = admin_response.json()["id"]

    # 创建一个角色
    role_data = {
        "name": f"测试角色_{generate_random_string()}",
        "description": "这是一个测试角色",
        "status": Status.ACTIVE
    }
    role_response = client.post("/api/v1/admin/roles/", json=role_data)
    role_id = role_response.json()["id"]

    # 为管理员添加角色
    client.post(f"/api/v1/admin/{admin_id}/roles/{role_id}")

    # 获取管理员的所有角色
    response = client.get(f"/api/v1/admin/{admin_id}/roles")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)

    # 验证返回的角色中包含新添加的角色
    role_found = False
    for role in content:
        if role["id"] == role_id:
            role_found = True
            break
    assert role_found


# 角色-权限关联测试用例
def test_add_permission_to_role(client):
    """测试为角色添加权限"""
    # 先创建一个角色
    role_data = {
        "name": f"测试角色_{generate_random_string()}",
        "description": "这是一个测试角色",
        "status": Status.ACTIVE
    }
    role_response = client.post("/api/v1/admin/roles/", json=role_data)
    role_id = role_response.json()["id"]

    # 创建一个权限
    permission_data = {
        "name": f"测试权限_{generate_random_string()}",
        "code": f"test:permission:{generate_random_string()}",
        "permission_type": "menu",
        "description": "这是一个测试权限",
        "status": Status.ACTIVE
    }
    permission_response = client.post("/api/v1/admin/permissions/", json=permission_data)
    permission_id = permission_response.json()["id"]

    # 为角色添加权限
    response = client.post(f"/api/v1/admin/roles/{role_id}/permissions/{permission_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["id"] == role_id

    # 验证权限是否已添加
    permission_found = False
    for permission in content["permissions"]:
        if permission["id"] == permission_id:
            permission_found = True
            break
    assert permission_found


def test_remove_permission_from_role(client):
    """测试从角色移除权限"""
    # 先创建一个角色
    role_data = {
        "name": f"测试角色_{generate_random_string()}",
        "description": "这是一个测试角色",
        "status": Status.ACTIVE
    }
    role_response = client.post("/api/v1/admin/roles/", json=role_data)
    role_id = role_response.json()["id"]

    # 创建一个权限
    permission_data = {
        "name": f"测试权限_{generate_random_string()}",
        "code": f"test:permission:{generate_random_string()}",
        "permission_type": "menu",
        "description": "这是一个测试权限",
        "status": Status.ACTIVE
    }
    permission_response = client.post("/api/v1/admin/permissions/", json=permission_data)
    permission_id = permission_response.json()["id"]

    # 为角色添加权限
    client.post(f"/api/v1/admin/roles/{role_id}/permissions/{permission_id}")

    # 从角色移除权限
    response = client.delete(f"/api/v1/admin/roles/{role_id}/permissions/{permission_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["id"] == role_id

    # 验证权限是否已移除
    permission_found = False
    for permission in content["permissions"]:
        if permission["id"] == permission_id:
            permission_found = True
            break
    assert not permission_found


def test_get_role_permissions(client):
    """测试获取角色的所有权限"""
    # 先创建一个角色
    role_data = {
        "name": f"测试角色_{generate_random_string()}",
        "description": "这是一个测试角色",
        "status": Status.ACTIVE
    }
    role_response = client.post("/api/v1/admin/roles/", json=role_data)
    role_id = role_response.json()["id"]

    # 创建一个权限
    permission_data = {
        "name": f"测试权限_{generate_random_string()}",
        "code": f"test:permission:{generate_random_string()}",
        "permission_type": "menu",
        "description": "这是一个测试权限",
        "status": Status.ACTIVE
    }
    permission_response = client.post("/api/v1/admin/permissions/", json=permission_data)
    permission_id = permission_response.json()["id"]

    # 为角色添加权限
    client.post(f"/api/v1/admin/roles/{role_id}/permissions/{permission_id}")

    # 获取角色的所有权限
    response = client.get(f"/api/v1/admin/roles/{role_id}/permissions")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)

    # 验证返回的权限中包含新添加的权限
    permission_found = False
    for permission in content:
        if permission["id"] == permission_id:
            permission_found = True
            break
    assert permission_found
