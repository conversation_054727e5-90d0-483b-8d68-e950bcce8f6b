import arrow
import pytest

from app.models.approval import ApprovalStatus
from app.models.enum import Status
from app.models.order import OrderStatus, PaymentStatus, PaymentMethod, OrderItem, Order
from app.models.product import Product
from app.models.reservation import ReservationStatus, ReservationRequest
from app.models.rule import Rule, RuleItem
from app.models.user import User, EnterpriseUserRelation, Enterprise, PersonalUser, UserType
from tests.conftest import TestingSessionLocal
from tests.utils import generate_random_string


@pytest.fixture
def prepare_test_data():
    """准备审批测试所需的基础数据"""
    db = TestingSessionLocal()

    # 创建测试用户
    user = User(
        username=f"test_user_{generate_random_string()}",
        status=Status.ACTIVE
    )
    db.add(user)
    db.flush()

    # 创建个人用户
    personal_user = PersonalUser(
        phone=f"138{generate_random_string(8)}",
        real_name=f"测试用户_{generate_random_string()}",
        username=f"personal_user_{generate_random_string()}",
        status=Status.ACTIVE,
        type=UserType.PERSONAL
    )
    db.add(personal_user)
    db.flush()

    # 创建企业用户
    enterprise = Enterprise(
        username=f"test_enterprise_{generate_random_string()}",
        status=Status.ACTIVE,
        company_name=f"测试企业_{generate_random_string()}",
        business_license=f"BL{generate_random_string(10)}",
        type=UserType.ENTERPRISE
    )
    db.add(enterprise)
    db.flush()

    # 创建企业用户关系
    enterprise_user_relation = EnterpriseUserRelation(
        enterprise_id=enterprise.id,
        personal_user_id=personal_user.id,
        is_admin=True,
        relation_status=Status.ACTIVE
    )
    db.add(enterprise_user_relation)
    db.flush()

    # 创建测试产品
    product = Product(
        name=f"测试产品_{generate_random_string()}",
        price=100.0,
        description="测试产品描述",
        stock=10,
        status=Status.ACTIVE
    )
    db.add(product)
    db.flush()

    # 创建测试规则
    rule = Rule(
        name=f"测试规则_{generate_random_string()}",
        status=Status.ACTIVE
    )
    db.add(rule)
    db.flush()

    rule_item = RuleItem(
        rule_id=rule.id,
        name=f"测试规则项_{generate_random_string()}",
        start_time=arrow.now('Asia/Shanghai').datetime,
        end_time=arrow.now('Asia/Shanghai').shift(days=1).datetime,
        start_time_cron_str="* 2 2 * *",
        end_time_cron_str="* 2 2 * *",
        quantity=10,
        order=1,
        allowed_operations="",
        forbidden_operations=""
    )
    db.add(rule_item)
    db.flush()

    # 添加产品和规则的关联关系
    product.rules.append(rule)
    db.flush()

    # 创建测试订单
    order = Order(
        user_id=user.id,
        status=OrderStatus.PENDING,
        payment_status=PaymentStatus.UNPAID,
        total_amount=100.0,
        payable_amount=90.0,
        actual_amount_paid=0.0,
        payment_method=PaymentMethod.ACCOUNT_BALANCE
    )
    db.add(order)
    db.flush()

    # 创建测试订单项
    order_item = OrderItem(
        order_id=order.id,
        product_id=product.id,
        quantity=1,
        price=100.0,
        subtotal=100.0,
        final_price=90.0,
        payable_amount=90.0
    )
    db.add(order_item)
    db.flush()

    # 创建测试预订请求
    reservation_request = ReservationRequest(
        order_item_id=order_item.id,
        user_id=user.id,
        product_id=product.id,
        rule_id=rule.id,
        rule_item_id=rule_item.id,
        status=ReservationStatus.PENDING,
        reservation_period="2023-06-01 ~ 2023-06-10",
        reservation_time=arrow.now('Asia/Shanghai').datetime,
        verification_code=f"VC{generate_random_string()}"
    )
    db.add(reservation_request)
    db.commit()

    test_data = {
        "user_id": user.id,
        "personal_user_id": personal_user.id,
        "enterprise_id": enterprise.id,
        "enterprise_user_relation_id": enterprise_user_relation.id,
        "product_id": product.id,
        "rule_id": rule.id,
        "order_id": order.id,
        "order_item_id": order_item.id,
        "reservation_request_id": reservation_request.id
    }

    db.close()
    return test_data


# 审批请求测试用例
@pytest.fixture
def create_test_approval(client, disable_foreign_keys, prepare_test_data):
    """创建测试审批请求的固定函数"""

    def _create_approval(status=ApprovalStatus.PENDING):
        data = {
            "reservation_request_id": prepare_test_data["reservation_request_id"],
            "applicant_id": prepare_test_data["enterprise_user_relation_id"],
            "enterprise_id": prepare_test_data["enterprise_id"],
            "status": status.value,
            "comment": f"测试审批_{generate_random_string()}"
        }
        response = client.post("/api/v1/approval/", json=data)
        return response.json()

    return _create_approval


def test_create_approval_request(client, disable_foreign_keys, prepare_test_data):
    """测试创建审批请求"""
    data = {
        "reservation_request_id": prepare_test_data["reservation_request_id"],
        "applicant_id": prepare_test_data["enterprise_user_relation_id"],
        "enterprise_id": prepare_test_data["enterprise_id"],
        "status": ApprovalStatus.PENDING.value,
        "comment": f"测试审批_{generate_random_string()}"
    }
    response = client.post("/api/v1/approval/", json=data)
    assert response.status_code == 201
    content = response.json()
    assert content["reservation_request_id"] == data["reservation_request_id"]
    assert content["applicant_id"] == data["applicant_id"]
    assert content["enterprise_id"] == data["enterprise_id"]
    assert content["status"] == data["status"]
    assert content["comment"] == data["comment"]
    assert "id" in content
    assert "created_at" in content


def test_read_approval_request(client, disable_foreign_keys, create_test_approval):
    """测试获取审批请求详情"""
    # 先创建一个审批请求
    approval = create_test_approval()
    approval_id = approval["id"]

    # 获取该审批请求
    response = client.get(f"/api/v1/approval/{approval_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["id"] == approval_id
    assert content["reservation_request_id"] == approval["reservation_request_id"]
    assert content["applicant_id"] == approval["applicant_id"]
    assert content["enterprise_id"] == approval["enterprise_id"]
    assert content["status"] == approval["status"]
    # 验证返回了关联数据
    assert "applicant" in content
    assert "enterprise" in content
    assert "reservation_request" in content


def test_read_approval_requests(client, disable_foreign_keys, create_test_approval):
    """测试获取审批请求列表"""
    # 创建几个审批请求
    for _ in range(3):
        create_test_approval()

    # 获取审批请求列表
    response = client.get("/api/v1/approval/")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)
    assert len(content) >= 3


def test_update_approval_request(client, disable_foreign_keys, create_test_approval):
    """测试更新审批请求"""
    # 先创建一个审批请求
    approval = create_test_approval()
    approval_id = approval["id"]

    # 更新审批请求
    update_data = {
        "status": ApprovalStatus.APPROVED.value,
        "comment": f"更新测试审批_{generate_random_string()}",
        "approver_id": approval["applicant_id"],  # 简化测试，使用申请人作为审批人
        "approval_time": arrow.now('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')
    }
    response = client.put(f"/api/v1/approval/{approval_id}", json=update_data)
    assert response.status_code == 200
    content = response.json()
    assert content["id"] == approval_id
    assert content["status"] == update_data["status"]
    assert content["comment"] == update_data["comment"]
    assert content["approver_id"] == update_data["approver_id"]
    assert "approval_time" in content


def test_delete_approval_request(client, disable_foreign_keys, create_test_approval):
    """测试删除审批请求"""
    # 先创建一个审批请求
    approval = create_test_approval()
    approval_id = approval["id"]

    # 删除审批请求
    response = client.delete(f"/api/v1/approval/{approval_id}")
    assert response.status_code == 204

    # 验证无法获取已删除的审批请求
    response = client.get(f"/api/v1/approval/{approval_id}")
    assert response.status_code == 404


def test_get_enterprise_approvals(client, disable_foreign_keys, create_test_approval, prepare_test_data):
    """测试获取企业的审批请求"""
    # 创建几个审批请求
    for _ in range(3):
        create_test_approval()

    # 获取企业的审批请求
    enterprise_id = prepare_test_data["enterprise_id"]
    response = client.get(f"/api/v1/approval/enterprise/{enterprise_id}")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)
    assert len(content) >= 3
    for item in content:
        assert item["enterprise_id"] == enterprise_id


def test_get_applicant_approvals(client, disable_foreign_keys, create_test_approval, prepare_test_data):
    """测试获取申请人的审批请求"""
    # 创建几个审批请求
    for _ in range(3):
        create_test_approval()

    # 获取申请人的审批请求
    applicant_id = prepare_test_data["enterprise_user_relation_id"]
    response = client.get(f"/api/v1/approval/applicant/{applicant_id}")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)
    assert len(content) >= 3
    for item in content:
        assert item["applicant_id"] == applicant_id


def test_get_reservation_request_approval(client, disable_foreign_keys, create_test_approval, prepare_test_data):
    """测试获取预订请求的审批"""
    # 创建一个审批请求
    create_test_approval()

    # 获取指定预订请求的审批
    reservation_request_id = prepare_test_data["reservation_request_id"]
    response = client.get(f"/api/v1/approval/reservation-request/{reservation_request_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["reservation_request_id"] == reservation_request_id


def test_get_approvals_by_status(client, disable_foreign_keys, create_test_approval):
    """测试获取指定状态的审批请求"""
    # 创建几个不同状态的审批请求
    create_test_approval(status=ApprovalStatus.PENDING)
    create_test_approval(status=ApprovalStatus.APPROVED)
    create_test_approval(status=ApprovalStatus.REJECTED)

    # 获取指定状态的审批请求
    status = ApprovalStatus.PENDING
    response = client.get(f"/api/v1/approval/status/{status.value}")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)
    assert len(content) >= 1
    for item in content:
        assert item["status"] == status.value


def test_approve_request(client, disable_foreign_keys, create_test_approval, prepare_test_data):
    """测试审批通过"""
    # 创建一个审批请求
    approval = create_test_approval()
    approval_id = approval["id"]

    # 审批通过
    approver_id = prepare_test_data["enterprise_user_relation_id"]
    comment = f"审批通过_{generate_random_string()}"
    response = client.post(
        f"/api/v1/approval/{approval_id}/approve",
        params={"approver_id": approver_id, "comment": comment}
    )
    assert response.status_code == 200
    content = response.json()
    assert content["success"] is True
    assert "审批已通过" in content["message"]
    assert content["approval"]["id"] == approval_id
    assert content["approval"]["status"] == ApprovalStatus.APPROVED.value
    assert content["approval"]["comment"] == comment
    assert content["approval"]["approver_id"] == approver_id
    assert "approval_time" in content["approval"]


def test_reject_request(client, disable_foreign_keys, create_test_approval, prepare_test_data):
    """测试审批拒绝"""
    # 创建一个审批请求
    approval = create_test_approval()
    approval_id = approval["id"]

    # 审批拒绝
    approver_id = prepare_test_data["enterprise_user_relation_id"]
    comment = f"审批拒绝_{generate_random_string()}"
    response = client.post(
        f"/api/v1/approval/{approval_id}/reject",
        params={"approver_id": approver_id, "comment": comment}
    )
    assert response.status_code == 200
    content = response.json()
    assert content["success"] is True
    assert "审批已拒绝" in content["message"]
    assert content["approval"]["id"] == approval_id
    assert content["approval"]["status"] == ApprovalStatus.REJECTED.value
    assert content["approval"]["comment"] == comment
    assert content["approval"]["approver_id"] == approver_id
    assert "approval_time" in content["approval"]
