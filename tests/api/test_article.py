# 文章测试用例
from tests.utils import generate_random_string


def test_create_article(client):
    """测试创建文章"""
    data = {
        "name": f"测试文章名称{generate_random_string()}",
        "content": f"这是测试文章内容{generate_random_string()}",
        "image": f"http://example.com/image_{generate_random_string()}.jpg",
        "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
        "sort_order": 1,
        "summary": f"这是文章摘要{generate_random_string()}"
    }
    response = client.post("/api/v1/content/article/", json=data)
    assert response.status_code == 201
    article = response.json()
    assert article["name"] == data["name"]
    assert article["content"] == data["content"]
    assert article["image"] == data["image"]
    assert article["thumbnail"] == data["thumbnail"]
    assert article["sort_order"] == data["sort_order"]
    assert article["summary"] == data["summary"]
    assert "id" in article
    assert "created_at" in article
    assert "updated_at" in article


def test_read_article(client):
    """测试获取文章详情"""
    # 先创建一个文章
    data = {
        "name": f"测试文章名称{generate_random_string()}",
        "content": f"这是测试文章内容{generate_random_string()}",
        "image": f"http://example.com/image_{generate_random_string()}.jpg",
        "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
        "sort_order": 1,
        "summary": f"这是文章摘要{generate_random_string()}"
    }
    create_response = client.post("/api/v1/content/article/", json=data)
    article_id = create_response.json()["id"]

    # 获取该文章
    response = client.get(f"/api/v1/content/article/{article_id}")
    assert response.status_code == 200
    article = response.json()
    assert article["name"] == data["name"]
    assert article["content"] == data["content"]
    assert article["image"] == data["image"]
    assert article["thumbnail"] == data["thumbnail"]
    assert article["sort_order"] == data["sort_order"]
    assert article["summary"] == data["summary"]
    assert "id" in article
    assert "created_at" in article
    assert "updated_at" in article
    assert "files" in article


def test_read_articles(client):
    """测试获取文章列表"""
    # 先创建几个文章
    for i in range(3):
        data = {
            "name": f"测试文章名称{generate_random_string()}",
            "content": f"这是测试文章内容{generate_random_string()}_{i}",
            "image": f"http://example.com/image_{generate_random_string()}.jpg",
            "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
            "sort_order": i,
            "summary": f"这是文章摘要{generate_random_string()}"
        }
        client.post("/api/v1/content/article/", json=data)

    # 获取文章列表
    response = client.get("/api/v1/content/article/")
    assert response.status_code == 200
    articles = response.json()
    assert isinstance(articles, list)
    assert len(articles) >= 3


def test_update_article(client):
    """测试更新文章"""
    # 先创建一个文章
    data = {
        "name": f"测试文章名称{generate_random_string()}",
        "content": f"这是测试文章内容{generate_random_string()}",
        "image": f"http://example.com/image_{generate_random_string()}.jpg",
        "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
        "sort_order": 1,
        "summary": f"这是文章摘要{generate_random_string()}"
    }
    create_response = client.post("/api/v1/content/article/", json=data)
    article_id = create_response.json()["id"]

    # 更新文章
    update_data = {
        "name": f"这是更新后的文章名称{generate_random_string()}",
        "content": f"这是更新后的文章内容{generate_random_string()}",
        "image": f"http://example.com/updated_image_{generate_random_string()}.jpg",
        "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
        "sort_order": 2,
        "summary": f"这是更新后的文章摘要{generate_random_string()}"
    }
    response = client.put(f"/api/v1/content/article/{article_id}", json=update_data)
    assert response.status_code == 200
    updated_article = response.json()
    assert updated_article["name"] == update_data["name"]
    assert updated_article["content"] == update_data["content"]
    assert updated_article["image"] == update_data["image"]
    assert updated_article["thumbnail"] == update_data["thumbnail"]
    assert updated_article["sort_order"] == update_data["sort_order"]
    assert updated_article["summary"] == update_data["summary"]
    assert "id" in updated_article
    assert "created_at" in updated_article
    assert "updated_at" in updated_article


def test_delete_article(client):
    """测试删除文章"""
    # 先创建一个文章
    data = {
        "name": f"这是测试文章名称{generate_random_string()}",
        "content": f"这是测试文章内容{generate_random_string()}",
        "image": f"http://example.com/image_{generate_random_string()}.jpg",
        "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
        "sort_order": 1,
        "summary": f"这是文章摘要{generate_random_string()}"
    }
    create_response = client.post("/api/v1/content/article/", json=data)
    article_id = create_response.json()["id"]

    # 删除文章
    response = client.delete(f"/api/v1/content/article/{article_id}")
    assert response.status_code == 204

    # 确认已删除
    get_response = client.get(f"/api/v1/content/article/{article_id}")
    assert get_response.status_code == 404


def test_article_by_product(client):
    """测试获取产品相关文章"""
    # 先创建一个产品
    product_data = {
        "name": f"测试产品{generate_random_string()}",
        "description": f"测试产品描述{generate_random_string()}",
        "price": 100.0,
        "stock": 10
    }
    product_response = client.post("/api/v1/product/", json=product_data)
    assert product_response.status_code == 201
    product_id = product_response.json()["id"]

    # 创建文章
    article_data = {
        "name": f"测试文章名称{generate_random_string()}",
        "content": f"这是测试文章内容{generate_random_string()}",
        "image": f"http://example.com/image_{generate_random_string()}.jpg",
        "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
        "sort_order": 1,
        "summary": f"这是文章摘要{generate_random_string()}"
    }
    article_response = client.post("/api/v1/content/article/", json=article_data)
    assert article_response.status_code == 201
    article_id = article_response.json()["id"]

    # 将文章绑定到产品
    binding_data = {
        "product_id": product_id,
        "content_ids": [article_id]
    }
    binding_response = client.post("/api/v1/product/add/contents", json=binding_data)
    assert binding_response.status_code == 200

    # 获取产品相关文章
    product_articles_response = client.get(f"/api/v1/content/article/by/product/{product_id}/")
    assert product_articles_response.status_code == 200
    product_articles = product_articles_response.json()
    assert isinstance(product_articles, list)
    assert len(product_articles) >= 1
    assert any(article["id"] == article_id for article in product_articles)
