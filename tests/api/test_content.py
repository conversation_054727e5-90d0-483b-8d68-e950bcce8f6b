# 内容测试用例
from app.models.enum import Status
from tests.utils import generate_random_string


def test_create_content(client):
    """测试创建内容"""
    data = {
        "name": f"测试内容名称{generate_random_string()}",
        "content": f"这是测试内容{generate_random_string()}",
        "image": f"http://example.com/image_{generate_random_string()}.jpg",
        "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
        "sort_order": 1
    }
    response = client.post("/api/v1/content/", json=data)
    assert response.status_code == 201
    content = response.json()
    assert content["name"] == data["name"]
    assert content["content"] == data["content"]
    assert content["image"] == data["image"]
    assert content["thumbnail"] == data["thumbnail"]
    assert content["sort_order"] == data["sort_order"]
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content
    assert "type" not in content


def test_read_content(client):
    """测试获取内容详情"""
    # 先创建一个内容
    data = {
        "name": f"测试内容名称{generate_random_string()}",
        "content": f"这是测试内容{generate_random_string()}",
        "image": f"http://example.com/image_{generate_random_string()}.jpg",
        "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
        "sort_order": 1
    }
    create_response = client.post("/api/v1/content/", json=data)
    content_id = create_response.json()["id"]

    # 获取该内容
    response = client.get(f"/api/v1/content/{content_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == data["name"]
    assert content["content"] == data["content"]
    assert content["image"] == data["image"]
    assert content["thumbnail"] == data["thumbnail"]
    assert content["sort_order"] == data["sort_order"]
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content
    assert "files" in content


def test_read_contents(client):
    """测试获取内容列表"""
    # 先创建几个内容
    for i in range(3):
        data = {
            "name": f"测试内容名称{generate_random_string()}",
            "content": f"这是测试内容{generate_random_string()}_{i}",
            "image": f"http://example.com/image_{generate_random_string()}.jpg",
            "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
            "sort_order": i
        }
        client.post("/api/v1/content/", json=data)

    # 获取内容列表
    response = client.get("/api/v1/content/")
    assert response.status_code == 200
    contents = response.json()
    assert isinstance(contents, list)
    assert len(contents) >= 3


def test_update_content(client):
    """测试更新内容"""
    # 先创建一个内容
    data = {
        "name": f"测试内容名称{generate_random_string()}",
        "content": f"这是测试内容{generate_random_string()}",
        "image": f"http://example.com/image_{generate_random_string()}.jpg",
        "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
        "sort_order": 1
    }
    create_response = client.post("/api/v1/content/", json=data)
    content_id = create_response.json()["id"]

    # 更新内容
    update_data = {
        "name": f"这是更新后的m内容名称{generate_random_string()}",
        "content": f"这是更新后的内容{generate_random_string()}",
        "image": f"http://example.com/updated_image_{generate_random_string()}.jpg",
        "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
        "sort_order": 2
    }
    response = client.put(f"/api/v1/content/{content_id}", json=update_data)
    assert response.status_code == 200
    updated_content = response.json()
    assert updated_content["name"] == update_data["name"]
    assert updated_content["content"] == update_data["content"]
    assert updated_content["image"] == update_data["image"]
    assert updated_content["thumbnail"] == update_data["thumbnail"]
    assert updated_content["sort_order"] == update_data["sort_order"]
    assert "id" in updated_content
    assert "created_at" in updated_content
    assert "updated_at" in updated_content


def test_delete_content(client):
    """测试删除内容"""
    # 先创建一个内容
    data = {
        "name": f"这是测试内容名称{generate_random_string()}",
        "content": f"这是测试内容{generate_random_string()}",
        "image": f"http://example.com/image_{generate_random_string()}.jpg",
        "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
        "sort_order": 1
    }
    create_response = client.post("/api/v1/content/", json=data)
    content_id = create_response.json()["id"]

    # 删除内容
    response = client.delete(f"/api/v1/content/{content_id}")
    assert response.status_code == 204

    # 确认已删除
    get_response = client.get(f"/api/v1/content/{content_id}")
    assert get_response.status_code == 404


# 批量文件绑定解绑内容测试
def test_bulk_file_content_binding(client):
    """测试批量文件绑定解绑内容"""
    # 创建一个内容
    content_data = {
        "name": f"这是测试内容名称{generate_random_string()}",
        "content": f"这是批量测试内容_{generate_random_string()}",
        "image": f"http://example.com/image_{generate_random_string()}.jpg",
        "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
        "sort_order": 1
    }
    content_response = client.post("/api/v1/content/", json=content_data)
    assert content_response.status_code == 201
    content_id = content_response.json()["id"]

    # 创建多个文件
    file_ids = []
    for i in range(3):
        file_data = {
            "filename": f"批量测试文件_{i}_{generate_random_string()}.jpg",
            "file_type": "jpg",
            "file_path": f"/uploads/test_{i}_{generate_random_string()}.jpg",
            "file_size": 1024 + i
        }
        file_response = client.post("/api/v1/file/files/", json=file_data)
        assert file_response.status_code == 201
        file_ids.append(file_response.json()["id"])

    # 批量绑定文件到内容
    binding_data = {
        "file_ids": file_ids,
        "content_id": content_id
    }
    bind_response = client.post("/api/v1/content/add/files", json=binding_data)
    assert bind_response.status_code == 200
    bind_result = bind_response.json()
    assert bind_result["success_count"] == 3
    assert len(bind_result["success_ids"]) == 3
    assert bind_result["failed_count"] == 0

    # 验证绑定是否成功
    content_files = client.get(f"/api/v1/content/{content_id}/files/")
    assert content_files.status_code == 200
    files = content_files.json()
    for file_id in file_ids:
        assert any(file["id"] == file_id for file in files)

    # 批量解绑文件和内容
    unbind_response = client.post("/api/v1/content/remove/files", json=binding_data)
    assert unbind_response.status_code == 200
    unbind_result = unbind_response.json()
    assert unbind_result["success_count"] == 3
    assert len(unbind_result["success_ids"]) == 3
    assert unbind_result["failed_count"] == 0

    # 验证解绑是否成功
    content_files = client.get(f"/api/v1/content/{content_id}/files/")
    assert content_files.status_code == 200
    files = content_files.json()
    for file_id in file_ids:
        assert not any(file["id"] == file_id for file in files)


def test_batch_set_content_status(client):
    """测试批量设置内容状态"""
    # 创建多个内容
    content_ids = []
    for i in range(3):
        data = {
            "name": f"状态测试内容_{i}_{generate_random_string()}",
            "content": f"这是状态测试内容_{i}_{generate_random_string()}",
            "image": f"http://example.com/image_{generate_random_string()}.jpg",
            "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
            "sort_order": i
        }
        create_response = client.post("/api/v1/content/", json=data)
        assert create_response.status_code == 201
        content_ids.append(create_response.json()["id"])

    # 批量设置内容状态为无效
    batch_data = {
        "content_ids": content_ids,
        "status": Status.INACTIVE.value
    }
    response = client.post("/api/v1/content/status/", json=batch_data)
    assert response.status_code == 200
    result = response.json()
    assert result["code"] == 200
    assert "成功更新" in result["message"]
    assert result["data"]["affected_count"] == 3

    # 验证状态是否更新成功
    for content_id in content_ids:
        content_response = client.get(f"/api/v1/content/{content_id}")
        assert content_response.status_code == 200
        content = content_response.json()
        assert content["status"] == Status.INACTIVE.value

    # 批量设置内容状态为有效
    batch_data = {
        "content_ids": content_ids,
        "status": Status.ACTIVE.value
    }
    response = client.post("/api/v1/content/status/", json=batch_data)
    assert response.status_code == 200
    result = response.json()
    assert result["code"] == 200
    assert "成功更新" in result["message"]
    assert result["data"]["affected_count"] == 3

    # 验证状态是否更新成功
    for content_id in content_ids:
        content_response = client.get(f"/api/v1/content/{content_id}")
        assert content_response.status_code == 200
        content = content_response.json()
        assert content["status"] == Status.ACTIVE.value
