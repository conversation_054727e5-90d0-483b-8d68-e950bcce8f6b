# 菜品测试用例
import pprint

from tests.utils import generate_random_string


def test_create_dish(client):
    """测试创建菜品"""
    data = {
        "name": f"测试菜品名称{generate_random_string()}",
        "content": f"这是测试菜品内容{generate_random_string()}",
        "image": f"http://example.com/image_{generate_random_string()}.jpg",
        "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
        "sort_order": 1
    }
    response = client.post("/api/v1/content/dish/", json=data)
    assert response.status_code == 201
    result = response.json()
    dish = result.get("data", {})
    assert dish["name"] == data["name"]
    assert dish["content"] == data["content"]
    assert dish["image"] == data["image"]
    assert dish["thumbnail"] == data["thumbnail"]
    assert dish["sort_order"] == data["sort_order"]
    assert "id" in dish
    assert "created_at" in dish
    assert "updated_at" in dish


def test_read_dish(client):
    """测试获取菜品详情"""
    # 先创建一个菜品
    data = {
        "name": f"测试菜品名称{generate_random_string()}",
        "content": f"这是测试菜品内容{generate_random_string()}",
        "image": f"http://example.com/image_{generate_random_string()}.jpg",
        "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
        "sort_order": 1
    }
    create_response = client.post("/api/v1/content/dish/", json=data)
    dish_id = create_response.json().get("data", {}).get("id")

    # 获取该菜品
    response = client.get(f"/api/v1/content/dish/{dish_id}")
    assert response.status_code == 200
    pprint.pprint(response.json())
    dish = response.json().get("data", None)
    assert dish["name"] == data["name"]
    assert dish["content"] == data["content"]
    assert dish["image"] == data["image"]
    assert dish["thumbnail"] == data["thumbnail"]
    assert dish["sort_order"] == data["sort_order"]
    assert "id" in dish
    assert "created_at" in dish
    assert "updated_at" in dish
    assert "files" in dish


def test_read_dishes(client):
    """测试获取菜品列表"""
    # 先创建几个菜品
    for i in range(3):
        data = {
            "name": f"测试菜品名称{generate_random_string()}",
            "content": f"这是测试菜品内容{generate_random_string()}_{i}",
            "image": f"http://example.com/image_{generate_random_string()}.jpg",
            "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
            "sort_order": i
        }
        client.post("/api/v1/content/dish/", json=data)

    # 获取菜品列表
    response = client.get("/api/v1/content/dish/")
    assert response.status_code == 200
    pprint.pprint(response.json())
    data_list = response.json().get("data", None)
    dishes = data_list.get("list", None)
    assert isinstance(dishes, list)
    assert len(dishes) >= 3


def test_search_dishes(client):
    """测试搜索菜品"""
    # 创建一些菜品用于测试搜索
    unique_keyword = f"特殊搜索关键词{generate_random_string()}"

    # 创建包含特殊关键词的菜品
    data1 = {
        "name": f"{unique_keyword}_测试菜品1",
        "content": "这是测试菜品内容1",
        "image": "http://example.com/image_1.jpg",
        "thumbnail": "http://example.com/thumbnail_1.jpg",
        "sort_order": 1
    }
    client.post("/api/v1/content/dish/", json=data1)

    # 创建描述中包含特殊关键词的菜品
    data2 = {
        "name": "测试菜品2",
        "content": f"这是{unique_keyword}测试菜品内容2",
        "image": "http://example.com/image_2.jpg",
        "thumbnail": "http://example.com/thumbnail_2.jpg",
        "sort_order": 2
    }
    client.post("/api/v1/content/dish/", json=data2)

    # 创建不包含特殊关键词的菜品
    data3 = {
        "name": "测试菜品3",
        "content": "这是测试菜品内容3",
        "image": "http://example.com/image_3.jpg",
        "thumbnail": "http://example.com/thumbnail_3.jpg",
        "sort_order": 3
    }
    client.post("/api/v1/content/dish/", json=data3)

    # 使用关键词搜索
    response = client.get(f"/api/v1/content/dish/search/?keyword={unique_keyword}")
    assert response.status_code == 200
    pprint.pprint(response.json())

    data_list = response.json().get("data", None)
    dishes = data_list.get("list", None)
    total = data_list.get("total", 0)

    # 验证结果
    assert isinstance(dishes, list)
    assert total >= 2  # 至少找到2个包含关键词的菜品
    assert len(dishes) >= 2

    # 验证搜索结果是否包含我们创建的带关键词的菜品
    dish_names = [dish["name"] for dish in dishes]
    dish_contents = [dish["content"] for dish in dishes]

    # 验证名称中包含关键词的菜品在结果中
    assert any(data1["name"] in name for name in dish_names)
    # 验证内容中包含关键词的菜品在结果中
    assert any(data2["content"] in content for content in dish_contents)
    # 验证不包含关键词的菜品不在结果中
    assert not any(data3["name"] == name for name in dish_names)


def test_update_dish(client):
    """测试更新菜品"""
    # 先创建一个菜品
    data = {
        "name": f"测试菜品名称{generate_random_string()}",
        "content": f"这是测试菜品内容{generate_random_string()}",
        "image": f"http://example.com/image_{generate_random_string()}.jpg",
        "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
        "sort_order": 1
    }
    create_response = client.post("/api/v1/content/dish/", json=data)
    dish_id = create_response.json().get("data", {}).get("id")

    # 更新菜品
    update_data = {
        "name": f"更新后的菜品名称{generate_random_string()}",
        "content": f"这是更新后的菜品内容{generate_random_string()}",
    }
    update_response = client.put(f"/api/v1/content/dish/{dish_id}", json=update_data)
    assert update_response.status_code == 200
    updated_dish = update_response.json().get("data", {})
    assert updated_dish["name"] == update_data["name"]
    assert updated_dish["content"] == update_data["content"]
    # 未更新的字段应保持不变
    assert updated_dish["image"] == data["image"]
    assert updated_dish["thumbnail"] == data["thumbnail"]
    assert updated_dish["sort_order"] == data["sort_order"]


def test_delete_dish(client):
    """测试删除菜品"""
    # 先创建一个菜品
    data = {
        "name": f"这是测试菜品名称{generate_random_string()}",
        "content": f"这是测试菜品内容{generate_random_string()}",
        "image": f"http://example.com/image_{generate_random_string()}.jpg",
        "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
        "sort_order": 1
    }
    create_response = client.post("/api/v1/content/dish/", json=data)
    dish_id = create_response.json().get("data", {}).get("id")

    # 删除菜品
    delete_response = client.delete(f"/api/v1/content/dish/{dish_id}")
    assert delete_response.status_code == 204

    # 再次获取菜品应该返回404
    get_response = client.get(f"/api/v1/content/dish/{dish_id}")
    assert get_response.status_code == 404


def test_dish_by_product(client):
    """测试获取产品相关菜品"""
    # 先创建一个产品
    product_data = {
        "name": f"测试产品{generate_random_string()}",
        "description": f"测试产品描述{generate_random_string()}",
        "price": 100.0,
        "stock": 10
    }
    product_response = client.post("/api/v1/product/", json=product_data)
    assert product_response.status_code == 201
    product_id = product_response.json()["id"]

    # 创建菜品
    dish_data = {
        "name": f"测试菜品名称{generate_random_string()}",
        "content": f"这是测试菜品内容{generate_random_string()}",
        "image": f"http://example.com/image_{generate_random_string()}.jpg",
        "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
        "sort_order": 1
    }
    dish_response = client.post("/api/v1/content/dish/", json=dish_data)
    assert dish_response.status_code == 201
    dish_id = dish_response.json().get("data", {}).get("id")

    # 将菜品绑定到产品
    binding_data = {
        "product_id": product_id,
        "content_ids": [dish_id]
    }
    binding_response = client.post("/api/v1/product/add/contents", json=binding_data)
    assert binding_response.status_code == 200

    # 获取产品相关菜品
    product_dishes_response = client.get(f"/api/v1/content/dish/by/product/{product_id}/")
    assert product_dishes_response.status_code == 200
    product_dishes = product_dishes_response.json()
    assert isinstance(product_dishes, list)
    assert len(product_dishes) >= 1
    assert any(dish["id"] == dish_id for dish in product_dishes)
