from tests.utils import generate_random_string


# 文件测试用例
def test_create_file(client):
    """测试创建文件"""
    data = {
        "filename": f"测试文件_{generate_random_string()}.jpg",
        "file_type": "jpg",
        "file_path": f"/uploads/test_{generate_random_string()}.jpg",
        "file_size": 1024
    }
    response = client.post("/api/v1/file/files/", json=data)
    assert response.status_code == 201
    content = response.json()
    assert content["filename"] == data["filename"]
    assert content["file_type"] == data["file_type"]
    assert content["file_path"] == data["file_path"]
    assert content["file_size"] == data["file_size"]
    assert "id" in content


def test_read_file(client):
    """测试获取文件详情"""
    # 先创建一个文件
    data = {
        "filename": f"测试文件_{generate_random_string()}.jpg",
        "file_type": "jpg",
        "file_path": f"/uploads/test_{generate_random_string()}.jpg",
        "file_size": 1024
    }
    create_response = client.post("/api/v1/file/files/", json=data)
    file_id = create_response.json()["id"]

    # 获取该文件
    response = client.get(f"/api/v1/file/{file_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["filename"] == data["filename"]
    assert content["file_type"] == data["file_type"]
    assert content["file_path"] == data["file_path"]
    assert content["file_size"] == data["file_size"]


def test_read_files(client):
    """测试获取文件列表"""
    # 先创建几个文件
    for i in range(3):
        data = {
            "filename": f"测试文件_{generate_random_string()}_{i}.jpg",
            "file_type": "jpg",
            "file_path": f"/uploads/test_{generate_random_string()}_{i}.jpg",
            "file_size": 1024 + i
        }
        client.post("/api/v1/file/files/", json=data)

    # 获取文件列表
    response = client.get("/api/v1/file/files/")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)
    assert len(content) >= 3


def test_update_file(client):
    """测试更新文件"""
    # 先创建一个文件
    data = {
        "filename": f"测试文件_{generate_random_string()}.jpg",
        "file_type": "jpg",
        "file_path": f"/uploads/test_{generate_random_string()}.jpg",
        "file_size": 1024
    }
    create_response = client.post("/api/v1/file/files/", json=data)
    file_id = create_response.json()["id"]

    # 更新文件
    update_data = {
        "filename": f"更新的文件_{generate_random_string()}.png",
        "file_type": "png",
        "file_size": 2048
    }
    response = client.put(f"/api/v1/file/{file_id}", json=update_data)
    assert response.status_code == 200
    updated_file = response.json()
    assert updated_file["filename"] == update_data["filename"]
    assert updated_file["file_type"] == update_data["file_type"]
    assert updated_file["file_size"] == update_data["file_size"]
    # 未更新的字段应保持不变
    assert updated_file["file_path"] == data["file_path"]


def test_delete_file(client):
    """测试删除文件"""
    # 先创建一个文件
    data = {
        "filename": f"测试文件_{generate_random_string()}.jpg",
        "file_type": "jpg",
        "file_path": f"/uploads/test_{generate_random_string()}.jpg",
        "file_size": 1024
    }
    create_response = client.post("/api/v1/file/files/", json=data)
    file_id = create_response.json()["id"]

    # 删除文件
    response = client.delete(f"/api/v1/file/{file_id}")
    assert response.status_code == 204

    # 确认已删除
    get_response = client.get(f"/api/v1/file/files/{file_id}")
    assert get_response.status_code == 404
