from unittest.mock import patch

import arrow
from fastapi.testclient import TestClient

from app.main import app
from app.models.order import OrderStatus, PaymentStatus, PaymentMethod

client = TestClient(app)


# 订单测试用例
def test_create_order(client, create_test_user, create_test_product):
    """测试创建订单"""

    data = {
        "user_id": create_test_user.id,
        "status": OrderStatus.PENDING.value,
        "total_amount": 200.0,
        "payable_amount": 190.0,
        "actual_amount_paid": 190.0,
        "payment_method": PaymentMethod.ALIPAY.value,
        "payment_status": PaymentStatus.UNPAID.value,
        "payment_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "order_no": f"ORDER{arrow.utcnow().int_timestamp}",

        "items": [
            {
                "product_id": create_test_product.id,
                "quantity": 2,
                "price": 100.0,
                "subtotal": 200.0,
                "final_price": 90.0,
                "payable_amount": 180.0,
            }
        ]
    }
    response = client.post("/api/v1/order/", json=data)
    assert response.status_code == 201
    content = response.json()
    assert content["user_id"] == data["user_id"]
    assert content["status"] == data["status"]
    assert content["total_amount"] == data["total_amount"]
    assert content["actual_amount_paid"] == data["actual_amount_paid"]
    assert content["payment_method"] == data["payment_method"]
    assert content["payment_status"] == data["payment_status"]
    assert content["payment_time"] == data["payment_time"]
    assert len(content["items"]) == 1
    assert content["items"][0]["product_id"] == data["items"][0]["product_id"]
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_read_order(client, create_test_user, create_test_product):
    data = {
        "user_id": create_test_user.id,
        "status": OrderStatus.PENDING.value,
        "total_amount": 200.0,
        "payable_amount": 190.0,
        "actual_amount_paid": 190.0,
        "payment_method": PaymentMethod.ALIPAY.value,
        "payment_status": PaymentStatus.UNPAID.value,
        "payment_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "order_no": f"ORDER{arrow.utcnow().int_timestamp}",

        "items": [
            {
                "product_id": create_test_product.id,
                "quantity": 2,
                "price": 100.0,
                "subtotal": 200.0,
                "final_price": 90.0,
                "payable_amount": 180.0,
            }
        ]
    }
    response = client.post("/api/v1/order/", json=data)
    order_id = response.json()["id"]

    # 获取存在的订单
    response = client.get(f"/api/v1/order/{order_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["id"] == order_id
    assert content["user_id"] == data["user_id"]
    assert content["status"] == data["status"]
    assert content["total_amount"] == data["total_amount"]
    assert content["actual_amount_paid"] == data["actual_amount_paid"]
    assert content["payment_method"] == data["payment_method"]
    assert content["payment_status"] == data["payment_status"]
    assert content["payment_time"] == data["payment_time"]
    assert len(content["items"]) == 1
    assert content["items"][0]["product_id"] == data["items"][0]["product_id"]
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content

    # 获取不存在的订单
    response = client.get("/api/v1/order/999")
    assert response.status_code == 404


def test_read_orders(client, create_test_user, create_test_product):
    for i in range(2):
        data = {
            "user_id": create_test_user.id,
            "status": OrderStatus.PENDING.value,
            "total_amount": 200.0,
            "payable_amount": 190.0,
            "actual_amount_paid": 190.0,
            "payment_method": PaymentMethod.ALIPAY.value,
            "payment_status": PaymentStatus.UNPAID.value,
            "payment_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "order_no": f"ORDER{arrow.utcnow().int_timestamp}{i}",

            "items": [
                {
                    "product_id": create_test_product.id,
                    "quantity": 2,
                    "price": 100.0,
                    "subtotal": 200.0,
                    "final_price": 90.0,
                    "payable_amount": 180.0,
                }
            ]
        }
        response = client.post("/api/v1/order/", json=data)

    response = client.get("/api/v1/order/")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)
    assert len(content) == 2
    assert content[0]["id"] == 1
    assert content[1]["id"] == 2


def test_update_order(client, create_test_user, create_test_product):
    """测试更新订单"""
    # 先创建一个订单
    data = {
        "user_id": create_test_user.id,
        "status": OrderStatus.PENDING.value,
        "total_amount": 200.0,
        "payable_amount": 190.0,
        "actual_amount_paid": 190.0,
        "payment_method": PaymentMethod.ALIPAY.value,
        "payment_status": PaymentStatus.UNPAID.value,
        "payment_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "order_no": f"ORDER{arrow.utcnow().int_timestamp}",

        "items": [
            {
                "product_id": create_test_product.id,
                "quantity": 2,
                "price": 100.0,
                "subtotal": 200.0,
                "final_price": 90.0,
                "payable_amount": 180.0,
            }
        ]
    }
    response = client.post("/api/v1/order/", json=data)
    order_id = response.json()["id"]

    # 更新存在的订单
    update_data = {
        "status": OrderStatus.PAID.value,
        "payment_status": PaymentStatus.PAID.value,
        "payment_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "actual_amount_paid": 180.0
    }
    response = client.put(f"/api/v1/order/{order_id}", json=update_data)
    assert response.status_code == 200
    content = response.json()
    assert content["id"] == order_id
    assert content["status"] == update_data["status"]
    assert content["payment_status"] == update_data["payment_status"]
    assert content["actual_amount_paid"] == update_data["actual_amount_paid"]

    # 更新不存在的订单
    response = client.put("/api/v1/order/999", json=update_data)
    assert response.status_code == 404


def test_delete_order(client, create_test_user, create_test_product):
    """测试删除订单"""
    data = {
        "user_id": create_test_user.id,
        "status": OrderStatus.PENDING.value,
        "total_amount": 200.0,
        "payable_amount": 190.0,
        "actual_amount_paid": 190.0,
        "payment_method": PaymentMethod.ALIPAY.value,
        "payment_status": PaymentStatus.UNPAID.value,
        "payment_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "order_no": f"ORDER{arrow.utcnow().int_timestamp}",

        "items": [
            {
                "product_id": create_test_product.id,
                "quantity": 2,
                "price": 100.0,
                "subtotal": 200.0,
                "final_price": 90.0,
                "payable_amount": 180.0
            }
        ]
    }
    response = client.post("/api/v1/order/", json=data)
    order_id = response.json()["id"]

    # 删除存在的订单
    response = client.delete(f"/api/v1/order/{order_id}")
    assert response.status_code == 204

    # 删除不存在的订单
    response = client.delete("/api/v1/order/999")
    assert response.status_code == 404


def test_get_user_orders(client, create_test_user, create_test_product):
    """测试获取用户订单列表"""
    # 为同一用户创建两个订单
    for i in range(2):
        data = {
            "user_id": create_test_user.id,
            "status": OrderStatus.PENDING.value if i == 0 else OrderStatus.PAID.value,
            "total_amount": 200.0 if i == 0 else 150.0,
            "payable_amount": 200.0 if i == 0 else 150.0,
            "actual_amount_paid": 200.0 if i == 0 else 150.0,
            "payment_method": PaymentMethod.ALIPAY.value if i == 0 else PaymentMethod.WECHAT_PAY.value,
            "payment_status": PaymentStatus.UNPAID.value if i == 0 else PaymentStatus.PAID.value,
            "payment_time": None if i == 0 else arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "order_no": f"ORDER{arrow.utcnow().int_timestamp}{i}",

            "items": [
                {
                    "product_id": create_test_product.id,
                    "quantity": 2 if i == 0 else 1,
                    "price": 100.0 if i == 0 else 150.0,
                    "subtotal": 200.0 if i == 0 else 150.0,
                    "final_price": 100.0 if i == 0 else 150.0,
                    "payable_amount": 200.0 if i == 0 else 150.0
                }
            ]
        }
        client.post("/api/v1/order/", json=data)

    # 获取用户的订单列表
    response = client.get(f"/api/v1/order/user/{create_test_user.id}")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)
    assert len(content) == 2
    assert content[0]["user_id"] == create_test_user.id
    assert content[1]["user_id"] == create_test_user.id


def test_update_order_status(client, create_test_user, create_test_product):
    """测试更新订单状态"""
    # 先创建一个订单
    data = {
        "user_id": create_test_user.id,
        "status": OrderStatus.PENDING.value,
        "total_amount": 200.0,
        "payable_amount": 190.0,
        "actual_amount_paid": 190.0,
        "payment_method": PaymentMethod.ALIPAY.value,
        "payment_status": PaymentStatus.UNPAID.value,
        "payment_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "order_no": f"ORDER{arrow.utcnow().int_timestamp}",

        "items": [
            {
                "product_id": create_test_product.id,
                "quantity": 2,
                "price": 100.0,
                "subtotal": 200.0,
                "final_price": 90.0,
                "payable_amount": 180.0
            }
        ]
    }
    response = client.post("/api/v1/order/", json=data)
    order_id = response.json()["id"]

    # 更新存在的订单状态
    status_update = {
        "status": OrderStatus.SHIPPED.value
    }
    response = client.patch(f"/api/v1/order/{order_id}/status", json=status_update)
    assert response.status_code == 200
    content = response.json()
    assert content["id"] == order_id
    assert content["status"] == status_update["status"]

    # 更新不存在的订单状态
    response = client.patch("/api/v1/order/999/status", json=status_update)
    assert response.status_code == 404


def test_update_order_payment_status(client, create_test_user, create_test_product):
    """测试更新订单支付状态"""
    # 先创建一个订单
    data = {
        "user_id": create_test_user.id,
        "status": OrderStatus.PENDING.value,
        "total_amount": 200.0,
        "payable_amount": 190.0,
        "actual_amount_paid": 190.0,
        "payment_method": PaymentMethod.ALIPAY.value,
        "payment_status": PaymentStatus.UNPAID.value,
        "order_no": f"ORDER{arrow.utcnow().int_timestamp}",

        "items": [
            {
                "product_id": create_test_product.id,
                "quantity": 2,
                "price": 100.0,
                "subtotal": 200.0,
                "final_price": 90.0,
                "payable_amount": 180.0
            }
        ]
    }
    response = client.post("/api/v1/order/", json=data)
    order_id = response.json()["id"]

    # 更新存在的订单支付状态
    payment_status_update = {
        "payment_status": PaymentStatus.PAID.value,
        "payment_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    response = client.patch(f"/api/v1/order/{order_id}/payment-status", json=payment_status_update)
    assert response.status_code == 200
    content = response.json()
    assert content["id"] == order_id
    assert content["payment_status"] == payment_status_update["payment_status"]

    # 更新不存在的订单支付状态
    response = client.patch("/api/v1/order/999/payment-status", json=payment_status_update)
    assert response.status_code == 404


# 订单项测试用例
def test_create_order_item(client, create_test_user, create_test_product):
    """测试创建订单项"""
    # 先创建一个订单
    data = {
        "user_id": create_test_user.id,
        "status": OrderStatus.PENDING.value,
        "total_amount": 200.0,
        "payable_amount": 190.0,
        "actual_amount_paid": 190.0,
        "payment_method": PaymentMethod.ALIPAY.value,
        "payment_status": PaymentStatus.UNPAID.value,
        "payment_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "order_no": f"ORDER{arrow.utcnow().int_timestamp}",

        "items": []
    }
    response = client.post("/api/v1/order/", json=data)
    order_id = response.json()["id"]

    # 为存在的订单创建订单项
    item_data = {
        "product_id": create_test_product.id,
        "quantity": 1,
        "price": 100.0,
        "subtotal": 200.0,
        "final_price": 90.0,
        "payable_amount": 180.0
    }
    response = client.post(f"/api/v1/order/{order_id}/items", json=item_data)
    assert response.status_code == 201
    content = response.json()
    assert content["order_id"] == order_id
    assert content["product_id"] == item_data["product_id"]
    assert content["quantity"] == item_data["quantity"]
    assert content["price"] == item_data["price"]
    assert content["subtotal"] == item_data["subtotal"]

    # 为不存在的订单创建订单项
    response = client.post("/api/v1/order/999/items", json=item_data)
    assert response.status_code == 404


def test_read_order_items(client, create_test_user, create_test_product):
    """测试获取订单的所有订单项"""
    # 先创建一个订单和订单项
    data = {
        "user_id": create_test_user.id,
        "status": OrderStatus.PENDING.value,
        "total_amount": 250.0,
        "payable_amount": 190.0,
        "actual_amount_paid": 190.0,
        "payment_method": PaymentMethod.ALIPAY.value,
        "payment_status": PaymentStatus.UNPAID.value,
        "payment_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "order_no": f"ORDER{arrow.utcnow().int_timestamp}",

        "items": [
            {
                "product_id": create_test_product.id,
                "quantity": 2,
                "price": 100.0,
                "subtotal": 200.0,
                "final_price": 90.0,
                "payable_amount": 180.0
            },
            {
                "product_id": create_test_product.id,
                "quantity": 1,
                "price": 50.0,
                "subtotal": 50.0,
                "final_price": 40.0,
                "payable_amount": 40.0
            }
        ]
    }
    response = client.post("/api/v1/order/", json=data)
    order_id = response.json()["id"]

    # 获取存在的订单的订单项
    response = client.get(f"/api/v1/order/{order_id}/items")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)
    assert len(content) == 2
    assert content[0]["order_id"] == order_id
    assert content[1]["order_id"] == order_id

    # 获取不存在的订单的订单项
    response = client.get("/api/v1/order/999/items")
    assert response.status_code == 404


def test_read_order_item(client, create_test_user, create_test_product):
    """测试获取订单项详情"""
    # 先创建一个订单和订单项
    data = {
        "user_id": create_test_user.id,
        "status": OrderStatus.PENDING.value,
        "total_amount": 200.0,
        "payable_amount": 190.0,
        "actual_amount_paid": 190.0,
        "payment_method": PaymentMethod.ALIPAY.value,
        "payment_status": PaymentStatus.UNPAID.value,
        "payment_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "order_no": f"ORDER{arrow.utcnow().int_timestamp}",

        "items": [
            {
                "product_id": create_test_product.id,
                "quantity": 2,
                "price": 100.0,
                "subtotal": 200.0,
                "final_price": 90.0,
                "payable_amount": 180.0
            }
        ]
    }
    response = client.post("/api/v1/order/", json=data)
    order = response.json()
    item_id = order["items"][0]["id"]

    # 获取存在的订单项
    response = client.get(f"/api/v1/order/items/{item_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["id"] == item_id
    assert content["quantity"] == 2
    assert content["price"] == 100.0
    assert content["subtotal"] == 200

    # 获取不存在的订单项
    response = client.get("/api/v1/order/items/999")
    assert response.status_code == 404


def test_update_order_item(client, create_test_user, create_test_product):
    """测试更新订单项"""
    # 先创建一个订单和订单项
    data = {
        "user_id": create_test_user.id,
        "status": OrderStatus.PENDING.value,
        "total_amount": 200.0,
        "payable_amount": 190.0,
        "actual_amount_paid": 190.0,
        "payment_method": PaymentMethod.ALIPAY.value,
        "payment_status": PaymentStatus.UNPAID.value,
        "payment_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "order_no": f"ORDER{arrow.utcnow().int_timestamp}",

        "items": [
            {
                "product_id": create_test_product.id,
                "quantity": 2,
                "price": 100.0,
                "subtotal": 200.0,
                "final_price": 90.0,
                "payable_amount": 180.0
            }
        ]
    }
    response = client.post("/api/v1/order/", json=data)
    order = response.json()
    item_id = order["items"][0]["id"]

    # 更新存在的订单项
    update_data = {
        "quantity": 3,
        "price": 150,
        "subtotal": 450.0
    }
    response = client.put(f"/api/v1/order/items/{item_id}", json=update_data)
    assert response.status_code == 200
    content = response.json()
    assert content["id"] == item_id
    assert content["price"] == update_data["price"]
    assert content["quantity"] == update_data["quantity"]
    assert content["subtotal"] == update_data["subtotal"]

    # 更新不存在的订单项
    response = client.put("/api/v1/order/items/999", json=update_data)
    assert response.status_code == 404


def test_delete_order_item(client, create_test_user, create_test_product):
    """测试删除订单项"""
    # 先创建一个订单和订单项
    data = {
        "user_id": create_test_user.id,
        "status": OrderStatus.PENDING.value,
        "total_amount": 200.0,
        "payable_amount": 190.0,
        "actual_amount_paid": 190.0,
        "payment_method": PaymentMethod.ALIPAY.value,
        "payment_status": PaymentStatus.UNPAID.value,
        "payment_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "order_no": f"ORDER{arrow.utcnow().int_timestamp}",

        "items": [
            {
                "product_id": create_test_product.id,
                "quantity": 2,
                "price": 100.0,
                "subtotal": 200.0,
                "final_price": 90.0,
                "payable_amount": 180.0
            },
            {
                "product_id": create_test_product.id,
                "quantity": 1,
                "price": 50.0,
                "subtotal": 50.0,
                "final_price": 40.0,
                "payable_amount": 40.0
            }
        ]
    }
    response = client.post("/api/v1/order/", json=data)
    order = response.json()
    item_id = order["items"][0]["id"]

    # 删除存在的订单项
    response = client.delete(f"/api/v1/order/items/{item_id}")
    assert response.status_code == 204

    # 删除不存在的订单项
    response = client.delete("/api/v1/order/items/999")
    assert response.status_code == 404


# 测试新增的订单创建端点
def test_create_new_order(client, create_test_user, create_test_product):
    """测试通过新接口创建订单"""
    data = {
        "user_id": create_test_user.id,
        "products": [
            {
                "product_id": create_test_product.id,
                "quantity": 2,
                "reservation_requests": []
            }
        ],
        "payment_method": PaymentMethod.ACCOUNT_BALANCE.value
    }

    # 模拟service.create_order的实现
    with patch('app.service.order.order_service.create_order') as mock_create_order:
        # 模拟返回值
        mock_order = {
            "id": 999,
            "user_id": data["user_id"],
            "status": OrderStatus.PENDING.value,
            "payment_status": PaymentStatus.UNPAID.value,
            "total_amount": 200.0,
            "payable_amount": 190.0,
            "actual_amount_paid": 190.0,
            "payment_method": data["payment_method"],
            "order_no": f"ORDER{arrow.utcnow().int_timestamp}",
            "created_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "updated_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "items": []
        }
        mock_create_order.return_value = mock_order

        # 发送请求
        response = client.post("/api/v1/order/create", json=data)

        # 验证响应
        assert response.status_code == 201
        content = response.json()
        assert content["success"] is True
        assert "订单创建成功" in content["message"]
        assert content["order"]["id"] == mock_order["id"]
        assert content["order"]["user_id"] == data["user_id"]


def test_create_new_order_error(client, create_test_user):
    """测试创建订单时发生错误"""
    data = {
        "user_id": create_test_user.id,
        "products": [
            {
                "product_id": 9999,  # 不存在的产品ID
                "quantity": 2,
                "reservation_requests": []
            }
        ],
        "payment_method": PaymentMethod.ACCOUNT_BALANCE.value
    }

    # 模拟service.create_order抛出异常
    with patch('app.service.order.order_service.create_order') as mock_create_order:
        mock_create_order.side_effect = ValueError("产品不存在")

        # 发送请求
        response = client.post("/api/v1/order/create", json=data)

        # 验证响应
        assert response.status_code == 201  # 仍然返回201，但success为False
        content = response.json()
        assert content["success"] is False
        assert "产品不存在" in content["message"]
        assert content["order"] is None


def test_pay_order_api(client, create_test_user, create_test_product):
    """测试支付订单API"""
    # 先创建一个订单
    order_data = {
        "user_id": create_test_user.id,
        "status": OrderStatus.PENDING.value,
        "total_amount": 200.0,
        "payable_amount": 190.0,
        "actual_amount_paid": 190.0,
        "payment_method": PaymentMethod.ACCOUNT_BALANCE.value,
        "payment_status": PaymentStatus.UNPAID.value,
        "payment_time": None,
        "order_no": f"ORDER{arrow.utcnow().int_timestamp}",
        "items": [
            {
                "product_id": create_test_product.id,
                "quantity": 2,
                "price": 100.0,
                "subtotal": 200.0,
                "final_price": 90.0,
                "payable_amount": 180.0
            }
        ]
    }
    response = client.post("/api/v1/order/", json=order_data)
    order_id = response.json()["id"]

    # 模拟service.pay_order的实现
    with patch('app.service.order.order_service.pay_order') as mock_pay_order:
        # 模拟返回值
        mock_updated_order = {
            "id": order_id,
            "user_id": order_data["user_id"],
            "status": OrderStatus.PAID.value,
            "payment_status": PaymentStatus.PAID.value,
            "total_amount": 200.0,
            "payable_amount": 190.0,
            "actual_amount_paid": 190.0,
            "payment_method": PaymentMethod.ALIPAY.value,
            "payment_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "order_no": order_data["order_no"],
            "created_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "updated_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "items": []
        }
        mock_pay_order.return_value = mock_updated_order

        # 发送请求
        response = client.patch(f"/api/v1/order/{order_id}/pay?payment_method=alipay")

        # 验证响应
        assert response.status_code == 200
        content = response.json()
        assert content["id"] == order_id
        assert content["status"] == OrderStatus.PAID.value
        assert content["payment_status"] == PaymentStatus.PAID.value
        assert content["payment_method"] == PaymentMethod.ALIPAY.value


def test_cancel_order_api(client, create_test_user, create_test_product):
    """测试取消订单API"""
    # 先创建一个订单
    order_data = {
        "user_id": create_test_user.id,
        "status": OrderStatus.PENDING.value,
        "total_amount": 200.0,
        "payable_amount": 190.0,
        "actual_amount_paid": 190.0,
        "payment_method": PaymentMethod.ACCOUNT_BALANCE.value,
        "payment_status": PaymentStatus.UNPAID.value,
        "payment_time": None,
        "order_no": f"ORDER{arrow.utcnow().int_timestamp}",
        "items": [
            {
                "product_id": create_test_product.id,
                "quantity": 2,
                "price": 100.0,
                "subtotal": 200.0,
                "final_price": 90.0,
                "payable_amount": 180.0
            }
        ]
    }
    response = client.post("/api/v1/order/", json=order_data)
    order_id = response.json()["id"]

    # 模拟service.cancel_order的实现
    with patch('app.service.order.order_service.cancel_order') as mock_cancel_order:
        # 模拟返回值
        mock_cancelled_order = {
            "id": order_id,
            "user_id": order_data["user_id"],
            "status": OrderStatus.CANCELLED.value,
            "payment_status": PaymentStatus.UNPAID.value,
            "total_amount": 200.0,
            "payable_amount": 190.0,
            "actual_amount_paid": 190.0,
            "payment_method": PaymentMethod.ACCOUNT_BALANCE.value,
            "payment_time": None,
            "order_no": order_data["order_no"],
            "created_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "updated_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "items": []
        }
        mock_cancel_order.return_value = mock_cancelled_order

        # 发送请求
        response = client.patch(f"/api/v1/order/{order_id}/cancel")

        # 验证响应
        assert response.status_code == 200
        content = response.json()
        assert content["id"] == order_id
        assert content["status"] == OrderStatus.CANCELLED.value
