from unittest.mock import patch
import arrow
import pytest
from fastapi.testclient import TestClient

from app.main import app
from app.models.enum import Status
from app.models.order import OrderStatus, PaymentStatus, PaymentMethod
from app.models.account import TransactionType

client = TestClient(app)


@pytest.fixture
def create_test_order(client, create_test_user, create_test_product):
    """创建测试订单"""
    data = {
        "user_id": create_test_user.id,
        "status": OrderStatus.PENDING.value,
        "total_amount": 200.0,
        "payable_amount": 190.0,
        "actual_amount_paid": 0.0,
        "payment_method": PaymentMethod.ACCOUNT_BALANCE.value,
        "payment_status": PaymentStatus.UNPAID.value,
        "order_no": f"ORDER{arrow.utcnow().int_timestamp}",
        "items": [
            {
                "product_id": create_test_product.id,
                "quantity": 2,
                "price": 100.0,
                "subtotal": 200.0,
                "final_price": 95.0,
                "payable_amount": 190.0,
            }
        ]
    }
    response = client.post("/api/v1/order/", json=data)
    return response.json()


def test_pay_order_with_account_balance(client, create_test_order):
    """测试使用个人账户余额支付订单"""
    order_id = create_test_order["id"]
    
    # 模拟支付服务
    with patch('app.service.payment.payment_service.pay_order') as mock_pay_order:
        # 准备模拟返回值
        mock_updated_order = {
            "id": order_id,
            "user_id": create_test_order["user_id"],
            "status": OrderStatus.PENDING.value,
            "payment_status": PaymentStatus.PAID.value,
            "total_amount": 200.0,
            "payable_amount": 190.0,
            "actual_amount_paid": 190.0,
            "payment_method": PaymentMethod.ACCOUNT_BALANCE.value,
            "payment_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "order_no": create_test_order["order_no"],
            "created_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "updated_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "items": []
        }
        mock_pay_order.return_value = mock_updated_order
        
        # 发送支付请求
        response = client.post(f"/api/v1/payment/order/{order_id}/pay", json={})
        
        # 验证响应
        assert response.status_code == 200
        content = response.json()
        assert content["id"] == order_id
        assert content["payment_status"] == PaymentStatus.PAID.value
        assert content["payment_method"] == PaymentMethod.ACCOUNT_BALANCE.value
        assert content["actual_amount_paid"] == 190.0
        
        # 验证服务调用
        mock_pay_order.assert_called_once()


def test_pay_order_with_enterprise_balance(client, create_test_order):
    """测试使用企业账户余额支付订单"""
    order_id = create_test_order["id"]
    
    # 模拟支付服务
    with patch('app.service.payment.payment_service.pay_order') as mock_pay_order:
        # 准备模拟返回值
        mock_updated_order = {
            "id": order_id,
            "user_id": create_test_order["user_id"],
            "status": OrderStatus.PENDING.value,
            "payment_status": PaymentStatus.PAID.value,
            "total_amount": 200.0,
            "payable_amount": 190.0,
            "actual_amount_paid": 190.0,
            "payment_method": PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE.value,
            "payment_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "order_no": create_test_order["order_no"],
            "created_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "updated_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "items": []
        }
        mock_pay_order.return_value = mock_updated_order
        
        # 发送支付请求，带企业ID
        payment_info = {"enterprise_id": 2}
        response = client.post(f"/api/v1/payment/order/{order_id}/pay", json=payment_info)
        
        # 验证响应
        assert response.status_code == 200
        content = response.json()
        assert content["id"] == order_id
        assert content["payment_status"] == PaymentStatus.PAID.value
        assert content["payment_method"] == PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE.value
        assert content["actual_amount_paid"] == 190.0
        
        # 验证服务调用
        mock_pay_order.assert_called_once()


def test_pay_order_with_error(client, create_test_order):
    """测试支付订单出错的情况"""
    order_id = create_test_order["id"]
    
    # 模拟支付服务抛出异常
    with patch('app.service.payment.payment_service.pay_order') as mock_pay_order:
        # 设置模拟抛出异常
        mock_pay_order.side_effect = ValueError("用户余额不足")
        
        # 发送支付请求
        response = client.post(f"/api/v1/payment/order/{order_id}/pay", json={})
        
        # 验证响应
        assert response.status_code == 400
        content = response.json()
        assert "detail" in content
        assert "用户余额不足" in content["detail"]
        
        # 验证服务调用
        mock_pay_order.assert_called_once()


def test_pay_nonexistent_order(client):
    """测试支付不存在的订单"""
    # 发送支付请求到不存在的订单
    response = client.post("/api/v1/payment/order/99999/pay", json={})
    
    # 验证响应
    assert response.status_code == 404
    content = response.json()
    assert "detail" in content
    assert "订单不存在" in content["detail"] 