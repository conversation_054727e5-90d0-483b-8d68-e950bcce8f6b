import arrow
from fastapi.testclient import TestClient

from app.main import app
from app.models.enum import Status
from app.models.pricing import PricingStrategyType, MemberLevel, PricingStrategyScope
from tests.utils import generate_random_string

client = TestClient(app)


def test_create_pricing_strategy(client):
    """测试创建定价策略"""
    data = {
        "name": "测试定价策略",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE,
    }
    response = client.post("/api/v1/pricing/pricing-strategies/", json=data)
    assert response.status_code == 201
    content = response.json()
    assert content["name"] == data["name"]
    assert content["description"] == data["description"]
    assert content["start_time"] == data["start_time"]
    assert content["end_time"] == data["end_time"]
    assert content["scope"] == data["scope"]
    assert content["is_mutual_exclusive"] == data["is_mutual_exclusive"]
    assert content["status"] == data["status"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_read_pricing_strategy(client):
    """测试获取定价策略"""
    # 先创建一个定价策略
    data = {
        "name": "测试定价策略",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE,
    }
    create_response = client.post("/api/v1/pricing/pricing-strategies/", json=data)
    strategy_id = create_response.json()["id"]

    # 获取该定价策略
    response = client.get(f"/api/v1/pricing/pricing-strategies/{strategy_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == data["name"]
    assert content["description"] == data["description"]
    assert content["start_time"] == data["start_time"]
    assert content["end_time"] == data["end_time"]
    assert content["scope"] == data["scope"]
    assert content["is_mutual_exclusive"] == data["is_mutual_exclusive"]
    assert content["status"] == data["status"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_read_pricing_strategies(client):
    """测试获取定价策略列表"""
    response = client.get("/api/v1/pricing/pricing-strategies/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)


def test_update_pricing_strategy(client):
    """测试更新定价策略"""
    # 先创建一个定价策略
    data = {
        "name": "测试定价策略",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE,
    }
    create_response = client.post("/api/v1/pricing/pricing-strategies/", json=data)
    strategy_id = create_response.json()["id"]
    # 更新定价策略信息
    update_data = {
        "name": "更新后测试定价策略",
        "description": "更新后这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').shift(days=1).format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=31).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.ORDER.value,
        "is_mutual_exclusive": True,
        "status": Status.INACTIVE,
    }
    response = client.put(f"/api/v1/pricing/pricing-strategies/{strategy_id}", json=update_data)
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == update_data["name"]
    assert content["description"] == update_data["description"]
    assert content["start_time"] == update_data["start_time"]
    assert content["end_time"] == update_data["end_time"]
    assert content["scope"] == update_data["scope"]
    assert content["is_mutual_exclusive"] == update_data["is_mutual_exclusive"]
    assert content["status"] == update_data["status"]
    assert content["type"] == PricingStrategyType.PRICING.value
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_delete_pricing_strategy(client):
    """测试删除定价策略"""
    # 先创建一个定价策略
    data = {
        "name": "测试定价策略",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE,
    }
    create_response = client.post("/api/v1/pricing/pricing-strategies/", json=data)
    strategy_id = create_response.json()["id"]

    # 删除该定价策略
    response = client.delete(f"/api/v1/pricing/pricing-strategies/{strategy_id}")
    assert response.status_code == 204


def test_create_discount_strategy(client):
    """测试创建折扣策略"""
    data = {
        "name": "测试折扣价格",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE,
        "discount_rate": 0.8,
        "min_amount": 100,
        "max_discount": 50
    }
    response = client.post("/api/v1/pricing/discount-strategies/", json=data)
    assert response.status_code == 201
    content = response.json()
    assert content["name"] == data["name"]
    assert content["description"] == data["description"]
    assert content["start_time"] == data["start_time"]
    assert content["end_time"] == data["end_time"]
    assert content["scope"] == data["scope"]
    assert content["is_mutual_exclusive"] == data["is_mutual_exclusive"]
    assert content["status"] == data["status"]
    assert content["type"] == PricingStrategyType.DISCOUNT.value
    assert content["discount_rate"] == data["discount_rate"]
    assert content["min_amount"] == data["min_amount"]
    assert content["max_discount"] == data["max_discount"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_read_discount_strategy(client):
    """测试获取折扣策略"""
    # 先创建一个折扣策略
    data = {
        "name": "测试折扣价格",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE,
        "discount_rate": 0.8,
        "min_amount": 100,
        "max_discount": 50
    }
    create_response = client.post("/api/v1/pricing/discount-strategies/", json=data)
    strategy_id = create_response.json()["id"]

    # 获取该折扣策略
    response = client.get(f"/api/v1/pricing/discount-strategies/{strategy_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == data["name"]
    assert content["description"] == data["description"]
    assert content["start_time"] == data["start_time"]
    assert content["end_time"] == data["end_time"]
    assert content["scope"] == data["scope"]
    assert content["is_mutual_exclusive"] == data["is_mutual_exclusive"]
    assert content["status"] == data["status"]
    assert content["type"] == PricingStrategyType.DISCOUNT.value
    assert content["discount_rate"] == data["discount_rate"]
    assert content["min_amount"] == data["min_amount"]
    assert content["max_discount"] == data["max_discount"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_read_discount_strategies(client):
    """测试获取折扣策略列表"""
    response = client.get("/api/v1/pricing/discount-strategies/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)


def test_update_discount_strategy(client):
    """测试更新折扣策略"""
    # 先创建一个折扣策略
    data = {
        "name": "测试折扣价格",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE,
        "discount_rate": 0.8,
        "min_amount": 100,
        "max_discount": 50
    }
    create_response = client.post("/api/v1/pricing/discount-strategies/", json=data)
    strategy_id = create_response.json()["id"]

    # 更新折扣策略信息
    update_data = {
        "name": "更新后折扣价格",
        "description": "更新后这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').shift(days=1).format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=31).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.ORDER.value,
        "is_mutual_exclusive": True,
        "status": Status.INACTIVE.value,
        "discount_rate": 0.3,
        "min_amount": 200,
        "max_discount": 51
    }
    response = client.put(f"/api/v1/pricing/discount-strategies/{strategy_id}", json=update_data)
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == update_data["name"]
    assert content["description"] == update_data["description"]
    assert content["start_time"] == update_data["start_time"]
    assert content["end_time"] == update_data["end_time"]
    assert content["scope"] == update_data["scope"]
    assert content["is_mutual_exclusive"] == update_data["is_mutual_exclusive"]
    assert content["status"] == update_data["status"]
    assert content["type"] == PricingStrategyType.DISCOUNT.value
    assert content["discount_rate"] == update_data["discount_rate"]
    assert content["min_amount"] == update_data["min_amount"]
    assert content["max_discount"] == update_data["max_discount"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_delete_discount_strategy(client):
    """测试删除折扣策略"""
    # 先创建一个折扣策略
    data = {
        "name": "测试折扣价格",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE,
        "discount_rate": 0.8,
        "min_amount": 100,
        "max_discount": 50
    }
    create_response = client.post("/api/v1/pricing/discount-strategies/", json=data)
    strategy_id = create_response.json()["id"]

    # 删除该折扣策略
    response = client.delete(f"/api/v1/pricing/discount-strategies/{strategy_id}")
    assert response.status_code == 204


def test_create_full_reduction_strategy(client):
    """测试创建满减策略"""
    data = {
        "name": "测试创建满减策略",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE,
        "full_amount": 200,
        "reduction_amount": 50
    }
    response = client.post("/api/v1/pricing/full-reduction-strategies/", json=data)
    assert response.status_code == 201
    content = response.json()
    assert content["name"] == data["name"]
    assert content["description"] == data["description"]
    assert content["start_time"] == data["start_time"]
    assert content["end_time"] == data["end_time"]
    assert content["scope"] == data["scope"]
    assert content["is_mutual_exclusive"] == data["is_mutual_exclusive"]
    assert content["status"] == data["status"]
    assert content["type"] == PricingStrategyType.FULL_REDUCTION.value
    assert content["full_amount"] == data["full_amount"]
    assert content["reduction_amount"] == data["reduction_amount"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_read_full_reduction_strategy(client):
    """测试获取满减策略"""
    # 先创建一个满减策略
    data = {
        "name": "测试创建满减策略",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE,
        "full_amount": 200,
        "reduction_amount": 50
    }
    create_response = client.post("/api/v1/pricing/full-reduction-strategies/", json=data)
    strategy_id = create_response.json()["id"]

    # 获取该满减策略
    response = client.get(f"/api/v1/pricing/full-reduction-strategies/{strategy_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == data["name"]
    assert content["description"] == data["description"]
    assert content["start_time"] == data["start_time"]
    assert content["end_time"] == data["end_time"]
    assert content["scope"] == data["scope"]
    assert content["is_mutual_exclusive"] == data["is_mutual_exclusive"]
    assert content["status"] == data["status"]
    assert content["type"] == PricingStrategyType.FULL_REDUCTION.value
    assert content["full_amount"] == data["full_amount"]
    assert content["reduction_amount"] == data["reduction_amount"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_read_full_reduction_strategies(client):
    """测试获取满减策略列表"""
    response = client.get("/api/v1/pricing/full-reduction-strategies/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)


def test_update_full_reduction_strategy(client):
    """测试更新满减策略"""
    # 先创建一个满减策略
    data = {
        "name": "测试创建满减策略",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE,
        "full_amount": 200,
        "reduction_amount": 50
    }
    create_response = client.post("/api/v1/pricing/full-reduction-strategies/", json=data)
    strategy_id = create_response.json()["id"]

    # 更新满减策略信息
    update_data = {
        "name": "更新满减策略信息",
        "description": "更新后这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').shift(days=1).format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=31).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.ORDER.value,
        "is_mutual_exclusive": True,
        "status": Status.INACTIVE.value,
        "full_amount": 300,
        "reduction_amount": 100
    }
    response = client.put(f"/api/v1/pricing/full-reduction-strategies/{strategy_id}", json=update_data)
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == update_data["name"]
    assert content["description"] == update_data["description"]
    assert content["start_time"] == update_data["start_time"]
    assert content["end_time"] == update_data["end_time"]
    assert content["scope"] == update_data["scope"]
    assert content["is_mutual_exclusive"] == update_data["is_mutual_exclusive"]
    assert content["status"] == update_data["status"]
    assert content["type"] == PricingStrategyType.FULL_REDUCTION.value
    assert content["full_amount"] == update_data["full_amount"]
    assert content["reduction_amount"] == update_data["reduction_amount"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_delete_full_reduction_strategy(client):
    """测试删除满减策略"""
    # 先创建一个满减策略
    data = {
        "name": "测试创建满减策略",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE,
        "full_amount": 200,
        "reduction_amount": 50
    }
    create_response = client.post("/api/v1/pricing/full-reduction-strategies/", json=data)
    strategy_id = create_response.json()["id"]

    # 删除该满减策略
    response = client.delete(f"/api/v1/pricing/full-reduction-strategies/{strategy_id}")
    assert response.status_code == 204


def test_created_at_limited_strategy(client):
    """测试创建限时策略"""
    data = {
        "name": "测试创建限时策略",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE,
        "special_price": 99.9,
        "stock_limit": 100
    }
    response = client.post("/api/v1/pricing/time-limited-strategies/", json=data)
    assert response.status_code == 201
    content = response.json()
    assert content["name"] == data["name"]
    assert content["description"] == data["description"]
    assert content["start_time"] == data["start_time"]
    assert content["end_time"] == data["end_time"]
    assert content["scope"] == data["scope"]
    assert content["is_mutual_exclusive"] == data["is_mutual_exclusive"]
    assert content["status"] == data["status"]
    assert content["type"] == PricingStrategyType.TIME_LIMITED.value
    assert content["special_price"] == data["special_price"]
    assert content["stock_limit"] == data["stock_limit"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_read_time_limited_strategy(client):
    """测试获取限时策略"""
    # 先创建一个限时策略
    data = {
        "name": "测试创建限时策略",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE,
        "special_price": 99.9,
        "stock_limit": 100
    }
    create_response = client.post("/api/v1/pricing/time-limited-strategies/", json=data)
    strategy_id = create_response.json()["id"]

    # 获取该限时策略
    response = client.get(f"/api/v1/pricing/time-limited-strategies/{strategy_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == data["name"]
    assert content["description"] == data["description"]
    assert content["start_time"] == data["start_time"]
    assert content["end_time"] == data["end_time"]
    assert content["scope"] == data["scope"]
    assert content["is_mutual_exclusive"] == data["is_mutual_exclusive"]
    assert content["status"] == data["status"]
    assert content["type"] == PricingStrategyType.TIME_LIMITED.value
    assert content["special_price"] == data["special_price"]
    assert content["stock_limit"] == data["stock_limit"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_read_time_limited_strategies(client):
    """测试获取限时策略列表"""
    response = client.get("/api/v1/pricing/time-limited-strategies/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)


def test_updated_at_limited_strategy(client):
    """测试更新限时策略"""
    # 先创建一个限时策略
    data = {
        "name": "测试创建限时策略",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE,
        "special_price": 99.9,
        "stock_limit": 100
    }
    create_response = client.post("/api/v1/pricing/time-limited-strategies/", json=data)
    strategy_id = create_response.json()["id"]

    # 更新限时策略信息
    update_data = {
        "name": "更新限时策略信息",
        "description": "更新后这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').shift(days=1).format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=31).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.ORDER.value,
        "is_mutual_exclusive": True,
        "status": Status.INACTIVE.value,
        "special_price": 89.9,
        "stock_limit": 50
    }
    response = client.put(f"/api/v1/pricing/time-limited-strategies/{strategy_id}", json=update_data)
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == update_data["name"]
    assert content["description"] == update_data["description"]
    assert content["start_time"] == update_data["start_time"]
    assert content["end_time"] == update_data["end_time"]
    assert content["scope"] == update_data["scope"]
    assert content["is_mutual_exclusive"] == update_data["is_mutual_exclusive"]
    assert content["status"] == update_data["status"]
    assert content["type"] == PricingStrategyType.TIME_LIMITED.value
    assert content["special_price"] == update_data["special_price"]
    assert content["stock_limit"] == update_data["stock_limit"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_delete_time_limited_strategy(client):
    """测试删除限时策略"""
    # 先创建一个限时策略
    data = {
        "name": "测试创建限时策略",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE,
        "special_price": 99.9,
        "stock_limit": 100
    }
    create_response = client.post("/api/v1/pricing/time-limited-strategies/", json=data)
    strategy_id = create_response.json()["id"]

    # 删除该限时策略
    response = client.delete(f"/api/v1/pricing/time-limited-strategies/{strategy_id}")
    assert response.status_code == 204


def test_create_member_price_strategy(client):
    """测试创建会员价格策略"""
    data = {
        "name": "测试创建限时策略",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE,
        "member_level": MemberLevel.BASIC.value,
        "price": 199.9
    }
    response = client.post("/api/v1/pricing/member-price-strategies/", json=data)
    assert response.status_code == 201
    content = response.json()
    assert content["name"] == data["name"]
    assert content["description"] == data["description"]
    assert content["start_time"] == data["start_time"]
    assert content["end_time"] == data["end_time"]
    assert content["scope"] == data["scope"]
    assert content["is_mutual_exclusive"] == data["is_mutual_exclusive"]
    assert content["status"] == data["status"]
    assert content["type"] == PricingStrategyType.MEMBER_PRICE.value
    assert content["member_level"] == data["member_level"]
    assert content["price"] == data["price"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_read_member_price_strategy(client):
    """测试获取会员价格策略"""
    # 先创建一个会员价格策略
    data = {
        "name": "测试创建限时策略",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE,
        "member_level": MemberLevel.BASIC.value,
        "price": 199.9
    }
    create_response = client.post("/api/v1/pricing/member-price-strategies/", json=data)
    strategy_id = create_response.json()["id"]

    # 获取该会员价格策略
    response = client.get(f"/api/v1/pricing/member-price-strategies/{strategy_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == data["name"]
    assert content["description"] == data["description"]
    assert content["start_time"] == data["start_time"]
    assert content["end_time"] == data["end_time"]
    assert content["scope"] == data["scope"]
    assert content["is_mutual_exclusive"] == data["is_mutual_exclusive"]
    assert content["status"] == data["status"]
    assert content["type"] == PricingStrategyType.MEMBER_PRICE.value
    assert content["member_level"] == data["member_level"]
    assert content["price"] == data["price"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_read_member_price_strategies(client):
    """测试获取会员价格策略列表"""
    response = client.get("/api/v1/pricing/member-price-strategies/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)


def test_update_member_price_strategy(client):
    """测试更新会员价格策略"""
    # 先创建一个会员价格策略
    data = {
        "name": "测试更新会员价格策略",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE,
        "member_level": MemberLevel.BASIC.value,
        "price": 199.9
    }
    create_response = client.post("/api/v1/pricing/member-price-strategies/", json=data)
    strategy_id = create_response.json()["id"]

    # 更新会员价格策略信息
    update_data = {
        "name": "更新会员价格策略信息",
        "description": "更新后这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').shift(days=1).format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=31).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.ORDER.value,
        "is_mutual_exclusive": True,
        "status": Status.INACTIVE.value,
        "member_level": MemberLevel.PREMIUM.value,
        "price": 299.9
    }
    response = client.put(f"/api/v1/pricing/member-price-strategies/{strategy_id}", json=update_data)
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == update_data["name"]
    assert content["description"] == update_data["description"]
    assert content["start_time"] == update_data["start_time"]
    assert content["end_time"] == update_data["end_time"]
    assert content["scope"] == update_data["scope"]
    assert content["is_mutual_exclusive"] == update_data["is_mutual_exclusive"]
    assert content["status"] == update_data["status"]
    assert content["type"] == PricingStrategyType.MEMBER_PRICE.value
    assert content["member_level"] == update_data["member_level"]
    assert content["price"] == update_data["price"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_delete_member_price_strategy(client):
    """测试删除会员价格策略"""
    # 先创建一个会员价格策略
    data = {
        "name": "测试更新会员价格策略",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE,
        "member_level": MemberLevel.BASIC.value,
        "price": 199.9
    }
    create_response = client.post("/api/v1/pricing/member-price-strategies/", json=data)
    strategy_id = create_response.json()["id"]

    # 删除该会员价格策略
    response = client.delete(f"/api/v1/pricing/member-price-strategies/{strategy_id}")
    assert response.status_code == 204


def test_pricing_strategy_bind_products(client):
    """测试定价策略绑定产品"""
    # 创建一个定价策略
    strategy_data = {
        "name": f"测试定价策略_{generate_random_string()}",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE.value,
    }
    strategy_response = client.post("/api/v1/pricing/pricing-strategies/", json=strategy_data)
    assert strategy_response.status_code == 201
    strategy_id = strategy_response.json()["id"]

    # 创建多个产品
    product_ids = []
    for i in range(3):
        product_data = {
            "name": f"测试产品_{i}_{generate_random_string()}",
            "price": 99.99 + i,
            "description": f"这是测试产品_{i}",
            "stock": 100 + i,
            "status": Status.ACTIVE.value,
            "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
        }
        product_response = client.post("/api/v1/product/", json=product_data)
        assert product_response.status_code == 201
        product_ids.append(product_response.json()["id"])

    # 将产品绑定到定价策略
    binding_data = {
        "pricing_strategy_id": strategy_id,
        "product_ids": product_ids
    }
    bind_response = client.post("/api/v1/pricing/pricing-strategies/add/products/", json=binding_data)
    assert bind_response.status_code == 200
    bind_result = bind_response.json()
    assert bind_result["success_count"] == 3
    assert len(bind_result["success_ids"]) == 3
    assert bind_result["failed_count"] == 0

    # 使用新添加的接口验证绑定
    strategy_products = client.get(f"/api/v1/pricing/pricing-strategies/{strategy_id}/products")
    assert strategy_products.status_code == 200
    products = strategy_products.json()
    assert len(products) == 3
    product_ids_in_response = [product["id"] for product in products]
    for product_id in product_ids:
        assert product_id in product_ids_in_response


def test_pricing_strategy_unbind_products(client):
    """测试定价策略解绑产品"""
    # 创建一个定价策略
    strategy_data = {
        "name": f"测试定价策略_{generate_random_string()}",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE.value,
    }
    strategy_response = client.post("/api/v1/pricing/pricing-strategies/", json=strategy_data)
    assert strategy_response.status_code == 201
    strategy_id = strategy_response.json()["id"]

    # 创建多个产品
    product_ids = []
    for i in range(3):
        product_data = {
            "name": f"测试产品_{i}_{generate_random_string()}",
            "price": 99.99 + i,
            "description": f"这是测试产品_{i}",
            "stock": 100 + i,
            "status": Status.ACTIVE.value,
            "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
        }
        product_response = client.post("/api/v1/product/", json=product_data)
        assert product_response.status_code == 201
        product_ids.append(product_response.json()["id"])

    # 先将产品绑定到定价策略
    binding_data = {
        "pricing_strategy_id": strategy_id,
        "product_ids": product_ids
    }
    client.post("/api/v1/pricing/pricing-strategies/add/products/", json=binding_data)

    # 将产品从定价策略解绑
    unbind_response = client.post("/api/v1/pricing/pricing-strategies/remove/products/", json=binding_data)
    assert unbind_response.status_code == 200
    unbind_result = unbind_response.json()
    assert unbind_result["success_count"] == 3
    assert len(unbind_result["success_ids"]) == 3
    assert unbind_result["failed_count"] == 0

    # 验证解绑成功（这里需要有一个获取定价策略关联产品的接口，假设存在）
    # strategy_products = client.get(f"/api/v1/product/by/pricing-strategy/{strategy_id}/")
    # assert strategy_products.status_code == 200
    # products = strategy_products.json()
    # for product_id in product_ids:
    #     assert not any(product["id"] == product_id for product in products)

    # 使用新添加的接口验证解绑
    strategy_products = client.get(f"/api/v1/pricing/pricing-strategies/{strategy_id}/products")
    assert strategy_products.status_code == 200
    products = strategy_products.json()
    assert len(products) == 0


def test_pricing_strategy_bind_nonexistent_products(client):
    """测试定价策略绑定不存在的产品"""
    # 创建一个定价策略
    strategy_data = {
        "name": f"测试定价策略_{generate_random_string()}",
        "description": "这是一个测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE.value,
    }
    strategy_response = client.post("/api/v1/pricing/pricing-strategies/", json=strategy_data)
    assert strategy_response.status_code == 201
    strategy_id = strategy_response.json()["id"]

    # 绑定不存在的产品
    non_existent_product_ids = [9999, 10000]
    binding_data = {
        "pricing_strategy_id": strategy_id,
        "product_ids": non_existent_product_ids
    }
    bind_response = client.post("/api/v1/pricing/pricing-strategies/add/products/", json=binding_data)
    assert bind_response.status_code == 200
    bind_result = bind_response.json()
    assert bind_result["success_count"] == 0
    assert bind_result["failed_count"] == 2


def test_nonexistent_pricing_strategy_bind_products(client):
    """测试不存在的定价策略绑定产品"""
    # 创建产品
    product_data = {
        "name": f"测试产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是测试产品",
        "stock": 100,
        "status": Status.ACTIVE.value,
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    product_response = client.post("/api/v1/product/", json=product_data)
    assert product_response.status_code == 201
    product_id = product_response.json()["id"]

    # 尝试将产品绑定到不存在的定价策略
    nonexistent_strategy_id = 99999
    binding_data = {
        "pricing_strategy_id": nonexistent_strategy_id,
        "product_ids": [product_id]
    }
    bind_response = client.post("/api/v1/pricing/pricing-strategies/add/products/", json=binding_data)
    assert bind_response.status_code == 200  # API设计为即使失败也返回200，带详细信息
    bind_result = bind_response.json()
    assert bind_result["success_count"] == 0
    assert bind_result["failed_count"] == 1


def test_get_pricing_strategies_by_product(client):
    """测试获取产品关联的定价策略"""
    # 创建产品
    product_data = {
        "name": f"测试产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是测试产品",
        "stock": 100,
        "status": Status.ACTIVE.value,
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    product_response = client.post("/api/v1/product/", json=product_data)
    assert product_response.status_code == 201
    product_id = product_response.json()["id"]

    # 创建多个定价策略
    strategy_ids = []
    for i in range(3):
        strategy_data = {
            "name": f"测试定价策略_{i}_{generate_random_string()}",
            "description": f"这是一个测试定价策略_{i}",
            "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
            "scope": PricingStrategyScope.PRODUCT.value,
            "is_mutual_exclusive": False,
            "status": Status.ACTIVE.value,
        }
        strategy_response = client.post("/api/v1/pricing/pricing-strategies/", json=strategy_data)
        assert strategy_response.status_code == 201
        strategy_ids.append(strategy_response.json()["id"])

    # 绑定定价策略到产品
    for strategy_id in strategy_ids:
        binding_data = {
            "pricing_strategy_id": strategy_id,
            "product_ids": [product_id]
        }
        bind_response = client.post("/api/v1/pricing/pricing-strategies/add/products/", json=binding_data)
        assert bind_response.status_code == 200

    # 获取产品关联的定价策略
    product_strategies = client.get(f"/api/v1/product/{product_id}/pricing-strategies")
    assert product_strategies.status_code == 200
    strategies = product_strategies.json()
    assert len(strategies) == 3
    strategy_ids_in_response = [strategy["id"] for strategy in strategies]
    for strategy_id in strategy_ids:
        assert strategy_id in strategy_ids_in_response

    # 测试解绑后的结果
    # 解绑一个定价策略
    unbind_data = {
        "pricing_strategy_id": strategy_ids[0],
        "product_ids": [product_id]
    }
    unbind_response = client.post("/api/v1/pricing/pricing-strategies/remove/products/", json=unbind_data)
    assert unbind_response.status_code == 200

    # 验证解绑后只有2个定价策略
    product_strategies = client.get(f"/api/v1/product/{product_id}/pricing-strategies")
    assert product_strategies.status_code == 200
    strategies = product_strategies.json()
    assert len(strategies) == 2
    strategy_ids_in_response = [strategy["id"] for strategy in strategies]
    assert strategy_ids[0] not in strategy_ids_in_response
    assert strategy_ids[1] in strategy_ids_in_response
    assert strategy_ids[2] in strategy_ids_in_response
