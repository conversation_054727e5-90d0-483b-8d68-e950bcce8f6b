from datetime import datetime, timedelta

import arrow

from app.models.enum import Status
from app.models.pricing import PricingStrategyScope
from tests.utils import generate_random_string


# 产品测试用例
def test_create_product(client):
    """测试创建产品"""
    data = {
        "name": f"测试产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个测试产品",
        "stock": 100,
        "status": Status.ACTIVE.value,
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    response = client.post("/api/v1/product/", json=data)
    assert response.status_code == 201
    content = response.json()
    assert content["name"] == data["name"]
    assert content["price"] == data["price"]
    assert content["description"] == data["description"]
    assert content["stock"] == data["stock"]
    assert content["status"] == data["status"]
    assert content["listed_at"] == data["listed_at"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_read_product(client):
    """测试获取产品详情"""
    # 先创建一个产品
    data = {
        "name": f"测试产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个测试产品",
        "stock": 100,
        "status": Status.ACTIVE.value,
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    create_response = client.post("/api/v1/product/", json=data)
    product_id = create_response.json()["id"]

    # 获取该产品
    response = client.get(f"/api/v1/product/{product_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == data["name"]
    assert content["price"] == data["price"]
    assert content["description"] == data["description"]
    assert content["stock"] == data["stock"]
    assert content["status"] == data["status"]
    assert content["listed_at"] == data["listed_at"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content
    assert "contents" in content
    assert "tags" in content


def test_read_products(client):
    """测试获取产品列表"""
    # 先创建几个产品
    for i in range(3):
        data = {
            "name": f"测试产品_{generate_random_string()}",
            "price": 99.99 + i,
            "description": f"这是测试产品{i}",
            "stock": 100 + i,
            "status": Status.ACTIVE.value,
            "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
        }
        client.post("/api/v1/product/", json=data)

    # 获取产品列表
    response = client.get("/api/v1/product/")
    assert response.status_code == 200
    content = response.json()
    assert "code" in content
    assert "message" in content
    assert "data" in content
    assert content["code"] == 200
    assert "total" in content["data"]
    assert "list" in content["data"]
    assert len(content["data"]["list"]) >= 3


def test_search_products(client):
    """测试搜索产品"""
    # 创建测试产品
    test_products = [
        {
            "name": f"素食沙拉_{generate_random_string()}",
            "price": 50.0,
            "description": "健康美味的素食沙拉",
            "stock": 30,
            "status": Status.ACTIVE.value,
            "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
        },
        {
            "name": f"素食汉堡_{generate_random_string()}",
            "price": 60.0,
            "description": "美味的素食汉堡",
            "stock": 25,
            "status": Status.ACTIVE.value,
            "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
        },
        {
            "name": f"肉类汉堡_{generate_random_string()}",
            "price": 70.0,
            "description": "传统肉类汉堡",
            "stock": 20,
            "status": Status.INACTIVE.value,
            "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
        }
    ]
    
    for product_data in test_products:
        client.post("/api/v1/product/", json=product_data)
    
    # 测试关键字搜索
    response = client.get("/api/v1/product/search/?keyword=素食")
    assert response.status_code == 200
    content = response.json()
    assert content["code"] == 200
    assert content["message"] == "搜索产品列表成功"
    assert "data" in content
    assert "total" in content["data"]
    assert "list" in content["data"]
    assert content["data"]["total"] >= 2  # 至少有两个素食产品
    
    # 测试名称搜索
    response = client.get("/api/v1/product/search/?name=汉堡")
    assert response.status_code == 200
    content = response.json()
    assert content["data"]["total"] >= 2  # 至少有两个汉堡产品
    
    # 测试状态搜索
    response = client.get(f"/api/v1/product/search/?status={Status.INACTIVE.value}")
    assert response.status_code == 200
    content = response.json()
    assert content["data"]["total"] >= 1  # 至少有一个非活跃产品
    for product in content["data"]["list"]:
        assert product["status"] == Status.INACTIVE.value
    
    # 测试组合搜索
    response = client.get(f"/api/v1/product/search/?name=汉堡&status={Status.ACTIVE.value}")
    assert response.status_code == 200
    content = response.json()
    assert content["data"]["total"] >= 1  # 至少有一个活跃的汉堡产品
    for product in content["data"]["list"]:
        assert "汉堡" in product["name"]
        assert product["status"] == Status.ACTIVE.value
    
    # 测试分页
    response = client.get("/api/v1/product/search/?page=1&pageSize=2")
    assert response.status_code == 200
    content = response.json()
    assert len(content["data"]["list"]) <= 2  # 每页最多2条记录


def test_update_product(client):
    """测试更新产品"""
    # 先创建一个产品
    data = {
        "name": f"测试产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个测试产品",
        "stock": 100,
        "status": Status.ACTIVE.value,
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    create_response = client.post("/api/v1/product/", json=data)
    product_id = create_response.json()["id"]

    # 更新产品信息
    update_data = {
        "name": f"更新产品_{generate_random_string()}",
        "price": 199.99,
        "description": "这是更新后的产品描述",
        "stock": 50,
        "status": Status.INACTIVE.value,
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    response = client.put(f"/api/v1/product/{product_id}", json=update_data)
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == update_data["name"]
    assert content["price"] == update_data["price"]
    assert content["description"] == update_data["description"]
    assert content["stock"] == update_data["stock"]
    assert content["status"] == update_data["status"]
    assert content["listed_at"] == update_data["listed_at"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_delete_product(client):
    """测试删除产品"""
    # 先创建一个产品
    data = {
        "name": f"测试产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个测试产品",
        "stock": 100,
        "status": Status.ACTIVE.value,
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    create_response = client.post("/api/v1/product/", json=data)
    product_id = create_response.json()["id"]

    # 删除该产品
    response = client.delete(f"/api/v1/product/{product_id}")
    assert response.status_code == 204

    # 确认已删除
    get_response = client.get(f"/api/v1/product/{product_id}")
    assert get_response.status_code == 404


def test_get_active_products(client):
    """测试获取活跃产品列表"""
    # 创建活跃产品
    active_data = {
        "name": f"活跃产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个活跃产品",
        "stock": 100,
        "status": Status.ACTIVE.value,  # 活跃状态
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    client.post("/api/v1/product/", json=active_data)

    # 创建非活跃产品
    inactive_data = {
        "name": f"非活跃产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个非活跃产品",
        "stock": 100,
        "status": Status.INACTIVE.value,  # 非活跃状态
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    client.post("/api/v1/product/", json=inactive_data)

    # 获取活跃产品列表
    response = client.get("/api/v1/product/active/")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)
    # 验证所有返回的产品都是活跃状态
    for product in content:
        assert product["status"] == Status.ACTIVE.value


def test_get_products_by_type(client):
    """测试根据类型获取产品"""
    # 创建普通产品
    product_data = {
        "name": f"普通产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个普通产品",
        "stock": 100,
        "status": Status.ACTIVE.value,
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    client.post("/api/v1/product/", json=product_data)

    # 创建直销产品
    direct_sale_data = {
        "name": f"直销产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个直销产品",
        "stock": 100,
        "status": Status.ACTIVE.value,
        "shipping_fee": 10,
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    client.post("/api/v1/product/direct-sale/", json=direct_sale_data)

    # 获取普通产品
    response = client.get("/api/v1/product/by-type/product")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)
    for product in content:
        assert product["type"] == "product"

    # 获取直销产品
    response = client.get("/api/v1/product/by-type/direct_sale")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)
    for product in content:
        assert product["type"] == "direct_sale"


# 直销产品测试用例
def test_create_direct_sale_product(client):
    """测试创建直销产品"""
    data = {
        "name": f"直销产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个直销产品",
        "stock": 100,
        "status": Status.ACTIVE.value,
        "shipping_fee": 10,
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    response = client.post("/api/v1/product/direct-sale/", json=data)
    assert response.status_code == 201
    content = response.json()

    assert content["shipping_fee"] == data["shipping_fee"]
    assert content["name"] == data["name"]
    assert content["price"] == data["price"]
    assert content["description"] == data["description"]
    assert content["stock"] == data["stock"]
    assert content["status"] == data["status"]
    assert content["listed_at"] == data["listed_at"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_read_direct_sale_product(client):
    """测试获取直销产品详情"""
    # 先创建一个直销产品
    data = {
        "name": f"直销产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个直销产品",
        "stock": 100,
        "status": Status.ACTIVE.value,
        "shipping_fee": 10,
        "type": "direct_sale",
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    create_response = client.post("/api/v1/product/direct-sale/", json=data)
    product_id = create_response.json()["id"]

    # 获取该产品
    response = client.get(f"/api/v1/product/direct-sale/{product_id}")
    assert response.status_code == 200
    content = response.json()

    assert content["name"] == data["name"]
    assert content["price"] == data["price"]
    assert content["description"] == data["description"]
    assert content["stock"] == data["stock"]
    assert content["status"] == data["status"]
    assert content["listed_at"] == data["listed_at"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content
    assert "contents" in content
    assert "tags" in content

    assert content["shipping_fee"] == data["shipping_fee"]
    assert "contents" in content
    assert "tags" in content


# 预定产品测试用例
def test_create_reservation_product(client):
    """测试创建预定产品"""
    data = {
        "name": f"预定产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个预定产品",
        "stock": 100,
        "status": Status.ACTIVE.value,
        "reservation_fee": 10,
        "max_reservations": 50,
        "reservation_deadline": (datetime.now() + timedelta(days=7)).isoformat(),
        "cancellation_deadline": (datetime.now() + timedelta(days=1)).isoformat(),
        "is_approval_required": True,
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    response = client.post("/api/v1/product/reservation/", json=data)
    assert response.status_code == 201
    content = response.json()

    assert content["name"] == data["name"]
    assert content["price"] == data["price"]
    assert content["description"] == data["description"]
    assert content["stock"] == data["stock"]
    assert content["status"] == data["status"]
    assert content["listed_at"] == data["listed_at"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content

    assert content["reservation_fee"] == data["reservation_fee"]
    assert content["max_reservations"] == data["max_reservations"]
    assert content["is_approval_required"] == data["is_approval_required"]


def test_read_reservation_product(client):
    """测试获取预定产品详情"""
    # 先创建一个预定产品
    data = {
        "name": f"预定产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个预定产品",
        "stock": 100,
        "status": Status.ACTIVE.value,
        "type": "reservation",
        "reservation_fee": 10,
        "max_reservations": 50,
        "reservation_deadline": (datetime.now() + timedelta(days=7)).isoformat(),
        "cancellation_deadline": (datetime.now() + timedelta(days=1)).isoformat(),
        "is_approval_required": True,
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    create_response = client.post("/api/v1/product/reservation/", json=data)
    product_id = create_response.json()["id"]

    # 获取该产品
    response = client.get(f"/api/v1/product/reservation/{product_id}")
    assert response.status_code == 200
    content = response.json()

    assert content["name"] == data["name"]
    assert content["price"] == data["price"]
    assert content["description"] == data["description"]
    assert content["stock"] == data["stock"]
    assert content["status"] == data["status"]
    assert content["listed_at"] == data["listed_at"]
    assert "type" in content
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content
    assert "contents" in content
    assert "tags" in content

    assert content["reservation_fee"] == data["reservation_fee"]
    assert "contents" in content
    assert "tags" in content


def test_get_active_reservation_products(client):
    """测试获取可预定的产品列表"""
    # 创建可预定产品（有库存且状态为活跃）
    available_data = {
        "name": f"可预定产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个可预定产品",
        "stock": 100,  # 有库存
        "status": Status.ACTIVE.value,  # 活跃状态
        "reservation_fee": 10,
        "max_reservations": 50,
        "reservation_deadline": (datetime.now() + timedelta(days=7)).isoformat(),
        "cancellation_deadline": (datetime.now() + timedelta(days=1)).isoformat(),
        "is_approval_required": True,
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    client.post("/api/v1/product/reservation/", json=available_data)

    # 创建不可预定产品（无库存）
    unavailable_data = {
        "name": f"不可预定产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个不可预定产品",
        "stock": 0,  # 无库存
        "status": Status.ACTIVE.value,  # 活跃状态
        "reservation_fee": 10,
        "max_reservations": 50,
        "reservation_deadline": (datetime.now() + timedelta(days=7)).isoformat(),
        "cancellation_deadline": (datetime.now() + timedelta(days=1)).isoformat(),
        "is_approval_required": True,
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    client.post("/api/v1/product/reservation/", json=unavailable_data)

    # 获取可预定产品列表
    response = client.get("/api/v1/product/reservation/available/")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)
    # 验证所有返回的产品都有库存且为活跃状态
    for product in content:
        assert product["stock"] > 0
        assert product["status"] == Status.ACTIVE.value


# 批量内容绑定解绑产品测试
def test_bulk_content_product_binding(client):
    """测试批量内容绑定解绑产品"""
    # 创建一个产品
    product_data = {
        "name": f"批量测试产品_{generate_random_string()}",
        "price": 100.0,
        "description": "这是一个批量测试产品",
        "stock": 10,
        "status": Status.ACTIVE.value
    }
    product_response = client.post("/api/v1/product/", json=product_data)
    assert product_response.status_code == 201
    product_id = product_response.json()["id"]

    # 创建多个内容
    content_ids = []
    for i in range(3):
        content_data = {
            "name": f"这是批量测试内容名称_{i}_{generate_random_string()}",
            "content": f"这是批量测试内容_{i}_{generate_random_string()}",
            "image": f"http://example.com/image_{generate_random_string()}.jpg",
            "thumbnail": f"http://example.com/thumbnail_{generate_random_string()}.jpg",
            "type": "content",
            "sort_order": i
        }
        content_response = client.post("/api/v1/content/", json=content_data)
        assert content_response.status_code == 201
        content_ids.append(content_response.json()["id"])

    # 批量绑定内容到产品
    binding_data = {
        "content_ids": content_ids,
        "product_id": product_id
    }
    bind_response = client.post("/api/v1/product/add/contents/", json=binding_data)
    assert bind_response.status_code == 200
    bind_result = bind_response.json()
    assert bind_result["success_count"] == 3
    assert len(bind_result["success_ids"]) == 3
    assert bind_result["failed_count"] == 0

    # 验证绑定是否成功
    product_contents = client.get(f"/api/v1/content/by/product/{product_id}/")
    assert product_contents.status_code == 200
    contents = product_contents.json()
    for content_id in content_ids:
        assert any(content["id"] == content_id for content in contents)

    # 批量解绑内容和产品
    unbind_response = client.post("/api/v1/product/remove/contents/", json=binding_data)
    assert unbind_response.status_code == 200
    unbind_result = unbind_response.json()
    assert unbind_result["success_count"] == 3
    assert len(unbind_result["success_ids"]) == 3
    assert unbind_result["failed_count"] == 0

    # 验证解绑是否成功
    product_contents = client.get(f"/api/v1/content/by/product/{product_id}/")
    assert product_contents.status_code == 200
    contents = product_contents.json()
    for content_id in content_ids:
        assert not any(content["id"] == content_id for content in contents)


def test_add_pricing_strategies_to_product(client):
    """测试产品绑定定价策略"""
    # 创建一个产品
    product_data = {
        "name": f"测试产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个测试产品",
        "stock": 100,
        "status": Status.ACTIVE.value,
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    product_response = client.post("/api/v1/product/", json=product_data)
    assert product_response.status_code == 201
    product_id = product_response.json()["id"]

    # 创建多个定价策略
    pricing_strategy_ids = []
    for i in range(3):
        strategy_data = {
            "name": f"测试定价策略_{i}_{generate_random_string()}",
            "description": f"这是测试定价策略_{i}",
            "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
            "scope": PricingStrategyScope.PRODUCT.value,
            "is_mutual_exclusive": False,
            "status": Status.ACTIVE.value,
        }
        strategy_response = client.post("/api/v1/pricing/pricing-strategies/", json=strategy_data)
        assert strategy_response.status_code == 201
        pricing_strategy_ids.append(strategy_response.json()["id"])

    # 将定价策略绑定到产品
    binding_data = {
        "product_id": product_id,
        "pricing_strategy_ids": pricing_strategy_ids
    }
    bind_response = client.post("/api/v1/product/add/pricing-strategies/", json=binding_data)
    assert bind_response.status_code == 200
    bind_result = bind_response.json()
    assert bind_result["success_count"] == 3
    assert len(bind_result["success_ids"]) == 3
    assert bind_result["failed_count"] == 0

    # 验证绑定成功（这里需要有一个获取产品关联定价策略的接口，假设存在）
    # product_pricing_strategies = client.get(f"/api/v1/pricing/by/product/{product_id}/")
    # assert product_pricing_strategies.status_code == 200
    # strategies = product_pricing_strategies.json()
    # for strategy_id in pricing_strategy_ids:
    #     assert any(strategy["id"] == strategy_id for strategy in strategies)


def test_remove_pricing_strategies_from_product(client):
    """测试产品解绑定价策略"""
    # 创建一个产品
    product_data = {
        "name": f"测试产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个测试产品",
        "stock": 100,
        "status": Status.ACTIVE.value,
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    product_response = client.post("/api/v1/product/", json=product_data)
    assert product_response.status_code == 201
    product_id = product_response.json()["id"]

    # 创建多个定价策略
    pricing_strategy_ids = []
    for i in range(3):
        strategy_data = {
            "name": f"测试定价策略_{i}_{generate_random_string()}",
            "description": f"这是测试定价策略_{i}",
            "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
            "scope": PricingStrategyScope.PRODUCT.value,
            "is_mutual_exclusive": False,
            "status": Status.ACTIVE.value,
        }
        strategy_response = client.post("/api/v1/pricing/pricing-strategies/", json=strategy_data)
        assert strategy_response.status_code == 201
        pricing_strategy_ids.append(strategy_response.json()["id"])

    # 先将定价策略绑定到产品
    binding_data = {
        "product_id": product_id,
        "pricing_strategy_ids": pricing_strategy_ids
    }
    client.post("/api/v1/product/add/pricing-strategies/", json=binding_data)

    # 将定价策略从产品解绑
    unbind_response = client.post("/api/v1/product/remove/pricing-strategies/", json=binding_data)
    assert unbind_response.status_code == 200
    unbind_result = unbind_response.json()
    assert unbind_result["success_count"] == 3
    assert len(unbind_result["success_ids"]) == 3
    assert unbind_result["failed_count"] == 0

    # 验证解绑成功（这里需要有一个获取产品关联定价策略的接口，假设存在）
    # product_pricing_strategies = client.get(f"/api/v1/pricing/by/product/{product_id}/")
    # assert product_pricing_strategies.status_code == 200
    # strategies = product_pricing_strategies.json()
    # for strategy_id in pricing_strategy_ids:
    #     assert not any(strategy["id"] == strategy_id for strategy in strategies)


def test_add_nonexistent_pricing_strategies_to_product(client):
    """测试产品绑定不存在的定价策略"""
    # 创建一个产品
    product_data = {
        "name": f"测试产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个测试产品",
        "stock": 100,
        "status": Status.ACTIVE.value,
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    product_response = client.post("/api/v1/product/", json=product_data)
    assert product_response.status_code == 201
    product_id = product_response.json()["id"]

    # 绑定不存在的定价策略
    non_existent_strategy_ids = [9999, 10000]
    binding_data = {
        "product_id": product_id,
        "pricing_strategy_ids": non_existent_strategy_ids
    }
    bind_response = client.post("/api/v1/product/add/pricing-strategies/", json=binding_data)
    assert bind_response.status_code == 200
    bind_result = bind_response.json()
    assert bind_result["success_count"] == 0
    assert bind_result["failed_count"] == 2


def test_add_pricing_strategies_to_nonexistent_product(client):
    """测试不存在的产品绑定定价策略"""
    # 创建定价策略
    strategy_data = {
        "name": f"测试定价策略_{generate_random_string()}",
        "description": "这是测试定价策略",
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "scope": PricingStrategyScope.PRODUCT.value,
        "is_mutual_exclusive": False,
        "status": Status.ACTIVE.value,
    }
    strategy_response = client.post("/api/v1/pricing/pricing-strategies/", json=strategy_data)
    assert strategy_response.status_code == 201
    strategy_id = strategy_response.json()["id"]

    # 绑定到不存在的产品
    non_existent_product_id = 9999
    binding_data = {
        "product_id": non_existent_product_id,
        "pricing_strategy_ids": [strategy_id]
    }
    bind_response = client.post("/api/v1/product/add/pricing-strategies/", json=binding_data)
    assert bind_response.status_code == 200
    bind_result = bind_response.json()
    assert bind_result["success_count"] == 0
    assert bind_result["failed_count"] == 1


def test_get_pricing_strategies_by_product(client):
    """测试获取产品绑定的定价策略"""
    # 先创建一个产品
    product_data = {
        "name": f"测试产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个测试产品",
        "stock": 100,
        "status": Status.ACTIVE.value,
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    product_response = client.post("/api/v1/product/", json=product_data)
    assert product_response.status_code == 201
    product_id = product_response.json()["id"]

    # 创建多个定价策略
    pricing_strategy_ids = []
    for i in range(3):
        strategy_data = {
            "name": f"测试定价策略_{i}_{generate_random_string()}",
            "description": f"这是测试定价策略_{i}",
            "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
            "scope": PricingStrategyScope.PRODUCT.value,
            "is_mutual_exclusive": False,
            "status": Status.ACTIVE.value,
        }
        strategy_response = client.post("/api/v1/pricing/pricing-strategies/", json=strategy_data)
        assert strategy_response.status_code == 201
        pricing_strategy_ids.append(strategy_response.json()["id"])

    # 将定价策略绑定到产品
    binding_data = {
        "product_id": product_id,
        "pricing_strategy_ids": pricing_strategy_ids
    }
    bind_response = client.post("/api/v1/product/add/pricing-strategies/", json=binding_data)
    assert bind_response.status_code == 200

    # 测试获取产品绑定的定价策略
    strategies_response = client.get(f"/api/v1/product/{product_id}/pricing-strategies")
    assert strategies_response.status_code == 200
    strategies = strategies_response.json()
    assert isinstance(strategies, list)
    assert len(strategies) == 3

    # 验证返回的定价策略ID是否正确
    strategy_ids_from_response = [strategy["id"] for strategy in strategies]
    for strategy_id in pricing_strategy_ids:
        assert strategy_id in strategy_ids_from_response


def test_add_rules_to_product(client):
    """测试批量将规则绑定到产品"""
    # 创建一个产品
    product_data = {
        "name": f"测试产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个测试产品",
        "stock": 100,
        "status": Status.ACTIVE.value,
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    product_response = client.post("/api/v1/product/", json=product_data)
    assert product_response.status_code == 201
    product_id = product_response.json()["id"]

    # 创建多个规则
    rule_ids = []
    for i in range(3):
        rule_data = {
            "name": f"测试规则_{generate_random_string()}",
            "status": Status.ACTIVE.value
        }
        rule_response = client.post("/api/v1/rule/", json=rule_data)
        assert rule_response.status_code == 201
        rule_ids.append(rule_response.json()["id"])

    # 批量绑定规则到产品
    binding_data = {
        "product_id": product_id,
        "rule_ids": rule_ids
    }
    bind_response = client.post("/api/v1/product/add/rules/", json=binding_data)
    assert bind_response.status_code == 200
    bind_result = bind_response.json()
    assert bind_result["success_count"] == 3
    assert len(bind_result["success_ids"]) == 3
    assert bind_result["failed_count"] == 0

    # 验证绑定是否成功
    rules_response = client.get(f"/api/v1/product/{product_id}/rules")
    assert rules_response.status_code == 200
    rules = rules_response.json()
    assert len(rules) == 3
    response_rule_ids = [rule["id"] for rule in rules]
    for rule_id in rule_ids:
        assert rule_id in response_rule_ids


def test_remove_rules_from_product(client):
    """测试批量将规则从产品解绑"""
    # 创建一个产品
    product_data = {
        "name": f"测试产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个测试产品",
        "stock": 100,
        "status": Status.ACTIVE.value,
        "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
    }
    product_response = client.post("/api/v1/product/", json=product_data)
    assert product_response.status_code == 201
    product_id = product_response.json()["id"]

    # 创建多个规则
    rule_ids = []
    for i in range(3):
        rule_data = {
            "name": f"测试规则_{generate_random_string()}",
            "status": Status.ACTIVE.value
        }
        rule_response = client.post("/api/v1/rule/", json=rule_data)
        assert rule_response.status_code == 201
        rule_ids.append(rule_response.json()["id"])

    # 先批量绑定规则到产品
    binding_data = {
        "product_id": product_id,
        "rule_ids": rule_ids
    }
    client.post("/api/v1/product/add/rules/", json=binding_data)

    # 批量解绑规则从产品
    unbind_response = client.post("/api/v1/product/remove/rules/", json=binding_data)
    assert unbind_response.status_code == 200
    unbind_result = unbind_response.json()
    assert unbind_result["success_count"] == 3
    assert len(unbind_result["success_ids"]) == 3
    assert unbind_result["failed_count"] == 0

    # 验证解绑是否成功
    rules_response = client.get(f"/api/v1/product/{product_id}/rules")
    assert rules_response.status_code == 200
    rules = rules_response.json()
    assert len(rules) == 0


def test_get_products_by_rule(client):
    """测试通过规则获取关联的产品"""
    # 创建一个规则
    rule_data = {
        "name": f"测试规则_{generate_random_string()}",
        "status": Status.ACTIVE.value
    }
    rule_response = client.post("/api/v1/rule/", json=rule_data)
    assert rule_response.status_code == 201
    rule_id = rule_response.json()["id"]

    # 创建多个产品
    product_ids = []
    for i in range(3):
        product_data = {
            "name": f"测试产品_{i}_{generate_random_string()}",
            "price": 99.99 + i,
            "description": f"这是测试产品{i}",
            "stock": 100 + i,
            "status": Status.ACTIVE.value,
            "listed_at": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")
        }
        product_response = client.post("/api/v1/product/", json=product_data)
        assert product_response.status_code == 201
        product_ids.append(product_response.json()["id"])

    # 批量绑定产品到规则
    for product_id in product_ids:
        binding_data = {
            "product_id": product_id,
            "rule_ids": [rule_id]
        }
        client.post("/api/v1/product/add/rules/", json=binding_data)

    # 通过规则获取产品
    products_response = client.get(f"/api/v1/rule/{rule_id}/products")
    assert products_response.status_code == 200
    products = products_response.json()
    assert len(products) == 3
    response_product_ids = [product["id"] for product in products]
    for product_id in product_ids:
        assert product_id in response_product_ids
