import random
import string

import arrow
import pytest

from app.models.enum import Status
from app.models.order import OrderStatus, PaymentStatus, PaymentMethod, OrderItem, Order
from app.models.product import Product
from app.models.reservation import ReservationStatus
from app.models.rule import Rule, RuleItem
from app.models.user import User
from tests.conftest import TestingSessionLocal


# 辅助函数生成随机字符串
def generate_random_string(length=6):
    """生成指定长度的随机字符串"""
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))


@pytest.fixture
def prepare_test_data():
    """准备预订测试所需的基础数据"""
    db = TestingSessionLocal()

    # 创建测试用户
    user = User(
        username=f"test_user_{generate_random_string()}",
        status=Status.ACTIVE
    )
    db.add(user)
    db.flush()

    # 创建测试产品
    product = Product(
        name=f"测试产品_{generate_random_string()}",
        price=100.0,
        description="测试产品描述",
        stock=10,
        status=Status.ACTIVE
    )
    db.add(product)
    db.flush()

    # 创建测试规则
    rule = Rule(
        name=f"测试规则_{generate_random_string()}",
        status=Status.ACTIVE
    )
    db.add(rule)
    db.flush()

    rule_item = RuleItem(
        rule_id=rule.id,
        name=f"测试规则项_{generate_random_string()}",
        start_time=arrow.now('Asia/Shanghai').datetime,
        end_time=arrow.now('Asia/Shanghai').shift(days=1).datetime,
        start_time_cron_str="* 2 2 * *",
        end_time_cron_str="* 2 2 * *",
        quantity=10,
        order=1,
        allowed_operations="",
        forbidden_operations=""
    )
    db.add(rule_item)
    db.flush()
    # 添加产品和规则的关联关系
    product.rules.append(rule)
    db.flush()

    # 创建测试订单
    order = Order(
        user_id=user.id,
        status=OrderStatus.PENDING,
        payment_status=PaymentStatus.UNPAID,
        total_amount=100.0,
        payable_amount=90.0,
        actual_amount_paid=0.0,
        payment_method=PaymentMethod.ACCOUNT_BALANCE
    )
    db.add(order)
    db.flush()

    # 创建测试订单项
    order_item = OrderItem(
        order_id=order.id,
        product_id=product.id,
        quantity=1,
        price=100.0,
        subtotal=100.0,
        final_price=90.0,
        payable_amount=90.0
    )
    db.add(order_item)
    db.commit()

    test_data = {
        "user_id": user.id,
        "product_id": product.id,
        "rule_id": rule.id,
        "rule_item_id": rule_item.id,
        "order_id": order.id,
        "order_item_id": order_item.id
    }

    db.close()
    return test_data


# 预订请求测试用例
@pytest.fixture
def create_test_reservation(client, disable_foreign_keys, prepare_test_data):
    """创建测试预订请求的固定函数"""

    def _create_reservation(status=ReservationStatus.PENDING):
        data = {
            "order_item_id": prepare_test_data["order_item_id"],
            "user_id": prepare_test_data["user_id"],
            "product_id": prepare_test_data["product_id"],
            "rule_id": prepare_test_data["rule_id"],
            "rule_item_id": prepare_test_data["rule_item_id"],
            "status": status.value,
            "reservation_period": f"2023-06-01 ~ 2023-06-10",
            "reservation_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "verification_code": f"VC{generate_random_string()}"
        }
        response = client.post("/api/v1/reservation/", json=data)
        return response.json()

    return _create_reservation


def test_create_reservation_request(client, disable_foreign_keys, prepare_test_data):
    """测试创建预订请求"""
    data = {
        "order_item_id": prepare_test_data["order_item_id"],
        "user_id": prepare_test_data["user_id"],
        "product_id": prepare_test_data["product_id"],
        "rule_id": prepare_test_data["rule_id"],
        "rule_item_id": prepare_test_data["rule_item_id"],
        "status": ReservationStatus.PENDING.value,
        "reservation_period": "2023-06-01 ~ 2023-06-10",
        "reservation_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "verification_code": f"VC{generate_random_string()}"
    }
    response = client.post("/api/v1/reservation/", json=data)
    assert response.status_code == 201
    content = response.json()
    assert content["order_item_id"] == data["order_item_id"]
    assert content["user_id"] == data["user_id"]
    assert content["product_id"] == data["product_id"]
    assert content["rule_id"] == data["rule_id"]
    assert content["status"] == data["status"]
    assert content["reservation_period"] == data["reservation_period"]
    assert content["verification_code"] == data["verification_code"]
    assert content["reservation_time"] == data["reservation_time"]
    assert "id" in content
    assert "reservation_time" in content


def test_read_reservation_request(client, disable_foreign_keys, create_test_reservation):
    """测试获取预订请求详情"""
    # 先创建一个预订请求
    reservation = create_test_reservation()
    reservation_id = reservation["id"]

    # 获取该预订请求
    response = client.get(f"/api/v1/reservation/{reservation_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["id"] == reservation_id
    assert content["order_item_id"] == reservation["order_item_id"]
    assert content["user_id"] == reservation["user_id"]
    assert "reservation_time" in content
    # 验证返回了关联数据（可能为None，因为测试环境没有实际关联数据）
    assert "user" in content
    assert "product" in content
    assert "rule" in content
    assert "order_item" in content


def test_read_reservation_requests(client, disable_foreign_keys, create_test_reservation):
    """测试获取预订请求列表"""
    # 创建几个预订请求
    for _ in range(3):
        create_test_reservation()

    # 获取预订请求列表
    response = client.get("/api/v1/reservation/")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)
    assert len(content) >= 3


def test_update_reservation_request(client, disable_foreign_keys, create_test_reservation):
    """测试更新预订请求"""
    # 先创建一个预订请求
    reservation = create_test_reservation()
    reservation_id = reservation["id"]

    # 更新预订请求
    update_data = {
        "status": ReservationStatus.PAID_DEPOSIT.value,
        "reservation_period": "2023-07-01 ~ 2023-07-10",
        "verification_code": f"VC{generate_random_string()}"
    }
    response = client.put(f"/api/v1/reservation/{reservation_id}", json=update_data)
    assert response.status_code == 200
    content = response.json()
    assert content["id"] == reservation_id
    assert content["status"] == update_data["status"]
    assert content["reservation_period"] == update_data["reservation_period"]
    assert content["verification_code"] == update_data["verification_code"]


def test_delete_reservation_request(client, disable_foreign_keys, create_test_reservation):
    """测试删除预订请求"""
    # 先创建一个预订请求
    reservation = create_test_reservation()
    reservation_id = reservation["id"]

    # 删除该预订请求
    response = client.delete(f"/api/v1/reservation/{reservation_id}")
    assert response.status_code == 204

    # 确认已删除
    get_response = client.get(f"/api/v1/reservation/{reservation_id}")
    assert get_response.status_code == 404


def test_get_user_reservations(client, disable_foreign_keys, create_test_reservation, prepare_test_data):
    """测试获取用户的预订请求"""
    # 为同一用户创建几个预订请求
    user_id = prepare_test_data["user_id"]
    for _ in range(3):
        create_test_reservation()

    # 获取该用户的预订请求
    response = client.get(f"/api/v1/reservation/user/{user_id}")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)
    # 验证所有返回的预订请求都属于指定用户
    for reservation in content:
        assert reservation["user_id"] == user_id


def test_get_product_reservations(client, disable_foreign_keys, create_test_reservation, prepare_test_data):
    """测试获取产品的预订请求"""
    # 为同一产品创建几个预订请求
    product_id = prepare_test_data["product_id"]
    for _ in range(3):
        create_test_reservation()

    # 获取该产品的预订请求
    response = client.get(f"/api/v1/reservation/product/{product_id}")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)
    # 验证所有返回的预订请求都属于指定产品
    for reservation in content:
        assert reservation["product_id"] == product_id


def test_get_reservations_by_status(client, disable_foreign_keys, create_test_reservation):
    """测试获取指定状态的预订请求"""
    # 创建不同状态的预订请求
    for status in [ReservationStatus.PENDING, ReservationStatus.PAID_DEPOSIT, ReservationStatus.PAID_FULL]:
        create_test_reservation(status=status)

    # 获取PENDING状态的预订请求
    response = client.get(f"/api/v1/reservation/status/{ReservationStatus.PENDING.value}")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)
    # 验证所有返回的预订请求都是PENDING状态
    for reservation in content:
        assert reservation["status"] == ReservationStatus.PENDING.value


def test_pay_deposit(client, disable_foreign_keys, create_test_reservation):
    """测试支付定金"""
    # 先创建一个预订请求
    reservation = create_test_reservation()
    reservation_id = reservation["id"]

    # 支付定金
    response = client.post(f"/api/v1/reservation/{reservation_id}/pay-deposit")
    assert response.status_code == 200
    content = response.json()
    assert content["success"] is True
    assert "定金支付成功" in content["message"]
    assert content["reservation"]["id"] == reservation_id
    assert content["reservation"]["status"] == ReservationStatus.PAID_DEPOSIT.value


def test_pay_balance(client, disable_foreign_keys, create_test_reservation):
    """测试支付尾款"""
    # 先创建一个已支付定金的预订请求
    reservation = create_test_reservation(status=ReservationStatus.PAID_DEPOSIT)
    reservation_id = reservation["id"]

    # 支付尾款
    response = client.post(f"/api/v1/reservation/{reservation_id}/pay-balance")
    assert response.status_code == 200
    content = response.json()
    assert content["success"] is True
    assert "尾款支付成功" in content["message"]
    assert content["reservation"]["id"] == reservation_id
    assert content["reservation"]["status"] == ReservationStatus.PAID_FULL.value


def test_pay_balance_without_deposit(client, disable_foreign_keys, create_test_reservation):
    """测试未支付定金直接支付尾款的情况"""
    # 先创建一个未支付定金的预订请求（PENDING状态）
    reservation = create_test_reservation()
    reservation_id = reservation["id"]

    # 尝试支付尾款，应该失败
    response = client.post(f"/api/v1/reservation/{reservation_id}/pay-balance")
    assert response.status_code == 400
    content = response.json()
    assert "当前状态无法支付尾款" in content["detail"]


def test_cancel_reservation(client, disable_foreign_keys, create_test_reservation):
    """测试取消预订"""
    # 先创建一个预订请求
    reservation = create_test_reservation()
    reservation_id = reservation["id"]

    # 取消预订
    response = client.post(f"/api/v1/reservation/{reservation_id}/cancel")
    assert response.status_code == 200
    content = response.json()
    assert content["success"] is True
    assert "预订已取消" in content["message"]
    assert content["reservation"]["id"] == reservation_id
    assert content["reservation"]["status"] == ReservationStatus.CANCELLED.value
