import arrow

from app.models.enum import Status
from tests.utils import generate_random_string


def test_create_rule(client):
    """测试创建规则"""
    data = {
        "name": generate_random_string(),
        "status": Status.ACTIVE.value
    }
    response = client.post("/api/v1/rule/", json=data)
    assert response.status_code == 201
    content = response.json()
    assert content["name"] == data["name"]
    assert content["status"] == Status.ACTIVE.value
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_read_rule(client):
    """测试获取规则详情"""
    # 先创建一个规则
    data = {
        "name": generate_random_string(),
        "status": Status.ACTIVE.value
    }
    create_response = client.post("/api/v1/rule/", json=data)
    rule_id = create_response.json()["id"]

    # 获取该规则
    response = client.get(f"/api/v1/rule/{rule_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == data["name"]
    assert content["status"] == Status.ACTIVE.value
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content
    assert "rule_items" in content


def test_read_rules(client):
    """测试获取规则列表"""
    # 先创建几个规则
    for i in range(3):
        data = {
            "name": generate_random_string(),
            "status": Status.ACTIVE.value if i % 2 == 0 else Status.INACTIVE.value
        }
        client.post("/api/v1/rule/", json=data)

    # 获取规则列表
    response = client.get("/api/v1/rule/")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)
    assert len(content) >= 3


def test_read_active_rules(client):
    """测试获取活跃规则列表"""
    # 创建活跃规则
    active_data = {
        "name": generate_random_string(),
        "status": Status.ACTIVE.value
    }
    client.post("/api/v1/rule/", json=active_data)

    # 创建非活跃规则
    inactive_data = {
        "name": generate_random_string(),
        "status": Status.INACTIVE.value
    }
    client.post("/api/v1/rule/", json=inactive_data)

    # 获取活跃规则列表
    response = client.get("/api/v1/rule/active/")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)
    # 验证所有返回的规则都是活跃状态
    for rule in content:
        assert rule["status"] == Status.ACTIVE.value


def test_update_rule(client):
    """测试更新规则"""
    # 先创建一个规则
    data = {
        "name": generate_random_string(),
        "status": Status.ACTIVE.value
    }
    create_response = client.post("/api/v1/rule/", json=data)
    rule_id = create_response.json()["id"]

    # 更新规则信息
    update_data = {
        "name": generate_random_string(),
        "status": Status.INACTIVE.value
    }
    response = client.put(f"/api/v1/rule/{rule_id}", json=update_data)
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == update_data["name"]
    assert content["status"] == update_data["status"]
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content


def test_delete_rule(client):
    """测试删除规则"""
    # 先创建一个规则
    data = {
        "name": generate_random_string(),
        "status": Status.ACTIVE.value
    }
    create_response = client.post("/api/v1/rule/", json=data)
    rule_id = create_response.json()["id"]

    # 删除该规则
    response = client.delete(f"/api/v1/rule/{rule_id}")
    assert response.status_code == 204

    # 确认已删除
    get_response = client.get(f"/api/v1/rule/{rule_id}")
    assert get_response.status_code == 404


def test_create_rule_item(client):
    """测试创建规则项"""
    # 创建规则
    rule_data = {
        "name": generate_random_string(),
        "status": Status.ACTIVE.value
    }
    rule_response = client.post("/api/v1/rule/", json=rule_data)
    rule_id = rule_response.json()["id"]

    # 创建规则项
    rule_item_data = {
        "rule_id": rule_id,
        "name": generate_random_string(),
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "quantity": 10,
        "start_time_cron_str": "0 3 * * *",
        "end_time_cron_str": "0 3 * * *",
        "allowed_operations": "usage,reservation",
        "forbidden_operations": ""
    }
    response = client.post("/api/v1/rule/item/", json=rule_item_data)
    assert response.status_code == 201
    content = response.json()
    assert content["rule_id"] == rule_item_data["rule_id"]
    assert content["name"] == rule_item_data["name"]
    assert content["start_time"] == rule_item_data["start_time"]
    assert content["end_time"] == rule_item_data["end_time"]
    assert content["quantity"] == rule_item_data["quantity"]
    assert content["start_time_cron_str"] == rule_item_data["start_time_cron_str"]
    assert content["end_time_cron_str"] == rule_item_data["end_time_cron_str"]
    assert content["allowed_operations"] == rule_item_data["allowed_operations"]
    assert content["forbidden_operations"] == rule_item_data["forbidden_operations"]
    assert "id" in content


def test_read_rule_item(client):
    """测试获取规则项详情"""
    # 创建规则
    rule_data = {
        "name": generate_random_string(),
        "status": Status.ACTIVE.value
    }
    rule_response = client.post("/api/v1/rule/", json=rule_data)
    rule_id = rule_response.json()["id"]

    # 创建规则项
    rule_item_data = {
        "rule_id": rule_id,
        "name": generate_random_string(),
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "quantity": 10,
        "start_time_cron_str": "0 3 * * *",
        "end_time_cron_str": "0 3 * * *",
        "allowed_operations": "usage,reservation",
        "forbidden_operations": ""
    }
    create_response = client.post("/api/v1/rule/item/", json=rule_item_data)
    rule_item_id = create_response.json()["id"]

    # 获取该规则项
    response = client.get(f"/api/v1/rule/item/{rule_item_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["rule_id"] == rule_item_data["rule_id"]
    assert content["name"] == rule_item_data["name"]
    assert content["start_time"] == rule_item_data["start_time"]
    assert content["end_time"] == rule_item_data["end_time"]
    assert content["quantity"] == rule_item_data["quantity"]
    assert content["start_time_cron_str"] == rule_item_data["start_time_cron_str"]
    assert content["end_time_cron_str"] == rule_item_data["end_time_cron_str"]
    assert content["allowed_operations"] == rule_item_data["allowed_operations"]
    assert content["forbidden_operations"] == rule_item_data["forbidden_operations"]
    assert "id" in content


def test_read_rule_items_by_rule(client):
    """测试获取规则关联的规则项列表"""
    # 创建规则
    rule_data = {
        "name": generate_random_string(),
        "status": Status.ACTIVE.value
    }
    rule_response = client.post("/api/v1/rule/", json=rule_data)
    rule_id = rule_response.json()["id"]

    # 为规则创建多个规则项
    for i in range(3):
        rule_item_data = {
            "rule_id": rule_id,
            "name": generate_random_string(),
            "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
            "quantity": i * 5,
            "start_time_cron_str": "0 3 * * *",
            "end_time_cron_str": "0 3 * * *",
            "allowed_operations": "usage,reservation",
            "forbidden_operations": ""
        }
        client.post("/api/v1/rule/item/", json=rule_item_data)

    # 获取规则关联的规则项列表
    response = client.get(f"/api/v1/rule/{rule_id}/items/")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)
    assert len(content) == 3
    for item in content:
        assert item["rule_id"] == rule_id


def test_update_rule_item(client):
    """测试更新规则项"""
    # 创建规则
    rule_data = {
        "name": generate_random_string(),
        "status": Status.ACTIVE.value
    }
    rule_response = client.post("/api/v1/rule/", json=rule_data)
    rule_id = rule_response.json()["id"]

    # 创建规则项
    rule_item_data = {
        "rule_id": rule_id,
        "name": generate_random_string(),
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "quantity": 10,
        "start_time_cron_str": "0 4 * * *",
        "end_time_cron_str": "0 4 * * *",
        "allowed_operations": "usage,reservation",
        "forbidden_operations": ""
    }
    create_response = client.post("/api/v1/rule/item/", json=rule_item_data)
    rule_item_id = create_response.json()["id"]

    # 更新规则项
    update_data = {
        "name": generate_random_string(),
        "start_time": arrow.now('Asia/Shanghai').shift(days=1).format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=31).format("YYYY-MM-DD HH:mm:ss"),
        "quantity": 23,
        "start_time_cron_str": "0 3 * * *",
        "end_time_cron_str": "0 3 * * *",
        "allowed_operations": "usage,order",
        "forbidden_operations": "reservation"
    }
    response = client.put(f"/api/v1/rule/item/{rule_item_id}", json=update_data)
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == update_data["name"]
    assert content["start_time"] == update_data["start_time"]
    assert content["end_time"] == update_data["end_time"]
    assert content["quantity"] == update_data["quantity"]
    assert content["start_time_cron_str"] == update_data["start_time_cron_str"]
    assert content["end_time_cron_str"] == update_data["end_time_cron_str"]
    assert content["allowed_operations"] == update_data["allowed_operations"]
    assert content["forbidden_operations"] == update_data["forbidden_operations"]
    assert "id" in content
    assert "rule_id" in content


def test_delete_rule_item(client):
    """测试删除规则项"""
    # 创建规则
    rule_data = {
        "name": generate_random_string(),
        "status": Status.ACTIVE.value
    }
    rule_response = client.post("/api/v1/rule/", json=rule_data)
    rule_id = rule_response.json()["id"]

    # 创建规则项
    rule_item_data = {
        "rule_id": rule_id,
        "name": generate_random_string(),
        "start_time": arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
        "end_time": arrow.now('Asia/Shanghai').shift(days=30).format("YYYY-MM-DD HH:mm:ss"),
        "quantity": 10,
        "start_time_cron_str": "0 3 * * *",
        "end_time_cron_str": "0 3 * * *",
        "allowed_operations": "usage,reservation",
        "forbidden_operations": ""
    }
    create_response = client.post("/api/v1/rule/item/", json=rule_item_data)
    rule_item_id = create_response.json()["id"]

    # 删除规则项
    response = client.delete(f"/api/v1/rule/item/{rule_item_id}")
    assert response.status_code == 204

    # 确认已删除
    get_response = client.get(f"/api/v1/rule/item/{rule_item_id}")
    assert get_response.status_code == 404
