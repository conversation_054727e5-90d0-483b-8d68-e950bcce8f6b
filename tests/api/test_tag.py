# 标签测试用例
from tests.utils import generate_random_string


def test_create_tag(client):
    """测试创建标签"""
    data = {
        "name": f"测试标签_{generate_random_string()}"
    }
    response = client.post("/api/v1/tag/", json=data)
    assert response.status_code == 201
    content = response.json()
    assert content["name"] == data["name"]
    assert "id" in content


def test_read_tag(client):
    """测试获取标签详情"""
    # 先创建一个标签
    data = {
        "name": f"测试标签_{generate_random_string()}"
    }
    create_response = client.post("/api/v1/tag/", json=data)
    tag_id = create_response.json()["id"]

    # 获取该标签
    response = client.get(f"/api/v1/tag/{tag_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == data["name"]
    assert "id" in content


def test_read_tag_by_name(client):
    """测试通过名称获取标签"""
    # 先创建一个标签
    tag_name = f"测试标签_{generate_random_string()}"
    data = {
        "name": tag_name
    }
    client.post("/api/v1/tag/", json=data)

    # 通过名称获取该标签
    response = client.get(f"/api/v1/tag/by-name/{tag_name}")
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == tag_name
    assert "id" in content


def test_read_tags(client):
    """测试获取标签列表"""
    # 先创建几个标签
    for i in range(3):
        data = {
            "name": f"测试标签_{generate_random_string()}_{i}"
        }
        client.post("/api/v1/tag/", json=data)

    # 获取标签列表
    response = client.get("/api/v1/tag/")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)
    assert len(content) >= 3


def test_update_tag(client):
    """测试更新标签"""
    # 先创建一个标签
    data = {
        "name": f"测试标签_{generate_random_string()}"
    }
    create_response = client.post("/api/v1/tag/", json=data)
    tag_id = create_response.json()["id"]

    # 更新标签信息
    update_data = {
        "name": f"更新标签_{generate_random_string()}"
    }
    response = client.put(f"/api/v1/tag/{tag_id}", json=update_data)
    assert response.status_code == 200
    content = response.json()
    assert content["name"] == update_data["name"]
    assert content["id"] == tag_id


def test_delete_tag(client):
    """测试删除标签"""
    # 先创建一个标签
    data = {
        "name": f"测试标签_{generate_random_string()}"
    }
    create_response = client.post("/api/v1/tag/", json=data)
    tag_id = create_response.json()["id"]

    # 删除该标签
    response = client.delete(f"/api/v1/tag/{tag_id}")
    assert response.status_code == 204

    # 确认已删除
    get_response = client.get(f"/api/v1/tag/{tag_id}")
    assert get_response.status_code == 404


def test_get_product_tags(client):
    """测试获取产品相关的标签列表"""
    # 先创建一个标签
    tag_data = {
        "name": f"测试标签_{generate_random_string()}"
    }
    tag_response = client.post("/api/v1/tag/", json=tag_data)
    tag_id = tag_response.json()["id"]

    # 创建一个产品
    product_data = {
        "name": f"测试产品_{generate_random_string()}",
        "price": 99.99,
        "description": "这是一个测试产品",
        "stock": 100,
        "status": 1,
        "type": "product"
    }
    product_response = client.post("/api/v1/product/", json=product_data)
    product_id = product_response.json()["id"]

    # 将标签绑定到产品
    binding_data = {
        "product_ids": [product_id]
    }
    bind_response = client.post(f"/api/v1/tag/{tag_id}/bind/products", json=binding_data)
    assert bind_response.status_code == 200

    response = client.get(f"/api/v1/tag/products/{product_id}/")
    assert response.status_code == 200  # 可能没有关联的标签，返回空列表


def test_bind_multiple_products_to_tag(client):
    """测试将标签同时绑定到多个产品"""
    # 先创建一个标签
    tag_data = {
        "name": f"测试批量绑定标签_{generate_random_string()}"
    }
    tag_response = client.post("/api/v1/tag/", json=tag_data)
    tag_id = tag_response.json()["id"]

    # 创建多个产品
    product_ids = []
    for i in range(3):
        product_data = {
            "name": f"测试产品_{generate_random_string()}_{i}",
            "price": 99.99 + i,
            "description": f"这是测试产品{i}",
            "stock": 100 + i,
            "status": 1,
            "type": "product"
        }
        product_response = client.post("/api/v1/product/", json=product_data)
        product_ids.append(product_response.json()["id"])

    # 批量绑定产品
    binding_data = {
        "product_ids": product_ids
    }
    bind_response = client.post(f"/api/v1/tag/{tag_id}/bind/products", json=binding_data)
    assert bind_response.status_code == 200

    # 检查每个产品是否都绑定了该标签
    for product_id in product_ids:
        product_tags_response = client.get(f"/api/v1/tag/products/{product_id}/")
        assert product_tags_response.status_code == 200
        tags = product_tags_response.json()

        # 检查标签是否在列表中
        found = False
        for tag in tags:
            if tag["id"] == tag_id:
                found = True
                break
        assert found, f"产品 {product_id} 未绑定标签"


def test_unbind_products_from_tag(client):
    """测试将标签同时从多个产品解绑"""
    # 先创建一个标签
    tag_data = {
        "name": f"测试批量解绑标签_{generate_random_string()}"
    }
    tag_response = client.post("/api/v1/tag/", json=tag_data)
    tag_id = tag_response.json()["id"]

    # 创建多个产品并绑定
    product_ids = []
    for i in range(3):
        product_data = {
            "name": f"测试产品_{generate_random_string()}_{i}",
            "price": 99.99 + i,
            "description": f"这是测试产品{i}",
            "stock": 100 + i,
            "status": 1,
            "type": "product"
        }
        product_response = client.post("/api/v1/product/", json=product_data)
        product_id = product_response.json()["id"]
        product_ids.append(product_id)

        # 绑定标签到每个产品
        binding_data = {
            "product_id": product_id
        }
        client.post(f"/api/v1/tag/{tag_id}/bind", json=binding_data)

    # 批量解绑产品
    unbinding_data = {
        "product_ids": product_ids
    }
    unbind_response = client.post(f"/api/v1/tag/{tag_id}/unbind/products", json=unbinding_data)
    assert unbind_response.status_code == 200

    # 检查每个产品是否都已解绑该标签
    for product_id in product_ids:
        product_tags_response = client.get(f"/api/v1/tag/products/{product_id}/")
        if product_tags_response.status_code == 200:  # 可能返回空列表
            tags = product_tags_response.json()
            for tag in tags:
                assert tag["id"] != tag_id, f"产品 {product_id} 仍然绑定着标签"


def test_bind_with_empty_product_ids(client):
    """测试批量绑定时提供空的产品ID列表"""
    # 先创建一个标签
    tag_data = {
        "name": f"测试空绑定标签_{generate_random_string()}"
    }
    tag_response = client.post("/api/v1/tag/", json=tag_data)
    tag_id = tag_response.json()["id"]

    # 使用空的产品ID列表尝试绑定
    binding_data = {
        "product_ids": []
    }
    bind_response = client.post(f"/api/v1/tag/{tag_id}/bind/products", json=binding_data)
    assert bind_response.status_code == 400  # 应该返回错误
