import pprint

from app.models.enum import Status
from app.models.user import UserType
from app.schemas.user import PersonalUserCreate
from tests.utils import generate_random_string


# 用户测试用例
def test_create_user(client):
    """测试创建基础用户"""
    data = {
        "username": f"test_user_{generate_random_string()}",
    }
    response = client.post("/api/v1/user/", json=data)
    assert response.status_code == 201
    content = response.json()
    assert content["username"] == data["username"]
    assert content["status"] == Status.ACTIVE.value
    assert "id" in content
    assert "register_time" in content
    assert "created_at" in content
    assert "updated_at" in content
    assert "password" not in content
    assert "type" not in content


def test_read_user(client):
    """测试获取用户"""
    # 先创建一个用户
    data = {
        "username": f"test_user_{generate_random_string()}",
        "password": f"{generate_random_string()}"
    }
    create_response = client.post("/api/v1/user/", json=data)
    user_id = create_response.json()["id"]

    # 获取该用户
    response = client.get(f"/api/v1/user/{user_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["username"] == data["username"]
    assert content["status"] == Status.ACTIVE.value
    assert "id" in content
    assert "register_time" in content
    assert "created_at" in content
    assert "updated_at" in content
    assert "password" not in content
    assert "type" not in content


def test_read_users(client):
    """测试获取用户列表"""
    response = client.get("/api/v1/user/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)


def test_update_user(client):
    """测试更新用户"""
    # 先创建一个用户
    data = {
        "username": f"test_user_{generate_random_string()}",
    }
    create_response = client.post("/api/v1/user/", json=data)
    user_id = create_response.json()["id"]

    # 更新用户信息
    new_password = f"{generate_random_string()}"
    update_data = {
        "status": Status.INACTIVE.value
    }
    response = client.put(f"/api/v1/user/{user_id}", json=update_data)
    assert response.status_code == 200
    content = response.json()
    assert content["status"] == Status.INACTIVE.value
    assert "id" in content
    assert "register_time" in content
    assert "created_at" in content
    assert "updated_at" in content
    assert "password" not in content
    assert "type" not in content


def test_delete_user(client):
    """测试删除用户"""
    # 先创建一个用户
    data = {
        "username": f"test_user_{generate_random_string()}"
    }
    create_response = client.post("/api/v1/user/", json=data)
    user_id = create_response.json()["id"]

    # 删除该用户
    response = client.delete(f"/api/v1/user/{user_id}")
    assert response.status_code == 204


# 个人用户测试用例
# 使用DAO创建或更新个人用户时，username会强制设为手机号
# 因此，在测试时，需要使用手机号作为username
def test_create_personal_user(client):
    """测试创建个人用户"""
    data = {
        "username": f"test_personal_{generate_random_string()}",
        "password": f"{generate_random_string()}",
        "real_name": "测试用户",
        "id_card": f"{generate_random_string(18)}",
        "phone": f"1{generate_random_string(10)}",
        "email": f"{generate_random_string()}@example.com",
        "address": f"{generate_random_string()}"
    }
    response = client.post("/api/v1/user/personal/", json=data)
    assert response.status_code == 201
    content = response.json()
    assert content["code"] == 200
    assert content["message"] == "创建个人用户成功"
    assert content["data"]["username"] == data["phone"]


# 使用DAO创建或更新个人用户时，username会强制设为手机号
# 因此，在测试时，需要使用手机号作为username
def test_read_personal_user(client):
    """测试获取个人用户"""
    # 先创建一个个人用户
    data = {
        "username": f"test_personal_{generate_random_string()}",
        "password": "test123456",
        "phone": f"1{generate_random_string(10)}",
        "email": f"{generate_random_string()}@example.com",
        "address": "测试地址",
        "real_name": "测试用户",
        "id_card": f"{generate_random_string(18)}"
    }
    create_response = client.post("/api/v1/user/personal/", json=data)
    user_id = create_response.json()["data"]["id"]

    # 获取用户详情
    response = client.get(f"/api/v1/user/personal/{user_id}")
    assert response.status_code == 200
    content = response.json()
    assert content["code"] == 200
    assert content["message"] == "获取个人用户详情成功"
    assert content["data"]["username"] == data["phone"]
    assert content["data"]["phone"] == data["phone"]
    assert content["data"]["email"] == data["email"]
    assert content["data"]["address"] == data["address"]
    assert content["data"]["real_name"] == data["real_name"]
    assert content["data"]["id_card"] == data["id_card"]


def test_read_personal_users(client):
    """测试获取个人用户列表"""
    response = client.get("/api/v1/user/personal/")
    assert response.status_code == 200
    content = response.json()
    assert content["code"] == 200
    assert content["message"] == "获取个人用户列表成功"
    assert isinstance(content["data"]["list"], list)
    assert isinstance(content["data"]["total"], int)


def test_update_personal_user(client):
    """测试更新个人用户"""
    # 先创建一个个人用户
    data = {
        "username": f"test_personal_{generate_random_string()}",
        "password": "test123456",
        "phone": f"1{generate_random_string(10)}",
        "email": f"{generate_random_string()}@example.com",
        "address": "测试地址",
        "real_name": "测试用户",
        "id_card": f"{generate_random_string(18)}"
    }
    create_response = client.post("/api/v1/user/personal/", json=data)
    user_id = create_response.json()["data"]["id"]

    # 更新用户信息
    update_data = {
        "real_name": "更新后的用户名",
        "email": "<EMAIL>"
    }
    update_response = client.put(f"/api/v1/user/personal/{user_id}", json=update_data)
    assert update_response.status_code == 200
    content = update_response.json()
    assert content["code"] == 200
    assert content["message"] == "更新个人用户成功"
    assert content["data"]["real_name"] == update_data["real_name"]
    assert content["data"]["email"] == update_data["email"]


# def test_delete_personal_user(client):
#     """测试删除个人用户"""
#     # 先创建一个个人用户
#     data = {
#         "username": f"test_personal_{generate_random_string()}",
#         "status": Status.ACTIVE,
#         "password": "test123456",
#         "type": UserType.PERSONAL.value,
#         "phone": f"1{generate_random_string(10)}",
#         "email": f"{generate_random_string()}@example.com",
#         "address": "测试地址",
#         "real_name": "测试用户",
#         "id_card": f"{generate_random_string(18)}"
#     }
#     create_response = client.post("/api/v1/user/personal/", json=data)
#     user_id = create_response.json()["data"]["id"]
#
#     # 删除用户
#     delete_response = client.delete(f"/api/v1/user/personal/{user_id}")
#     assert delete_response.status_code == 204
#
#     # 验证用户已被删除
#     get_response = client.get(f"/api/v1/user/personal/{user_id}")
#     assert get_response.status_code == 404


def test_get_personal_user_enterprises(client):
    """测试获取个人用户关联的企业列表"""
    # 先创建一个个人用户
    data = {
        "username": f"test_personal_{generate_random_string()}",
        "status": Status.ACTIVE,
        "password": "test123456",
        "type": UserType.PERSONAL.value,
        "wechat_id": f"wx_{generate_random_string()}",
        "phone": f"1{generate_random_string(10)}",
        "email": f"{generate_random_string()}@example.com",
        "address": "测试地址",
        "real_name": "测试用户",
        "id_card": f"{generate_random_string(18)}"
    }
    create_response = client.post("/api/v1/user/personal/", json=data)
    user_id = create_response.json()["data"]["id"]

    # 获取用户关联的企业列表
    response = client.get(f"/api/v1/user/personal/{user_id}/enterprises")
    assert response.status_code == 200
    content = response.json()
    assert isinstance(content, list)


# 企业用户测试用例
# 使用DAO创建或更新企业时，username会强制设为企业名称
# 因此，在测试时，需要使用企业名称作为username
def test_create_enterprise(client):
    """测试创建企业用户"""
    data = {
        "username": f"test_enterprise_{generate_random_string()}",
        "company_name": f"测试公司_{generate_random_string()}",
        "business_license": f"BL{generate_random_string(15)}",
        "phone": f"1{generate_random_string(10)}",
        "email": f"{generate_random_string()}@company.com",
        "address": "测试公司地址",
        "industry_type": "科技"
    }
    response = client.post("/api/v1/user/enterprise/", json=data)
    assert response.status_code == 201
    content = response.json()
    assert content["code"] == 200
    assert content["message"] == "创建企业用户成功"
    assert content["data"]["username"] == data["company_name"]
    assert content["data"]["company_name"] == data["company_name"]
    assert content["data"]["business_license"] == data["business_license"]
    assert content["data"]["phone"] == data["phone"]
    assert content["data"]["email"] == data["email"]
    assert content["data"]["address"] == data["address"]
    assert content["data"]["status"] == Status.ACTIVE
    assert "id" in content["data"]
    assert "register_time" in content["data"]
    assert "created_at" in content["data"]
    assert "updated_at" in content["data"]
    assert "password" not in content["data"]
    assert "type" not in content["data"]


# 使用DAO创建或更新企业时，username会强制设为企业名称
# 因此，在测试时，需要使用企业名称作为username
def test_read_enterprise(client):
    """测试获取企业用户"""
    # 先创建一个企业用户
    data = {
        "username": f"test_enterprise_{generate_random_string()}",
        "company_name": f"测试公司_{generate_random_string()}",
        "business_license": f"BL{generate_random_string(15)}",
        "phone": f"1{generate_random_string(10)}",
        "email": f"{generate_random_string()}@company.com",
        "address": "测试公司地址"
    }
    create_response = client.post("/api/v1/user/enterprise/", json=data)
    enterprise_id = create_response.json()["data"]["id"]

    # 获取该企业用户
    response = client.get(f"/api/v1/user/enterprise/{enterprise_id}")
    content = response.json()["data"]
    assert response.status_code == 200
    assert content["username"] == data["company_name"]
    assert content["company_name"] == data["company_name"]
    assert content["business_license"] == data["business_license"]
    assert content["phone"] == data["phone"]
    assert content["email"] == data["email"]
    assert content["address"] == data["address"]
    assert content["status"] == Status.ACTIVE
    assert "id" in content
    assert "register_time" in content
    assert "created_at" in content
    assert "updated_at" in content
    assert "password" not in content
    assert "type" not in content


def test_read_enterprises(client):
    """测试获取企业用户列表"""
    response = client.get("/api/v1/user/enterprise/")
    assert response.status_code == 200
    content = response.json()
    assert content["code"] == 200
    assert content["message"] == "获取企业用户列表成功"
    assert isinstance(content["data"]["list"], list)
    assert isinstance(content["data"]["total"], int)


def test_search_enterprises(client):
    """测试搜索企业用户"""
    # 创建测试企业
    enterprise1 = {
        "username": "test_company1",
        "company_name": "测试公司1",
        "phone": "***********",
        "status": Status.ACTIVE.value
    }
    enterprise2 = {
        "username": "test_company2",
        "company_name": "测试公司2",
        "phone": "***********",
        "status": Status.ACTIVE.value
    }
    enterprise3 = {
        "username": "test_company3",
        "company_name": "测试公司3",
        "phone": "***********",
        "status": Status.INACTIVE.value  # 注意状态为非活跃
    }

    # 创建企业
    response1 = client.post("/api/v1/user/enterprise/", json=enterprise1)
    response2 = client.post("/api/v1/user/enterprise/", json=enterprise2)
    response3 = client.post("/api/v1/user/enterprise/", json=enterprise3)

    assert response1.status_code == 201
    assert response2.status_code == 201
    assert response3.status_code == 201

    # 搜索包含"138"的企业 - 使用关键字搜索
    response = client.get("/api/v1/user/enterprise/search/?keyword=138")
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200
    assert "测试公司1" in [e["company_name"] for e in data["data"]["list"]]

    # 搜索包含"测试公司"的企业 - 使用关键字搜索
    response = client.get("/api/v1/user/enterprise/search/?keyword=测试公司")
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200
    assert len(data["data"]["list"]) >= 3

    # 使用name参数搜索
    response = client.get("/api/v1/user/enterprise/search/?name=测试公司1")
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200
    assert len(data["data"]["list"]) >= 1
    assert data["data"]["list"][0]["company_name"] == "测试公司1"

    # 使用phone参数搜索
    response = client.get("/api/v1/user/enterprise/search/?phone=***********")
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200
    assert len(data["data"]["list"]) >= 1
    assert data["data"]["list"][0]["phone"] == "***********"

    # 使用status参数搜索
    response = client.get(f"/api/v1/user/enterprise/search/?status={Status.INACTIVE.value}")
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200
    assert len(data["data"]["list"]) >= 1
    assert data["data"]["list"][0]["status"] == Status.INACTIVE.value

    # 组合多个参数搜索
    response = client.get(f"/api/v1/user/enterprise/search/?phone=13&status={Status.ACTIVE.value}")
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200
    assert len(data["data"]["list"]) >= 2
    phones = [enterprise["phone"] for enterprise in data["data"]["list"]]
    assert "***********" in phones
    assert "***********" in phones

    # 清理测试数据
    client.delete(f"/api/v1/user/enterprise/{response1.json()['data']['id']}")
    client.delete(f"/api/v1/user/enterprise/{response2.json()['data']['id']}")
    client.delete(f"/api/v1/user/enterprise/{response3.json()['data']['id']}")


def test_update_enterprise(client):
    """测试更新企业用户"""
    # 先创建一个企业用户
    data = {
        "username": f"test_enterprise_{generate_random_string()}",
        "company_name": f"测试公司_{generate_random_string()}",
        "business_license": f"BL{generate_random_string(15)}",
        "phone": f"1{generate_random_string(10)}",
        "email": f"{generate_random_string()}@company.com",
        "address": "测试公司地址",
        "industry_type": "科技"
    }
    create_response = client.post("/api/v1/user/enterprise/", json=data)
    enterprise_id = create_response.json()["data"]["id"]
    # 更新企业用户信息
    update_data = {
        "status": Status.INACTIVE,
        "company_name": f"更改的测试公司_{generate_random_string()}",
        "business_license": f"更改的BL{generate_random_string(15)}",
        "phone": f"更改的1{generate_random_string(10)}",
        "email": f"更改的{generate_random_string()}@company.com",
        "address": "更改的测试公司地址",
    }
    response = client.put(f"/api/v1/user/enterprise/{enterprise_id}", json=update_data)
    assert response.status_code == 200
    content = response.json()
    assert content["code"] == 200
    assert content["message"] == "更新企业用户成功"
    assert content["data"]["status"] == Status.INACTIVE
    assert content["data"]["company_name"] == update_data["company_name"]
    assert content["data"]["business_license"] == update_data["business_license"]
    assert content["data"]["phone"] == update_data["phone"]
    assert content["data"]["email"] == update_data["email"]
    assert content["data"]["address"] == update_data["address"]
    assert "id" in [k for k in content["data"].keys()]


# def test_delete_enterprise(client):
#     """测试删除企业用户"""
#     # 先创建一个企业用户
#     data = {
#         "username": f"test_enterprise_{generate_random_string()}",
#         "status": Status.ACTIVE,
#         "company_name": f"测试公司_{generate_random_string()}",
#         "phone": f"1{generate_random_string(10)}",
#         "email": f"{generate_random_string()}@company.com",
#         "address": "测试公司地址",
#         "business_license": f"BL{generate_random_string(15)}"
#     }
#     create_response = client.post("/api/v1/user/enterprise/", json=data)
#     enterprise_id = create_response.json()["data"]["id"]
#
#     # 删除该企业用户
#     response = client.delete(f"/api/v1/user/enterprise/{enterprise_id}")
#     assert response.status_code == 204


def test_batch_set_enterprise_status(client):
    """测试批量设置企业用户状态"""
    # 创建测试企业
    enterprise1 = {
        "username": f"test_batch_status1_{generate_random_string()}",
        "company_name": f"批量状态测试1_{generate_random_string()}",
        "phone": f"1{generate_random_string(10)}",
        "status": Status.ACTIVE.value
    }
    enterprise2 = {
        "username": f"test_batch_status2_{generate_random_string()}",
        "company_name": f"批量状态测试2_{generate_random_string()}",
        "phone": f"1{generate_random_string(10)}",
        "status": Status.ACTIVE.value
    }

    # 创建企业
    response1 = client.post("/api/v1/user/enterprise/", json=enterprise1)
    response2 = client.post("/api/v1/user/enterprise/", json=enterprise2)

    assert response1.status_code == 201
    assert response2.status_code == 201

    # 批量设置企业状态
    enterprise_ids = [
        response1.json()["data"]["id"],
        response2.json()["data"]["id"]
    ]

    response = client.post("/api/v1/user/enterprise/status/", json={
        "enterprise_ids": enterprise_ids,
        "status": Status.INACTIVE.value
    })
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200
    assert "成功更新" in data["message"]

    # 验证企业状态已更新
    for enterprise_id in enterprise_ids:
        response = client.get(f"/api/v1/user/enterprise/{enterprise_id}")
        assert response.status_code == 200
        data = response.json()["data"]
        assert data["status"] == Status.INACTIVE.value

    # 清理测试数据
    for enterprise_id in enterprise_ids:
        client.delete(f"/api/v1/user/enterprise/{enterprise_id}")


# 企业用户关联测试用例
def test_create_relation(client):
    """测试创建企业用户关联"""
    # 先创建一个企业用户
    enterprise_data = {
        "username": f"test_enterprise_{generate_random_string()}",
        "status": Status.ACTIVE,
        "company_name": f"测试公司_{generate_random_string()}",
        "phone": f"1{generate_random_string(10)}",
        "email": f"{generate_random_string()}@company.com",
        "address": "测试公司地址",
        "business_license": f"BL{generate_random_string(15)}",
        "industry_type": "科技"
    }
    enterprise_response = client.post("/api/v1/user/enterprise/", json=enterprise_data)
    enterprise_id = enterprise_response.json()["data"]["id"]

    # 创建一个个人用户
    user_data = {
        "username": f"test_personal_{generate_random_string()}",
        "status": Status.ACTIVE,
        "wechat_id": f"wx_{generate_random_string()}",
        "phone": f"1{generate_random_string(10)}",
        "email": f"{generate_random_string()}@example.com",
        "address": "测试地址",
        "real_name": "测试用户",
        "id_card": f"{generate_random_string(18)}"
    }
    personal_user_response = client.post("/api/v1/user/personal/", json=user_data)
    personal_user_id = personal_user_response.json()["data"]["id"]

    # 创建企业用户关联
    relation_data = {
        "enterprise_id": enterprise_id,
        "personal_user_id": personal_user_id,
        "password": "test123456",
        "is_admin": True,
        "relation_status": Status.ACTIVE,
        "remark": "测试关联"
    }
    response = client.post("/api/v1/user/relation/", json=relation_data)
    assert response.status_code == 201
    content = response.json()["data"]
    assert content["enterprise_id"] == relation_data["enterprise_id"]
    assert content["personal_user_id"] == relation_data["personal_user_id"]
    assert content["is_admin"] == relation_data["is_admin"]
    assert content["relation_status"] == relation_data["relation_status"]
    assert content["remark"] == relation_data["remark"]
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content
    assert "password" not in content


def test_read_relation(client):
    """测试获取企业用户关联"""
    # 先创建一个企业用户
    enterprise_data = {
        "username": f"test_enterprise_{generate_random_string()}",
        "status": Status.ACTIVE,
        "company_name": f"测试公司_{generate_random_string()}",
        "phone": f"1{generate_random_string(10)}",
        "email": f"{generate_random_string()}@company.com",
        "address": "测试公司地址",
        "business_license": f"BL{generate_random_string(15)}",
        "industry_type": "科技"
    }
    enterprise_response = client.post("/api/v1/user/enterprise/", json=enterprise_data)
    enterprise_id = enterprise_response.json()["data"]["id"]

    # 创建一个个人用户
    personal_user_data = {
        "username": f"test_personal_{generate_random_string()}",
        "status": Status.ACTIVE,
        "wechat_id": f"wx_{generate_random_string()}",
        "phone": f"1{generate_random_string(10)}",
        "email": f"{generate_random_string()}@example.com",
        "address": "测试地址",
        "real_name": "测试用户",
        "id_card": f"{generate_random_string(18)}"
    }
    personal_user_response = client.post("/api/v1/user/personal/", json=personal_user_data)
    personal_user_id = personal_user_response.json()["data"]["id"]

    # 创建企业用户关联
    relation_data = {
        "enterprise_id": enterprise_id,
        "personal_user_id": personal_user_id,
        "password": "test123456",
        "is_admin": True,
        "relation_status": Status.ACTIVE,
        "remark": "测试关联"
    }
    create_response = client.post("/api/v1/user/relation/", json=relation_data)
    relation_id = create_response.json()["data"]["id"]

    # 获取该企业用户关联
    response = client.get(f"/api/v1/user/relation/{relation_id}")
    assert response.status_code == 200
    content = response.json()["data"]
    assert content["enterprise_id"] == relation_data["enterprise_id"]
    assert content["personal_user_id"] == relation_data["personal_user_id"]
    assert content["is_admin"] == relation_data["is_admin"]
    assert content["relation_status"] == relation_data["relation_status"]
    assert content["remark"] == relation_data["remark"]
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content
    assert "password" not in content


def test_update_relation(client):
    """测试更新企业用户关联"""
    # 先创建一个企业用户
    enterprise_data = {
        "username": f"test_enterprise_{generate_random_string()}",
        "status": Status.ACTIVE,
        "password": "test123456",
        "type": UserType.ENTERPRISE.value,
        "company_name": f"测试公司_{generate_random_string()}",
        "phone": f"1{generate_random_string(10)}",
        "email": f"{generate_random_string()}@company.com",
        "address": "测试公司地址",
        "business_license": f"BL{generate_random_string(15)}",
        "industry_type": "科技"
    }
    enterprise_response = client.post("/api/v1/user/enterprise/", json=enterprise_data)
    enterprise_id = enterprise_response.json()["data"]["id"]

    # 创建一个个人用户
    personal_user_data = {
        "username": f"test_personal_{generate_random_string()}",
        "status": Status.ACTIVE,
        "password": "test123456",
        "type": UserType.PERSONAL.value,
        "wechat_id": f"wx_{generate_random_string()}",
        "phone": f"1{generate_random_string(10)}",
        "email": f"{generate_random_string()}@example.com",
        "address": "测试地址",
        "real_name": "测试用户",
        "id_card": f"{generate_random_string(18)}"
    }
    personal_user_response = client.post("/api/v1/user/personal/", json=personal_user_data)
    personal_user_id = personal_user_response.json()["data"]["id"]

    # 创建企业用户关联
    relation_data = {
        "enterprise_id": enterprise_id,
        "personal_user_id": personal_user_id,
        "password": "test123456",
        "is_admin": True,
        "relation_status": Status.ACTIVE,
        "remark": "测试关联"
    }
    create_response = client.post("/api/v1/user/relation/", json=relation_data)
    relation_id = create_response.json()["data"]["id"]

    # 更新企业用户关联
    update_data = {
        "password": "test12345633343",
        "is_admin": False,
        "relation_status": Status.INACTIVE,
        "remark": "更新后的关联"
    }
    response = client.put(f"/api/v1/user/relation/{relation_id}", json=update_data)
    assert response.status_code == 200
    content = response.json()["data"]
    assert content["is_admin"] == update_data["is_admin"]
    assert content["relation_status"] == update_data["relation_status"]
    assert content["remark"] == update_data["remark"]
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content
    assert "password" not in content


def test_delete_relation(client):
    """测试删除企业用户关联"""
    # 先创建一个企业用户
    enterprise_data = {
        "username": f"test_enterprise_{generate_random_string()}",
        "status": Status.ACTIVE,
        "company_name": f"测试公司_{generate_random_string()}",
        "phone": f"1{generate_random_string(10)}",
        "email": f"{generate_random_string()}@company.com",
        "address": "测试公司地址",
        "business_license": f"BL{generate_random_string(15)}",
        "industry_type": "科技"
    }
    enterprise_response = client.post("/api/v1/user/enterprise/", json=enterprise_data)
    enterprise_id = enterprise_response.json()["data"]["id"]

    # 创建一个个人用户
    personal_user_data = {
        "username": f"test_personal_{generate_random_string()}",
        "status": Status.ACTIVE,
        "phone": f"1{generate_random_string(10)}",
        "email": f"{generate_random_string()}@example.com",
        "address": "测试地址",
        "real_name": "测试用户",
        "id_card": f"{generate_random_string(18)}"
    }
    personal_user_response = client.post("/api/v1/user/personal/", json=personal_user_data)
    personal_user_id = personal_user_response.json()["data"]["id"]

    # 创建企业用户关联
    relation_data = {
        "enterprise_id": enterprise_id,
        "personal_user_id": personal_user_id,
        "password": "test123456",
        "is_admin": True,
        "relation_status": Status.ACTIVE,
        "remark": "测试关联"
    }
    create_response = client.post("/api/v1/user/relation/", json=relation_data)
    relation_id = create_response.json()["data"]["id"]

    # 删除该企业用户关联
    response = client.delete(f"/api/v1/user/relation/{relation_id}")
    assert response.status_code == 204


def test_get_relation_by_enterprise_and_user(client):
    """测试通过企业ID和用户ID获取关联"""
    # 先创建一个企业用户
    enterprise_data = {
        "username": f"test_enterprise_{generate_random_string()}",
        "status": Status.ACTIVE,
        "company_name": f"测试公司_{generate_random_string()}",
        "phone": f"1{generate_random_string(10)}",
        "email": f"{generate_random_string()}@company.com",
        "address": "测试公司地址",
        "business_license": f"BL{generate_random_string(15)}",
        "industry_type": "科技"
    }
    enterprise_response = client.post("/api/v1/user/enterprise/", json=enterprise_data)
    enterprise_id = enterprise_response.json()["data"]["id"]

    # 创建一个个人用户
    personal_user_data = {
        "username": f"test_personal_{generate_random_string()}",
        "status": Status.ACTIVE,
        "wechat_id": f"wx_{generate_random_string()}",
        "phone": f"1{generate_random_string(10)}",
        "email": f"{generate_random_string()}@example.com",
        "address": "测试地址",
        "real_name": "测试用户",
        "id_card": f"{generate_random_string(18)}"
    }
    personal_user_response = client.post("/api/v1/user/personal/", json=personal_user_data)
    personal_user_id = personal_user_response.json()["data"]["id"]

    # 创建企业用户关联
    relation_data = {
        "enterprise_id": enterprise_id,
        "personal_user_id": personal_user_id,
        "password": "test123456",
        "is_admin": True,
        "relation_status": Status.ACTIVE,
        "remark": "测试关联"
    }
    client.post("/api/v1/user/relation/", json=relation_data)

    # 通过企业ID和用户ID获取关联
    response = client.get(f"/api/v1/user/enterprise/{enterprise_id}/personal/{personal_user_id}")
    assert response.status_code == 200
    content = response.json()["data"]
    assert content["enterprise_id"] == relation_data["enterprise_id"]
    assert content["personal_user_id"] == relation_data["personal_user_id"]
    assert content["is_admin"] == relation_data["is_admin"]
    assert content["relation_status"] == relation_data["relation_status"]
    assert content["remark"] == relation_data["remark"]
    assert "id" in content
    assert "created_at" in content
    assert "updated_at" in content
    assert "password" not in content


# 带账户创建接口测试用例
# 使用DAO创建或更新个人用户时，username会强制设为手机号
# 因此，在测试时，需要使用手机号作为username
def test_create_personal_user_with_accounts(client):
    """测试创建带账户的个人用户"""
    data = {
        "username": f"test_personal_with_accounts_{generate_random_string()}",
        "password": f"{generate_random_string()}",
        "real_name": "测试用户带账户",
        "id_card": f"{generate_random_string(18)}",
        "phone": f"1{generate_random_string(10)}",
        "email": f"{generate_random_string()}@example.com",
        "address": f"{generate_random_string()}"
    }
    response = client.post("/api/v1/user/personal/with-accounts/", json=data)
    # 打印错误详情
    assert response.status_code == 201
    content = response.json()

    # 验证用户信息
    assert "user" in content
    user_data = content["user"]
    assert user_data["username"] == data["phone"]
    assert user_data["phone"] == data["phone"]
    assert user_data["email"] == data["email"]
    assert user_data["address"] == data["address"]
    assert user_data["real_name"] == data["real_name"]
    assert user_data["id_card"] == data["id_card"]
    assert "id" in user_data

    # 验证普通账户信息
    assert "regular_account" in content
    regular_account = content["regular_account"]
    assert regular_account["user_id"] == user_data["id"]
    assert regular_account["balance"] == 0.0
    assert regular_account["status"] == Status.ACTIVE.value
    assert "id" in regular_account

    # 验证赠送账户信息
    assert "gift_account" in content
    gift_account = content["gift_account"]
    assert gift_account["user_id"] == user_data["id"]
    assert gift_account["balance"] == 0.0
    assert gift_account["gift_amount"] == 0.0
    assert gift_account["status"] == Status.ACTIVE.value
    assert "id" in gift_account


# 使用DAO创建或更新企业时，username会强制设为企业名称
# 因此，在测试时，需要使用企业名称作为username
def test_create_enterprise_with_accounts(client):
    """测试创建带账户的企业用户"""
    data = {
        "username": f"test_enterprise_with_accounts_{generate_random_string()}",
        "company_name": f"测试企业_{generate_random_string()}",
        "business_license": f"{generate_random_string(18)}",
        "phone": f"1{generate_random_string(10)}",
        "email": f"{generate_random_string()}@example.com",
        "address": f"{generate_random_string()}"
    }
    response = client.post("/api/v1/user/enterprise/with-accounts/", json=data)
    # 打印错误详情
    assert response.status_code == 201
    content = response.json()

    # 验证企业信息
    assert "enterprise" in content
    enterprise_data = content["enterprise"]
    assert enterprise_data["username"] == data["company_name"]
    assert enterprise_data["company_name"] == data["company_name"]
    assert enterprise_data["business_license"] == data["business_license"]
    assert enterprise_data["phone"] == data["phone"]
    assert enterprise_data["email"] == data["email"]
    assert enterprise_data["address"] == data["address"]
    assert "id" in enterprise_data

    # 验证普通账户信息
    assert "regular_account" in content
    regular_account = content["regular_account"]
    assert regular_account["user_id"] == enterprise_data["id"]
    assert regular_account["balance"] == 0.0
    assert regular_account["status"] == Status.ACTIVE.value
    assert "id" in regular_account

    # 验证赠送账户信息
    assert "gift_account" in content
    gift_account = content["gift_account"]
    assert gift_account["user_id"] == enterprise_data["id"]
    assert gift_account["balance"] == 0.0
    assert gift_account["gift_amount"] == 0.0
    assert gift_account["status"] == Status.ACTIVE.value
    assert "id" in gift_account


# 添加搜索个人用户测试用例
# 使用DAO创建或更新个人用户时，username会强制设为手机号
# 因此，在测试时，需要使用手机号作为username
def test_search_personal_users(client):
    """测试搜索个人用户"""
    # 创建测试用户
    user1 = PersonalUserCreate(
        username="zhangsan",
        phone="***********",
        real_name="张三",
        status=Status.ACTIVE.value
    )
    user2 = PersonalUserCreate(
        username="lisi",
        phone="***********",
        real_name="李四",
        status=Status.ACTIVE.value
    )
    user3 = PersonalUserCreate(
        username="wangwu",
        phone="***********",
        real_name="王五",
        status=Status.INACTIVE.value  # 注意状态为非活跃
    )

    # 创建用户
    response1 = client.post("/api/v1/user/personal/", json=user1.model_dump())
    response2 = client.post("/api/v1/user/personal/", json=user2.model_dump())
    response3 = client.post("/api/v1/user/personal/", json=user3.model_dump())

    assert response1.status_code == 201
    assert response2.status_code == 201
    assert response3.status_code == 201

    # 搜索包含"138"的用户 - 使用关键字搜索
    response = client.get("/api/v1/user/personal/search/?keyword=138")
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200
    assert len(data["data"]["list"]) == 1
    assert data["data"]["list"][0]["phone"] == "***********"

    # 搜索包含"张"的用户 - 使用关键字搜索
    response = client.get("/api/v1/user/personal/search/?keyword=张")
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200
    assert len(data["data"]["list"]) == 1
    assert data["data"]["list"][0]["real_name"] == "张三"

    # 使用name参数搜索
    response = client.get("/api/v1/user/personal/search/?name=张")
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200
    assert len(data["data"]["list"]) == 1
    assert data["data"]["list"][0]["real_name"] == "张三"

    # 使用phone参数搜索
    response = client.get("/api/v1/user/personal/search/?phone=***********")
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200
    assert len(data["data"]["list"]) == 1
    assert data["data"]["list"][0]["phone"] == "***********"

    # 使用status参数搜索
    response = client.get(f"/api/v1/user/personal/search/?status={Status.INACTIVE.value}")
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200
    assert len(data["data"]["list"]) == 1
    assert data["data"]["list"][0]["username"] == "***********"

    # 组合多个参数搜索
    response = client.get(f"/api/v1/user/personal/search/?phone=13&status={Status.ACTIVE.value}")
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200
    assert len(data["data"]["list"]) == 2
    phones = [user["phone"] for user in data["data"]["list"]]
    assert "***********" in phones
    assert "***********" in phones

    # 清理测试数据
    client.delete(f"/api/v1/user/personal/{response1.json()['data']['id']}")
    client.delete(f"/api/v1/user/personal/{response2.json()['data']['id']}")
    client.delete(f"/api/v1/user/personal/{response3.json()['data']['id']}")


# 添加批量设置个人用户状态测试用例
def test_batch_set_personal_user_status(client):
    """测试批量设置个人用户状态"""
    # 创建测试用户
    user1 = PersonalUserCreate(
        username="zhangsan",
        phone="***********",
        real_name="张三",
        status=Status.ACTIVE.value
    )
    user2 = PersonalUserCreate(
        username="lisi",
        phone="***********",
        real_name="李四",
        status=Status.ACTIVE.value
    )
    user3 = PersonalUserCreate(
        username="wangwu",
        phone="***********",
        real_name="王五",
        status=Status.ACTIVE.value
    )

    # 创建用户
    response1 = client.post("/api/v1/user/personal/", json=user1.model_dump())
    response2 = client.post("/api/v1/user/personal/", json=user2.model_dump())
    response3 = client.post("/api/v1/user/personal/", json=user3.model_dump())

    assert response1.status_code == 201
    assert response2.status_code == 201
    assert response3.status_code == 201

    # 获取用户ID
    user_ids = [
        response1.json()["data"]["id"],
        response2.json()["data"]["id"],
        response3.json()["data"]["id"]
    ]

    # 批量设置用户状态为 inactive
    response = client.post("/api/v1/user/personal/status/", json={
        "user_ids": user_ids,
        "status": Status.INACTIVE.value
    })
    assert response.status_code == 200
    data = response.json()
    assert data["code"] == 200
    assert data["message"] == f"成功更新{len(user_ids)}个用户的状态"

    # 验证用户状态已更新
    for user_id in user_ids:
        response = client.get(f"/api/v1/user/personal/{user_id}")
        assert response.status_code == 200
        data = response.json()
        assert data["data"]["status"] == Status.INACTIVE.value

    # 清理测试数据
    for user_id in user_ids:
        client.delete(f"/api/v1/user/personal/{user_id}")
