import random
import string
from pathlib import Path

from fastapi.testclient import TestClient

from app.main import app
from app.schemas.callback import AudioEnum
from app.service.feieyun import feieyun_service

# 测试客户端
client = TestClient(app)

# 公钥文件路径
PUBLIC_KEY_PATH = Path(__file__).parent / "keys" / "public_scan.key"


def generate_random_sn(length=6):
    """生成随机序列号"""
    return ''.join(random.choices(string.digits, k=length))


def test_feieyun_callback_success():
    """测试飞鹅云回调成功场景"""
    # 1. 生成测试数据
    test_content = "https://www.feieyun.com/"
    random_sn = generate_random_sn()

    # 2. 使用公钥加密内容
    result = feieyun_service.callback_encrypt(test_content, str(PUBLIC_KEY_PATH))

    # 3. 构建请求数据
    payload = {
        "result": result,
        "sn": random_sn
    }

    # 4. 发送POST请求到回调端点
    response = client.post("/callback/feieyun", json=payload)

    # 5. 验证响应
    assert response.status_code == 200
    data = response.json()
    assert data["res"] == "SUCCESS"
    assert data["audio"] == AudioEnum.WRITE_OFF_SUCCESS


def test_feieyun_callback_invalid_result():
    """测试飞鹅云回调解密失败场景"""
    # 1. 生成测试数据
    random_sn = generate_random_sn()
    invalid_result = "invalid_encrypted_data"  # 无效的加密数据

    # 2. 构建请求数据
    payload = {
        "result": invalid_result,
        "sn": random_sn
    }

    # 3. 发送POST请求到回调端点
    response = client.post("/callback/feieyun", json=payload)

    # 4. 验证响应
    assert response.status_code == 200
    data = response.json()
    assert data["res"] == "FAILURE"
    assert data["audio"] == AudioEnum.WRITE_OFF_FAILURE


def test_feieyun_callback_missing_parameters():
    """测试飞鹅云回调缺少参数场景"""
    # 1. 构建缺少参数的请求数据
    payload = {
        "sn": generate_random_sn()
        # 缺少 result 参数
    }

    # 2. 发送POST请求到回调端点
    response = client.post("/callback/feieyun", json=payload)

    # 3. 验证响应
    assert response.status_code == 422  # Unprocessable Entity
