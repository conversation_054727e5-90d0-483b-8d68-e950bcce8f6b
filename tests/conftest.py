import sys
from pathlib import Path

import arrow
import pytest
from alembic import command
from alembic.config import Config
from fastapi.testclient import TestClient
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

from app.core.deps import get_db
from app.main import app
from app.models import PersonalUser
from app.models.enum import Status
from app.models.pricing import PricingStrategyScope
from app.models.rule import RuleItem, RuleType
from app.schemas.pricing import DiscountStrategyCreate, FullReductionStrategyCreate, TimeLimitedStrategyCreate
from app.schemas.user import PersonalUserCreate, EnterpriseCreate
from app.service import user_service
from app.utils.common import get_current_time
from tests.utils import generate_random_string

# 获取项目根目录
root_dir = Path(__file__).parent.parent
sys.path.insert(0, str(root_dir))

# 测试数据库配置
TEST_DATABASE_URL = "mysql+pymysql://root:123456@127.0.0.1"
TEST_DATABASE_NAME = "yh_vegan_dev_test"
TEST_DATABASE_FULL_URL = f"{TEST_DATABASE_URL}/{TEST_DATABASE_NAME}?charset=utf8mb4"


@pytest.fixture(scope="session")
def test_engine():
    """创建测试数据库引擎"""
    engine = create_engine(TEST_DATABASE_URL)
    yield engine
    engine.dispose()


@pytest.fixture(scope="session")
def test_session(test_engine):
    """创建测试数据库会话"""
    Session = sessionmaker(bind=test_engine)
    session = Session()
    yield session
    session.close()


@pytest.fixture(scope="session", autouse=True)
def setup_test_database(test_engine):
    """设置测试数据库，运行迁移并清理"""
    # 创建测试数据库
    with test_engine.connect() as conn:
        sql_commands = [
            "DROP DATABASE IF EXISTS {}".format(TEST_DATABASE_NAME),
            "CREATE DATABASE {} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci".format(TEST_DATABASE_NAME)
        ]
        for sql in sql_commands:
            conn.execute(text(sql))

    # 使用包含数据库名称的完整URL创建新的连接
    full_engine = create_engine(TEST_DATABASE_FULL_URL)

    # 运行迁移
    alembic_cfg = Config("alembic.ini")
    alembic_cfg.set_main_option("sqlalchemy.url", TEST_DATABASE_FULL_URL)
    command.upgrade(alembic_cfg, "head")

    yield

    # 清理测试数据库
    # with test_engine.connect() as conn:
    #     sql_commands = [
    #         "DROP DATABASE IF EXISTS {}".format(TEST_DATABASE_NAME)
    #     ]
    #     for sql in sql_commands:
    #         conn.execute(text(sql))


# 创建测试数据库会话工厂
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=create_engine(TEST_DATABASE_FULL_URL))


# 重写依赖项，使用测试数据库
@pytest.fixture
def override_get_db():
    """覆盖数据库依赖，使用测试数据库"""
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()


# 单元测试用的数据库会话
@pytest.fixture
def db(override_get_db):
    """提供测试数据库会话给测试用例"""
    return override_get_db


@pytest.fixture(scope="function")
def client(override_get_db):
    """创建测试客户端"""

    # 设置应用使用测试数据库会话
    def override_get_db_for_app():
        db = TestingSessionLocal()
        try:
            yield db
        finally:
            db.close()

    app.dependency_overrides[get_db] = override_get_db_for_app

    with TestClient(app) as test_client:
        yield test_client

    # 测试完成后，清除依赖项覆盖
    app.dependency_overrides.clear()


@pytest.fixture
def disable_foreign_keys():
    """使用monkeypatch禁用外键约束，用于测试"""
    # 创建数据库连接
    engine = create_engine(TEST_DATABASE_FULL_URL)

    # 禁用外键约束
    with engine.connect() as conn:
        conn.execute(text("SET FOREIGN_KEY_CHECKS = 0"))

    yield

    # 恢复外键约束
    with engine.connect() as conn:
        conn.execute(text("SET FOREIGN_KEY_CHECKS = 1"))


@pytest.fixture
def create_test_user(db):
    """创建测试用户"""
    from app.models.user import User
    from app.models.enum import Status

    # 检查用户是否已存在，如果不存在则创建
    personal_user = db.query(PersonalUser).filter(User.id == 1).first()
    if not personal_user:
        # 创建带帐户的个人用户
        personal_user_data = PersonalUserCreate(
            id=1,
            username="testuser",
            phone="***********",
            status=Status.ACTIVE,
            created_at=get_current_time(),
            updated_at=get_current_time()
        )
        personal_user, accounts = user_service.create_personal_user(db, personal_user_data)

        # 给帐户充值
        gift_account = accounts.get("gift_account", None)
        regular_account = accounts.get("regular_account", None)
        if gift_account:
            gift_account.balance = 50
            db.commit()
        if regular_account:
            regular_account.balance = 100
            db.commit()
    return personal_user


@pytest.fixture
def create_test_product(db):
    from app.models.product import DirectSaleProduct
    # 检查产品是否已存在，如果不存在则创建
    product = db.query(DirectSaleProduct).filter(DirectSaleProduct.name == "Test Product").first()
    if not product:
        product = DirectSaleProduct(
            name=f"直销产品_{generate_random_string()}",
            price=99.99,
            description="这是一个直销产品",
            stock=100,
            status=Status.ACTIVE.value,
            shipping_fee=10,
            listed_at=arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss")  # 可以根据实际模型添加更多属性
        )
        db.add(product)
        db.commit()
        db.refresh(product)

    return product


@pytest.fixture(autouse=True)
def clear_tables():
    """每次测试前清空所有表数据"""
    engine = create_engine(TEST_DATABASE_FULL_URL)
    with engine.connect() as conn:
        # 暂时禁用外键约束
        conn.execute(text("SET FOREIGN_KEY_CHECKS = 0"))

        # 获取所有表名
        result = conn.execute(text("SHOW TABLES"))
        tables = [row[0] for row in result.fetchall()]

        # 清空所有表数据
        for table in tables:
            conn.execute(text(f"TRUNCATE TABLE {table}"))

        # 重新启用外键约束
        conn.execute(text("SET FOREIGN_KEY_CHECKS = 1"))
    yield


@pytest.fixture
def get_reservation_rule(db):
    from app.models.rule import Rule

    rule_item = db.query(RuleItem).filter(RuleItem.name == "周一到五中午11点半到12点预订").first()
    if not rule_item:
        rule_item = RuleItem(
            name="周一到五中午11点半到12点预订",
            start_time=arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),
            end_time=arrow.now('Asia/Shanghai').shift(days=1).format("YYYY-MM-DD HH:mm:ss"),
            start_time_cron_str="30 11 * * 1-5",
            end_time_cron_str="0 12 * * 1-5",
            quantity=2,
            order=1,
            allowed_operations="",
            forbidden_operations=""
        )
        db.add(rule_item)
        db.commit()
        db.refresh(rule_item)
        # 检查产品是否已存在，如果不存在则创建
    rule = db.query(Rule).filter(Rule.name == "午餐自助餐预订时段").first()

    if not rule:
        rule = Rule(name="午餐自助餐预订时段", type=RuleType.RESERVATION, status=Status.ACTIVE.value)
        rule.rule_items.append(rule_item)
        db.add(rule)
        db.commit()
        db.refresh(rule)
    return rule


@pytest.fixture
def get_content(db):
    from app.models.content import Content
    content = db.query(Content).filter(Content.name == "Test Content").first()
    if not content:
        content = Content(
            name="Test Content",
            content="这是一个测试内容")
        db.add(content)
        db.commit()
        db.refresh(content)
    return content


@pytest.fixture
def get_product_pricing_strategies(db):
    pricing_stragies = list()
    from app.dao.pricing import discount_strategy_dao, full_reduction_strategy_dao, time_limited_strategy_dao

    time_limited = TimeLimitedStrategyCreate(
        name="限时特价23元",
        description="这是一个测试限时折扣策略",
        start_time=arrow.now('Asia/Shanghai').shift(minutes=-1).datetime,
        end_time=arrow.now('Asia/Shanghai').shift(days=1).datetime,
        special_price=23
    )
    time_limited_strategy = time_limited_strategy_dao.create(db, time_limited)

    discount = DiscountStrategyCreate(
        name="九折优惠",
        description="这是一个测试折扣策略",
        start_time=arrow.now('Asia/Shanghai').shift(minutes=-1).datetime,
        end_time=arrow.now('Asia/Shanghai').shift(days=1).datetime,
        discount_rate=0.9,
        min_amount=0,
        max_discount=100
    )
    discount_strategy = discount_strategy_dao.create(db, discount)

    full_reduction = FullReductionStrategyCreate(
        name="满20减5",
        description="这是一个测试满减策略",
        start_time=arrow.now('Asia/Shanghai').shift(minutes=-1).datetime,
        end_time=arrow.now('Asia/Shanghai').shift(days=1).datetime,
        full_amount=20,
        reduction_amount=5
    )
    full_reduction_strategy = full_reduction_strategy_dao.create(db, full_reduction)
    pricing_stragies.append(time_limited_strategy)
    pricing_stragies.append(discount_strategy)
    pricing_stragies.append(full_reduction_strategy)
    return pricing_stragies


@pytest.fixture
def get_order_pricing_strategies(db):
    pricing_stragies = list()
    from app.dao.pricing import discount_strategy_dao
    from app.dao.pricing import full_reduction_strategy_dao

    discount = DiscountStrategyCreate(
        name="全单九八折优惠",
        description="这是一个测试折扣策略",
        start_time=arrow.now('Asia/Shanghai').shift(minutes=-1).datetime,
        end_time=arrow.now('Asia/Shanghai').shift(days=1).datetime,
        scope=PricingStrategyScope.ORDER,
        discount_rate=0.98,
        min_amount=0,
        max_discount=100
    )
    discount_strategy = discount_strategy_dao.create(db, discount)

    full_reduction = FullReductionStrategyCreate(
        name="满30减2",
        description="这是一个测试满减策略",
        start_time=arrow.now('Asia/Shanghai').shift(minutes=-1).datetime,
        end_time=arrow.now('Asia/Shanghai').shift(days=1).datetime,
        scope=PricingStrategyScope.ORDER,
        full_amount=30,
        reduction_amount=2
    )
    full_reduction_strategy = full_reduction_strategy_dao.create(db, full_reduction)
    pricing_stragies.append(discount_strategy)
    pricing_stragies.append(full_reduction_strategy)
    return pricing_stragies


@pytest.fixture
def get_reservation_prodct(db, get_content, get_reservation_rule, get_product_pricing_strategies):
    from app.models.product import ReservationProduct
    # 检查产品是否已存在，如果不存在则创建
    product = db.query(ReservationProduct).filter(ReservationProduct.name == "Test Product").first()
    if not product:
        product = ReservationProduct(
            name=f"自助餐工作餐_{generate_random_string()}",
            price=25,
            description="自助餐工作餐",
            stock=-1,
            status=Status.ACTIVE.value,
            listed_at=arrow.now('Asia/Shanghai').format("YYYY-MM-DD HH:mm:ss"),  # 可以根据实际模型添加更多属性,
            reservation_fee=0,
            max_reservations=-1,
            reservation_deadline=get_current_time(),
            cancellation_deadline=get_current_time(),
            is_approval_required=True
        )
        product.contents.append(get_content)
        product.rules.append(get_reservation_rule)
        for strategy in get_product_pricing_strategies:
            product.pricing_strategies.append(strategy)
        db.add(product)
        db.commit()
        db.refresh(product)
    return product


@pytest.fixture
def create_test_enterprise(db):
    """创建测试企业用户"""
    from app.models.user import Enterprise

    # 检查企业用户是否已存在
    enterprise = db.query(Enterprise).filter(Enterprise.company_name == "Test Enterprise").first()
    if not enterprise:
        # 创建企业用户

        enterprise_data = EnterpriseCreate(
            username="yihe-vegan",
            company_name="乙禾素",
            business_license="21o34u2o3ngouta",
            phone="***********",
            email="<EMAIL>",
            address="黄埔东路"
        )
        enterprise, accounts = user_service.create_enterprise(db, enterprise_data)

        # 给帐户充值
        gift_account = accounts.get("gift_account", None)
        regular_account = accounts.get("regular_account", None)
        if gift_account:
            gift_account.balance = 1000
            db.commit()
        if regular_account:
            regular_account.balance = 300
            db.commit()

    return enterprise
