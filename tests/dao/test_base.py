import pytest
from sqlalchemy import create_engine, <PERSON><PERSON>n, Integer, String
from sqlalchemy.orm import Session, declarative_base
from app.dao.base import DAO

# 使用新的导入方式
Base = declarative_base()


# 使用 SampleDataModel 作为测试数据模型
class SampleDataModel(Base):
    __tablename__ = 'sample_data'

    id = Column(Integer, primary_key=True)
    name = Column(String(50))
    value = Column(String(50))

    def __init__(self, name=None, value=None):
        self.name = name
        self.value = value


@pytest.fixture
def engine():
    return create_engine('sqlite:///:memory:')


@pytest.fixture
def session(engine):
    Base.metadata.create_all(engine)
    session = Session(engine)
    yield session
    session.close()
    Base.metadata.drop_all(engine)


@pytest.fixture
def dao():
    return DAO(SampleDataModel)


def test_dao_create(dao, session):
    data = {'name': 'test', 'value': 'value'}
    instance = dao.create(session, **data)
    assert instance.name == data['name']
    assert instance.value == data['value']
    assert instance.id is not None


def test_dao_get(dao, session):
    instance = dao.create(session, name='test', value='value')
    retrieved = dao.get(session, instance.id)
    assert retrieved.id == instance.id
    assert retrieved.name == instance.name
    assert retrieved.value == instance.value


def test_dao_get_all(dao, session):
    dao.create(session, name='test1', value='value1')
    dao.create(session, name='test2', value='value2')
    instances = dao.get_all(session)
    assert len(instances) == 2


def test_dao_filter(dao, session):
    dao.create(session, name='test1', value='value1')
    dao.create(session, name='test2', value='value2')
    instances = dao.filter(session, name='test1')
    assert len(instances) == 1
    assert instances[0].value == 'value1'


def test_dao_update(dao, session):
    instance = dao.create(session, name='test', value='value')
    updated = dao.update(session, instance.id, value='new_value')
    assert updated.value == 'new_value'


def test_dao_delete(dao, session):
    instance = dao.create(session, name='test', value='value')
    assert dao.delete(session, instance.id)
    assert dao.get(session, instance.id) is None


def test_dao_bulk_create(dao, session):
    data_list = [
        {'name': f'test{i}', 'value': f'value{i}'}
        for i in range(3)
    ]
    instances = dao.bulk_create(session, data_list)
    assert len(instances) == 3
    for i, instance in enumerate(instances):
        assert instance.name == f'test{i}'
        assert instance.value == f'value{i}'


def test_dao_bulk_update(dao, session):
    instances = dao.bulk_create(session, [
        {'name': 'test1', 'value': 'value1'},
        {'name': 'test2', 'value': 'value2'}
    ])
    updates = [
        {'id': instance.id, 'value': f'new_value{i}'}
        for i, instance in enumerate(instances)
    ]
    updated_count = dao.bulk_update(session, updates)
    assert updated_count == 2
    for i, instance in enumerate(dao.get_all(session)):
        assert instance.value == f'new_value{i}'


def test_dao_bulk_update_by_ids(dao, session):
    instances = dao.bulk_create(session, [
        {'name': 'test1', 'value': 'value1'},
        {'name': 'test2', 'value': 'value2'}
    ])
    ids = [instance.id for instance in instances]
    updated_count = dao.bulk_update_by_ids(session, ids, {'value': 'new_value'})
    assert updated_count == 2
    for instance in dao.get_all(session):
        assert instance.value == 'new_value'


def test_dao_bulk_update_by_filter(dao, session):
    dao.bulk_create(session, [
        {'name': 'test1', 'value': 'value1'},
        {'name': 'test2', 'value': 'value2'}
    ])
    updated_count = dao.bulk_update_by_filter(
        session,
        {'value': 'new_value'},
        {'name': 'test1'}
    )
    assert updated_count == 1
    instances = dao.get_all(session)
    assert instances[0].value == 'new_value'
    assert instances[1].value == 'value2'
