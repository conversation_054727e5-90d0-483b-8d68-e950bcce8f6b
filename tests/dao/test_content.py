# 内容DAO测试

from app.dao.content import content_dao
from app.models.content import Content
from app.models.enum import Status
from tests.utils import generate_random_string


def test_create_content(db):
    """测试创建内容"""
    content_data = {
        "name": f"测试内容_{generate_random_string()}",
        "content": f"这是测试内容_{generate_random_string()}",
        "image": f"/images/test_{generate_random_string()}.jpg",
        "thumbnail": f"/thumbnails/test_{generate_random_string()}.jpg",
        "sort_order": 1
    }
    from app.schemas.content import ContentCreate
    content = ContentCreate(**content_data)
    db_content = content_dao.create(session=db, content=content)

    assert db_content.name == content_data["name"]
    assert db_content.content == content_data["content"]
    assert db_content.image == content_data["image"]
    assert db_content.thumbnail == content_data["thumbnail"]
    assert db_content.sort_order == content_data["sort_order"]
    assert db_content.status == Status.ACTIVE


def test_batch_set_status(db):
    """测试批量设置内容状态"""
    # 创建多个内容用于测试
    content_ids = []
    for i in range(5):
        content_data = {
            "name": f"状态测试内容_{i}_{generate_random_string()}",
            "content": f"这是状态测试内容_{i}_{generate_random_string()}",
            "image": f"/images/test_{i}_{generate_random_string()}.jpg",
            "thumbnail": f"/thumbnails/test_{i}_{generate_random_string()}.jpg",
            "sort_order": i
        }
        from app.schemas.content import ContentCreate
        content = ContentCreate(**content_data)
        db_content = content_dao.create(session=db, content=content)
        content_ids.append(db_content.id)

    # 测试批量设置为无效状态
    affected_count = content_dao.batch_set_status(
        session=db,
        content_ids=content_ids,
        status=Status.INACTIVE
    )

    assert affected_count == 5

    # 验证状态是否已更新
    for content_id in content_ids:
        content = db.query(Content).filter(Content.id == content_id).first()
        assert content.status == Status.INACTIVE

    # 测试批量设置回有效状态
    affected_count = content_dao.batch_set_status(
        session=db,
        content_ids=content_ids,
        status=Status.ACTIVE
    )

    assert affected_count == 5

    # 验证状态是否已更新回有效
    for content_id in content_ids:
        content = db.query(Content).filter(Content.id == content_id).first()
        assert content.status == Status.ACTIVE

    # 测试部分ID不存在的情况
    non_existent_ids = [999999, 999998]
    all_ids = content_ids + non_existent_ids

    affected_count = content_dao.batch_set_status(
        session=db,
        content_ids=all_ids,
        status=Status.INACTIVE
    )

    # 只有实际存在的内容会被更新
    assert affected_count == 5
