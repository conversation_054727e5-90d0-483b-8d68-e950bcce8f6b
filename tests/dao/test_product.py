import pytest

from app.dao.product import ProductDAO
from app.models.enum import Status
from app.models.product import Product, ProductType


@pytest.fixture
def product_dao():
    return ProductDAO()


def test_search_empty(product_dao, db):
    """测试在空数据库中搜索产品"""
    result = product_dao.search(db)
    assert result["total"] == 0
    assert len(result["list"]) == 0


def test_search_with_keyword(product_dao, db):
    """测试使用关键字搜索产品"""
    # 创建测试产品
    product1 = Product(
        name="测试产品1",
        description="这是一个测试产品",
        price=100.0,
        stock=10,
        type=ProductType.REGULAR,
        status=Status.ACTIVE,
        sku="TEST001"
    )
    product2 = Product(
        name="其他产品",
        description="这是其他测试产品",
        price=200.0,
        stock=20,
        type=ProductType.REGULAR,
        status=Status.ACTIVE,
        sku="OTHER001"
    )
    db.add(product1)
    db.add(product2)
    db.commit()

    # 使用关键字"测试"搜索
    result = product_dao.search(db, keyword="测试")
    assert result["total"] == 2  # 两个产品描述中都包含"测试"
    assert len(result["list"]) == 2

    # 使用关键字"测试产品1"搜索
    result = product_dao.search(db, keyword="测试产品1")
    assert result["total"] == 1
    assert result["list"][0].name == "测试产品1"

    # 使用SKU搜索
    result = product_dao.search(db, keyword="TEST")
    assert result["total"] == 1
    assert result["list"][0].sku == "TEST001"


def test_search_with_name(product_dao, db):
    """测试使用名称搜索产品"""
    # 创建测试产品
    product1 = Product(
        name="素食沙拉",
        description="健康美味的素食沙拉",
        price=50.0,
        stock=30,
        type=ProductType.DIRECT_SALE,
        status=Status.ACTIVE
    )
    product2 = Product(
        name="素食汉堡",
        description="美味的素食汉堡",
        price=60.0,
        stock=25,
        type=ProductType.DIRECT_SALE,
        status=Status.ACTIVE
    )
    product3 = Product(
        name="肉类汉堡",
        description="传统肉类汉堡",
        price=70.0,
        stock=20,
        type=ProductType.DIRECT_SALE,
        status=Status.ACTIVE
    )
    db.add(product1)
    db.add(product2)
    db.add(product3)
    db.commit()

    # 使用名称"素食"搜索
    result = product_dao.search(db, name="素食")
    assert result["total"] == 2
    assert "素食" in result["list"][0].name
    assert "素食" in result["list"][1].name

    # 使用名称"汉堡"搜索
    result = product_dao.search(db, name="汉堡")
    assert result["total"] == 2
    assert "汉堡" in result["list"][0].name
    assert "汉堡" in result["list"][1].name


def test_search_with_product_type(product_dao, db):
    """测试使用产品类型搜索产品"""
    # 创建测试产品
    product1 = Product(
        name="普通产品",
        description="普通产品描述",
        price=100.0,
        stock=10,
        type=ProductType.REGULAR,
        status=Status.ACTIVE
    )
    product2 = Product(
        name="直销产品",
        description="直销产品描述",
        price=200.0,
        stock=20,
        type=ProductType.DIRECT_SALE,
        status=Status.ACTIVE
    )
    product3 = Product(
        name="预订产品",
        description="预订产品描述",
        price=300.0,
        stock=30,
        type=ProductType.RESERVATION,
        status=Status.ACTIVE
    )
    db.add(product1)
    db.add(product2)
    db.add(product3)
    db.commit()

    # 搜索普通产品
    result = product_dao.search(db, product_type=ProductType.REGULAR)
    assert result["total"] == 1
    assert result["list"][0].type == ProductType.REGULAR

    # 搜索直销产品
    result = product_dao.search(db, product_type=ProductType.DIRECT_SALE)
    assert result["total"] == 1
    assert result["list"][0].type == ProductType.DIRECT_SALE

    # 搜索预订产品
    result = product_dao.search(db, product_type=ProductType.RESERVATION)
    assert result["total"] == 1
    assert result["list"][0].type == ProductType.RESERVATION


def test_search_with_status(product_dao, db):
    """测试使用状态搜索产品"""
    # 创建测试产品
    product1 = Product(
        name="活跃产品",
        description="活跃产品描述",
        price=100.0,
        stock=10,
        type=ProductType.REGULAR,
        status=Status.ACTIVE
    )
    product2 = Product(
        name="非活跃产品",
        description="非活跃产品描述",
        price=200.0,
        stock=20,
        type=ProductType.REGULAR,
        status=Status.INACTIVE
    )
    db.add(product1)
    db.add(product2)
    db.commit()

    # 搜索活跃产品
    result = product_dao.search(db, status=Status.ACTIVE)
    assert result["total"] == 1
    assert result["list"][0].status == Status.ACTIVE

    # 搜索非活跃产品
    result = product_dao.search(db, status=Status.INACTIVE)
    assert result["total"] == 1
    assert result["list"][0].status == Status.INACTIVE


def test_search_with_multiple_filters(product_dao, db):
    """测试使用多个过滤条件搜索产品"""
    # 创建测试产品
    product1 = Product(
        name="素食沙拉-活跃",
        description="健康美味的素食沙拉",
        price=50.0,
        stock=30,
        type=ProductType.DIRECT_SALE,
        status=Status.ACTIVE
    )
    product2 = Product(
        name="素食沙拉-非活跃",
        description="健康美味的素食沙拉",
        price=50.0,
        stock=30,
        type=ProductType.DIRECT_SALE,
        status=Status.INACTIVE
    )
    product3 = Product(
        name="素食汉堡-活跃",
        description="美味的素食汉堡",
        price=60.0,
        stock=25,
        type=ProductType.REGULAR,
        status=Status.ACTIVE
    )
    db.add(product1)
    db.add(product2)
    db.add(product3)
    db.commit()

    # 使用名称和状态搜索
    result = product_dao.search(db, name="素食", status=Status.ACTIVE)
    assert result["total"] == 2
    assert all(p.status == Status.ACTIVE and "素食" in p.name for p in result["list"])

    # 使用名称、类型和状态搜索
    result = product_dao.search(
        db,
        name="素食",
        product_type=ProductType.DIRECT_SALE,
        status=Status.ACTIVE
    )
    assert result["total"] == 1
    assert result["list"][0].name == "素食沙拉-活跃"


def test_search_with_pagination(product_dao, db):
    """测试带分页的产品搜索"""
    # 创建多个测试产品
    for i in range(15):
        product = Product(
            name=f"测试产品{i + 1}",
            description=f"测试产品{i + 1}的描述",
            price=100.0 + i,
            stock=10 + i,
            type=ProductType.REGULAR,
            status=Status.ACTIVE
        )
        db.add(product)
    db.commit()

    # 第一页，每页5条
    result = product_dao.search(db, skip=0, limit=5)
    assert result["total"] == 15  # 总数应该是15
    assert len(result["list"]) == 5  # 返回的列表应该有5条

    # 第二页，每页5条
    result = product_dao.search(db, skip=5, limit=5)
    assert result["total"] == 15
    assert len(result["list"]) == 5

    # 第三页，每页5条
    result = product_dao.search(db, skip=10, limit=5)
    assert result["total"] == 15
    assert len(result["list"]) == 5
