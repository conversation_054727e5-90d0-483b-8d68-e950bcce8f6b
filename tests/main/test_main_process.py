import pprint

from app.service.revervation import reservation_service


def test_order_full_process(db, create_test_user, get_reservation_prodct, get_product_pricing_strategies,
                            get_order_pricing_strategies, get_reservation_rule):
    print(f"id: {get_reservation_prodct.id}")
    print(f"name: {get_reservation_prodct.name}")
    print(f"price: {get_reservation_prodct.price}")
    print(f"stock: {get_reservation_prodct.stock}")
    print(f"type: {get_reservation_prodct.type}")
    print(f"reservation_fee: {get_reservation_prodct.reservation_fee}")
    print(f"max_reservations: {get_reservation_prodct.max_reservations}")
    print(f"reservation_deadline: {get_reservation_prodct.reservation_deadline}")
    print(f"cancellation_deadline: {get_reservation_prodct.cancellation_deadline}")
    print(f"is_approval_required: {get_reservation_prodct.is_approval_required}")

    print("==============================================")
    print(f"contents: {get_reservation_prodct.contents}")
    contents = get_reservation_prodct.contents
    for content in contents:
        print(f"content: {content}")
        print(f"content.id: {content.id}")
        print(f"content.name: {content.name}")

    print("==============================================")
    print(f"pricing_strategies: {get_reservation_prodct.pricing_strategies}")
    pricing_strategies = get_reservation_prodct.pricing_strategies
    for pricing_strategy in pricing_strategies:
        print(f"pricing_strategy: {pricing_strategy}")
        print(f"pricing_strategy.id: {pricing_strategy.id}")
        print(f"pricing_strategy.name: {pricing_strategy.name}")
        print(f"pricing_strategy.description: {pricing_strategy.description}")
        print(f"pricing_strategy.start_time: {pricing_strategy.start_time}")
        print(f"pricing_strategy.end_time: {pricing_strategy.end_time}")
        print(f"pricing_strategy.scope: {pricing_strategy.scope}")
        print(f"pricing_strategy.is_mutual_exclusive: {pricing_strategy.is_mutual_exclusive}")
        print(f"pricing_strategy.status: {pricing_strategy.status}")
        print(f"pricing_strategy.type: {pricing_strategy.type}")

    print("==============================================")
    print(f"rules: {get_reservation_prodct.rules}")
    rules = get_reservation_prodct.rules
    for rule in rules:
        print(f"rule: {rule}")
        print(f"rule.id: {rule.id}")
        print(f"rule.name: {rule.name}")
        print(f"rule.rule_items: {rule.rule_items}")
        for rule_item in rule.rule_items:
            print(f"rule_item: {rule_item}")
            print(f"rule_item.id: {rule_item.id}")
            print(f"rule_item.name: {rule_item.name}")
            print(f"rule_item.quantity: {rule_item.quantity}")
            print(f"rule_item.start_time: {rule_item.start_time}")
            print(f"rule_item.end_time: {rule_item.end_time}")
            print(f"rule_item.start_time_cron_str: {rule_item.start_time_cron_str}")
            print(f"rule_item.end_time_cron_str: {rule_item.end_time_cron_str}")
            print(f"rule_item.allowed_operations: {rule_item.allowed_operations}")
            print(f"rule_item.forbidden_operations: {rule_item.forbidden_operations}")

    availabiity_list = reservation_service.get_availability_list(db, get_reservation_rule.id)
    pprint.pprint(availabiity_list)
    chosen_reservation_requests = availabiity_list[:2]
    print("reservation_requests")

    print("==============================================")
    from app.service.order import order_service
    user_id = create_test_user.id
    products = [
        {
            "product_id": get_reservation_prodct.id,
            "quantity": 2,
            "reservation_requests": chosen_reservation_requests
        }
    ]
    pprint.pprint(products)
    order = order_service.create_order(db, user_id, products)
    print(f"order.id: {order.id}")
    print(f"order.user_id: {order.user_id}")
    print(f"order.status: {order.status}")
    print(f"order.payment_status: {order.payment_status}")
    print(f"order.total_amount: {order.total_amount}")
    print(f"order.payable_amount: {order.payable_amount}")
    print(f"order.actual_amount_paid: {order.actual_amount_paid}")
    print(f"order.payment_method: {order.payment_method}")
    print(f"order.items: {order.items}")
    print(f"order.pricing_remark: {order.pricing_remark}")
    for item in order.items:
        print(f"item: {item}")
        print(f"item.id: {item.id}")
        print(f"item.product_id: {item.product_id}")
        print(f"item.quantity: {item.quantity}")
        print(f"item.price: {item.price}")
        print(f"item.subtotal: {item.subtotal}")
        print(f"item.final_price: {item.final_price}")
        print(f"item.payable_amount: {item.payable_amount}")
        print(f"item.pricing_remark: {item.pricing_remark}")
