from app.dao.user import enterprise_user_relation_dao
from app.models.order import OrderStatus, PaymentStatus, PaymentMethod
from app.models.reservation import ReservationStatus
from app.schemas.callback import AudioEnum
from app.schemas.user import EnterpriseUserRelationCreate
from app.service.feieyun import feieyun_service
from app.service.order import order_service
from app.service.payment import payment_service
from app.service.revervation import reservation_service
from app.service.verify import verify_service


def test_order_payment_verification_process(db, create_test_user, create_test_enterprise, get_reservation_prodct,
                                            get_product_pricing_strategies,
                                            get_order_pricing_strategies, get_reservation_rule):
    """测试订单生成、支付和核销的全流程"""

    # 第一部分：获取可用预约时间段
    availability_list = reservation_service.get_availability_list(db, get_reservation_rule.id)
    assert availability_list, "应该有可用的预约时间段"
    chosen_reservation_requests = availability_list[:2]

    # 第二部分：创建订单
    user_id = create_test_user.id
    products = [
        {
            "product_id": get_reservation_prodct.id,
            "quantity": 1,
            "reservation_requests": chosen_reservation_requests
        }
    ]

    # 创建订单
    order = order_service.create_order(db, user_id, products)

    # 验证订单创建成功
    assert order.id is not None, "订单ID不应为空"
    assert order.user_id == user_id, "用户ID应匹配"
    assert order.status == OrderStatus.PENDING, "订单状态应为待处理"
    assert order.payment_status == PaymentStatus.UNPAID, "支付状态应为未支付"
    assert order.total_amount > 0, "订单总金额应大于0"
    assert order.payable_amount > 0, "应付金额应大于0"
    assert order.actual_amount_paid == 0, "实际支付金额应为0"

    # 验证订单项
    assert len(order.items) == 1, "应该有1个订单项"
    order_item = order.items[0]
    assert order_item.product_id == get_reservation_prodct.id, "产品ID应匹配"
    assert order_item.quantity == 1, "数量应为1"

    # 验证预订请求
    reservation_requests = order_item.reservation_requests
    assert len(reservation_requests) == 2, "应该有2个预订请求"
    for req in reservation_requests:
        assert req.user_id == user_id, "预订请求的用户ID应匹配"
        assert req.product_id == get_reservation_prodct.id, "预订请求的产品ID应匹配"
        assert req.status == ReservationStatus.PENDING, "预订请求状态应为待处理"
        assert req.verification_code is None, "验证码应为空"

    # 第三部分：支付订单
    payment_info = {
        "payment_method": PaymentMethod.ACCOUNT_BALANCE
    }
    paid_order = payment_service.pay_order(db, order.id, payment_info)

    # 验证订单支付成功
    assert paid_order.payment_status == PaymentStatus.PAID, "支付状态应为已支付"
    assert paid_order.actual_amount_paid == paid_order.payable_amount, "实际支付金额应等于应付金额"
    assert paid_order.payment_method == PaymentMethod.ACCOUNT_BALANCE, "支付方式应为账户余额"

    # 验证预订请求状态更新和验证码生成
    db.refresh(order_item)
    for req in order_item.reservation_requests:
        assert req.status == ReservationStatus.PAID_FULL, "预订请求状态应为已支付"
        assert req.verification_code is not None, "验证码不应为空"
        assert isinstance(req.verification_code, str), "验证码应为字符串"
        assert len(req.verification_code) > 0, "验证码长度应大于0"

    # 第四部分：核销流程测试
    # 选取第一个预订请求进行核销
    reservation_request = order_item.reservation_requests[0]
    verification_code = reservation_request.verification_code

    # 模拟加密后的验证码（在实际场景中，这应该是由飞鹅云扫描后回调的加密数据）
    # 在测试中我们只能模拟这一步，假设已经扫描并加密
    encrypted_result = "encrypted_" + verification_code  # 实际应用中这里是真正的加密数据

    # 模拟feieyun_service.callback_decrypt方法的行为
    original_decrypt = feieyun_service.callback_decrypt

    try:
        # 替换为测试用的mock实现
        feieyun_service.callback_decrypt = lambda x: verification_code if x == encrypted_result else ""

        # 执行核销
        success, audio_code = verify_service.verify_and_write_off(encrypted_result)

        # 验证核销结果
        assert success is True, "核销应该成功"
        assert audio_code == AudioEnum.WRITE_OFF_SUCCESS, "应返回核销成功的音频代码"

        # 验证预订请求状态（在实际实现中，核销后可能会更新状态）
        # 注意：由于当前verify_service.verify_and_write_off的实现是示例性的，
        # 并没有实际更新预订请求状态，所以这里不验证状态变化

    finally:
        # 恢复原始方法
        feieyun_service.callback_decrypt = original_decrypt


def test_enterprise_payment_process(db, create_test_user, create_test_enterprise, get_reservation_prodct,
                                    get_reservation_rule):
    """测试企业支付流程"""
    # 用户加入企业
    relation = EnterpriseUserRelationCreate(
        personal_user_id=create_test_user.id,
        enterprise_id=create_test_enterprise.id
    )
    enterprise_user_relation_dao.create(db, relation)

    # 获取可用预约时间段
    availability_list = reservation_service.get_availability_list(db, get_reservation_rule.id)
    chosen_reservation_requests = availability_list[:1]

    # 创建订单
    user_id = create_test_user.id
    products = [
        {
            "product_id": get_reservation_prodct.id,
            "quantity": 1,
            "reservation_requests": chosen_reservation_requests
        }
    ]

    order = order_service.create_order(db, user_id, products)

    # 准备企业支付信息
    payment_info = {
        "payment_method": PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE,
        "enterprise_id": create_test_enterprise.id
    }

    # 支付订单
    paid_order = payment_service.pay_order(db, order.id, payment_info)

    # 验证企业支付结果
    assert paid_order.payment_status == PaymentStatus.PAID, "支付状态应为已支付"
    assert paid_order.payment_method == PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE, "支付方式应为企业账户余额"

    # 验证预订请求
    order_item = paid_order.items[0]
    for req in order_item.reservation_requests:
        assert req.status == ReservationStatus.PAID_FULL, "预订请求状态应为已支付定金"
        assert req.verification_code is not None, "验证码不应为空"
