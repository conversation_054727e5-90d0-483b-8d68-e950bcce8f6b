import pytest
from unittest.mock import patch, MagicMock

from app.models.account import AccountType, RegularAccount, TransactionType
from app.models.enum import Status
from app.models.order import PaymentMethod, OrderType, PaymentStatus, OrderStatus, Order, RechargeOrder
from app.models.user import PersonalUser, UserType
from app.service.account import AccountService
from app.dao.account import account_transaction_dao
from app.schemas.account import AccountTransactionCreate
from app.service.payment import payment_service
from app.schemas.user_account import AccountRechargeReq
from tests.utils import generate_random_string


@pytest.fixture
def account_service():
    """账户服务实例"""
    return AccountService()


@pytest.fixture
def test_user_with_account(db):
    """创建测试用户及其账户"""
    # 创建个人用户
    personal_user = PersonalUser(
        username=f"test_user_{generate_random_string()}",
        status=Status.ACTIVE,
        type=UserType.PERSONAL,
        phone=f"138{generate_random_string(8)}",
        email=f"user{generate_random_string()}@example.com",
        real_name=f"测试用户{generate_random_string()}",
        address=f"测试地址{generate_random_string()}",
    )
    db.add(personal_user)
    db.flush()

    # 创建普通账户
    regular_account = RegularAccount(
        user_id=personal_user.id,
        balance=100.0,
        status=Status.ACTIVE,
        type=AccountType.REGULAR
    )
    db.add(regular_account)
    db.flush()

    user_data = {
        "user_id": personal_user.id,
        "username": personal_user.username,
        "phone": personal_user.phone,
        "email": personal_user.email,
        "real_name": personal_user.real_name,
        "status": personal_user.status,
        "regular_account": regular_account
    }

    db.commit()

    yield user_data, db

    # 清理测试数据
    try:
        # 清理交易记录
        transactions = account_transaction_dao.get_by_account_id(db, regular_account.id)
        for transaction in transactions:
            db.delete(transaction)
            
        # 查询和删除与该用户关联的订单
        orders = db.query(Order).filter(Order.user_id == personal_user.id).all()
        for order in orders:
            db.delete(order)
            
        # 删除普通账户
        regular_accounts = db.query(RegularAccount).filter(RegularAccount.user_id == personal_user.id).all()
        for account in regular_accounts:
            db.delete(account)

        # 删除个人用户
        user = db.query(PersonalUser).filter(PersonalUser.id == personal_user.id).first()
        if user:
            db.delete(user)
            
        db.commit()
    except Exception as e:
        db.rollback()
        print(f"清理测试数据失败: {e}")


def test_recharge_success(account_service, test_user_with_account):
    """测试充值成功"""
    user_data, db = test_user_with_account
    user_id = user_data["user_id"]
    regular_account = user_data["regular_account"]
    
    # 记录初始余额
    initial_balance = regular_account.balance
    
    # 充值金额
    amount = 200.0
    payment_method = PaymentMethod.CASH
    
    # 执行充值
    recharge_order = account_service.recharge(db, user_id, amount, payment_method)

    # 验证订单信息
    assert recharge_order is not None
    assert isinstance(recharge_order, RechargeOrder)
    assert recharge_order.user_id == user_id
    assert recharge_order.total_amount == amount
    assert recharge_order.payable_amount == amount
    assert recharge_order.actual_amount_paid == amount
    assert recharge_order.payment_method == payment_method
    assert recharge_order.payment_status == PaymentStatus.UNPAID
    assert recharge_order.status == OrderStatus.PENDING

    recharge_info = AccountRechargeReq(user_id=user_id, amount=amount, payment_method=payment_method).model_dump()
    paid_recharge_order = payment_service.recharge(db, recharge_order.id, recharge_info)

    assert paid_recharge_order.status == OrderStatus.PAID
    assert paid_recharge_order.payment_status == PaymentStatus.PAID
    # 验证账户余额已增加
    db.refresh(regular_account)
    assert regular_account.balance == initial_balance + amount

    # 验证交易记录已创建
    transactions = account_transaction_dao.get_by_order_id(db, paid_recharge_order.id)
    assert len(transactions) > 0

    # 验证第一条交易记录
    transaction = transactions[0]
    assert transaction.account_id == regular_account.id
    assert transaction.order_id == paid_recharge_order.id
    assert transaction.transaction_type == TransactionType.DEPOSIT
    assert transaction.amount == amount


def test_recharge_invalid_amount(account_service, test_user_with_account):
    """测试充值金额无效"""
    user_data, db = test_user_with_account
    user_id = user_data["user_id"]
    
    # 使用无效金额尝试充值
    with pytest.raises(ValueError, match="充值金额必须大于0"):
        account_service.recharge(db, user_id, 0, PaymentMethod.CASH)
    
    with pytest.raises(ValueError, match="充值金额必须大于0"):
        account_service.recharge(db, user_id, -10, PaymentMethod.CASH)


def test_recharge_invalid_method(account_service, test_user_with_account):
    """测试充值方式无效"""
    user_data, db = test_user_with_account
    user_id = user_data["user_id"]
    
    # 使用账户余额尝试充值
    with pytest.raises(ValueError, match="不允许通过账户余额进行充值"):
        account_service.recharge(db, user_id, 100, PaymentMethod.ACCOUNT_BALANCE)


def test_recharge_user_not_found(account_service, db):
    """测试用户账户不存在"""
    # 使用不存在的用户ID尝试充值
    with pytest.raises(ValueError, match="用户ID 999999 的普通账户不存在"):
        account_service.recharge(db, 999999, 100, PaymentMethod.CASH)


def test_recharge_with_different_methods(account_service, test_user_with_account):
    """测试使用不同的充值方式"""
    user_data, db = test_user_with_account
    user_id = user_data["user_id"]
    regular_account = user_data["regular_account"]
    
    # 记录初始余额
    initial_balance = regular_account.balance
    
    # 测试银行转账充值
    bank_amount = 150.0
    bank_order = account_service.recharge(db, user_id, bank_amount, PaymentMethod.BANK_TRANSFER)

    payment_info = {
        "payment_method": PaymentMethod.BANK_TRANSFER,
    }
    paid_bank_order = payment_service.recharge(db, bank_order.id, payment_info)
    # 验证银行转账充值成功
    assert bank_order.payment_method == PaymentMethod.BANK_TRANSFER

    db.refresh(regular_account)
    assert regular_account.balance == initial_balance + bank_amount
    
    # 测试现金充值
    cash_amount = 50.0
    updated_balance = regular_account.balance
    cash_order = account_service.recharge(db, user_id, cash_amount, PaymentMethod.CASH)

    payment_info = {
        "payment_method": PaymentMethod.CASH,
    }
    paid_bank_order = payment_service.recharge(db, cash_order.id, payment_info)

    # 验证现金充值成功
    assert cash_order.payment_method == PaymentMethod.CASH
    db.refresh(regular_account)
    assert regular_account.balance == updated_balance + cash_amount 