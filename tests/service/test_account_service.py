from datetime import datetime

import pytest

from app.dao.user import user_dao
from app.models.account import RegularAccount, GiftAccount, AccountType, AccountTransaction, Account
from app.models.enum import Status
from app.models.user import PersonalUser, UserType, Enterprise
from app.service.account import AccountService
from app.service.user import user_service
from app.schemas.user import PersonalUserCreate, EnterpriseCreate
from tests.utils import generate_random_string
from app.models.order import Order, OrderStatus, PaymentStatus, PaymentMethod, OrderType, RechargeOrder
from app.utils.common import get_current_time


@pytest.fixture
def account_service():
    """账户服务实例"""
    return AccountService()


@pytest.fixture
def test_users_with_accounts(db):
    """创建测试用户及其账户"""

    # 创建测试数据
    users = []
    for i in range(3):
        # 直接创建个人用户，而不是先创建基础用户再创建个人用户
        # 这样可以避免 ID 冲突问题
        personal_user = PersonalUser(
            username=f"test_user_{i}_{generate_random_string()}",
            status=Status.ACTIVE if i < 2 else Status.INACTIVE,
            type=UserType.PERSONAL,
            phone=f"**********{i}",
            email=f"user{i}@example.com",
            real_name=f"测试用户{i}",
            address=f"测试地址{i}",
        )
        db.add(personal_user)
        db.flush()

        # 创建普通账户
        regular_account = RegularAccount(
            user_id=personal_user.id,
            balance=100.0 * (i + 1),
            status=Status.ACTIVE,
            type=AccountType.REGULAR
        )
        db.add(regular_account)
        db.flush()

        # 创建赠送账户
        gift_account = GiftAccount(
            user_id=personal_user.id,
            balance=50.0 * (i + 1),
            gift_amount=60.0 * (i + 1),
            status=Status.ACTIVE,
            type=AccountType.GIFT
        )
        db.add(gift_account)
        db.flush()

        users.append({
            "user_id": personal_user.id,
            "username": personal_user.username,
            "phone": personal_user.phone,
            "email": personal_user.email,
            "real_name": personal_user.real_name,
            "status": personal_user.status,
            "regular_balance": regular_account.balance,
            "gift_balance": gift_account.balance,
            "gift_amount": gift_account.gift_amount
        })

    db.commit()

    yield users, db

    # 清理测试数据
    for user_data in users:
        user_id = user_data["user_id"]

        # 删除赠送账户
        gift_accounts = db.query(GiftAccount).filter(GiftAccount.user_id == user_id).all()
        for account in gift_accounts:
            db.delete(account)

        # 删除普通账户
        regular_accounts = db.query(RegularAccount).filter(RegularAccount.user_id == user_id).all()
        for account in regular_accounts:
            db.delete(account)

        # 删除个人用户 (这会同时删除基础用户，因为它们是同一条记录的不同表现形式)
        personal_user = db.query(PersonalUser).filter(PersonalUser.id == user_id).first()
        if personal_user:
            db.delete(personal_user)

    db.commit()
    db.close()


def test_search_without_filters(account_service, test_users_with_accounts):
    """测试无过滤条件的搜索"""
    users, db = test_users_with_accounts

    # 执行搜索
    result = account_service.search(db)

    # 验证结果
    assert "data" in result
    assert "total" in result["data"]
    assert "list" in result["data"]
    assert result["data"]["total"] >= 3  # 至少包含我们创建的3个测试用户

    # 验证返回数据中包含用户信息和余额信息
    user_list = result["data"]["list"]
    for user_data in users:
        matching_users = [u for u in user_list if u["id"] == user_data["user_id"]]
        if matching_users:  # 如果找到匹配的用户
            matched_user = matching_users[0]
            assert matched_user["username"] == user_data["username"]
            assert matched_user["regular_balance"] == user_data["regular_balance"]
            assert matched_user["gift_balance"] == user_data["gift_balance"]
            assert matched_user["gift_amount"] == user_data["gift_amount"]


def test_search_with_keyword(account_service, test_users_with_accounts):
    """测试使用关键字搜索"""
    users, db = test_users_with_accounts
    test_user = users[0]

    # 使用用户名作为关键字搜索
    result = account_service.search(db, keyword=test_user["username"])

    # 验证结果
    assert "data" in result
    assert result["data"]["total"] >= 1
    assert any(u["username"] == test_user["username"] for u in result["data"]["list"])

    # 使用电话号码作为关键字搜索
    result = account_service.search(db, keyword=test_user["phone"])

    # 验证结果
    assert "data" in result
    assert result["data"]["total"] >= 1
    assert any(u["phone"] == test_user["phone"] for u in result["data"]["list"])


def test_search_with_phone(account_service, test_users_with_accounts):
    """测试使用手机号搜索"""
    users, db = test_users_with_accounts
    test_user = users[0]

    # 使用完整手机号搜索
    result = account_service.search(db, phone=test_user["phone"])

    # 验证结果
    assert "data" in result
    assert result["data"]["total"] >= 1
    assert any(u["phone"] == test_user["phone"] for u in result["data"]["list"])

    # 使用部分手机号搜索
    partial_phone = test_user["phone"][:5]
    result = account_service.search(db, phone=partial_phone)

    # 验证结果
    assert "data" in result
    assert result["data"]["total"] >= 1
    assert any(test_user["phone"] in u["phone"] for u in result["data"]["list"])


def test_search_with_name(account_service, test_users_with_accounts):
    """测试使用名称搜索"""
    users, db = test_users_with_accounts
    test_user = users[0]

    # 使用用户名搜索
    result = account_service.search(db, name=test_user["username"])

    # 验证结果
    assert "data" in result
    assert result["data"]["total"] >= 1
    assert any(u["username"] == test_user["username"] for u in result["data"]["list"])

    # 使用真实姓名搜索
    result = account_service.search(db, name=test_user["real_name"])

    # 验证结果
    assert "data" in result
    assert result["data"]["total"] >= 1
    assert any(u["real_name"] == test_user["real_name"] for u in result["data"]["list"])


def test_search_with_status(account_service, test_users_with_accounts):
    """测试使用状态搜索"""
    users, db = test_users_with_accounts

    # 搜索活跃用户
    result = account_service.search(db, status=Status.ACTIVE)

    # 验证结果
    assert "data" in result
    assert result["data"]["total"] >= 2  # 我们创建了2个活跃用户
    active_users = [u for u in result["data"]["list"] if u["status"] == Status.ACTIVE]
    assert len(active_users) >= 2

    # 搜索非活跃用户
    result = account_service.search(db, status=Status.INACTIVE)

    # 验证结果
    assert "data" in result
    assert result["data"]["total"] >= 1  # 我们创建了1个非活跃用户
    inactive_users = [u for u in result["data"]["list"] if u["status"] == Status.INACTIVE]
    assert len(inactive_users) >= 1


def test_search_with_pagination(account_service, test_users_with_accounts):
    """测试分页功能"""
    users, db = test_users_with_accounts

    # 测试limit
    result = account_service.search(db, limit=1)

    # 验证结果
    assert "data" in result
    assert len(result["data"]["list"]) == 1

    # 测试skip
    result1 = account_service.search(db, limit=1, skip=0)
    result2 = account_service.search(db, limit=1, skip=1)

    # 验证两次查询返回不同的用户
    if result1["data"]["list"] and result2["data"]["list"]:
        assert result1["data"]["list"][0]["id"] != result2["data"]["list"][0]["id"]


def test_get_user_accounts(db, account_service, create_test_user):
    """测试获取用户账户信息"""
    # 创建测试用户
    username = f"testuser_{generate_random_string(8)}"
    phone = f"1380000{generate_random_string(4)}"
    
    # 使用用户服务创建用户，它会同时创建账户
    user_data = PersonalUserCreate(
        username=username,
        phone=phone,
        real_name="测试用户",
        email="<EMAIL>",
        status=Status.ACTIVE
    )
    user, accounts = user_service.create_personal_user(db, user_data)
    
    # 直接使用返回的账户更新余额
    regular_account = accounts.get("regular_account")
    gift_account = accounts.get("gift_account")
    
    if regular_account:
        regular_account.balance = 1000.0
    
    if gift_account:
        gift_account.balance = 500.0
        gift_account.gift_amount = 200.0
    
    db.commit()
    db.refresh(user)
    
    # 调用测试方法
    result = account_service.get_user_accounts(db, user.id)
    
    # 验证结果
    assert result.user_info.id == user.id
    assert result.user_info.username == user.username
    assert result.user_info.phone == user.phone
    assert result.user_info.real_name == user.real_name
    assert result.user_info.email == user.email
    
    # 检查账户信息
    assert result.account_info["regular_account"]["balance"] == 1000.0
    assert result.account_info["gift_account"]["balance"] == 500.0
    assert result.account_info["gift_account"]["gift_amount"] == 200.0
    
    # 测试用户不存在的情况
    with pytest.raises(ValueError):
        account_service.get_user_accounts(db, 99999)


def test_get_user_transactions(db, account_service, test_users_with_accounts):
    """测试获取用户的交易记录"""
    users, db = test_users_with_accounts
    test_user = users[0]
    user_id = test_user["user_id"]
    
    # 创建测试交易记录
    from app.models.account import AccountTransaction, Account, TransactionType
    from app.models.order import Order, OrderStatus, PaymentStatus, PaymentMethod, OrderType
    from app.utils.common import get_current_time
    
    # 查找用户的账户
    accounts = db.query(Account).filter(Account.user_id == user_id).all()
    assert len(accounts) >= 1
    account = accounts[0]
    
    # 创建测试订单
    order = Order(
        order_no="TEST_ORDER_" + str(user_id),
        user_id=user_id,
        status=OrderStatus.PENDING,
        payment_status=PaymentStatus.UNPAID,
        total_amount=100.0,
        payable_amount=100.0,
        actual_amount_paid=100.0,
        payment_method=PaymentMethod.WECHAT_PAY,
        type=OrderType.DIRECT_SALE,
        created_at=get_current_time(),
        updated_at=get_current_time()
    )
    db.add(order)
    db.flush()
    
    # 创建多条交易记录
    transactions = []
    for i in range(3):
        transaction = AccountTransaction(
            account_id=account.id,
            order_id=order.id,
            transaction_type=TransactionType.PAYMENT,
            amount=50.0 * (i + 1),
            transaction_time=get_current_time(),
            description=f"测试交易 {i+1}"
        )
        db.add(transaction)
        transactions.append(transaction)
    
    db.commit()
    
    try:
        # 测试获取交易记录
        result = account_service.get_user_transactions(session=db, user_id=user_id)
        
        # 验证结果
        assert "data" in result
        assert "total" in result["data"]
        assert "list" in result["data"]
        assert result["data"]["total"] >= 3  # 至少包含我们创建的3条记录
        
        # 验证分页功能
        result_page_1 = account_service.get_user_transactions(session=db, user_id=user_id, skip=0, limit=2)
        result_page_2 = account_service.get_user_transactions(session=db, user_id=user_id, skip=2, limit=2)
        
        assert len(result_page_1["data"]["list"]) == 2
        if len(result_page_2["data"]["list"]) > 0:
            assert result_page_1["data"]["list"][0]["id"] != result_page_2["data"]["list"][0]["id"]
        
        # 验证返回数据包含所需字段
        transaction_list = result["data"]["list"]
        if transaction_list:
            transaction = transaction_list[0]
            assert "id" in transaction
            assert "username" in transaction
            assert "account_id" in transaction
            assert "account_type" in transaction
            assert "order_id" in transaction
            assert "order_no" in transaction
            assert "payment_method" in transaction
            assert "transaction_type" in transaction
            assert "amount" in transaction
            assert "transaction_time" in transaction
            assert "description" in transaction
    finally:
        # 清理测试数据
        for transaction in transactions:
            db.delete(transaction)
        db.delete(order)
        db.commit()


@pytest.fixture
def test_enterprises_with_accounts(db):
    """创建测试企业及其账户"""

    # 创建测试数据
    enterprises = []
    for i in range(3):
        # 创建企业用户
        enterprise = Enterprise(
            username=f"test_enterprise_{i}_{generate_random_string()}",
            status=Status.ACTIVE if i < 2 else Status.INACTIVE,
            type=UserType.ENTERPRISE,
            company_name=f"测试企业{i}",
            business_license=f"BL{i}{generate_random_string(8)}",
            phone=f"**********{i}",
            email=f"enterprise{i}@example.com",
            address=f"企业地址{i}",
        )
        db.add(enterprise)
        db.flush()

        # 创建普通账户
        regular_account = RegularAccount(
            user_id=enterprise.id,
            balance=1000.0 * (i + 1),
            status=Status.ACTIVE,
            type=AccountType.REGULAR
        )
        db.add(regular_account)
        db.flush()

        # 创建赠送账户
        gift_account = GiftAccount(
            user_id=enterprise.id,
            balance=500.0 * (i + 1),
            gift_amount=600.0 * (i + 1),
            status=Status.ACTIVE,
            type=AccountType.GIFT
        )
        db.add(gift_account)
        db.flush()

        enterprises.append({
            "enterprise_id": enterprise.id,
            "username": enterprise.username,
            "company_name": enterprise.company_name,
            "business_license": enterprise.business_license,
            "phone": enterprise.phone,
            "email": enterprise.email,
            "address": enterprise.address,
            "status": enterprise.status,
            "regular_balance": regular_account.balance,
            "gift_balance": gift_account.balance,
            "gift_amount": gift_account.gift_amount
        })

    db.commit()

    yield enterprises, db

    # 清理测试数据
    try:
        for enterprise_data in enterprises:
            enterprise_id = enterprise_data["enterprise_id"]
            
            # 首先删除关联的交易记录
            accounts = db.query(Account).filter(Account.user_id == enterprise_id).all()
            for account in accounts:
                transactions = db.query(AccountTransaction).filter(AccountTransaction.account_id == account.id).all()
                for transaction in transactions:
                    db.delete(transaction)
            
            db.flush()

            # 删除赠送账户
            gift_accounts = db.query(GiftAccount).filter(GiftAccount.user_id == enterprise_id).all()
            for account in gift_accounts:
                db.delete(account)

            # 删除普通账户
            regular_accounts = db.query(RegularAccount).filter(RegularAccount.user_id == enterprise_id).all()
            for account in regular_accounts:
                db.delete(account)

            # 删除企业用户
            enterprise = db.query(Enterprise).filter(Enterprise.id == enterprise_id).first()
            if enterprise:
                db.delete(enterprise)

        db.commit()
    except Exception as e:
        db.rollback()
        print(f"清理测试数据时出错: {e}")
    finally:
        db.close()


def test_search_enterprise_without_filters(account_service, test_enterprises_with_accounts):
    """测试无过滤条件的企业搜索"""
    enterprises, db = test_enterprises_with_accounts

    # 执行搜索
    result = account_service.search_enterprise(db)

    # 验证结果
    assert "data" in result
    assert "total" in result["data"]
    assert "list" in result["data"]
    assert result["data"]["total"] >= 3  # 至少包含我们创建的3个测试企业

    # 验证返回数据中包含企业信息和余额信息
    enterprise_list = result["data"]["list"]
    for enterprise_data in enterprises:
        matching_enterprises = [e for e in enterprise_list if e["id"] == enterprise_data["enterprise_id"]]
        if matching_enterprises:  # 如果找到匹配的企业
            matched_enterprise = matching_enterprises[0]
            assert matched_enterprise["username"] == enterprise_data["username"]
            assert matched_enterprise["company_name"] == enterprise_data["company_name"]
            assert matched_enterprise["regular_balance"] == enterprise_data["regular_balance"]
            assert matched_enterprise["gift_balance"] == enterprise_data["gift_balance"]
            assert matched_enterprise["gift_amount"] == enterprise_data["gift_amount"]


def test_search_enterprise_with_keyword(account_service, test_enterprises_with_accounts):
    """测试使用关键字搜索企业"""
    enterprises, db = test_enterprises_with_accounts
    test_enterprise = enterprises[0]

    # 使用用户名作为关键字搜索
    result = account_service.search_enterprise(db, keyword=test_enterprise["username"])

    # 验证结果
    assert "data" in result
    assert result["data"]["total"] >= 1
    assert any(e["username"] == test_enterprise["username"] for e in result["data"]["list"])

    # 使用企业名称作为关键字搜索
    result = account_service.search_enterprise(db, keyword=test_enterprise["company_name"])

    # 验证结果
    assert "data" in result
    assert result["data"]["total"] >= 1
    assert any(e["company_name"] == test_enterprise["company_name"] for e in result["data"]["list"])


def test_search_enterprise_with_phone(account_service, test_enterprises_with_accounts):
    """测试使用电话号码搜索企业"""
    enterprises, db = test_enterprises_with_accounts
    test_enterprise = enterprises[0]

    # 使用完整手机号搜索
    result = account_service.search_enterprise(db, phone=test_enterprise["phone"])

    # 验证结果
    assert "data" in result
    assert result["data"]["total"] >= 1
    assert any(e["phone"] == test_enterprise["phone"] for e in result["data"]["list"])

    # 使用部分手机号搜索
    partial_phone = test_enterprise["phone"][:5]
    result = account_service.search_enterprise(db, phone=partial_phone)

    # 验证结果
    assert "data" in result
    assert result["data"]["total"] >= 1
    assert any(test_enterprise["phone"] in e["phone"] for e in result["data"]["list"])


def test_search_enterprise_with_company_name(account_service, test_enterprises_with_accounts):
    """测试使用企业名称搜索"""
    enterprises, db = test_enterprises_with_accounts
    test_enterprise = enterprises[0]

    # 使用完整企业名称搜索
    result = account_service.search_enterprise(db, company_name=test_enterprise["company_name"])

    # 验证结果
    assert "data" in result
    assert result["data"]["total"] >= 1
    assert any(e["company_name"] == test_enterprise["company_name"] for e in result["data"]["list"])

    # 使用部分企业名称搜索
    partial_name = test_enterprise["company_name"][:2]
    result = account_service.search_enterprise(db, company_name=partial_name)

    # 验证结果
    assert "data" in result
    assert result["data"]["total"] >= 1
    assert any(partial_name in e["company_name"] for e in result["data"]["list"])


def test_search_enterprise_with_status(account_service, test_enterprises_with_accounts):
    """测试使用状态搜索企业"""
    enterprises, db = test_enterprises_with_accounts

    # 搜索活跃企业
    result = account_service.search_enterprise(db, status=Status.ACTIVE)

    # 验证结果
    assert "data" in result
    assert result["data"]["total"] >= 2  # 我们创建了2个活跃企业
    active_enterprises = [e for e in result["data"]["list"] if e["status"] == Status.ACTIVE]
    assert len(active_enterprises) >= 2

    # 搜索非活跃企业
    result = account_service.search_enterprise(db, status=Status.INACTIVE)

    # 验证结果
    assert "data" in result
    assert result["data"]["total"] >= 1  # 我们创建了1个非活跃企业
    inactive_enterprises = [e for e in result["data"]["list"] if e["status"] == Status.INACTIVE]
    assert len(inactive_enterprises) >= 1


def test_search_enterprise_with_pagination(account_service, test_enterprises_with_accounts):
    """测试企业搜索分页功能"""
    enterprises, db = test_enterprises_with_accounts

    # 测试limit
    result = account_service.search_enterprise(db, limit=1)

    # 验证结果
    assert "data" in result
    assert len(result["data"]["list"]) == 1

    # 测试skip
    result1 = account_service.search_enterprise(db, limit=1, skip=0)
    result2 = account_service.search_enterprise(db, limit=1, skip=1)

    # 验证两次查询返回不同的企业
    if result1["data"]["list"] and result2["data"]["list"]:
        assert result1["data"]["list"][0]["id"] != result2["data"]["list"][0]["id"]


def test_get_enterprise_accounts(db, account_service, test_enterprises_with_accounts):
    """测试获取企业账户信息"""
    enterprises, db = test_enterprises_with_accounts
    test_enterprise = enterprises[0]
    enterprise_id = test_enterprise["enterprise_id"]
    
    # 调用测试方法
    result = account_service.get_enterprise_accounts(db, enterprise_id)
    
    # 验证结果
    assert result["data"]["enterprise_info"]["id"] == enterprise_id
    assert result["data"]["enterprise_info"]["username"] == test_enterprise["username"]
    assert result["data"]["enterprise_info"]["company_name"] == test_enterprise["company_name"]
    assert result["data"]["enterprise_info"]["phone"] == test_enterprise["phone"]
    assert result["data"]["enterprise_info"]["email"] == test_enterprise["email"]
    assert result["data"]["enterprise_info"]["business_license"] == test_enterprise["business_license"]
    
    # 检查账户信息
    assert result["data"]["account_info"]["regular_account"]["balance"] == test_enterprise["regular_balance"]
    assert result["data"]["account_info"]["gift_account"]["balance"] == test_enterprise["gift_balance"]
    assert result["data"]["account_info"]["gift_account"]["gift_amount"] == test_enterprise["gift_amount"]
    
    # 测试企业不存在的情况
    with pytest.raises(ValueError):
        account_service.get_enterprise_accounts(db, 99999)


def test_get_enterprise_transactions(db, account_service, test_enterprises_with_accounts):
    """测试获取企业的交易记录"""
    enterprises, db = test_enterprises_with_accounts
    test_enterprise = enterprises[0]
    enterprise_id = test_enterprise["enterprise_id"]

    # 开始事务
    db.begin_nested()

    try:
        # 创建测试交易记录
        from app.models.account import AccountTransaction, Account, TransactionType
        from app.models.order import Order, OrderStatus, PaymentStatus, PaymentMethod, OrderType, RechargeOrder
        from app.utils.common import get_current_time

        # 查找企业的账户
        accounts = db.query(Account).filter(Account.user_id == enterprise_id).all()
        assert len(accounts) >= 1
        account = accounts[0]

        # 创建测试订单 - 使用RechargeOrder子类
        order = RechargeOrder(
            order_no="TEST_ORDER_ENTERPRISE_" + str(enterprise_id),
            user_id=enterprise_id,
            status=OrderStatus.PENDING,
            payment_status=PaymentStatus.UNPAID,
            total_amount=1000.0,
            payable_amount=1000.0,
            actual_amount_paid=1000.0,
            payment_method=PaymentMethod.BANK_TRANSFER,
            type=OrderType.RECHARGE,  # 确保类型与RechargeOrder匹配
            created_at=get_current_time(),
            updated_at=get_current_time()
        )
        db.add(order)
        db.flush()

        # 创建多条交易记录
        transactions = []
        for i in range(3):
            transaction = AccountTransaction(
                account_id=account.id,
                order_id=order.id,
                transaction_type=TransactionType.PAYMENT,
                amount=500.0 * (i + 1),
                transaction_time=get_current_time(),
                description=f"企业测试交易 {i + 1}"
            )
            db.add(transaction)
            transactions.append(transaction)

        db.commit()

        # 测试获取交易记录
        result = account_service.get_enterprise_transactions(session=db, enterprise_id=enterprise_id)

        # 验证结果
        assert "data" in result
        assert "total" in result["data"]
        assert "list" in result["data"]
        assert result["data"]["total"] >= 3  # 至少包含我们创建的3条记录

        # 验证分页功能
        result_page_1 = account_service.get_enterprise_transactions(session=db, enterprise_id=enterprise_id, skip=0,
                                                                    limit=2)
        result_page_2 = account_service.get_enterprise_transactions(session=db, enterprise_id=enterprise_id, skip=2,
                                                                    limit=2)

        assert len(result_page_1["data"]["list"]) == 2
        if len(result_page_2["data"]["list"]) > 0:
            assert result_page_1["data"]["list"][0]["id"] != result_page_2["data"]["list"][0]["id"]

        # 验证返回数据包含所需字段
        transaction_list = result["data"]["list"]
        if transaction_list:
            transaction = transaction_list[0]
            assert "id" in transaction
            assert "username" in transaction
            assert "account_id" in transaction
            assert "account_type" in transaction
            assert "order_id" in transaction
            assert "order_no" in transaction
            assert "payment_method" in transaction
            assert "transaction_type" in transaction
            assert "amount" in transaction
            assert "transaction_time" in transaction
            assert "description" in transaction

    except Exception as e:
        # 发生异常时回滚事务
        db.rollback()
        raise e
    finally:
        try:
            # 清理测试数据，确保删除所有可能的外键引用
            # 先删除交易记录
            for transaction in transactions:
                db.delete(transaction)
            db.flush()

            # 再删除订单
            db.delete(order)
            db.commit()
        except Exception as e:
            # 清理失败时回滚事务
            db.rollback()
            print(f"清理测试数据时出错: {e}")