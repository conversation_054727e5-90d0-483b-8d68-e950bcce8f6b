import pprint
from datetime import datetime
from unittest.mock import MagicMock, patch

import pytest
from sqlalchemy.orm import Session

from app.models import Content
from app.models.enum import Status
from app.models.order import OrderStatus, PaymentStatus, PaymentMethod
from app.models.product import Product, ProductType
from app.models.rule import Rule, RuleItem
from app.models.user import User
from app.schemas.order import OrderDetailResponse
from app.service.order import OrderService
from app.service.revervation import reservation_service


@pytest.fixture
def mock_session():
    """模拟数据库会话"""
    return MagicMock(spec=Session)


@pytest.fixture
def order_service():
    """订单服务实例"""
    return OrderService()


@pytest.fixture
def mock_user():
    """模拟用户"""
    user = MagicMock(spec=User)
    user.id = 1
    user.username = "test_user"
    return user


@pytest.fixture
def mock_content():
    content = MagicMock(spec=Content)
    content.id = 1
    content.title = "测试内容"
    content.content = "这是一个测试内容"
    content.status = Status.ACTIVE


@pytest.fixture
def mock_product(mock_content):
    """模拟产品"""
    product = MagicMock(spec=Product)
    product.id = 1
    product.name = "测试产品"
    product.price = 100.0
    product.stock = 10
    product.type = ProductType.PRODUCT
    product.contents.add(mock_content)
    return product


@pytest.fixture
def mock_reservation_product():
    """模拟可预订产品"""
    product = MagicMock(spec=Product)
    product.id = 2
    product.name = "预订产品"
    product.price = 200.0
    product.stock = 5
    product.type = ProductType.RESERVATION
    return product


@pytest.fixture
def mock_rule():
    """模拟规则"""
    rule = MagicMock(spec=Rule)
    rule.id = 1
    rule.name = "测试规则"
    rule.rule_items = [MagicMock(quantity=3)]
    return rule


@pytest.fixture
def mock_rule_item(mock_rule):
    """模拟规则"""
    rule_item = MagicMock(spec=RuleItem)
    rule_item.id = 1
    rule_item.rule_id = mock_rule.id
    rule_item.name = "测试规则"
    return rule_item


@pytest.fixture
def mock_order():
    """模拟订单"""
    order = MagicMock(spec=OrderDetailResponse)
    order.id = 1
    order.user_id = 1
    order.status = OrderStatus.PENDING
    order.payment_status = PaymentStatus.UNPAID
    order.total_amount = 200.0
    order.actual_amount_paid = 200.0
    order.payment_method = PaymentMethod.ACCOUNT_BALANCE
    order.items = []
    return order


def test_data(mock_session, mock_user, mock_product, mock_order):
    """测试数据"""
    # 模拟DAO层方法
    with patch('app.dao.user.user_dao.get', return_value=mock_user), \
            patch('app.dao.product.product_dao.get', return_value=mock_product), \
            patch('app.dao.order.order_dao.get', return_value=mock_order):
        # 准备测试数据
        products = [
            {

            }
        ]


def test_create_order(order_service, mock_session, mock_user, mock_product):
    """测试创建普通订单"""
    # 模拟订单对象 - 确保在patch之前定义
    mock_order = MagicMock(spec=OrderDetailResponse)
    mock_order.id = 1
    mock_order.user_id = mock_user.id
    mock_order.status = OrderStatus.PENDING
    mock_order.payment_status = PaymentStatus.UNPAID
    mock_order.total_amount = 200.0
    mock_order.actual_amount_paid = 200.0
    mock_order.items = []

    # 模拟DAO层方法
    with patch('app.dao.user.user_dao.get', return_value=mock_user), \
            patch('app.dao.product.product_dao.get', return_value=mock_product), \
            patch('app.dao.product.product_dao.get_pricing_strategies_by_product', return_value=[]), \
            patch('app.dao.pricing.pricing_strategy_dao.get_strategies_by_scope', return_value=[]), \
            patch('app.dao.order.order_dao.create', return_value=mock_order), \
            patch('app.dao.order.order_item_dao.get_by_order', return_value=[]):
        # 准备测试数据
        products = [
            {
                "product_id": mock_product.id,
                "quantity": 2,
                "reservation_requests": []
            }
        ]

        # 执行测试
        result = order_service.create_order(mock_session, mock_user.id, products)

        # 验证结果
        assert result is not None
        assert result.id == mock_order.id
        assert result.user_id == mock_user.id
        assert result.status == OrderStatus.PENDING
        assert result.payment_status == PaymentStatus.UNPAID


def test_create_order_with_reservation(order_service, mock_session, mock_user, mock_reservation_product, mock_rule,
                                       mock_rule_item):
    """测试创建带预订的订单"""
    # 模拟订单对象
    mock_order = MagicMock(spec=OrderDetailResponse)
    mock_order.id = 1
    mock_order.user_id = mock_user.id
    mock_order.status = OrderStatus.PENDING
    mock_order.payment_status = PaymentStatus.UNPAID
    mock_order.total_amount = 400.0
    mock_order.payable_amount = 380.0
    mock_order.actual_amount_paid = 380.0

    # 模拟订单项
    mock_order_item = MagicMock()
    mock_order_item.id = 1
    mock_order_item.product_id = mock_reservation_product.id
    mock_order_item.quantity = 2
    mock_order_item.price = 200.0
    mock_order_item.subtotal = 400.0
    mock_order_item.reservation_requests = []

    # 模拟DAO层方法
    with patch('app.dao.user.user_dao.get', return_value=mock_user), \
            patch('app.dao.product.product_dao.get', return_value=mock_reservation_product), \
            patch('app.dao.product.product_dao.get_pricing_strategies_by_product', return_value=[]), \
            patch('app.dao.pricing.pricing_strategy_dao.get_strategies_by_scope', return_value=[]), \
            patch('app.dao.rule.rule_dao.get', return_value=mock_rule), \
            patch('app.dao.order.order_dao.create', return_value=mock_order), \
            patch('app.dao.order.order_item_dao.get_by_order', return_value=[mock_order_item]), \
            patch('app.dao.reservation.reservation_request_dao.create', return_value=None):
        # 准备测试数据
        products = [
            {
                "product_id": mock_reservation_product.id,
                "quantity": 2,
                "reservation_requests": [
                    {
                        "rule_id": mock_rule.id,
                        "rule_item_id": mock_rule_item.id,
                        "reservation_period": "2023-10-01",
                        "reservation_time": datetime.now()
                    }
                ]
            }
        ]

        # 执行测试
        result = order_service.create_order(mock_session, mock_user.id, products)

        # 验证结果
        assert result is not None
        assert result.id == mock_order.id
        assert result.user_id == mock_user.id
        assert result.status == OrderStatus.PENDING
        assert result.payment_status == PaymentStatus.UNPAID


def test_create_order_with_discount(order_service, mock_session, mock_user, mock_product):
    """测试创建带折扣的订单"""
    # 模拟折扣策略
    mock_discount_strategy = MagicMock()
    mock_discount_strategy.discount_rate = 0.8

    # 模拟订单对象
    mock_order = MagicMock(spec=OrderDetailResponse)
    mock_order.id = 1
    mock_order.user_id = mock_user.id
    mock_order.status = OrderStatus.PENDING
    mock_order.payment_status = PaymentStatus.UNPAID
    mock_order.total_amount = 160.0  # 打8折: 100 * 2 * 0.8
    mock_order.actual_amount_paid = 160.0

    # 模拟DAO层方法
    with patch('app.dao.user.user_dao.get', return_value=mock_user), \
            patch('app.dao.product.product_dao.get', return_value=mock_product), \
            patch('app.dao.product.product_dao.get_pricing_strategies_by_product',
                  return_value=[mock_discount_strategy]), \
            patch('app.dao.pricing.pricing_strategy_dao.get_strategies_by_scope', return_value=[]), \
            patch('app.dao.order.order_dao.create', return_value=mock_order), \
            patch('app.dao.order.order_item_dao.get_by_order', return_value=[]):
        # 准备测试数据
        products = [
            {
                "product_id": mock_product.id,
                "quantity": 2,
                "reservation_requests": []
            }
        ]

        # 执行测试
        result = order_service.create_order(mock_session, mock_user.id, products)

        # 验证结果
        assert result is not None
        assert result.id == mock_order.id
        assert result.user_id == mock_user.id
        assert result.status == OrderStatus.PENDING
        assert result.payment_status == PaymentStatus.UNPAID


def test_pay_order(order_service, mock_session, mock_order):
    """测试支付订单"""
    # 模拟更新后的订单
    mock_updated_order = MagicMock(spec=OrderDetailResponse)
    mock_updated_order.id = mock_order.id
    mock_updated_order.status = OrderStatus.PAID
    mock_updated_order.payment_status = PaymentStatus.PAID
    mock_updated_order.payment_method = PaymentMethod.ALIPAY

    # 模拟DAO层方法
    with patch('app.dao.order.order_dao.get', return_value=mock_order), \
            patch('app.dao.order.order_dao.update', return_value=mock_updated_order), \
            patch('app.dao.order.order_item_dao.get_by_order', return_value=[]):
        # 执行测试
        result = order_service.pay_order(mock_session, mock_order.id, PaymentMethod.ALIPAY)

        # 验证结果
        assert result is not None
        assert result.id == mock_order.id
        assert result.status == OrderStatus.PAID
        assert result.payment_status == PaymentStatus.PAID
        assert result.payment_method == PaymentMethod.ALIPAY


def test_cancel_order(order_service, mock_session, mock_order, mock_product):
    """测试取消订单"""
    # 模拟订单项
    mock_order_item = MagicMock()
    mock_order_item.id = 1
    mock_order_item.product_id = mock_product.id
    mock_order_item.quantity = 2
    mock_order_item.reservation_requests = []

    # 模拟更新后的订单
    mock_cancelled_order = MagicMock(spec=OrderDetailResponse)
    mock_cancelled_order.id = mock_order.id
    mock_cancelled_order.status = OrderStatus.CANCELLED

    # 模拟DAO层方法
    with patch('app.dao.order.order_dao.get', return_value=mock_order), \
            patch('app.dao.order.order_dao.update', return_value=mock_cancelled_order), \
            patch('app.dao.order.order_item_dao.get_by_order', return_value=[mock_order_item]), \
            patch('app.dao.product.product_dao.get', return_value=mock_product):
        # 执行测试
        result = order_service.cancel_order(mock_session, mock_order.id)

        # 验证结果
        assert result is not None
        assert result.id == mock_order.id
        assert result.status == OrderStatus.CANCELLED

        # 验证库存已恢复
        mock_product.stock += mock_order_item.quantity
        mock_session.commit.assert_called()

