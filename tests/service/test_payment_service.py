import pytest

from app.dao.user import enterprise_user_relation_dao
from app.models.enum import Status
from app.models.order import OrderStatus, PaymentStatus, PaymentMethod
from app.models.reservation import ReservationStatus
from app.schemas.user import EnterpriseUserRelationCreate
from app.service.order import order_service
from app.service.payment import payment_service
from app.service.revervation import reservation_service


def test_pay_order_with_account_balance(db, create_test_user, create_test_enterprise, get_reservation_prodct,
                                        get_reservation_rule):
    """测试使用个人账户余额支付订单"""
    # 获取可用预约时间段
    availability_list = reservation_service.get_availability_list(db, get_reservation_rule.id)
    assert availability_list, "应该有可用的预约时间段"
    chosen_reservation_requests = availability_list[:1]

    # 创建订单
    user_id = create_test_user.id
    products = [
        {
            "product_id": get_reservation_prodct.id,
            "quantity": 1,
            "reservation_requests": chosen_reservation_requests
        }
    ]

    order = order_service.create_order(db, user_id, products)

    # 检查用户账户余额
    accounts = create_test_user.accounts
    initial_balance = 0
    for account in accounts:
        if account.type == "regular":
            initial_balance = account.balance
            break

    # 支付订单
    payment_info = {
        "payment_method": PaymentMethod.ACCOUNT_BALANCE
    }
    paid_order = payment_service.pay_order(db, order.id, payment_info)

    # 验证订单支付成功
    assert paid_order.payment_status == PaymentStatus.PAID, "支付状态应为已支付"
    assert paid_order.payment_method == PaymentMethod.ACCOUNT_BALANCE, "支付方式应为账户余额"
    assert paid_order.actual_amount_paid == paid_order.payable_amount, "实际支付金额应等于应付金额"
    assert paid_order.payment_time is not None, "支付时间不应为空"

    # 验证账户余额已减少
    db.refresh(create_test_user)
    for account in create_test_user.accounts:
        if account.type == "regular":
            assert account.balance == initial_balance - paid_order.payable_amount, "账户余额应减少"

    # 验证预订请求状态更新和验证码生成
    for item in paid_order.items:
        for req in item.reservation_requests:
            assert req.status == ReservationStatus.PAID_FULL, "预订请求状态应为已支付"
            assert req.verification_code is not None, "验证码不应为空"
            assert isinstance(req.verification_code, str), "验证码应为字符串"
            assert len(req.verification_code) > 0, "验证码长度应大于0"


def test_pay_order_with_insufficient_balance(db, create_test_user, create_test_enterprise, get_reservation_prodct,
                                             get_reservation_rule):
    """测试余额不足的情况"""
    # 获取可用预约时间段
    availability_list = reservation_service.get_availability_list(db, get_reservation_rule.id)
    assert availability_list, "应该有可用的预约时间段"
    chosen_reservation_requests = availability_list[:1]

    # 创建订单
    user_id = create_test_user.id
    products = [
        {
            "product_id": get_reservation_prodct.id,
            "quantity": 10,  # 增加数量，使金额变大
            "reservation_requests": chosen_reservation_requests
        }
    ]

    order = order_service.create_order(db, user_id, products)

    # 将用户账户余额设置为0
    for account in create_test_user.accounts:
        if account.type == "regular":
            account.balance = 0
            db.commit()
            break

    # 支付订单，预期抛出异常
    payment_info = {
        "payment_method": PaymentMethod.ACCOUNT_BALANCE
    }
    with pytest.raises(ValueError) as exc_info:
        payment_service.pay_order(db, order.id, payment_info)

    # 验证异常信息
    assert "用户余额不足" in str(exc_info.value)

    # 验证订单状态未改变
    db.refresh(order)
    assert order.payment_status == PaymentStatus.UNPAID, "支付状态应为未支付"
    assert order.actual_amount_paid == 0, "实际支付金额应为0"


def test_pay_order_with_enterprise_balance(db, create_test_user, create_test_enterprise, get_reservation_prodct,
                                           get_reservation_rule):
    """测试使用企业账户余额支付订单"""
    # 用户加入企业
    relation = EnterpriseUserRelationCreate(
        personal_user_id=create_test_user.id,
        enterprise_id=create_test_enterprise.id
    )
    enterprise_user_relation_dao.create(db, relation)

    # 获取可用预约时间段
    availability_list = reservation_service.get_availability_list(db, get_reservation_rule.id)
    assert availability_list, "应该有可用的预约时间段"
    chosen_reservation_requests = availability_list[:1]

    # 创建订单
    user_id = create_test_user.id
    products = [
        {
            "product_id": get_reservation_prodct.id,
            "quantity": 1,
            "reservation_requests": chosen_reservation_requests
        }
    ]

    order = order_service.create_order(db, user_id, products)

    # 检查企业账户余额
    enterprise_accounts = create_test_enterprise.accounts
    initial_balance = 0
    for account in enterprise_accounts:
        if account.type == "regular":
            initial_balance = account.balance
            break

    # 准备企业支付信息
    payment_info = {
        "payment_method": PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE,
        "enterprise_id": create_test_enterprise.id
    }

    # 支付订单
    paid_order = payment_service.pay_order(db, order.id, payment_info)

    # 验证订单支付成功
    assert paid_order.payment_status == PaymentStatus.PAID, "支付状态应为已支付"
    assert paid_order.payment_method == PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE, "支付方式应为企业账户余额"
    assert paid_order.actual_amount_paid == paid_order.payable_amount, "实际支付金额应等于应付金额"

    # 验证企业账户余额已减少
    db.refresh(create_test_enterprise)
    for account in create_test_enterprise.accounts:
        if account.type == "regular":
            assert account.balance == initial_balance - paid_order.payable_amount, "企业账户余额应减少"

    # 验证预订请求
    for item in paid_order.items:
        for req in item.reservation_requests:
            assert req.status == ReservationStatus.PAID_FULL, "预订请求状态应为已支付"
            assert req.verification_code is not None, "验证码不应为空"


def test_pay_order_with_inactive_relation(db, create_test_user, create_test_enterprise, get_reservation_prodct,
                                          get_reservation_rule):
    """测试用户与企业关系非活跃状态的情况"""
    # 用户加入企业，但关系设为非活跃
    relation = EnterpriseUserRelationCreate(
        personal_user_id=create_test_user.id,
        enterprise_id=create_test_enterprise.id
    )
    relation_obj = enterprise_user_relation_dao.create(db, relation)
    relation_obj.relation_status = Status.INACTIVE
    db.commit()

    # 获取可用预约时间段
    availability_list = reservation_service.get_availability_list(db, get_reservation_rule.id)
    assert availability_list, "应该有可用的预约时间段"
    chosen_reservation_requests = availability_list[:1]

    # 创建订单
    user_id = create_test_user.id
    products = [
        {
            "product_id": get_reservation_prodct.id,
            "quantity": 1,
            "reservation_requests": chosen_reservation_requests
        }
    ]

    order = order_service.create_order(db, user_id, products)

    # 准备企业支付信息
    payment_info = {
        "payment_method": PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE,
        "enterprise_id": create_test_enterprise.id
    }

    # 支付订单，预期抛出异常
    with pytest.raises(ValueError) as exc_info:
        payment_service.pay_order(db, order.id, payment_info)

    # 验证异常信息
    assert "用户与企业不存在有效关系" in str(exc_info.value)

    # 验证订单状态未改变
    db.refresh(order)
    assert order.payment_status == PaymentStatus.UNPAID, "支付状态应为未支付"


def test_pay_order_with_unsupported_payment_method(db, create_test_user, create_test_enterprise, get_reservation_prodct,
                                                   get_reservation_rule):
    """测试不支持的支付方式"""
    # 获取可用预约时间段
    availability_list = reservation_service.get_availability_list(db, get_reservation_rule.id)
    assert availability_list, "应该有可用的预约时间段"
    chosen_reservation_requests = availability_list[:1]

    # 创建订单
    user_id = create_test_user.id
    products = [
        {
            "product_id": get_reservation_prodct.id,
            "quantity": 1,
            "reservation_requests": chosen_reservation_requests
        }
    ]

    order = order_service.create_order(db, user_id, products)

    # 支付订单，使用不支持的支付方式
    payment_info = {
        "payment_method": PaymentMethod.WECHAT_PAY
    }

    # 预期抛出异常
    with pytest.raises(ValueError) as exc_info:
        payment_service.pay_order(db, order.id, payment_info)

    # 验证异常信息
    assert "微信支付不支持" in str(exc_info.value)

    # 验证订单状态未改变
    db.refresh(order)
    assert order.payment_status == PaymentStatus.UNPAID, "支付状态应为未支付"


def test_pay_order_with_invalid_order_status(db, create_test_user, create_test_enterprise, get_reservation_prodct,
                                             get_reservation_rule):
    """测试订单状态不正确的情况"""
    # 获取可用预约时间段
    availability_list = reservation_service.get_availability_list(db, get_reservation_rule.id)
    assert availability_list, "应该有可用的预约时间段"
    chosen_reservation_requests = availability_list[:1]

    # 创建订单
    user_id = create_test_user.id
    products = [
        {
            "product_id": get_reservation_prodct.id,
            "quantity": 1,
            "reservation_requests": chosen_reservation_requests
        }
    ]

    order = order_service.create_order(db, user_id, products)

    # 将订单状态设为已支付
    order.status = OrderStatus.PAID
    db.commit()

    # 支付订单，预期抛出异常
    payment_info = {
        "payment_method": PaymentMethod.ACCOUNT_BALANCE
    }
    with pytest.raises(ValueError) as exc_info:
        payment_service.pay_order(db, order.id, payment_info)

    # 验证异常信息
    assert "订单状态不正确" in str(exc_info.value)


def test_pay_order_with_invalid_payment_status(db, create_test_user, create_test_enterprise, get_reservation_prodct,
                                               get_reservation_rule):
    """测试订单支付状态不正确的情况"""
    # 获取可用预约时间段
    availability_list = reservation_service.get_availability_list(db, get_reservation_rule.id)
    assert availability_list, "应该有可用的预约时间段"
    chosen_reservation_requests = availability_list[:1]

    # 创建订单
    user_id = create_test_user.id
    products = [
        {
            "product_id": get_reservation_prodct.id,
            "quantity": 1,
            "reservation_requests": chosen_reservation_requests
        }
    ]

    order = order_service.create_order(db, user_id, products)

    # 将订单支付状态设为已支付
    order.payment_status = PaymentStatus.PAID
    db.commit()

    # 支付订单，预期抛出异常
    payment_info = {
        "payment_method": PaymentMethod.ACCOUNT_BALANCE
    }
    with pytest.raises(ValueError) as exc_info:
        payment_service.pay_order(db, order.id, payment_info)

    # 验证异常信息
    assert "订单支付状态不正确" in str(exc_info.value)


def test_pay_order_missing_enterprise_id(db, create_test_user, create_test_enterprise, get_reservation_prodct,
                                         get_reservation_rule):
    """测试缺少企业ID的情况"""
    # 用户加入企业
    relation = EnterpriseUserRelationCreate(
        personal_user_id=create_test_user.id,
        enterprise_id=create_test_enterprise.id
    )
    enterprise_user_relation_dao.create(db, relation)

    # 获取可用预约时间段
    availability_list = reservation_service.get_availability_list(db, get_reservation_rule.id)
    assert availability_list, "应该有可用的预约时间段"
    chosen_reservation_requests = availability_list[:1]

    # 创建订单
    user_id = create_test_user.id
    products = [
        {
            "product_id": get_reservation_prodct.id,
            "quantity": 1,
            "reservation_requests": chosen_reservation_requests
        }
    ]

    order = order_service.create_order(db, user_id, products)

    # 准备企业支付信息，但缺少企业ID
    payment_info = {
        "payment_method": PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE
        # 缺少 enterprise_id
    }

    # 支付订单，预期抛出异常
    with pytest.raises(ValueError) as exc_info:
        payment_service.pay_order(db, order.id, payment_info)

    # 验证异常信息
    assert "缺少企业ID" in str(exc_info.value)

    # 验证订单状态未改变
    db.refresh(order)
    assert order.payment_status == PaymentStatus.UNPAID, "支付状态应为未支付"
