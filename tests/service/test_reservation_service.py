from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

import pytest
from sqlalchemy.orm import Session

from app.models.product import Product
from app.models.reservation import ReservationRequest, ReservationStatus
from app.models.rule import Rule, RuleItem
from app.service.revervation import reservation_service


@pytest.fixture
def mock_session():
    """模拟数据库会话"""
    return MagicMock(spec=Session)


@pytest.fixture
def mock_product():
    """模拟产品"""
    product = MagicMock(spec=Product)
    product.id = 1
    product.name = "测试产品"
    return product


@pytest.fixture
def mock_rule():
    """模拟规则"""
    rule = MagicMock(spec=Rule)
    rule.id = 1
    rule.name = "测试规则"
    rule.products = [MagicMock(id=1)]  # 关联产品ID为1
    return rule


@pytest.fixture
def mock_rule_item_valid_time():
    """模拟规则项 - 在有效时间范围内"""
    now = datetime.now()
    rule_item = MagicMock(spec=RuleItem)
    rule_item.id = 1
    rule_item.rule_id = 1
    rule_item.name = "规则项1"
    rule_item.start_time = now - timedelta(days=1)  # 昨天
    rule_item.end_time = now + timedelta(days=1)  # 明天
    rule_item.quantity = 5
    return rule_item


@pytest.fixture
def mock_rule_item_expired():
    """模拟规则项 - 已过期"""
    now = datetime.now()
    rule_item = MagicMock(spec=RuleItem)
    rule_item.id = 2
    rule_item.rule_id = 1
    rule_item.name = "过期规则项"
    rule_item.start_time = now - timedelta(days=2)  # 前天
    rule_item.end_time = now - timedelta(days=1)  # 昨天
    rule_item.quantity = 5
    return rule_item


@pytest.fixture
def mock_rule_item_future():
    """模拟规则项 - 未来开始"""
    now = datetime.now()
    rule_item = MagicMock(spec=RuleItem)
    rule_item.id = 3
    rule_item.rule_id = 1
    rule_item.name = "未来规则项"
    rule_item.start_time = now + timedelta(days=1)  # 明天
    rule_item.end_time = now + timedelta(days=2)  # 后天
    rule_item.quantity = 5
    return rule_item


@pytest.fixture
def mock_rule_without_product():
    """模拟规则 - 未绑定产品"""
    rule = MagicMock(spec=Rule)
    rule.id = 2
    rule.name = "无产品规则"
    rule.products = [MagicMock(id=2)]  # 关联产品ID为2，而非1
    return rule


@pytest.fixture
def mock_rule_item_without_product():
    """模拟规则项 - 对应的规则未绑定产品"""
    rule_item = MagicMock(spec=RuleItem)
    rule_item.id = 4
    rule_item.rule_id = 2  # 对应mock_rule_without_product
    rule_item.name = "无产品规则项"
    rule_item.start_time = None
    rule_item.end_time = None
    rule_item.quantity = 5
    return rule_item


@pytest.fixture
def mock_reservations_empty():
    """模拟空的预约请求列表"""
    return []


@pytest.fixture
def mock_reservations_full():
    """模拟已满的预约请求列表"""
    reservations = []
    for i in range(5):  # 创建5个预约请求，对应mock_rule_item_valid_time的quantity
        reservation = MagicMock(spec=ReservationRequest)
        reservation.id = i + 1
        reservation.product_id = 1
        reservation.rule_id = 1
        reservation.rule_item_id = 1  # 添加rule_item_id，对应mock_rule_item_valid_time的id
        reservation.status = ReservationStatus.PENDING
        reservations.append(reservation)
    return reservations


def test_is_can_reserved_success(
        mock_session,
        mock_product,
        mock_rule,
        mock_rule_item_valid_time,
        mock_reservations_empty
):
    """测试预约成功的情况"""
    with patch('app.dao.rule.rule_item_dao.get', return_value=mock_rule_item_valid_time), \
            patch('app.dao.rule.rule_dao.get', return_value=mock_rule), \
            patch('sqlalchemy.orm.Session.query') as mock_query:
        # 模拟查询结果
        mock_query.return_value.filter.return_value.all.return_value = mock_reservations_empty

        # 执行测试
        result, message = reservation_service.is_can_reserved(
            mock_session,
            [mock_rule_item_valid_time.id],
            mock_product.id
        )

        # 验证结果
        assert result is True
        assert message == "可以预约"


def test_is_can_reserved_expired(
        mock_session,
        mock_product,
        mock_rule,
        mock_rule_item_expired
):
    """测试预约时间已过期的情况"""
    with patch('app.dao.rule.rule_item_dao.get', return_value=mock_rule_item_expired), \
            patch('app.dao.rule.rule_dao.get', return_value=mock_rule):
        # 执行测试
        result, message = reservation_service.is_can_reserved(
            mock_session,
            [mock_rule_item_expired.id],
            mock_product.id
        )

        # 验证结果
        assert result is False
        assert "不在可预订时间范围内" in message


def test_is_can_reserved_future(
        mock_session,
        mock_product,
        mock_rule,
        mock_rule_item_future
):
    """测试预约时间未开始的情况"""
    with patch('app.dao.rule.rule_item_dao.get', return_value=mock_rule_item_future), \
            patch('app.dao.rule.rule_dao.get', return_value=mock_rule):
        # 执行测试
        result, message = reservation_service.is_can_reserved(
            mock_session,
            [mock_rule_item_future.id],
            mock_product.id
        )

        # 验证结果
        assert result is False
        assert "不在可预订时间范围内" in message


def test_is_can_reserved_without_product(
        mock_session,
        mock_product,
        mock_rule_without_product,
        mock_rule_item_without_product
):
    """测试规则未绑定产品的情况"""
    with patch('app.dao.rule.rule_item_dao.get', return_value=mock_rule_item_without_product), \
            patch('app.dao.rule.rule_dao.get', return_value=mock_rule_without_product):
        # 执行测试
        result, message = reservation_service.is_can_reserved(
            mock_session,
            [mock_rule_item_without_product.id],
            mock_product.id
        )

        # 验证结果
        assert result is False
        assert "未绑定产品" in message


def test_is_can_reserved_full(
        mock_session,
        mock_product,
        mock_rule,
        mock_rule_item_valid_time,
        mock_reservations_full
):
    """测试预约数量已满的情况"""
    with patch('app.dao.rule.rule_item_dao.get', return_value=mock_rule_item_valid_time), \
            patch('app.dao.rule.rule_dao.get', return_value=mock_rule):
        # 直接在这里模拟数据库查询，不使用patch装饰器
        mock_session.query.return_value.filter.return_value.all.return_value = mock_reservations_full

        # 执行测试
        result, message = reservation_service.is_can_reserved(
            mock_session,
            [mock_rule_item_valid_time.id],
            mock_product.id
        )

        # 验证结果
        assert result is False
        assert "预约数量已达上限" in message


def test_is_can_reserved_multiple_items(
        mock_session,
        mock_product,
        mock_rule,
        mock_rule_item_valid_time,
        mock_reservations_empty
):
    """测试多个规则项的情况"""
    with patch('app.dao.rule.rule_item_dao.get', return_value=mock_rule_item_valid_time), \
            patch('app.dao.rule.rule_dao.get', return_value=mock_rule), \
            patch('sqlalchemy.orm.Session.query') as mock_query:
        # 模拟查询结果
        mock_query.return_value.filter.return_value.all.return_value = mock_reservations_empty

        # 执行测试 - 传入相同ID两次，模拟多个规则项
        result, message = reservation_service.is_can_reserved(
            mock_session,
            [mock_rule_item_valid_time.id, mock_rule_item_valid_time.id],
            mock_product.id
        )

        # 验证结果
        assert result is True
        assert message == "可以预约"


def test_is_can_reserved_nonexistent_rule_item(
        mock_session,
        mock_product
):
    """测试规则项不存在的情况"""
    with patch('app.dao.rule.rule_item_dao.get', return_value=None):
        # 执行测试
        result, message = reservation_service.is_can_reserved(
            mock_session,
            [999],  # 不存在的ID
            mock_product.id
        )

        # 验证结果
        assert result is False
        assert "规则项 999 不存在" in message


def test_is_can_reserved_nonexistent_rule(
        mock_session,
        mock_product,
        mock_rule_item_valid_time
):
    """测试规则不存在的情况"""
    with patch('app.dao.rule.rule_item_dao.get', return_value=mock_rule_item_valid_time), \
            patch('app.dao.rule.rule_dao.get', return_value=None):
        # 执行测试
        result, message = reservation_service.is_can_reserved(
            mock_session,
            [mock_rule_item_valid_time.id],
            mock_product.id
        )

        # 验证结果
        assert result is False
        assert f"规则 {mock_rule_item_valid_time.rule_id} 不存在" in message
