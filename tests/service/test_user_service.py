import pytest
from sqlalchemy.orm import Session
from unittest.mock import patch, MagicMock

from app.schemas.user import PersonalUserCreate, EnterpriseCreate
from app.service.user import user_service
from app.models.user import PersonalUser, Enterprise, User
from app.models.account import RegularAccount, GiftAccount
from tests.utils import generate_random_string


# 使用DAO创建或更新个人用户时，username会强制设为手机号
# 因此，在测试时，需要使用手机号作为username
def test_create_personal_user(db: Session):
    """测试创建个人用户及其关联账户"""
    # 准备测试数据
    user_data = PersonalUserCreate(
        username=f"test_personal_{generate_random_string()}",
        phone=f"1{generate_random_string(10)}",
        password=f"{generate_random_string()}",
        email=f"{generate_random_string()}@example.com",
        address="测试地址",
        real_name="测试用户",
        id_card=f"{generate_random_string(18)}"
    )
    
    # 调用服务创建个人用户
    personal_user, accounts = user_service.create_personal_user(db, user_data)
    
    # 验证个人用户已创建
    assert isinstance(personal_user, PersonalUser)
    assert personal_user.username == user_data.phone
    assert personal_user.phone == user_data.phone
    assert personal_user.email == user_data.email
    assert personal_user.address == user_data.address
    assert personal_user.real_name == user_data.real_name
    assert personal_user.id_card == user_data.id_card
    
    # 验证账户已创建并关联
    assert "regular_account" in accounts
    assert "gift_account" in accounts
    
    regular_account = accounts["regular_account"]
    assert isinstance(regular_account, RegularAccount)
    assert regular_account.user_id == personal_user.id
    
    gift_account = accounts["gift_account"]
    assert isinstance(gift_account, GiftAccount)
    assert gift_account.user_id == personal_user.id
    
    # 从数据库中验证
    db_regular_account = db.query(RegularAccount).filter(
        RegularAccount.user_id == personal_user.id
    ).first()
    assert db_regular_account is not None
    assert db_regular_account.id == regular_account.id
    
    db_gift_account = db.query(GiftAccount).filter(
        GiftAccount.user_id == personal_user.id
    ).first()
    assert db_gift_account is not None
    assert db_gift_account.id == gift_account.id


# 使用DAO创建或更新企业时，username会强制设为企业名称
# 因此，在测试时，需要使用企业名称作为username
def test_create_enterprise(db: Session):
    """测试创建企业用户及其关联账户"""
    # 准备测试数据
    enterprise_data = EnterpriseCreate(
        username=f"test_enterprise_{generate_random_string()}",
        company_name=f"测试企业_{generate_random_string()}",
        business_license=f"{generate_random_string(18)}",
        phone=f"1{generate_random_string(10)}",
        email=f"{generate_random_string()}@example.com",
        address="测试地址"
    )
    
    # 调用服务创建企业用户
    enterprise, accounts = user_service.create_enterprise(db, enterprise_data)
    
    # 验证企业用户已创建
    assert isinstance(enterprise, Enterprise)
    assert enterprise.username == enterprise_data.company_name
    assert enterprise.company_name == enterprise_data.company_name
    assert enterprise.business_license == enterprise_data.business_license
    assert enterprise.phone == enterprise_data.phone
    assert enterprise.email == enterprise_data.email
    assert enterprise.address == enterprise_data.address
    
    # 验证账户已创建并关联
    assert "regular_account" in accounts
    assert "gift_account" in accounts
    
    regular_account = accounts["regular_account"]
    assert isinstance(regular_account, RegularAccount)
    assert regular_account.user_id == enterprise.id
    
    gift_account = accounts["gift_account"]
    assert isinstance(gift_account, GiftAccount)
    assert gift_account.user_id == enterprise.id
    
    # 从数据库中验证
    db_regular_account = db.query(RegularAccount).filter(
        RegularAccount.user_id == enterprise.id
    ).first()
    assert db_regular_account is not None
    assert db_regular_account.id == regular_account.id
    
    db_gift_account = db.query(GiftAccount).filter(
        GiftAccount.user_id == enterprise.id
    ).first()
    assert db_gift_account is not None
    assert db_gift_account.id == gift_account.id 