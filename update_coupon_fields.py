#!/usr/bin/env python3
"""
更新现有优惠券记录的新字段
将 None 值更新为默认值
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
import os

# 直接使用数据库URL，避免导入配置问题
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:123456@localhost:5432/yh_vegan_admin")

def update_coupon_fields():
    """更新优惠券表中的新字段"""
    
    # 创建数据库连接
    engine = create_engine(DATABASE_URL)
    
    with engine.connect() as conn:
        # 开始事务
        trans = conn.begin()
        
        try:
            # 更新 coupons 表中的 NULL 字段
            update_sql = """
            UPDATE coupons SET 
                condition_scope = 'order' WHERE condition_scope IS NULL;
            """
            conn.execute(text(update_sql))
            
            update_sql = """
            UPDATE coupons SET 
                condition_objects = '[]' WHERE condition_objects IS NULL;
            """
            conn.execute(text(update_sql))
            
            update_sql = """
            UPDATE coupons SET 
                condition_amount = 0 WHERE condition_amount IS NULL;
            """
            conn.execute(text(update_sql))
            
            update_sql = """
            UPDATE coupons SET 
                usage_quantity = 0 WHERE usage_quantity IS NULL;
            """
            conn.execute(text(update_sql))
            
            update_sql = """
            UPDATE coupons SET 
                usage_cycle = 'per_order' WHERE usage_cycle IS NULL;
            """
            conn.execute(text(update_sql))
            
            update_sql = """
            UPDATE coupons SET 
                usage_limit = 0 WHERE usage_limit IS NULL;
            """
            conn.execute(text(update_sql))
            
            update_sql = """
            UPDATE coupons SET 
                mutual_exclusive_rules = '[]' WHERE mutual_exclusive_rules IS NULL;
            """
            conn.execute(text(update_sql))
            
            update_sql = """
            UPDATE coupons SET 
                payment_channels = '[]' WHERE payment_channels IS NULL;
            """
            conn.execute(text(update_sql))
            
            update_sql = """
            UPDATE coupons SET 
                apply_objects = '[]' WHERE apply_objects IS NULL;
            """
            conn.execute(text(update_sql))
            
            update_sql = """
            UPDATE coupons SET 
                apply_order = 0 WHERE apply_order IS NULL;
            """
            conn.execute(text(update_sql))
            
            # 提交事务
            trans.commit()
            print("优惠券字段更新成功！")
            
        except Exception as e:
            # 回滚事务
            trans.rollback()
            print(f"更新失败: {e}")
            raise

if __name__ == "__main__":
    update_coupon_fields()
