// 环境辅助工具
// utils/env-helper.js

/**
 * 显示当前环境信息
 */
function showCurrentEnvInfo() {
  try {
    const app = getApp()
    if (!app || !app.globalData) {
      console.warn('无法获取应用实例或全局数据')
      return
    }
    
    const accountInfo = wx.getAccountInfoSync()
    const envVersion = accountInfo.miniProgram.envVersion
    
    console.log('=== 当前环境信息 ===')
    console.log('小程序版本:', envVersion)
    console.log('baseUrlHost:', app.globalData.baseUrlHost)
    console.log('baseUrl:', app.globalData.baseUrl)
    console.log('debug模式:', app.globalData.debug)
    console.log('==================')
    
    // 在开发环境下显示更多信息
    if (app.globalData.debug) {
      console.log('=== 调试信息 ===')
      console.log('appId:', accountInfo.miniProgram.appId)
      console.log('版本号:', accountInfo.miniProgram.version)
      console.log('===============')
    }
  } catch (error) {
    console.error('显示环境信息失败:', error)
  }
}

/**
 * 检查网络连接状态
 */
function checkNetworkStatus() {
  return new Promise((resolve) => {
    wx.getNetworkType({
      success: (res) => {
        console.log('网络类型:', res.networkType)
        resolve(res.networkType !== 'none')
      },
      fail: () => {
        console.warn('获取网络状态失败')
        resolve(false)
      }
    })
  })
}

/**
 * 测试API连接
 */
async function testApiConnection() {
  try {
    const app = getApp()
    if (!app || !app.globalData) {
      throw new Error('无法获取应用配置')
    }
    
    const isConnected = await checkNetworkStatus()
    if (!isConnected) {
      throw new Error('网络连接不可用')
    }
    
    console.log('正在测试API连接:', app.globalData.baseUrl)
    
    // 这里可以添加一个简单的健康检查接口
    // 暂时只返回配置信息
    return {
      success: true,
      baseUrl: app.globalData.baseUrl,
      baseUrlHost: app.globalData.baseUrlHost
    }
  } catch (error) {
    console.error('API连接测试失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 在页面中显示环境信息（用于调试）
 */
function getEnvDisplayInfo() {
  try {
    const app = getApp()
    const accountInfo = wx.getAccountInfoSync()
    
    return {
      version: accountInfo.miniProgram.envVersion,
      baseUrl: app.globalData.baseUrl,
      baseUrlHost: app.globalData.baseUrlHost,
      debug: app.globalData.debug,
      appId: accountInfo.miniProgram.appId
    }
  } catch (error) {
    console.error('获取环境显示信息失败:', error)
    return {
      version: '未知',
      baseUrl: '未知',
      baseUrlHost: '未知',
      debug: false,
      appId: '未知'
    }
  }
}

module.exports = {
  showCurrentEnvInfo,
  checkNetworkStatus,
  testApiConnection,
  getEnvDisplayInfo
}
