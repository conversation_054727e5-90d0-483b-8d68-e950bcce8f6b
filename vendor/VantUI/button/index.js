import { VantComponent } from '../common/component';
import { button } from '../mixins/button';
import { openType } from '../mixins/open-type';
VantComponent({
  classes: ['loading-class'],
  mixins: [button, openType],
  props: {
    plain: <PERSON><PERSON><PERSON>,
    block: <PERSON><PERSON><PERSON>,
    round: <PERSON><PERSON><PERSON>,
    square: <PERSON><PERSON><PERSON>,
    loading: <PERSON><PERSON><PERSON>,
    disabled: <PERSON><PERSON><PERSON>,
    type: {
      type: String,
      value: 'default'
    },
    size: {
      type: String,
      value: 'normal'
    }
  },
  methods: {
    onClick: function onClick() {
      if (!this.data.disabled && !this.data.loading) {
        this.$emit('click');
      }
    }
  }
});