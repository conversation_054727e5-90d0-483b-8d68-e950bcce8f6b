<wxs src="../wxs/utils.wxs" module="utils" />

<button
  id="{{ id }}"
  lang="{{ lang }}"
  class="custom-class {{ utils.bem('button', [type, size, { block, round, plain, square, loading, disabled, unclickable: disabled || loading }]) }}"
  open-type="{{ openType }}"
  session-from="{{ sessionFrom }}"
  app-parameter="{{ appParameter }}"
  send-message-img="{{ sendMessageImg }}"
  send-message-path="{{ sendMessagePath }}"
  show-message-card="{{ showMessageCard }}"
  send-message-title="{{ sendMessageTitle }}"
  bind:tap="onClick"
  binderror="bindError"
  bindcontact="bindContact"
  bindopensetting="bindOpenSetting"
  bindgetuserinfo="bindGetUserInfo"
  bindgetphonenumber="bindGetPhoneNumber"
>
  <van-loading
    wx:if="{{ loading }}"
    custom-class="loading-class"
    size="{{ size === 'mini' ? '14px' : '20px' }}"
    color="{{ type === 'default' ? '#c9c9c9' : '' }}"
  />
  <slot wx:else />
</button>
