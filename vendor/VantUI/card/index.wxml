<view class="custom-class van-card {{ centered ? 'van-card--center' : '' }}">
  <view class="van-card__header">
    <view class="van-card__thumb" bind:tap="onClickThumb">
      <image
        wx:if="{{ thumb }}"
        src="{{ thumb }}"
        mode="{{ thumbMode }}"
        lazy-load="{{ lazyLoad }}"
        class="van-card__img thumb-class"
      />
      <slot name="thumb" />
      <van-tag
        wx:if="{{ tag }}"
        mark
        type="danger"
        custom-class="van-card__tag"
      >
        {{ tag }}
      </van-tag>
    </view>

    <view class="van-card__content">
      <view wx:if="{{ title }}" class="van-card__title van-multi-ellipsis--l2 title-class">{{ title }}</view>
      <slot wx:else name="title" />

      <view wx:if="{{ desc }}" class="van-card__desc van-ellipsis desc-class">{{ desc }}</view>
      <slot wx:else name="desc" />

      <slot name="tags" />

      <view class="van-card__bottom">
        <view wx:if="{{ price || price === 0 }}" class="van-card__price price-class">{{ currency }} {{ price }}</view>
        <view wx:if="{{ originPrice || originPrice === 0 }}" class="van-card__origin-price origin-price-class">{{ currency }} {{ originPrice }}</view>
        <view wx:if="{{ num }}" class="van-card__num num-class">x {{ num }}</view>
      </view>
    </view>
  </view>

  <view class="van-card__footer">
    <slot name="footer" />
  </view>
</view>
