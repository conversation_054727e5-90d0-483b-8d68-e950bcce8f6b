<wxs src="../wxs/utils.wxs" module="utils" />

<view class="van-collapse-item van-hairline--top custom-class">
  <van-cell
    title="{{ title }}"
    icon="{{ icon }}"
    is-link="{{ isLink }}"
    value="{{ value }}"
    label="{{ label }}"
    border="{{ border && expanded }}"
    class="{{ utils.bem('collapse-item__title', { disabled, expanded }) }}"
    right-icon-class="van-cell__right-icon"
    custom-class="van-cell"
    bind:click="onClick"
  >
    <slot
      name="title"
      slot="title"
    />
    <slot
      name="icon"
      slot="icon"
    />
    <slot name="value" />
    <slot
      name="right-icon"
      slot="right-icon"
    />
  </van-cell>
  <view
    class="van-collapse-item__wrapper"
    style="height: {{ contentHeight }};"
    animation="{{ animationData }}"
    bind:transitionend="onTransitionEnd"
  >
    <view
      class="van-collapse-item__content content-class"
    >
      <slot />
    </view>
  </view>
</view>
