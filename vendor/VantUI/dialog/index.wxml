<van-popup
  show="{{ show }}"
  z-index="{{ zIndex }}"
  overlay="{{ overlay }}"
  custom-class="van-dialog"
  transition="{{ transition }}"
  close-on-click-overlay="{{ closeOnClickOverlay }}"
  bind:close="onClickOverlay"
>
  <view
    wx:if="{{ title }}"
    class="van-dialog__header {{ message || useSlot ? '' : 'van-dialog--isolated' }}"
  >
    {{ title }}
  </view>

  <slot wx:if="{{ useSlot }}" />
  <view
    wx:elif="{{ message }}"
    class="van-dialog__message {{ title ? 'van-dialog__message--has-title' : '' }} {{ messageAlign ? 'van-dialog__message--' + messageAlign : '' }}"
  >
    <text>{{ message }}</text>
  </view>

  <view class="van-hairline--top van-dialog__footer">
    <van-button
      wx:if="{{ showCancelButton }}"
      size="large"
      loading="{{ loading.cancel }}"
      class="van-dialog__button van-hairline--right"
      custom-class="van-dialog__cancel"
      bind:click="onCancel"
    >
      {{ cancelButtonText }}
    </van-button>
    <van-button
      wx:if="{{ showConfirmButton }}"
      size="large"
      class="van-dialog__button"
      loading="{{ loading.confirm }}"
      custom-class="van-dialog__confirm"
      open-type="{{ confirmButtonOpenType }}"
      bind:click="onConfirm"
      binderror="bindError"
      bindcontact="bindContact"
      bindopensetting="bindOpenSetting"
      bindgetuserinfo="bindGetUserInfo"
      bindgetphonenumber="bindGetPhoneNumber"
    >
      {{ confirmButtonText }}
    </van-button>
  </view>
</van-popup>
