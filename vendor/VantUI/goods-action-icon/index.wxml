<van-button
  square
  id="{{ id }}"
  size="large"
  lang="{{ lang }}"
  loading="{{ loading }}"
  disabled="{{ disabled }}"
  open-type="{{ openType }}"
  custom-class="van-goods-action-icon"
  session-from="{{ sessionFrom }}"
  app-parameter="{{ appParameter }}"
  send-message-img="{{ sendMessageImg }}"
  send-message-path="{{ sendMessagePath }}"
  show-message-card="{{ showMessageCard }}"
  send-message-title="{{ sendMessageTitle }}"
  bind:click="onClick"
  binderror="bindError"
  bindcontact="bindContact"
  bindopensetting="bindOpenSetting"
  bindgetuserinfo="bindGetUserInfo"
  bindgetphonenumber="bindGetPhoneNumber"
>
  <view class="van-goods-action-icon__content van-hairline--right">
    <van-icon
      size="20px"
      name="{{ icon }}"
      info="{{ info }}"
      class="van-goods-action-icon__icon"
    />
    {{ text }}
  </view>
</van-button>
