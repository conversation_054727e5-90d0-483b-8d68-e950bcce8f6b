function setAsync(context, data) {
  return new Promise(function (resolve) {
    context.setData(data, resolve);
  });
}

;
export var behavior = Behavior({
  created: function created() {
    var _this = this;

    if (!this.$options) {
      return;
    }

    var cache = {};

    var _this$$options = this.$options(),
        computed = _this$$options.computed;

    var keys = Object.keys(computed);

    this.calcComputed = function () {
      var needUpdate = {};
      keys.forEach(function (key) {
        var value = computed[key].call(_this);

        if (cache[key] !== value) {
          cache[key] = needUpdate[key] = value;
        }
      });
      return needUpdate;
    };
  },
  attached: function attached() {
    this.set();
  },
  methods: {
    // set data and set computed data
    set: function set(data, callback) {
      var _this2 = this;

      var stack = [];

      if (data) {
        stack.push(setAsync(this, data));
      }

      if (this.calcComputed) {
        stack.push(setAsync(this, this.calcComputed()));
      }

      return Promise.all(stack).then(function (res) {
        if (callback && typeof callback === 'function') {
          callback.call(_this2);
        }

        return res;
      });
    }
  }
});