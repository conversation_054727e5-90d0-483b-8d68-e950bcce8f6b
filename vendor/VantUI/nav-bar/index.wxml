<view
  class="custom-class van-nav-bar {{ border ? 'van-hairline--bottom' : '' }} {{ fixed ? 'van-nav-bar--fixed' : '' }}"
  style="z-index: {{ zIndex }}"
>
  <view class="van-nav-bar__left" bind:tap="onClickLeft">
    <block wx:if="{{ leftArrow || leftText }}">
      <van-icon
        wx:if="{{ leftArrow }}"
        size="16px"
        name="arrow-left"
        custom-class="van-nav-bar__arrow"
      />
      <view wx:if="{{ leftText }}" class="van-nav-bar__text">{{ leftText }}</view>
    </block>
    <slot wx:else name="left" />
  </view>
  <view class="van-nav-bar__title title-class van-ellipsis">
    <block wx:if="{{ title }}">{{ title }}</block>
    <slot wx:else name="title" />
  </view>
  <view class="van-nav-bar__right" bind:tap="onClickRight">
    <view wx:if="{{ rightText }}" class="van-nav-bar__text">{{ rightText }}</view>
    <slot wx:else name="right" />
  </view>
</view>
