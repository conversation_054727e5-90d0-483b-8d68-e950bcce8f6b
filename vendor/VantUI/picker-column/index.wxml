<view
  class="van-picker-column custom-class"
  style="height: {{ itemHeight * visibleItemCount }}px"
  bind:touchstart="onTouchStart"
  catch:touchmove="onTouchMove"
  bind:touchend="onTouchEnd"
  bind:touchcancel="onTouchEnd"
>
  <view style="{{ wrapperStyle }}">
    <view
      wx:for="{{ options }}"
      wx:for-item="option"
      wx:key="index"
      data-index="{{ index }}"
      style="height: {{ itemHeight }}px"
      class="van-ellipsis van-picker-column__item {{ option && option.disabled ? 'van-picker-column__item--disabled' : '' }} {{ index === currentIndex ? 'van-picker-column__item--selected active-class' : '' }}"
      bindtap="onClickItem"
    >{{ getOptionText(option, valueKey) }}</view>
  </view>
</view>

<wxs module="getOptionText">
function isObj(x) {
  var type = typeof x;
  return x !== null && (type === 'object' || type === 'function');
}

module.exports = function (option, valueKey) {
  return isObj(option) && option[valueKey] ? option[valueKey] : option;
}
</wxs>
