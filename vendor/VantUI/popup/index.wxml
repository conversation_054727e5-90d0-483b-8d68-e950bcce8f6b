<wxs src="../wxs/utils.wxs" module="utils" />

<van-overlay
  wx:if="{{ inited && overlay }}"
  mask
  show="{{ show }}"
  z-index="{{ zIndex }}"
  custom-style="{{ overlayStyle }}"
  bind:click="onClickOverlay"
/>
<view
  wx:if="{{ inited }}"
  class="custom-class {{ utils.bem('popup', [position, { safe: isIPhoneX && safeAreaInsetBottom && position === 'bottom' }]) }}"
  style="z-index: {{ zIndex }}; -webkit-animation: van-{{ transition || position }}-{{ type }} {{ duration }}ms both; animation: van-{{ transition || position }}-{{ type }} {{ duration }}ms both; {{ display ? '' : 'display: none;' }}{{ customStyle }}"
  bind:animationend="onAnimationEnd"
>
  <slot />
</view>
