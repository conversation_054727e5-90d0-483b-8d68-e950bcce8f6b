import { VantComponent } from '../common/component';
import { BLUE } from '../common/color';
VantComponent({
  props: {
    inactive: Boolean,
    percentage: Number,
    pivotText: String,
    pivotColor: String,
    showPivot: {
      type: Boolean,
      value: true
    },
    color: {
      type: String,
      value: BLUE
    },
    textColor: {
      type: String,
      value: '#fff'
    }
  },
  data: {
    pivotWidth: 0,
    progressWidth: 0
  },
  watch: {
    pivotText: 'getWidth',
    showPivot: 'getWidth'
  },
  computed: {
    portionStyle: function portionStyle() {
      var width = (this.data.progressWidth - this.data.pivotWidth) * this.data.percentage / 100 + 'px';
      var background = this.getCurrentColor();
      return "width: " + width + "; background: " + background + "; ";
    },
    pivotStyle: function pivotStyle() {
      var color = this.data.textColor;
      var background = this.data.pivotColor || this.getCurrentColor();
      return "color: " + color + "; background: " + background;
    },
    text: function text() {
      return this.data.pivotText || this.data.percentage + '%';
    }
  },
  mounted: function mounted() {
    this.getWidth();
  },
  methods: {
    getCurrentColor: function getCurrentColor() {
      return this.data.inactive ? '#cacaca' : this.data.color;
    },
    getWidth: function getWidth() {
      var _this = this;

      this.getRect('.van-progress').then(function (rect) {
        _this.set({
          progressWidth: rect.width
        });
      });
      this.getRect('.van-progress__pivot').then(function (rect) {
        _this.set({
          pivotWidth: rect.width || 0
        });
      });
    }
  }
});