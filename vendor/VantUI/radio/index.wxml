<wxs src="../wxs/utils.wxs" module="utils" />

<view class="van-radio custom-class">
  <view class="van-radio__input">
    <radio-group bindchange="onChange">
      <radio
        value="{{ name }}"
        checked="{{ value === name }}"
        disabled="{{ disabled }}"
        class="van-radio__control"
      />
    </radio-group>
    <van-icon
      class="{{ utils.bem('radio__icon', { disabled, checked: !disabled && name === value, check: !disabled && name !== value }) }}"
      custom-class="icon-class"
      color="{{ value === name ? checkedColor : '' }}"
      name="{{ value === name ? 'checked' : 'circle' }}"
    />
  </view>
  <view class="van-radio__label van-radio__label--{{ labelPosition }} label-class" bindtap="onClickLabel">
    <slot />
  </view>
</view>
