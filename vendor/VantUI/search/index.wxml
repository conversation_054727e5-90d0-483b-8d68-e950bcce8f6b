<view
  class="van-search custom-class {{ showAction || useActionSlot ? 'van-search--show-action' : '' }}"
  style="background: {{ background }}"
>
  <van-field
    clearable
    type="search"
    left-icon="search"
    focus="{{ focus }}"
    error="{{ error }}"
    border="{{ false }}"
    confirm-type="search"
    class="van-search__field field-class"
    value="{{ value }}"
    disabled="{{ disabled }}"
    readonly="{{ readony }}"
    maxlength="{{ maxlength }}"
    input-align="{{ inputAlign }}"
    input-class="input-class"
    placeholder="{{ placeholder }}"
    placeholder-style="{{ placeholderStyle }}"
    custom-style="padding: 3px 10px"
    bind:blur="onBlur"
    bind:focus="onFocus"
    bind:change="onChange"
    bind:confirm="onSearch"
    bind:clear="onClear"
  />
  <view wx:if="{{ showAction || useActionSlot }}" class="van-search__action">
    <slot wx:if="{{ useActionSlot }}" name="action" />
    <view wx:else bind:tap="onCancel" class="cancel-class">取消</view>
  </view>
</view>
