import { VantComponent } from '../common/component';
VantComponent({
  field: true,
  props: {
    title: String,
    border: Boolean,
    checked: <PERSON><PERSON><PERSON>,
    loading: <PERSON><PERSON><PERSON>,
    disabled: <PERSON><PERSON>an,
    activeColor: String,
    inactiveColor: String,
    size: {
      type: String,
      value: '24px'
    }
  },
  watch: {
    checked: function checked(value) {
      this.set({
        value: value
      });
    }
  },
  created: function created() {
    this.set({
      value: this.data.checked
    });
  },
  methods: {
    onChange: function onChange(event) {
      this.$emit('change', event.detail);
    }
  }
});