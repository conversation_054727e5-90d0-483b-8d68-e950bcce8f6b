<wxs src="../wxs/utils.wxs" module="utils" />

<view class="custom-class van-tabs van-tabs--{{ type }}">
  <view style="z-index: {{ zIndex }}; {{ wrapStyle }}" class="van-tabs__wrap {{ scrollable ? 'van-tabs__wrap--scrollable' : '' }} {{ type === 'line' && border ? 'van-hairline--top-bottom' : '' }}">
    <scroll-view
      scroll-x="{{ scrollable }}"
      scroll-with-animation
      scroll-left="{{ scrollLeft }}"
      class="van-tabs__scroll--{{ type }}"
      style="{{ color ? 'border-color: ' + color : '' }}"
    >
      <view class="van-tabs__nav van-tabs__nav--{{ type }}">
        <view wx:if="{{ type === 'line' }}" class="van-tabs__line" style="{{ lineStyle }}" />
        <view
          wx:for="{{ tabs }}"
          wx:key="index"
          data-index="{{ index }}"
          class="van-ellipsis {{ utils.bem('tab', { active: index === active, disabled: item.disabled }) }}"
          style="{{ color && index !== active && type === 'card' && !item.disabled ? 'color: ' + color : '' }} {{ color && index === active && type === 'card' ? ';background-color:' + color : '' }} {{ color ? ';border-color: ' + color : '' }} {{ scrollable ? ';flex-basis:' + (88 / swipeThreshold) + '%' : '' }}"
          bind:tap="onTap"
        >
          <view class="van-ellipsis {{ utils.bem('tab__title', { dot: item.dot }) }}" style="{{ item.titleStyle }}">
            {{ item.title }}
            <van-info
              wx:if="{{ item.info !== null }}"
              info="{{ item.info }}"
              custom-class="van-tab__title__info"
            />
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
  <view
    class="van-tabs__content"
    bind:touchstart="onTouchStart"
    bind:touchmove="onTouchMove"
    bind:touchend="onTouchEnd"
    bind:touchcancel="onTouchEnd"
  >
    <view class="van-tabs__track" style="{{ trackStyle }}">
      <slot />
    </view>
  </view>
</view>
