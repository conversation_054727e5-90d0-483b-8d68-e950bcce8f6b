<wxs src="../wxs/utils.wxs" module="utils" />

<view
  class="van-tree-select"
  style="height: {{ mainHeight }}px"
>
  <scroll-view scroll-y class="van-tree-select__nav">
    <view
      wx:for="{{ items }}"
      wx:key="index"
      class="van-ellipsis main-item-class {{ utils.bem('tree-select__nitem', { active: mainActiveIndex === index, disabled: item.disabled }) }} {{ mainActiveIndex === index ? 'main-active-class' : '' }} {{ item.disabled ? 'main-disabled-class' : '' }}"
      data-index="{{ index }}"
      bind:tap="onClickNav"
    >
      {{ item.text }}
    </view>
  </scroll-view>
  <scroll-view
    scroll-y
    class="van-tree-select__content"
    style="height: {{ itemHeight }}px"
  >
    <view
      wx:for="{{ subItems }}"
      wx:key="item.id"
      class="van-ellipsis van-hairline--bottom content-item-class {{ utils.bem('tree-select__item', { active: activeId === item.id, disabled: item.disabled }) }} {{ activeId === item.id ? 'content-active-class' : '' }} {{ item.disabled ? 'content-disabled-class' : '' }}"
      data-item="{{ item }}"
      bind:tap="onSelectItem"
    >
      {{ item.text }}
      <van-icon
        wx:if="{{ activeId === item.id }}"
        name="checked"
        size="16px"
        class="van-tree-select__selected"
      />
    </view>
  </scroll-view>
</view>
