<template name="capsule">
  <view class="zan-capsule zan-capsule--{{type}}">
    <block wx:if="{{color}}">
      <view class="zan-capsule__left" style="background: {{ color }}; border-color: {{ color }}">{{ leftText }}</view>
      <view class="zan-capsule__right" style="color: {{ color }}; border-color: {{ color }}">{{ rightText }}</view>
    </block>
    <block wx:else>
      <view class="zan-capsule__left">{{ leftText }}</view>
      <view class="zan-capsule__right">{{ rightText }}</view>
    </block>
  </view>
</template>
