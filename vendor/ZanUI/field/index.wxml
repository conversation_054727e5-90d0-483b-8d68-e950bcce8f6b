<template name="zan-field">
  <view class="zan-cell zan-field {{ error ? 'zan-field--error' : '' }} {{ mode === 'wrapped' ? 'zan-field--wrapped' : '' }}">
    <view
      wx:if="{{ title }}"
      class="zan-cell__hd zan-field__title">{{ title }}</view>
    <textarea
      wx:if="{{ type === 'textarea' }}"
      auto-height
      name="{{ name || componentId || '' }}"
      value="{{ value }}"
      placeholder="{{ placeholder }}"
      class="zan-field__input zan-cell__bd {{ right ? 'zan-field__input--right' : '' }}"
      placeholder-class="zan-field__placeholder"
      bindinput="_handleZanFieldChange"
      bindfocus="_handleZanFieldFocus"
      bindblur="_handleZanFieldBlur"
      data-component-id="{{ componentId || '' }}"></textarea>
    <input
      wx:else
      type="{{ inputType || 'text' }}"
      name="{{ name || componentId || '' }}"
      value="{{ value }}"
      placeholder="{{ placeholder }}"
      class="zan-field__input zan-cell__bd {{ right ? 'zan-field__input--right' : '' }}"
      placeholder-class="zan-field__placeholder"
      bindinput="_handleZanFieldChange"
      bindfocus="_handleZanFieldFocus"
      bindblur="_handleZanFieldBlur"
      data-component-id="{{ componentId || '' }}"/>
  </view>
</template>
